# Git
.git
.gitignore
.gitattributes

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Documentation
README.md
*.md

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
docker/logs/

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar
*.tar.gz
*.rar

# Cache directories
.cache/
cache/

# WordPress specific
wp-config-sample.php
.htaccess.sample

# Deployment files
Makefile
exclude.txt

# Development tools
phpunit.xml
phpunit.xml.dist
.phpunit.result.cache

# Composer (keep composer.json and composer.lock if they exist)
vendor/

# Database dumps
*.sql
*.sql.gz

sp_dev_db_.sql
# WordPress core files
/wp-admin/
/wp-includes/

# Sensitive files
wp-config.php
wp-config-sample.php
wp-content/plugins/supportboard/supportboard/config.php

# Log files
*.log
*.wpress

# OS generated files
.DS_Store
Thumbs.db

# Environment files
.env
.env.*

# Vendor and dependency directories
/vendor/
/node_modules/

# Uploads (optional – you might want to version media during development)
wp-content/uploads/

# Theme and plugin caches or builds
wp-content/**/*.log
wp-content/**/*.cache
wp-content/**/dist/
wp-content/**/build/
wp-content/ai1wm-backups/

# Ignore all plugins and themes except custom ones
!wp-content/plugins/
wp-content/plugins/*
!wp-content/plugins/supportboard/
!wp-content/plugins/primalcom-custom-plugins/
!wp-content/plugins/custom-multiple-addresses/
!wp-content/plugins/custom-support-ticket/

!wp-content/themes/
wp-content/themes/*
# !wp-content/themes/supportboard/
.well-known
.wp-cli

license.txt
mysql/
wp-content/maintenance
config.php
wp-content/wpo-cache
log.txt
sp_dev_db.sql
conf/*
local-xdebuginfo.php
backups


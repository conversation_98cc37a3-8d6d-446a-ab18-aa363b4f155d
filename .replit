modules = ["php-8.2", "nodejs-20", "web"]
compile = "make"
run = ""

[nix]
channel = "stable-24_05"
packages = [
  "unzip",
  "docker_26",
  "apacheHttpd",
  "phpWithExtensions",
  "phpPackages.composer",
]


[deployment]
build = ["sh", "-c", "make"]
run = ["sh", "-c", "docker compose up"]

[workflows]
runButton = "WordPress Server"

[[workflows.workflow]]
name = "Start Web Server"
author = 42817034
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "sudo systemctl start php8.2-fpm"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "sudo systemctl start apache2"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "echo \"Web server started on port 5000\""

[[workflows.workflow]]
name = "Start Apache PHP-FPM"
author = 42817034
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "./start-server.sh"

[[workflows.workflow]]
name = "WordPress Server"
author = 42817034
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "chmod +x start-server.sh"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "./start-server.sh"

[[ports]]
localPort = 80
externalPort = 80

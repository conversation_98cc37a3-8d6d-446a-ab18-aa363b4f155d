# WordPress Support Board - Development Setup

## Initial Setup (Run Once After Git Clone)

⚠️ **Important: These steps are required only once after cloning the repository.**

1. **Add your user to www-data group:**
   ```bash
   sudo usermod -aG www-data $USER
   ```

2. **Restart your session:**
   - Log out and log back in, OR
   - Run `newgrp www-data` to refresh group membership

3. **Verify group membership:**
   ```bash
   groups
   ```
   You should see `www-data` in the output.

## Docker Commands for Development

### Build Commands
```bash
# Build docker images
make dev-build

# Build docker images without cache (clean build)
make dev-build-no-cache
```

### Container Management
```bash
# Start containers (automatically fixes permissions)
make dev-up

# Stop containers
make dev-down
```

### Permission Management
```bash
# Fix permission issues manually (if needed)
make dev-fix-perms
```

> **Note:** The `make dev-up` command automatically handles permission setup, so you typically won't need to run `make dev-fix-perms` separately.

## Troubleshooting

If you encounter permission issues:
1. Ensure you completed the initial setup steps above
2. Run `make dev-fix-perms` to fix permissions manually
3. Verify your user is in the www-data group with `groups` command



## Update sb_settings with below script on phpmyadmin if got error NULL :
-- First, let's check what's in the external_settings column of the settings table
SELECT * FROM sb_settings WHERE name = 'external-settings';

-- If external-settings is NULL or empty, let's create it with proper structure
UPDATE sb_settings 
SET value = '{"embedding-sources":[],"embedding-texts":[],"embedding-source-names":[],"embedding-source-files":[],"embedding-source-websites":[],"embedding-source-qea":[],"embedding-source-conversations":[],"embedding-source-articles":[],"external-settings-translations":[]}' 
WHERE name = 'external-settings' AND (value IS NULL OR value = '' OR value = 'null');

-- If external-settings doesn't exist, create it
INSERT INTO sb_settings (name, value)
SELECT 'external-settings', '{"embedding-sources":[],"embedding-texts":[],"embedding-source-names":[],"embedding-source-files":[],"embedding-source-websites":[],"embedding-source-qea":[],"embedding-source-conversations":[],"embedding-source-articles":[],"external-settings-translations":[]}'
WHERE NOT EXISTS (SELECT 1 FROM sb_settings WHERE name = 'external-settings');

-- Now fix all the individual settings
-- Fix embedding-sources setting
INSERT INTO sb_settings (name, value)
SELECT 'embedding-sources', '[]'
WHERE NOT EXISTS (SELECT 1 FROM sb_settings WHERE name = 'embedding-sources');

UPDATE sb_settings 
SET value = '[]' 
WHERE name = 'embedding-sources' AND (value IS NULL OR value = '' OR value = 'null');

-- Fix embedding-texts setting
INSERT INTO sb_settings (name, value)
SELECT 'embedding-texts', '[]'
WHERE NOT EXISTS (SELECT 1 FROM sb_settings WHERE name = 'embedding-texts');

UPDATE sb_settings 
SET value = '[]' 
WHERE name = 'embedding-texts' AND (value IS NULL OR value = '' OR value = 'null');

-- Fix embedding-source-names setting
INSERT INTO sb_settings (name, value)
SELECT 'embedding-source-names', '[]'
WHERE NOT EXISTS (SELECT 1 FROM sb_settings WHERE name = 'embedding-source-names');

UPDATE sb_settings 
SET value = '[]' 
WHERE name = 'embedding-source-names' AND (value IS NULL OR value = '' OR value = 'null');

-- Fix embedding-source-files setting
INSERT INTO sb_settings (name, value)
SELECT 'embedding-source-files', '[]'
WHERE NOT EXISTS (SELECT 1 FROM sb_settings WHERE name = 'embedding-source-files');

UPDATE sb_settings 
SET value = '[]' 
WHERE name = 'embedding-source-files' AND (value IS NULL OR value = '' OR value = 'null');

-- Fix embedding-source-websites setting
INSERT INTO sb_settings (name, value)
SELECT 'embedding-source-websites', '[]'
WHERE NOT EXISTS (SELECT 1 FROM sb_settings WHERE name = 'embedding-source-websites');

UPDATE sb_settings 
SET value = '[]' 
WHERE name = 'embedding-source-websites' AND (value IS NULL OR value = '' OR value = 'null');

-- Fix embedding-source-qea setting
INSERT INTO sb_settings (name, value)
SELECT 'embedding-source-qea', '[]'
WHERE NOT EXISTS (SELECT 1 FROM sb_settings WHERE name = 'embedding-source-qea');

UPDATE sb_settings 
SET value = '[]' 
WHERE name = 'embedding-source-qea' AND (value IS NULL OR value = '' OR value = 'null');

-- Fix embedding-source-conversations setting
INSERT INTO sb_settings (name, value)
SELECT 'embedding-source-conversations', '[]'
WHERE NOT EXISTS (SELECT 1 FROM sb_settings WHERE name = 'embedding-source-conversations');

UPDATE sb_settings 
SET value = '[]' 
WHERE name = 'embedding-source-conversations' AND (value IS NULL OR value = '' OR value = 'null');

-- Fix embedding-source-articles setting
INSERT INTO sb_settings (name, value)
SELECT 'embedding-source-articles', '[]'
WHERE NOT EXISTS (SELECT 1 FROM sb_settings WHERE name = 'embedding-source-articles');

UPDATE sb_settings 
SET value = '[]' 
WHERE name = 'embedding-source-articles' AND (value IS NULL OR value = '' OR value = 'null');

-- Fix external-settings-translations setting
INSERT INTO sb_settings (name, value)
SELECT 'external-settings-translations', '[]'
WHERE NOT EXISTS (SELECT 1 FROM sb_settings WHERE name = 'external-settings-translations');

UPDATE sb_settings 
SET value = '[]' 
WHERE name = 'external-settings-translations' AND (value IS NULL OR value = '' OR value = 'null');

-- Fix embeddings-language setting
INSERT INTO sb_settings (name, value)
SELECT 'embeddings-language', '["en"]'
WHERE NOT EXISTS (SELECT 1 FROM sb_settings WHERE name = 'embeddings-language');

UPDATE sb_settings 
SET value = '["en"]' 
WHERE name = 'embeddings-language' AND (value IS NULL OR value = '' OR value = 'null');
# WordPress Support Board - Docker Setup

This Docker setup provides a complete development environment for the WordPress Support Board application with nginx, PHP-FPM, MySQL, Redis, and phpMyAdmin.

## 🚀 Quick Start

### Prerequisites
- Docker Engine 20.10+
- Docker Compose 2.0+
- At least 2GB of available RAM

### 1. <PERSON><PERSON> and Setup
```bash
git clone <your-repo-url>
cd wpsupportboard
```

### 2. Start the Stack
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Check status
docker-compose ps
```

### 3. Access Your Application
- **WordPress Site**: http://localhost
- **phpMyAdmin**: http://localhost:8080
- **MySQL**: localhost:3306
- **Redis**: localhost:6379

## 📋 Services Overview

### Nginx (Web Server)
- **Port**: 80, 443
- **Configuration**: `docker/nginx/`
- **Features**: 
  - WordPress-optimized configuration
  - Security headers
  - Rate limiting
  - Static file caching
  - Gzip compression

### PHP-FPM
- **Version**: PHP 8.3
- **Configuration**: `docker/php/`
- **Extensions**: bcmath, gd, intl, mbstring, mysqli, opcache, pdo_mysql, redis, zip
- **Features**:
  - Optimized for WordPress
  - Redis support
  - Large file upload support (720MB)
  - Extended execution time (900s)

### MySQL
- **Version**: 8.0
- **Port**: 3306
- **Database**: wpsupportboard
- **Credentials**:
  - Root: `root` / `root_password`
  - WordPress: `wordpress` / `wordpress_password`

### Redis
- **Version**: Alpine
- **Port**: 6379
- **Purpose**: Object caching for WordPress

### phpMyAdmin
- **Port**: 8080
- **Credentials**: Use MySQL credentials above

## 🔧 Configuration

### Environment Variables
Create a `.env.docker` file to override default settings:

```bash
# Database
MYSQL_ROOT_PASSWORD=your_root_password
MYSQL_DATABASE=your_db_name
MYSQL_USER=your_db_user
MYSQL_PASSWORD=your_db_password

# WordPress
WORDPRESS_DB_HOST=mysql:3306
WORDPRESS_DB_NAME=your_db_name
WORDPRESS_DB_USER=your_db_user
WORDPRESS_DB_PASSWORD=your_db_password
WP_DEBUG=true

# Ports (if you need to change them)
HTTP_PORT=80
HTTPS_PORT=443
MYSQL_PORT=3306
PHPMYADMIN_PORT=8080
REDIS_PORT=6379
```

### WordPress Configuration
The setup automatically updates `wp-config.php` with:
- Database connection settings
- Redis configuration
- Debug settings (if enabled)

## 🛠️ Development Commands

### Docker Compose Commands
```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# Restart a specific service
docker-compose restart nginx

# View logs
docker-compose logs -f php-fpm
docker-compose logs -f nginx

# Execute commands in containers
docker-compose exec php-fpm bash
docker-compose exec mysql mysql -u root -p

# Update and rebuild
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### Database Operations
```bash
# Backup database
docker-compose exec mysql mysqldump -u root -p wpsupportboard > backup.sql

# Restore database
docker-compose exec -T mysql mysql -u root -p wpsupportboard < backup.sql

# Access MySQL CLI
docker-compose exec mysql mysql -u root -p
```

### File Permissions
```bash
# Fix file permissions
docker-compose exec php-fpm chown -R www-data:www-data /var/www/html
docker-compose exec php-fpm find /var/www/html -type d -exec chmod 755 {} \;
docker-compose exec php-fpm find /var/www/html -type f -exec chmod 644 {} \;
```

## 📁 Directory Structure

```
├── docker/
│   ├── nginx/
│   │   ├── nginx.conf          # Main nginx configuration
│   │   └── default.conf        # WordPress site configuration
│   ├── php/
│   │   ├── php.ini            # PHP configuration
│   │   └── php-fpm.conf       # PHP-FPM pool configuration
│   ├── mysql/
│   │   └── my.cnf             # MySQL configuration
│   ├── redis/
│   │   └── redis.conf         # Redis configuration
│   ├── logs/                  # Log files directory
│   └── docker-entrypoint.sh   # Container initialization script
├── Dockerfile                 # PHP-FPM container definition
├── docker-compose.yml         # Services orchestration
└── .dockerignore              # Docker build exclusions
```

## 🔍 Troubleshooting

### Common Issues

1. **Port conflicts**
   ```bash
   # Check what's using the port
   lsof -i :80
   
   # Change ports in docker-compose.yml or .env.docker
   ```

2. **Permission issues**
   ```bash
   # Fix WordPress file permissions
   docker-compose exec php-fpm /bin/bash
   chown -R www-data:www-data /var/www/html
   ```

3. **Database connection issues**
   ```bash
   # Check if MySQL is running
   docker-compose ps mysql
   
   # Check MySQL logs
   docker-compose logs mysql
   ```

4. **PHP errors**
   ```bash
   # Check PHP-FPM logs
   docker-compose logs php-fpm
   
   # Check error logs inside container
   docker-compose exec php-fpm tail -f /var/log/php/error.log
   ```

### Performance Optimization

1. **Increase PHP memory limit**
   - Edit `docker/php/php.ini`
   - Change `memory_limit = 512M` to desired value

2. **Optimize MySQL**
   - Edit `docker/mysql/my.cnf`
   - Adjust buffer sizes based on available RAM

3. **Enable Redis caching**
   - Install a WordPress Redis plugin
   - Redis is pre-configured and ready to use

## 🔒 Security Notes

- Change default passwords in production
- Use environment variables for sensitive data
- Enable HTTPS for production deployments
- Regularly update Docker images
- Review and adjust security headers in nginx configuration

## 📝 Logs

Logs are stored in `docker/logs/` directory:
- `nginx/` - Nginx access and error logs
- `php/` - PHP-FPM and error logs
- `mysql/` - MySQL logs (if enabled)

## 🚀 Production Deployment

For production deployment:

1. Use environment-specific configuration files
2. Enable HTTPS with SSL certificates
3. Use external database services
4. Implement proper backup strategies
5. Set up monitoring and alerting
6. Use Docker secrets for sensitive data

## 📞 Support

For issues related to:
- Docker setup: Check this README and Docker documentation
- WordPress: Check WordPress documentation
- Application-specific issues: Check the main project README

# Docker SAP B1 API Connection Test Instructions

## Prerequisites

1. **Start Docker Desktop** (if not already running)
2. **Navigate to project directory** in terminal/command prompt

## Step 1: Build and Start Docker Containers

```bash
# Navigate to project root
cd c:\Project\bv\wpsupportboard

# Build and start all services
docker-compose up -d --build

# Check if containers are running
docker-compose ps
```

Expected output should show all containers as "Up":
- wpsupportboard_nginx
- wpsupportboard_php  
- wpsupportboard_mysql
- wpsupportboard_phpmyadmin

## Step 2: Verify cURL Extension Installation

```bash
# Connect to PHP container
docker-compose exec php-fpm bash

# Check if cURL extension is loaded
php -m | grep curl

# Check cURL version
php -r "if(function_exists('curl_init')) { print_r(curl_version()); } else { echo 'cURL not available'; }"

# Exit container
exit
```

## Step 3: Test SAP B1 API Connection

### Option A: Run Docker-specific test script
```bash
# From project root, run the Docker API test
docker-compose exec php-fpm php /var/www/html/sapb1-woocommerce-integration/docker-test-api.php
```

### Option B: Run original test scripts
```bash
# Test basic functionality with mocks
docker-compose exec php-fpm php /var/www/html/sapb1-woocommerce-integration/run-tests.php

# Test actual API connectivity
docker-compose exec php-fpm php /var/www/html/sapb1-woocommerce-integration/test-api-connectivity.php
```

### Option C: Interactive testing
```bash
# Connect to container for interactive testing
docker-compose exec php-fpm bash

# Navigate to plugin directory
cd /var/www/html/sapb1-woocommerce-integration

# Run any test script
php docker-test-api.php
php run-tests.php
php test-api-connectivity.php

# Check logs
tail -f logs/sync.log

# Exit when done
exit
```

## Step 4: Test WordPress Integration

### Access WordPress Admin
1. **Open browser** and go to: http://localhost:8083
2. **Complete WordPress setup** if first time
3. **Login to WordPress admin**
4. **Navigate to Plugins** → Check if SAP B1 WooCommerce Integration is active

### Test Plugin Functionality
```bash
# Trigger manual sync from container
docker-compose exec php-fpm wp-cli --path=/var/www/html cron event run sbiw_product_sync_cron

# Or run PHP directly
docker-compose exec php-fpm php -r "
require_once('/var/www/html/wp-config.php');
require_once('/var/www/html/wp-load.php');
require_once('/var/www/html/sapb1-woocommerce-integration/includes/class-product-sync.php');
\$sync = new Product_Sync();
\$sync->sync_products();
"
```

## Step 5: Monitor and Debug

### Check Container Logs
```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f php-fpm
docker-compose logs -f nginx
docker-compose logs -f mysql

# View last 50 lines
docker-compose logs --tail=50 php-fpm
```

### Check Plugin Logs
```bash
# View SAP B1 sync logs
docker-compose exec php-fpm tail -f /var/www/html/sapb1-woocommerce-integration/logs/sync.log

# View PHP error logs
docker-compose exec php-fpm tail -f /var/log/php/error.log
```

### Check Network Connectivity
```bash
# Test network from container
docker-compose exec php-fpm ping google.com
docker-compose exec php-fpm nslookup b1.primalcom.com
docker-compose exec php-fpm curl -I https://b1.primalcom.com/b1s/v1/
```

## Expected Test Results

### ✅ Successful Test Output
```
=== Docker SAP B1 API Connection Test ===

PHP Environment Check:
----------------------
PHP Version: 8.3.x
✓ curl extension is loaded
✓ json extension is loaded  
✓ openssl extension is loaded

Network Connectivity Test:
---------------------------
✓ DNS resolution for google.com: 142.250.x.x
✓ DNS resolution for b1.primalcom.com: x.x.x.x

HTTP Connectivity Test:
------------------------
✓ Basic HTTPS connectivity works (httpbin.org)

SAP B1 API Test:
----------------
✓ SAP B1 server is reachable
✓ SAP B1 login successful!
✓ Product retrieval successful!
  Retrieved X products
```

### ❌ Common Issues and Solutions

**Issue: cURL extension not found**
```bash
# Rebuild containers with updated Dockerfile
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

**Issue: Network connectivity problems**
```bash
# Check Docker network
docker network ls
docker network inspect wpsupportboard_wpsupportboard

# Test from host
curl -I https://b1.primalcom.com/b1s/v1/
```

**Issue: WordPress not loading**
```bash
# Check WordPress installation
docker-compose exec php-fpm ls -la /var/www/html/
docker-compose exec php-fpm cat /var/www/html/wp-config.php
```

## Troubleshooting Commands

```bash
# Restart specific service
docker-compose restart php-fpm

# Rebuild and restart everything
docker-compose down
docker-compose up -d --build

# Clean rebuild (removes all data)
docker-compose down -v
docker-compose up -d --build

# Check container resource usage
docker stats

# Inspect container configuration
docker-compose exec php-fpm env
docker-compose exec php-fpm php --ini
```

## Success Criteria

The Docker connection test is successful when:

1. ✅ All Docker containers are running
2. ✅ PHP cURL extension is loaded
3. ✅ Network connectivity to SAP B1 server works
4. ✅ SAP B1 API login succeeds
5. ✅ Product data can be retrieved from SAP B1
6. ✅ WordPress and WooCommerce integration works
7. ✅ Sync logs show successful operations

Once all tests pass, the SAP B1 WooCommerce Integration will work properly in the Docker environment.

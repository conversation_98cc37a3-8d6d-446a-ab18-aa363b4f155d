# Multi-stage build for WordPress with PHP-FPM
FROM php:8.3-fpm-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    bash \
    curl \
    curl-dev \
    freetype-dev \
    icu-dev \
    jpeg-dev \
    libpng-dev \
    libwebp-dev \
    libzip-dev \
    oniguruma-dev \
    zip \
    unzip \
    mysql-client \
    nginx \
    supervisor \
    autoconf \
    gcc \
    g++ \
    make \
    pkgconfig

# Configure and install PHP extensions
RUN docker-php-ext-configure gd \
    --with-freetype \
    --with-jpeg \
    --with-webp

RUN docker-php-ext-install -j$(nproc) \
    bcmath \
    exif \
    gd \
    intl \
    mbstring \
    mysqli \
    opcache \
    pdo_mysql \
    zip

# Install Redis extension and clean up build dependencies
RUN pecl install redis && \
    docker-php-ext-enable redis && \
    apk del autoconf gcc g++ make pkgconfig

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Ensure www-data user exists with proper permissions
RUN if ! id www-data > /dev/null 2>&1; then \
        addgroup -g 1000 www-data 2>/dev/null || true && \
        adduser -u 1000 -G www-data -s /bin/sh -D www-data 2>/dev/null || true; \
    fi

# Set working directory
WORKDIR /var/www/html

# Copy PHP configuration
COPY docker/php/php.ini /usr/local/etc/php/conf.d/custom.ini
COPY docker/php/php-fpm.conf /usr/local/etc/php-fpm.d/www.conf

# Copy WordPress files
COPY --chown=www-data:www-data . /var/www/html/

# Create necessary directories
RUN mkdir -p /var/www/html/wp-content/uploads && \
    mkdir -p /var/log/php && \
    chown -R www-data:www-data /var/www/html && \
    chmod -R 755 /var/www/html

# Copy entrypoint and health check scripts
COPY docker/docker-entrypoint.sh /usr/local/bin/
COPY docker/php-fpm-healthcheck /usr/local/bin/
# Fix line endings and set permissions
RUN sed -i 's/\r$//' /usr/local/bin/docker-entrypoint.sh && \
    sed -i 's/\r$//' /usr/local/bin/php-fpm-healthcheck && \
    chmod +x /usr/local/bin/docker-entrypoint.sh /usr/local/bin/php-fpm-healthcheck

# Install fcgi for health checks
RUN apk add --no-cache fcgi

# Expose port 9000 for PHP-FPM
EXPOSE 9000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD php-fpm-healthcheck || exit 1

# Set entrypoint
ENTRYPOINT ["docker-entrypoint.sh"]
CMD ["php-fpm"]

include .env




# ==============================================================================
# Docker Development Environment Management
# ==============================================================================

# Build dev docker images without cache
dev-build-no-cache:
	docker-compose -f docker-compose-dev.yml build --no-cache

# Build dev docker images
dev-build:
	docker-compose -f docker-compose-dev.yml build	

# Start dev containers in the background
dev-up:
	$(eval HOST_GID=$(shell getent group www-data | cut -d: -f3))
	docker-compose -f docker-compose-dev.yml up -d
	@echo "Host www-data GID: $(HOST_GID)"
	@echo "Waiting for services to initialize before setting permissions (20 seconds)..."
	@sleep 20
	@echo "Checking if wp-config.php exists..."
	@docker-compose -f docker-compose-dev.yml exec -T php-fpm ls -la /var/www/html/wp-config.php || echo "wp-config.php not found"
	@echo "Setting ownership with host GID $(HOST_GID)..."
	@docker-compose -f docker-compose-dev.yml exec -T php-fpm chown -R www-data:$(HOST_GID) /var/www/html || echo "chown failed, continuing..."
	@docker-compose -f docker-compose-dev.yml exec -T php-fpm find /var/www/html -type d -exec chmod 775 {} \; || echo "chmod 775 on directories failed, continuing..."
	@docker-compose -f docker-compose-dev.yml exec -T php-fpm find /var/www/html -type f -exec chmod 664 {} \; || echo "chmod 664 on files failed, continuing..."
	@echo "Verifying wp-config.php permissions..."
	@docker-compose -f docker-compose-dev.yml exec -T php-fpm ls -la /var/www/html/wp-config.php || echo "wp-config.php still not accessible"
	@echo "Permissions applied. WordPress (www-data) and host user ($$USER) should have full access."

# Stop dev containers
dev-down:
	docker-compose -f docker-compose-dev.yml down

# Manual fix permission
dev-fix-perms:
	$(eval HOST_GID=$(shell getent group www-data | cut -d: -f3))
	@echo "Fixing permissions with host www-data GID: $(HOST_GID)"
	@docker-compose -f docker-compose-dev.yml exec php-fpm chown -R www-data:$(HOST_GID) /var/www/html || echo "chown failed"
	@docker-compose -f docker-compose-dev.yml exec php-fpm find /var/www/html -type d -exec chmod 775 {} \; || echo "chmod 775 on directories failed"
	@docker-compose -f docker-compose-dev.yml exec php-fpm find /var/www/html -type f -exec chmod 664 {} \; || echo "chmod 664 on files failed"
	@echo "Permissions fixed. WordPress (www-data) and host user ($$USER) should have full access."




# ====================================================================================
# Database Management Development
# ====================================================================================

# --- Phony Targets ---
.PHONY: import-db delete-db

# Imports the database. Creates it first if it doesn't exist.
import-db:
	@echo "--- Checking for and creating database '${DB_NAME}' if it doesn't exist... ---"
	@docker-compose -f docker-compose-dev.yml exec -T ${DB_SERVICE} mysql -u'${DB_USER}' -p'${DB_PASSWORD}' -e "CREATE DATABASE IF NOT EXISTS ${DB_NAME};" || echo "Error: Failed to create database '${DB_NAME}'."
	@echo "--- Importing data from ${DB_IMPORT_FILE} into '${DB_NAME}'... ---"
	@cat ${DB_IMPORT_FILE} | docker-compose -f docker-compose-dev.yml exec -T ${DB_SERVICE} mysql -u'${DB_USER}' -p'${DB_PASSWORD}' ${DB_NAME} || echo "Error: Failed to import data from ${DB_IMPORT_FILE}."
	@echo "--- Database import complete! ---"

# Deletes the database.
delete-db:
	@echo "--- Deleting database '${DB_NAME}'... ---"
	@docker-compose -f docker-compose-dev.yml exec -T ${DB_SERVICE} mysql -u'${DB_USER}' -p'${DB_PASSWORD}' -e "DROP DATABASE IF EXISTS ${DB_NAME};" || echo "Error: Failed to delete database '${DB_NAME}'."
	@echo "--- Database '${DB_NAME}' has been deleted. ---"




# ==============================================================================
# Remote Server Interaction
# ==============================================================================

.PHONY: backup

ssh:
	ssh -t $(REMOTE_USER)@$(REMOTE_HOST) "cd $(REMOTE_DIR); bash -l"

# This command will backup to `/home/<USER>/public_html_$(TIMESTAMP).tar.gz` first and then deploy
deploy:
	make backup
	rsync -avz --no-owner --no-perms --exclude-from=exclude.txt . $(REMOTE_USER)@$(REMOTE_HOST):$(REMOTE_DIR)
	make logs

logs:
	ssh $(REMOTE_USER)@$(REMOTE_HOST) "cd /home/<USER>/logs && tail -n 200 -f php_log access_log error_log"

download:
	rsync -avz $(REMOTE_USER)@$(REMOTE_HOST):$(REMOTE_DIR)/wp-includes/ ./wp-includes/
	rsync -avz $(REMOTE_USER)@$(REMOTE_HOST):$(REMOTE_DIR)/wp-admin/ ./wp-admin/
	rsync -avz --exclude 'plugins/supportboard/' $(REMOTE_USER)@$(REMOTE_HOST):$(REMOTE_DIR)/wp-content/ ./wp-content/

backup:
	@echo "Backing up public_html from $(REMOTE_HOST)..."
	# Generate a timestamp for the backup filename
	$(eval TIMESTAMP := $(shell date +'%Y-%m-%d_%H-%M-%S'))
	$(eval BACKUP_FILENAME := public_html_$(TIMESTAMP).tar.gz)
	# Use REMOTE_BACKUP_BASE_PATH from .env or default to /home/<USER>
	$(eval REMOTE_PATH_TO_BACKUP := $(REMOTE_BACKUP_BASE_PATH)/$(BACKUP_FILENAME))
	
	# Create tarball on remote server directly in the specified base path.
	# It will have a timestamp in its name.
	$(SSH_CMD) "tar -czvf $(REMOTE_PATH_TO_BACKUP) -C $(REMOTE_DIR) ."
	@if [ $$? -ne 0 ]; then echo "Backup FAILED: Remote archive creation failed."; exit 1; fi

	@echo "Remote backup created: $(REMOTE_PATH_TO_BACKUP) on $(REMOTE_HOST)."
	
	# Optional: Download the backup archive to the local machine
	@echo "Attempting to download backup to $(LOCAL_BACKUP_DESTINATION)..."
	mkdir -p $(LOCAL_BACKUP_DESTINATION) # Ensure local destination exists
	scp $(REMOTE_USER)@$(REMOTE_HOST):$(REMOTE_PATH_TO_BACKUP) $(LOCAL_BACKUP_DESTINATION)
	@if [ $$? -ne 0 ]; then echo "WARNING: Local download failed. Backup still exists on remote server: $(REMOTE_PATH_TO_BACKUP)."; fi
	
	@echo "Backup SUCCESS! File is located at $(REMOTE_PATH_TO_BACKUP) on $(REMOTE_HOST)."
	@echo "If local download succeeded, it's also in $(LOCAL_BACKUP_DESTINATION)."
# Docker Makefile for WordPress Support Board
.PHONY: help build up down restart logs shell db-backup db-restore clean

# Default target
help: ## Show this help message
	@echo "WordPress Support Board - Docker Commands"
	@echo "========================================="
	@awk 'BEGIN {FS = ":.*##"; printf "\nUsage:\n  make \033[36m<target>\033[0m\n"} /^[a-zA-Z_-]+:.*?##/ { printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2 } /^##@/ { printf "\n\033[1m%s\033[0m\n", substr($$0, 5) } ' $(MAKEFILE_LIST)

##@ Docker Operations
build: ## Build all Docker images
	docker-compose build --no-cache

up: ## Start all services
	docker-compose up -d

down: ## Stop all services
	docker-compose down

restart: ## Restart all services
	docker-compose restart

logs: ## Show logs for all services
	docker-compose logs -f

status: ## Show status of all services
	docker-compose ps

##@ Development
shell: ## Access PHP-FPM container shell
	docker-compose exec php-fpm bash

nginx-shell: ## Access Nginx container shell
	docker-compose exec nginx sh

mysql-shell: ## Access MySQL shell
	docker-compose exec mysql mysql -u root -p

redis-shell: ## Access Redis CLI
	docker-compose exec redis redis-cli

##@ Database Operations
db-backup: ## Backup database to backup.sql
	docker-compose exec mysql mysqldump -u root -p wpsupportboard > backup.sql
	@echo "Database backed up to backup.sql"

db-restore: ## Restore database from backup.sql
	@if [ ! -f backup.sql ]; then echo "backup.sql not found!"; exit 1; fi
	docker-compose exec -T mysql mysql -u root -p wpsupportboard < backup.sql
	@echo "Database restored from backup.sql"

db-reset: ## Reset database (WARNING: This will delete all data)
	@echo "WARNING: This will delete all database data!"
	@read -p "Are you sure? [y/N] " -n 1 -r; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		docker-compose exec mysql mysql -u root -p -e "DROP DATABASE IF EXISTS wpsupportboard; CREATE DATABASE wpsupportboard;"; \
		echo "Database reset complete"; \
	else \
		echo "Operation cancelled"; \
	fi

##@ Maintenance
clean: ## Remove all containers, volumes, and images
	docker-compose down -v --rmi all

clean-logs: ## Clear all log files
	sudo rm -rf docker/logs/*
	mkdir -p docker/logs/{nginx,php,mysql}

fix-permissions: ## Fix file permissions
	docker-compose exec php-fpm chown -R www-data:www-data /var/www/html
	docker-compose exec php-fpm find /var/www/html -type d -exec chmod 755 {} \;
	docker-compose exec php-fpm find /var/www/html -type f -exec chmod 644 {} \;
	@echo "Permissions fixed"

##@ Monitoring
logs-nginx: ## Show Nginx logs
	docker-compose logs -f nginx

logs-php: ## Show PHP-FPM logs
	docker-compose logs -f php-fpm

logs-mysql: ## Show MySQL logs
	docker-compose logs -f mysql

logs-redis: ## Show Redis logs
	docker-compose logs -f redis

##@ Quick Setup
install: ## Complete setup (build, start, and show status)
	@echo "Setting up WordPress Support Board Docker environment..."
	make build
	make up
	@echo "Waiting for services to start..."
	sleep 10
	make status
	@echo ""
	@echo "Setup complete! Access your application at:"
	@echo "  WordPress: http://localhost"
	@echo "  phpMyAdmin: http://localhost:8080"
	@echo ""
	@echo "Default credentials:"
	@echo "  MySQL root: root / root_password"
	@echo "  MySQL WordPress: wordpress / wordpress_password"

dev: ## Start development environment with logs
	make up
	make logs

##@ Information
info: ## Show service information
	@echo "WordPress Support Board - Service Information"
	@echo "============================================"
	@echo "WordPress Site:    http://localhost"
	@echo "phpMyAdmin:        http://localhost:8080"
	@echo "MySQL:             localhost:3306"
	@echo "Redis:             localhost:6379"
	@echo ""
	@echo "Container Status:"
	@docker-compose ps

urls: ## Show all service URLs
	@echo "Service URLs:"
	@echo "============="
	@echo "WordPress:     http://localhost"
	@echo "phpMyAdmin:    http://localhost:8080"
	@echo "MySQL:         localhost:3306"
	@echo "Redis:         localhost:6379"

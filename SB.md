# SupportBoard Chat Processing Flow

This document outlines the process by which SupportBoard handles incoming messages, routes them to LLMs (Dialogflow/OpenAI), and processes the responses.

## Incoming Message Handling

When a user sends a message, SupportBoard initiates a series of actions to process and respond to the user. The core function responsible for handling incoming messages is `sb_send_message()`.

*   **`sb_send_message()`**: This function is the primary entry point for new messages. It takes the user's message, sender information, and other relevant data as input. Its responsibilities likely include:
    *   Saving the message to the database.
    *   Notifying agents of the new message.
    *   Triggering any configured automated responses or workflows.
    *   *Location*: This is a core SupportBoard function. Search for `function sb_send_message(` within the SupportBoard plugin files (not located in `apps/dialogflow/functions.php`).
    *   [Open `sb_send_message` (approximate location)](file://wp-content/plugins/supportboard/supportboard/functions.php#search=function%20sb_send_message() or similar core file)

## Message Routing to LLMs

SupportBoard can integrate with LLMs like Dialogflow and OpenAI to provide automated responses. The routing of messages to these services is managed by functions that prepare and dispatch the messages.

*   **`sb_messaging_platforms_functions()`**: This function (or a set of similarly named functions) likely acts as a central hub or dispatcher for various messaging platform integrations. It would determine if an LLM integration is active and which service to use based on settings or specific triggers (e.g., keywords, user state).
    *   *Location*: This is likely a core SupportBoard function or a set of functions. Search for `function sb_messaging_platforms_functions(` or similar names within the SupportBoard plugin files (not located in `apps/dialogflow/functions.php`).
    *   [Open `sb_messaging_platforms_functions` (approximate location)](file://wp-content/plugins/supportboard/supportboard/include/messaging.php#search=function%20sb_messaging_platforms_functions() or similar core file)

### Dialogflow Integration

*   **`sb_dialogflow_message()`**: When a message is routed to Dialogflow, this function is called. Its role is to:
    *   Format the user's message into the structure required by the Dialogflow API.
    *   Send the formatted message to the configured Dialogflow agent.
    *   Receive the intent and response from Dialogflow.
    *   *Location*: `wp-content/plugins/supportboard/supportboard/apps/dialogflow/functions.php`. Search for `function sb_dialogflow_message(`.
    *   [Open `sb_dialogflow_message`](file://wp-content/plugins/supportboard/supportboard/apps/dialogflow/functions.php#search=function%20sb_dialogflow_message()

### OpenAI Integration

*   **`sb_open_ai_message()`**: If OpenAI is the chosen LLM, this function handles the interaction. It is responsible for:
    *   Preparing the message content (and potentially conversation history) for the OpenAI API (e.g., GPT-3, GPT-4), including iterative tool calls.
    *   Sending the request to the OpenAI API.
    *   Receiving and processing the generated text response or further tool call requests from OpenAI.
    *   *Location*: `wp-content/plugins/supportboard/supportboard/apps/dialogflow/functions.php`. Search for `function sb_open_ai_message(`.
    *   [Open `sb_open_ai_message`](file://wp-content/plugins/supportboard/supportboard/apps/dialogflow/functions.php#search=function%20sb_open_ai_message()

## LLM Response Processing

Once an LLM provides a response, SupportBoard needs to process it and deliver it back to the user.

1.  **Response Reception**: The respective LLM functions (`sb_dialogflow_message` or `sb_open_ai_message`) receive the raw response from the LLM service.
2.  **Formatting**: The response may need to be formatted to fit the chat interface (e.g., handling rich text, buttons, or other specific message types returned by the LLM).
3.  **Delivery to User**: The processed response is then sent back to the user through the SupportBoard interface. This might involve another call to a function like `sb_send_message()`, but this time with the LLM acting as the sender, or a specialized function for delivering bot messages.

## Key Functions Summary

*   **`sb_send_message()`**: Central function for sending and receiving messages within SupportBoard. Handles initial message intake and potentially the delivery of LLM responses.
    *   *Location*: This is a core SupportBoard function. Search for `function sb_send_message(` within the SupportBoard plugin files.
    *   [Open `sb_send_message` (approximate location)](file://wp-content/plugins/supportboard/supportboard/functions.php#search=function%20sb_send_message() or similar core file)
*   **`sb_messaging_platforms_functions()`**: Likely involved in determining which, if any, messaging platform or LLM should handle an incoming message.
    *   *Location*: This is likely a core SupportBoard function or a set of functions. Search for `function sb_messaging_platforms_functions(` or similar names within the SupportBoard plugin files.
    *   [Open `sb_messaging_platforms_functions` (approximate location)](file://wp-content/plugins/supportboard/supportboard/include/messaging.php#search=function%20sb_messaging_platforms_functions() or similar core file)
*   **`sb_dialogflow_message()`**: Manages the specific communication with Google's Dialogflow service, including sending the user's message and processing Dialogflow's reply.
    *   *Location*: `wp-content/plugins/supportboard/supportboard/apps/dialogflow/functions.php`. Search for `function sb_dialogflow_message(`.
    *   [Open `sb_dialogflow_message`](file://wp-content/plugins/supportboard/supportboard/apps/dialogflow/functions.php#search=function%20sb_dialogflow_message()
*   **`sb_open_ai_message()`**: Manages the specific communication with OpenAI's services, sending the user's query and handling the AI-generated response, including the loop for `tool_calls`.
    *   *Location*: `wp-content/plugins/supportboard/supportboard/apps/dialogflow/functions.php`. Search for `function sb_open_ai_message(`.
    *   [Open `sb_open_ai_message`](file://wp-content/plugins/supportboard/supportboard/apps/dialogflow/functions.php#search=function%20sb_open_ai_message()

This flow ensures that user messages are efficiently processed, intelligently routed if AI capabilities are enabled, and that responses are seamlessly delivered back to the user.
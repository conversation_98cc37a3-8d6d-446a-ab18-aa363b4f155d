
# Apache configuration for WordPress with PHP-FPM
ServerRoot "/usr"
Listen 0.0.0.0:5000

# Basic server configuration
ServerName localhost
DocumentRoot /home/<USER>/workspace
DirectoryIndex index.php index.html

# Load required modules
LoadModule rewrite_module /usr/lib/apache2/modules/mod_rewrite.so
LoadModule proxy_module /usr/lib/apache2/modules/mod_proxy.so
LoadModule proxy_fcgi_module /usr/lib/apache2/modules/mod_proxy_fcgi.so
LoadModule dir_module /usr/lib/apache2/modules/mod_dir.so
LoadModule mime_module /usr/lib/apache2/modules/mod_mime.so
LoadModule authz_core_module /usr/lib/apache2/modules/mod_authz_core.so

# Basic MIME types
TypesConfig /etc/mime.types

# Process and thread settings
StartServers 1
MinSpareServers 1
MaxSpareServers 3
MaxRequestWorkers 10

# Logging
ErrorLog /tmp/logs/apache-error.log
CustomLog /tmp/logs/apache-access.log combined
LogLevel warn

# PID file
PidFile /tmp/apache2/run/apache2.pid

<Directory /home/<USER>/workspace>
    Options Indexes FollowSymLinks
    AllowOverride All
    Require all granted
</Directory>

# PHP-FPM configuration
<FilesMatch \.php$>
    SetHandler "proxy:unix:/run/php/php8.2-fpm.sock|fcgi://localhost"
</FilesMatch>

# WordPress specific rules
<Directory /home/<USER>/workspace>
    RewriteEngine On
    RewriteBase /
    RewriteRule ^index\.php$ - [L]
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule . /index.php [L]
</Directory>

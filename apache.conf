
ServerRoot "/home/<USER>/workspace"
PidFile /tmp/apache2/run/httpd.pid
Listen 0.0.0.0:5000

# Load essential modules only
LoadModule authz_core_module modules/mod_authz_core.so
LoadModule dir_module modules/mod_dir.so
LoadModule mime_module modules/mod_mime.so
LoadModule rewrite_module modules/mod_rewrite.so

ServerName 0.0.0.0:5000
DocumentRoot "/home/<USER>/workspace"

<Directory "/home/<USER>/workspace">
    Options Indexes FollowSymLinks
    AllowOverride All
    Require all granted
</Directory>

DirectoryIndex index.php index.html

# Use CGI instead of FPM for simplicity
LoadModule cgi_module modules/mod_cgi.so
AddHandler cgi-script .cgi .pl
AddType application/x-httpd-php .php

TypesConfig /etc/mime.types

ErrorLog /tmp/logs/apache-error.log
CustomLog /tmp/logs/apache-access.log combined

# Basic MIME types
AddType text/html .html
AddType text/css .css
AddType application/javascript .js

#!/bin/bash

echo "=== Docker Environment Status Check ==="
echo

# Check if Docker is running
echo "1. Checking Docker status..."
if docker info > /dev/null 2>&1; then
    echo "✅ Docker is running"
    echo "   Docker version: $(docker --version)"
else
    echo "❌ Docker is not running or not accessible"
    echo "   Please start Docker Desktop and try again"
    exit 1
fi
echo

# Check if docker-compose is available
echo "2. Checking Docker Compose..."
if command -v docker-compose > /dev/null 2>&1; then
    echo "✅ Docker Compose is available"
    echo "   Version: $(docker-compose --version)"
else
    echo "❌ Docker Compose is not available"
    exit 1
fi
echo

# Check container status
echo "3. Checking container status..."
if docker-compose ps > /dev/null 2>&1; then
    echo "✅ Docker Compose project found"
    echo
    echo "Container Status:"
    docker-compose ps
else
    echo "⚠️  No running containers found"
    echo "   Run 'docker-compose up -d' to start services"
fi
echo

# Check if containers are running
echo "4. Checking specific services..."
services=("php-fpm" "nginx" "mysql")
for service in "${services[@]}"; do
    if docker-compose ps $service | grep -q "Up"; then
        echo "✅ $service is running"
    else
        echo "❌ $service is not running"
    fi
done
echo

# Test PHP and cURL if container is running
echo "5. Testing PHP environment..."
if docker-compose ps php-fpm | grep -q "Up"; then
    echo "Testing PHP version and extensions..."
    docker-compose exec -T php-fpm php -v
    echo
    echo "Checking cURL extension..."
    if docker-compose exec -T php-fpm php -m | grep -q curl; then
        echo "✅ cURL extension is loaded"
    else
        echo "❌ cURL extension is not loaded"
        echo "   You may need to rebuild containers with updated Dockerfile"
    fi
else
    echo "⚠️  PHP container not running - skipping PHP tests"
fi
echo

echo "=== Status Check Complete ==="
echo
echo "Next steps:"
echo "1. If containers are not running: docker-compose up -d --build"
echo "2. If cURL is missing: rebuild with updated Dockerfile"
echo "3. Test SAP B1 API: docker-compose exec php-fpm php /var/www/html/sapb1-woocommerce-integration/docker-test-api.php"

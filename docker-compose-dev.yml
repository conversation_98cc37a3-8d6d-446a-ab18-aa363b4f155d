services:
  # Nginx Web Server
  nginx:
    image: nginx:alpine
    container_name: wpsupportboard_nginx
    restart: unless-stopped
    ports:
      - "8081:80"  # Changed to port 8081 to avoid conflict with phpMyAdmin
      - "8443:443"
    volumes:
      - ./:/var/www/html:ro,z
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
      - ./docker/logs/nginx:/var/log/nginx
    depends_on:
      - php-fpm
    networks:
      - wpsupportboard

  # PHP-FPM
  php-fpm:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: wpsupportboard_php
    restart: unless-stopped
    volumes:
      - ./:/var/www/html:z
      - ./docker/php/php.ini:/usr/local/etc/php/conf.d/custom.ini
      # Added below to bypass open_basedir error
      - ./docker/php/php-fpm.conf:/usr/local/etc/php-fpm.d/www.conf
      - ./docker/logs/php:/var/log/php
    environment:
      - DB_HOST=mysql
      - DB_NAME=wpsupportboard
      - DB_USER=root
      - DB_PASSWORD="root_password"
      - WORDPRESS_DB_HOST=mysql:3306
      - WORDPRESS_DB_NAME=wpsupportboard
      - WORDPRESS_DB_USER=wordpress
      - WORDPRESS_DB_PASSWORD="root_password"
      - SKIP_DB_WAIT=true
      - R2R_BASE_URL=http://localhost:7272
      - R2R_USERNAME=<EMAIL>
      - R2R_PASSWORD=change_me_immediately
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - wpsupportboard
      # - r2r-network

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: wpsupportboard_mysql
    restart: unless-stopped
    ports:
      - "3307:3306"  # Changed from "3306:3306" to use port 3307 on host
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: wpsupportboard
      MYSQL_USER: wordpress
      MYSQL_PASSWORD: "root_password"
    volumes:
      - wpsp_mysql_data:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/custom.cnf:ro
      - ./docker/logs/mysql:/var/log/mysql
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u$$MYSQL_USER", "-p$$MYSQL_PASSWORD"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - wpsupportboard
  
  # phpMyAdmin
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: wpsupportboard_phpmyadmin
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: root_password
      MYSQL_ROOT_PASSWORD: root_password
      # PHP configuration to handle larger uploads
      UPLOAD_LIMIT: 256M
      MEMORY_LIMIT: 512M
      MAX_EXECUTION_TIME: 600
    networks:
      - wpsupportboard

volumes:
  wpsp_mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  wpsupportboard:
    driver: bridge

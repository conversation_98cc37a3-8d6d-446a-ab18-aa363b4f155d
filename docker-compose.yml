services:
  # Apache Web Server
  apache:
    image: httpd:2.4-alpine
    platform: linux/arm64/v8
    container_name: wpsupportboard_apache
    restart: unless-stopped
    ports:
      - "8083:80"
      - "8443:443"
    volumes:
      - ./:/var/www/html
      - ./docker/apache/httpd.conf:/usr/local/apache2/conf/httpd.conf:ro
      - ./docker/apache/000-default.conf:/usr/local/apache2/conf/extra/httpd-vhosts.conf:ro
      - ./docker/logs/apache:/var/log/apache2
    depends_on:
      - php-fpm
    networks:
      - wpsupportboard

  # PHP-FPM
  php-fpm:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: wpsupportboard_php
    restart: unless-stopped
    volumes:
      - ${PWD:-.}:/var/www/html
      - ./docker/php/php.ini:/usr/local/etc/php/conf.d/custom.ini:ro
      - ./docker/logs/php:/var/log/php
    environment:
      - DB_HOST=mysql
      - DB_NAME=wpsupportboard
      - DB_USER=root
      - DB_PASSWORD="root_password"
      - WORDPRESS_DB_HOST=mysql:3306
      - WORDPRESS_DB_NAME=wpsupportboard
      - WORDPRESS_DB_USER=wordpress
      - WORDPRESS_DB_PASSWORD="root_password"
      - SKIP_DB_WAIT=true
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - wpsupportboard

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: wpsupportboard_mysql
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: wpsupportboard
      MYSQL_USER: wordpress
      MYSQL_PASSWORD: "root_password"
    volumes:
      - ./mysql:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/custom.cnf:ro
      - ./docker/logs/mysql:/var/log/mysql
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u$$MYSQL_USER", "-p$$MYSQL_PASSWORD"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - wpsupportboard
      

  # Redis Cache
  # redis:
  #   image: redis:alpine
  #   container_name: wpsupportboard_redis
  #   restart: unless-stopped
  #   ports:
  #     - "6380:6379"
  #   volumes:
  #     - redis_data:/data
  #     - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
  #   command: redis-server /usr/local/etc/redis/redis.conf
  #   networks:
  #     - wpsupportboard
  # phpMyAdmin
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: wpsupportboard_phpmyadmin
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: root_password
      MYSQL_ROOT_PASSWORD: root_password
      # PHP configuration to handle larger uploads
      UPLOAD_LIMIT: 256M
      MEMORY_LIMIT: 512M
      MAX_EXECUTION_TIME: 600
    networks:
      - wpsupportboard


volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  wpsupportboard:
    driver: bridge

<VirtualHost *:80>
    DocumentRoot /var/www/html
    ServerName localhost

    <Directory "/var/www/html">
        AllowOverride All
        Require all granted
        Options Indexes FollowSymLinks
    </Directory>

    # PHP files
    <FilesMatch \.php$>
        SetHandler "proxy:fcgi://php-fpm:9000"
    </FilesMatch>

    # WordPress rewrite rules
    RewriteEngine On

    # For static files with query parameters, check if the base file exists
    RewriteCond %{QUERY_STRING} .+
    RewriteCond %{REQUEST_URI} \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ [NC]
    RewriteCond %{DOCUMENT_ROOT}%{REQUEST_URI} -f
    RewriteRule ^(.*)$ $1 [L]

    # For static files without query parameters
    RewriteCond %{REQUEST_FILENAME} \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ [NC]
    RewriteCond %{REQUEST_FILENAME} -f
    RewriteRule ^(.*)$ - [L]

    # Standard WordPress rewrite for non-existing files and directories
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule . /index.php [L]

    # Logging
    ErrorLog /var/log/apache2/error.log
    CustomLog /var/log/apache2/access.log combined
</VirtualHost>

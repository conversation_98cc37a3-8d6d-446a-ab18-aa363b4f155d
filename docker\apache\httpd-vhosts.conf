<VirtualHost *:80>
    ServerName localhost
    DocumentRoot /var/www/html

    # Enable rewrite engine
    RewriteEngine On

    # WordPress permalink structure
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule . /index.php [L]

    # PHP-FPM configuration
    <FilesMatch \.php$>
        SetHandler "proxy:fcgi://php-fpm:9000"
    </FilesMatch>

    # Directory permissions
    <Directory "/var/www/html">
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>

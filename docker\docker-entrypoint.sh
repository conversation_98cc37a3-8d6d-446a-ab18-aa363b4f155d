#!/bin/bash
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# Wait for MySQL to be ready
wait_for_mysql() {
    log "Waiting for MySQL to be ready..."

    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        log "Attempting to connect with credentials:"
        log "Host: ${DB_HOST:-mysql}"
        log "User: ${DB_USER:-wordpress}"
        log "Password: ${DB_PASSWORD:-wordpress_password}"
        if mysql -h"${DB_HOST:-mysql}" -u"${DB_USER:-wordpress}" -p"${DB_PASSWORD:-wordpress_password}" -e "SELECT 1" >/dev/null 2>&1; then
            log "MySQL is ready!"
            return 0
        fi

        warn "MySQL not ready yet (attempt $attempt/$max_attempts). Waiting 2 seconds..."
        sleep 2
        ((attempt++))
    done

    error "MySQL failed to become ready after $max_attempts attempts"
    exit 1
}

# Update wp-config.php with environment variables
update_wp_config() {
    log "Updating wp-config.php with environment variables..."

    local wp_config="/var/www/html/wp-config.php"

    if [ -f "$wp_config" ]; then
        # Update database configuration
        sed -i "s/define( 'DB_NAME', '.*' );/define( 'DB_NAME', '${WORDPRESS_DB_NAME:-wpsupportboard}' );/" "$wp_config"
        sed -i "s/define( 'DB_USER', '.*' );/define( 'DB_USER', '${WORDPRESS_DB_USER:-wordpress}' );/" "$wp_config"
        sed -i "s/define( 'DB_PASSWORD', '.*' );/define( 'DB_PASSWORD', '${WORDPRESS_DB_PASSWORD:-wordpress_password}' );/" "$wp_config"
        sed -i "s/define( 'DB_HOST', '.*' );/define( 'DB_HOST', '${WORDPRESS_DB_HOST:-mysql:3306}' );/" "$wp_config"

        # Enable debug mode if requested
        if [ "${WP_DEBUG:-false}" = "true" ]; then
            sed -i "s/define( 'WP_DEBUG', false );/define( 'WP_DEBUG', true );/" "$wp_config"

            # Add debug log configuration if not present
            if ! grep -q "WP_DEBUG_LOG" "$wp_config"; then
                sed -i "/define( 'WP_DEBUG', true );/a define( 'WP_DEBUG_LOG', true );" "$wp_config"
                sed -i "/define( 'WP_DEBUG_LOG', true );/a define( 'WP_DEBUG_DISPLAY', false );" "$wp_config"
            fi
        fi

        # Add Redis configuration if not present
        if ! grep -q "WP_REDIS_HOST" "$wp_config"; then
            sed -i "/\/\* That's all, stop editing! Happy publishing. \*\//i define( 'WP_REDIS_HOST', 'redis' );" "$wp_config"
            sed -i "/\/\* That's all, stop editing! Happy publishing. \*\//i define( 'WP_REDIS_PORT', 6379 );" "$wp_config"
            sed -i "/\/\* That's all, stop editing! Happy publishing. \*\//i define( 'WP_REDIS_DATABASE', 0 );" "$wp_config"
        fi

        log "wp-config.php updated successfully"
    else
        warn "wp-config.php not found, skipping configuration update"
    fi
}

# Set proper permissions
set_permissions() {
    log "Setting proper file permissions..."

    # Set ownership (excluding .git directories and socket files)
    # find /var/www/html -not -path "*/\.git*" -not -name "*.sock" -exec chown www-data:www-data {} \;

    # Set directory permissions
    find /var/www/html -type d -not -path "*/\.git*" -exec chmod 755 {} \;

    # Set file permissions (excluding socket files)
    # find /var/www/html -type f -not -path "*/\.git*" -not -name "*.sock" -exec chmod 644 {} \;

    # Set special permissions for wp-config.php
    if [ -f "/var/www/html/wp-config.php" ]; then
        chmod 600 /var/www/html/wp-config.php
    fi

    # Ensure uploads directory is writable
    if [ -d "/var/www/html/wp-content/uploads" ]; then
        chmod 755 /var/www/html/wp-content/uploads
    fi

    log "Permissions set successfully"
}

# Create log directories
create_log_dirs() {
    log "Creating log directories..."

    mkdir -p /var/log/php
    chown www-data:www-data /var/log/php
    chmod 755 /var/log/php

    log "Log directories created"
}

# Install WordPress if not present
install_wordpress() {
    if [ ! -f "/var/www/html/wp-config.php" ]; then
        log "WordPress not found, this appears to be a fresh installation"
        log "Please run the WordPress installation wizard at http://localhost"
    else
        log "WordPress installation detected"
    fi
}

# Health check function
php_fpm_healthcheck() {
    if pgrep -f "php-fpm: master process" > /dev/null; then
        return 0
    else
        return 1
    fi
}

# Main execution
main() {
    log "Starting WordPress Docker container initialization..."

    # Create necessary directories
    create_log_dirs

    # Wait for database if we're not in a standalone mode
    if [ "${SKIP_DB_WAIT:-false}" != "true" ]; then
        wait_for_mysql
    fi

    # Update WordPress configuration
    update_wp_config

    # Set permissions
    set_permissions

    # Check WordPress installation
    install_wordpress

    log "Initialization complete. Starting PHP-FPM..."

    # Execute the main command
    exec "$@"
}

# Export health check function for use in Dockerfile
export -f php_fpm_healthcheck

# Run main function
main "$@"

upstream php-fpm {
    server php-fpm:9000;
}

server {
    listen 80;
    server_name localhost;
    root /var/www/html;
    index index.php index.html index.htm;

    # Security
    server_tokens off;

    # Logging
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    # WordPress specific rules
    location / {
        try_files $uri $uri/ /index.php?$args;
    }

    # Handle PHP files
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass php-fpm:9000;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        
        # FastCGI settings
        fastcgi_connect_timeout 60;
        fastcgi_send_timeout 180;
        fastcgi_read_timeout 180;
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
        fastcgi_temp_file_write_size 256k;
        fastcgi_intercept_errors on;
    }

    # WordPress admin rate limiting
    location ~ ^/(wp-admin|wp-login\.php) {
        limit_req zone=login burst=5 nodelay;
        try_files $uri $uri/ /index.php?$args;
        
        location ~ \.php$ {
            try_files $uri =404;
            fastcgi_split_path_info ^(.+\.php)(/.+)$;
            fastcgi_pass php-fpm;
            fastcgi_index index.php;
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            fastcgi_param PATH_INFO $fastcgi_path_info;
        }
    }

    # WordPress REST API rate limiting
    location ~ ^/wp-json/ {
        limit_req zone=api burst=20 nodelay;
        try_files $uri $uri/ /index.php?$args;
    }

    # Static files caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Security rules
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Deny access to sensitive files
    location ~* \.(sql|log|conf|ini|sh|bak|old|tmp)$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # WordPress specific security
    location ~ ^/(wp-config\.php|wp-config-sample\.php|readme\.html|license\.txt) {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Deny access to uploads PHP files
    location ~* /(?:uploads|files)/.*\.php$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # WordPress xmlrpc.php protection
    location = /xmlrpc.php {
        limit_req zone=api burst=5 nodelay;
        try_files $uri =404;
        fastcgi_pass php-fpm;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }

    # Favicon
    location = /favicon.ico {
        log_not_found off;
        access_log off;
    }

    # Robots
    location = /robots.txt {
        allow all;
        log_not_found off;
        access_log off;
    }
}

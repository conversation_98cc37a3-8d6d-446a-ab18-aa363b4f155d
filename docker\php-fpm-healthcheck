#!/bin/bash
# PHP-FPM Health Check Script

# Check if PHP-FPM master process is running
if pgrep -f "php-fpm: master process" > /dev/null; then
    # Check if PHP-FPM is responding to requests
    if cgi-fcgi -bind -connect 127.0.0.1:9000 2>/dev/null; then
        exit 0
    else
        # Fallback: check if the socket is listening
        if netstat -ln | grep -q ":9000 "; then
            exit 0
        else
            exit 1
        fi
    fi
else
    exit 1
fi

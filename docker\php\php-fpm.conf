[www]
user = www-data
group = www-data

listen = 9000
listen.owner = www-data
listen.group = www-data
listen.mode = 0660

pm = dynamic
pm.max_children = 20
pm.start_servers = 3
pm.min_spare_servers = 2
pm.max_spare_servers = 8
pm.max_requests = 1000

; Logging
access.log = /var/log/php/access.log
access.format = "%R - %u %t \"%m %r%Q%q\" %s %f %{mili}d %{kilo}M %C%%"

; Slow log
slowlog = /var/log/php/slow.log
request_slowlog_timeout = 10s

; Process management
pm.process_idle_timeout = 30s

; Environment variables
env[HOSTNAME] = $HOSTNAME
env[PATH] = /usr/local/bin:/usr/bin:/bin
env[TMP] = /tmp
env[TMPDIR] = /tmp
env[TEMP] = /tmp

; PHP settings
php_admin_value[sendmail_path] = /usr/sbin/sendmail -t -i -f <EMAIL>
php_flag[display_errors] = off
php_admin_value[error_reporting] = "E_ALL & ~E_DEPRECATED & ~E_STRICT & ~E_NOTICE"
php_admin_value[error_log] = /var/log/php/error.log
php_admin_flag[log_errors] = on
php_admin_value[memory_limit] = 512M
php_admin_value[upload_max_filesize] = 720M
php_admin_value[post_max_size] = 256M
php_admin_value[max_execution_time] = 900
php_admin_value[max_input_time] = 900
php_admin_value[max_input_vars] = 1000

; Security
php_admin_value[disable_functions] = exec,passthru,shell_exec,system
; Removed restrictive open_basedir to prevent WooCommerce path issues
; php_admin_value[open_basedir] = /var/www/html:/tmp

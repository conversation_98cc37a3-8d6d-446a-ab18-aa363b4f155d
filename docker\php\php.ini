[PHP]
; Engine settings
engine = On
short_open_tag = Off
precision = 14
output_buffering = 8192
zlib.output_compression = Off
implicit_flush = Off
unserialize_callback_func =
serialize_precision = -1
disable_functions =
disable_classes =
zend.enable_gc = On

; Resource limits
max_execution_time = 900
max_input_time = 900
max_input_vars = 1000
memory_limit = 512M

; Error handling and logging
error_reporting = E_ERROR | E_WARNING | E_PARSE
display_errors = Off
display_startup_errors = Off
log_errors = On
log_errors_max_len = 8024
ignore_repeated_errors = Off
ignore_repeated_source = Off
report_memleaks = On
html_errors = On
; Log to stderr so errors appear in docker logs
error_log = /proc/self/fd/2

; Data handling
variables_order = "GPCS"
request_order = "GP"
register_argc_argv = Off
auto_globals_jit = On
post_max_size = 256M
auto_prepend_file =
auto_append_file =
default_mimetype = "text/html"
default_charset = "UTF-8"

; File uploads
file_uploads = On
upload_tmp_dir =
upload_max_filesize = 720M
max_file_uploads = 20

; Fopen wrappers
allow_url_fopen = On
allow_url_include = Off
default_socket_timeout = 60

; Disabled below because duplicated with Dockerfile
; Dynamic extensions
; extension=bcmath
; extension=gd
; extension=intl
; extension=mbstring
; extension=mysqli
; extension=opcache
; extension=pdo_mysql
; extension=redis
; extension=zip

; Module settings
[Date]
date.timezone = UTC

[filter]
filter.default = unsafe_raw
filter.default_flags =

[iconv]
iconv.input_encoding =
iconv.internal_encoding =
iconv.output_encoding =

[intl]
intl.default_locale =
intl.error_level = 0
intl.use_exceptions = 0

[sqlite3]
sqlite3.extension_dir =

[Pcre]
pcre.backtrack_limit = 100000
pcre.recursion_limit = 100000

[Pdo]
pdo_mysql.cache_size = 2000
pdo_mysql.default_socket =

[Pdo_mysql]
pdo_mysql.default_socket =

[Phar]
phar.readonly = On
phar.require_hash = On
phar.cache_list =

[mail function]
SMTP = localhost
smtp_port = 25
mail.add_x_header = Off

[ODBC]
odbc.allow_persistent = On
odbc.check_persistent = On
odbc.max_persistent = -1
odbc.max_links = -1
odbc.defaultlrl = 4096
odbc.defaultbinmode = 1

[Interbase]
ibase.allow_persistent = 1
ibase.max_persistent = -1
ibase.max_links = -1
ibase.timestampformat = "%Y-%m-%d %H:%M:%S"
ibase.dateformat = "%Y-%m-%d"
ibase.timeformat = "%H:%M:%S"

[MySQLi]
mysqli.max_persistent = -1
mysqli.allow_persistent = On
mysqli.max_links = -1
mysqli.cache_size = 2000
mysqli.default_port = 3306
mysqli.default_socket =
mysqli.default_host =
mysqli.default_user =
mysqli.default_pw =
mysqli.reconnect = Off

[mysqlnd]
mysqlnd.collect_statistics = On
mysqlnd.collect_memory_statistics = Off

[OCI8]

[PostgreSQL]
pgsql.allow_persistent = On
pgsql.auto_reset_persistent = Off
pgsql.max_persistent = -1
pgsql.max_links = -1
pgsql.ignore_notice = 0
pgsql.log_notice = 0

[bcmath]
bcmath.scale = 0

[browscap]

[Session]
session.save_handler = files
session.save_path = "/tmp"
session.use_strict_mode = 0
session.use_cookies = 1
session.use_only_cookies = 1
session.name = PHPSESSID
session.auto_start = 0
session.cookie_lifetime = 0
session.cookie_path = /
session.cookie_domain =
session.cookie_httponly =
session.cookie_samesite =
session.serialize_handler = php
session.gc_probability = 0
session.gc_divisor = 1000
session.gc_maxlifetime = 1440
session.referer_check =
session.cache_limiter = nocache
session.cache_expire = 180
session.use_trans_sid = 0
session.sid_length = 26
session.trans_sid_tags = "a=href,area=href,frame=src,form="
session.sid_bits_per_character = 5

[Assertion]
zend.assertions = -1

[COM]

[mbstring]
mbstring.language = English
mbstring.internal_encoding =
mbstring.http_input =
mbstring.http_output =
mbstring.encoding_translation = Off
mbstring.detect_order = auto
mbstring.substitute_character = none
mbstring.func_overload = 0
mbstring.strict_detection = On
mbstring.http_output_conv_mimetype =

[gd]
gd.jpeg_ignore_warning = 1

[exif]
exif.encode_unicode = ISO-8859-15
exif.decode_unicode_motorola = UCS-2BE
exif.decode_unicode_intel = UCS-2LE
exif.encode_jis =
exif.decode_jis_motorola = JIS
exif.decode_jis_intel = JIS

[Tidy]
tidy.clean_output = Off

[soap]
soap.wsdl_cache_enabled = 1
soap.wsdl_cache_dir = "/tmp"
soap.wsdl_cache_ttl = 86400
soap.wsdl_cache_limit = 5

[sysvshm]

[ldap]
ldap.max_links = -1

[dba]

[opcache]
opcache.enable = 1
opcache.enable_cli = 1
opcache.memory_consumption = 256
opcache.interned_strings_buffer = 16
opcache.max_accelerated_files = 10000
opcache.revalidate_freq = 60
opcache.fast_shutdown = 1
opcache.save_comments = 1
opcache.validate_timestamps = 1
opcache.file_cache_only = 0
opcache.max_wasted_percentage = 10

; --- Custom R2R API Credentials ---
; These directives will first check for system environment variables provided to the container.
; If an environment variable is NOT set, it will use the specified default value.
;
; This ensures that:
; - In Production: If no environment variables are explicitly set, the defaults are used.
; - In Development: If environment variables are set (e.g., via a .env file), they override these defaults.
[env]
r2r.base_url = "${R2R_BASE_URL:-https://rapi.primalcom.com}"
r2r.username = "${R2R_USERNAME:-<EMAIL>}"
r2r.password = "${R2R_PASSWORD:-.EXA3gzaGiqhEjs}"

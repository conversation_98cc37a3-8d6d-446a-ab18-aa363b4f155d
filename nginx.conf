
user runner;
worker_processes auto;
pid /tmp/nginx/nginx.pid;
error_log /tmp/logs/nginx-error.log;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Logging
    access_log /tmp/logs/nginx-access.log;
    
    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Upstream PHP-FPM
    upstream php {
        server unix:/tmp/php-fpm/php-fpm.sock;
    }
    
    server {
        listen 0.0.0.0:5000;
        server_name _;
        root /home/<USER>/workspace;
        index index.php index.html index.htm;
        
        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        
        # WordPress specific configuration
        location / {
            try_files $uri $uri/ /index.php?$args;
        }
        
        # Handle PHP files
        location ~ \.php$ {
            try_files $uri =404;
            fastcgi_split_path_info ^(.+\.php)(/.+)$;
            fastcgi_pass php;
            fastcgi_index index.php;
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            fastcgi_param PATH_INFO $fastcgi_path_info;
            fastcgi_read_timeout 300;
        }
        
        # WordPress security
        location ~ /\.ht {
            deny all;
        }
        
        location ~ /wp-config\.php {
            deny all;
        }
        
        # Static files caching
        location ~* \.(css|gif|ico|jpeg|jpg|js|png|svg|webp|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            log_not_found off;
        }
        
        # WordPress uploads
        location ~* /wp-content/uploads/.*\.php$ {
            deny all;
        }
    }
}

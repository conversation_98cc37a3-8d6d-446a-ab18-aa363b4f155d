
[global]
pid = /tmp/php-fpm/php-fpm.pid
error_log = /tmp/logs/php-fpm.log
daemonize = no

[www]
user = runner
group = runner

listen = /run/php/php8.2-fpm.sock
listen.owner = runner
listen.group = runner
listen.mode = 0666

pm = dynamic
pm.max_children = 5
pm.start_servers = 2
pm.min_spare_servers = 1
pm.max_spare_servers = 3
pm.max_requests = 100

; Environment variables
env[PATH] = /usr/local/bin:/usr/bin:/bin
env[TMP] = /tmp
env[TMPDIR] = /tmp
env[TEMP] = /tmp

; PHP settings
php_admin_value[error_reporting] = "E_ALL & ~E_DEPRECATED & ~E_STRICT & ~E_NOTICE"
php_admin_value[memory_limit] = 256M
php_admin_value[upload_max_filesize] = 64M
php_admin_value[post_max_size] = 64M
php_admin_value[max_execution_time] = 300
php_admin_value[max_input_time] = 300

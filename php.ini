; cPanel-generated php ini directives, do not edit
; Manual editing of this file may result in unexpected behavior.
; To make changes to this file, use the cPanel MultiPHP INI Editor (Home >> Software >> MultiPHP INI Editor)
; For more information, read our documentation (https://go.cpanel.net/EA4ModifyINI)

allow_url_fopen = On
display_errors = On
error_reporting = E_ALL & ~E_NOTICE & ~E_DEPRECATED & ~E_STRICT
enable_dl = Off
file_uploads = On
max_execution_time = 900
max_input_time = 900
max_input_vars = 1000
memory_limit = 512M
post_max_size = 256M
session.gc_maxlifetime = 1440
upload_max_filesize = 720M
zlib.output_compression = Off

[env]
r2r.base_url = "${R2R_BASE_URL:-https://rapi.primalcom.com}"
r2r.username = "${R2R_USERNAME:-<EMAIL>}"
r2r.password = "${R2R_PASSWORD:-.EXA3gzaGiqhEjs}"
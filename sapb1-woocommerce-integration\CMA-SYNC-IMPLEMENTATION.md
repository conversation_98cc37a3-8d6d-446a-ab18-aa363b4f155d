# SAP B1 to Custom Multiple Addresses Sync Implementation

## Overview

This implementation adds automatic synchronization of SAP B1 multiple addresses to the Custom Multiple Addresses (CMA) plugin for WooCommerce. When SAP B1 users are synced, their additional addresses are now properly stored in the CMA plugin format, making them available to SupportBoard and other integrations.

## 🔧 Implementation Details

### Files Modified

#### `wp-content/plugins/sapb1-woocommerce-integration/includes/class-client-sync.php`

**New Functions Added:**

1. **`sync_sap_addresses_to_cma($user_id, $sap_client_data)`**
   - Main sync function called after WooCommerce address sync
   - Processes SAP B1 BPAddresses array
   - Separates billing and shipping addresses
   - <PERSON><PERSON> single vs multiple address scenarios

2. **`convert_sap_address_to_cma_format($sap_address, $sap_client_data)`**
   - Converts SAP B1 address format to CMA plugin format
   - Maps SAP fields to WooCommerce/CMA field names
   - Adds SAP-specific metadata for tracking

3. **`sync_addresses_to_cma_meta($user_id, $address_type, $addresses)`**
   - Safely updates CMA plugin meta keys
   - Removes old SAP-sourced addresses to prevent duplicates
   - Uses CMA safe update methods when available

4. **`is_cma_plugin_active()`**
   - Checks if Custom Multiple Addresses plugin is active
   - Prevents sync attempts when CMA plugin is not available

5. **`use_cma_safe_update($user_id, $meta_key, $meta_value)`**
   - Attempts to use CMA plugin's safe update method
   - Fallback to standard WordPress update_user_meta
   - Helps avoid SupportBoard conflicts

### Integration Points

**Modified Functions:**
- `update_customer_data()` - Added CMA sync call after address update
- `create_new_customer()` - Added CMA sync call after customer creation

## 🔄 Sync Process Flow

### 1. SAP B1 User Sync Triggers
- Existing customer update via `update_customer_data()`
- New customer creation via `create_new_customer()`

### 2. WooCommerce Default Address Sync
- First billing address → WooCommerce billing_* meta
- First shipping address → WooCommerce shipping_* meta
- Handled by existing `set_customer_address_data()` function

### 3. Additional Address Sync to CMA
- Remaining addresses → CMA plugin meta keys
- `cma_billing_addresses` for additional billing addresses
- `cma_shipping_addresses` for additional shipping addresses

### 4. Address Processing Logic

**Multiple Addresses Scenario:**
```
SAP BPAddresses: [billing1, billing2, billing3, shipping1, shipping2]
↓
WooCommerce Defaults: billing1, shipping1
CMA Additional: [billing2, billing3], [shipping2]
```

**Single Address Scenario:**
```
SAP BPAddresses: [billing1, shipping1]
↓
WooCommerce Defaults: billing1, shipping1
CMA Default Markers: billing1 (is_default: true), shipping1 (is_default: true)
```

## 📊 Data Format Conversion

### SAP B1 Format → CMA Format

| SAP B1 Field | CMA Field | Notes |
|--------------|-----------|-------|
| Street | address_1 | Primary address line |
| Block | address_2 | Secondary address line |
| City | city | City name |
| County | state | State/Province |
| ZipCode | postcode | Postal code |
| Country | country | Country code |
| AddressName | sap_address_name | SAP address identifier |

### Additional CMA Fields Added

| Field | Value | Purpose |
|-------|-------|---------|
| first_name | From CardName | Extracted from SAP CardName |
| last_name | From CardName | Extracted from SAP CardName |
| company | CardName | Full company name |
| is_default | true/false | Default address marker |
| sap_source | true | Identifies SAP-sourced addresses |
| sap_sync_date | timestamp | Last sync timestamp |

## 🛡️ Conflict Prevention

### SupportBoard Compatibility
- Uses CMA plugin's `safe_update_user_meta()` when available
- Fallback to standard WordPress functions
- Comprehensive logging for debugging

### Duplicate Prevention
- Removes existing SAP-sourced addresses before adding new ones
- Uses `sap_source: true` flag for identification
- Preserves user-created addresses in CMA plugin

### Address ID Generation
- Format: `sap_addr_{timestamp}_{random}`
- Ensures unique identifiers for each address
- Prevents conflicts with user-created addresses

## 📝 Logging

### Log Messages Format
```
CMA Sync: [Action] for User ID: [ID], CardCode: [Code]
```

### Key Log Messages
- `Starting additional address sync`
- `Synced X additional billing/shipping addresses`
- `Added single address as default to CMA`
- `Used CMA safe update / standard update_user_meta`
- `Completed additional address sync`

## 🧪 Testing

### Test File: `test-cma-sync.php`
- Plugin status verification
- CMA plugin detection test
- Address format conversion test
- Existing data inspection
- Implementation status check

### Manual Testing Steps
1. Run SAP B1 client sync
2. Check user meta for `cma_*` keys
3. Verify SupportBoard can retrieve addresses
4. Check CMA plugin interface
5. Review sync logs

## 🔍 Troubleshooting

### Common Issues

**CMA Plugin Not Detected**
- Verify Custom Multiple Addresses plugin is active
- Check class_exists('Custom_Multiple_Addresses')

**Addresses Not Syncing**
- Check SAP data has BPAddresses array
- Verify user has multiple addresses in SAP
- Review sync logs for errors

**SupportBoard Conflicts**
- Ensure CMA safe update methods are working
- Check for cache clearing requirements
- Review SupportBoard logs

### Debug Information
- Enable WordPress debug logging
- Check SAP B1 plugin logs directory
- Use test file for status verification

## 🚀 Benefits

### For Users
- SAP B1 multiple addresses available in WooCommerce
- Seamless address selection in SupportBoard checkout
- Consistent address data across systems

### For Developers
- Modular, maintainable code structure
- Comprehensive error handling and logging
- Safe integration with existing systems

### For System Integration
- Automatic sync without manual intervention
- Conflict-free operation with SupportBoard
- Preserves user-created addresses

## 📋 Future Enhancements

### Potential Improvements
1. **Bidirectional Sync**: CMA → SAP B1 address updates
2. **Address Validation**: Integrate with address validation services
3. **Bulk Sync Tools**: Admin interface for mass address sync
4. **Address Mapping**: Custom field mapping configuration
5. **Sync Scheduling**: Automated periodic sync processes

### Configuration Options
- Enable/disable CMA sync via plugin settings
- Custom field mapping configuration
- Sync frequency settings
- Address validation rules

## 📞 Support

### For Issues
1. Check plugin logs for error messages
2. Verify all required plugins are active
3. Test with sample SAP data
4. Review WordPress debug logs

### Log Locations
- SAP B1 Plugin: `wp-content/plugins/sapb1-woocommerce-integration/logs/`
- WordPress: `wp-content/debug.log`
- SupportBoard: Check SupportBoard logs

---

**Implementation Date:** 2025-01-18  
**Version:** 1.0  
**Compatibility:** SAP B1 Integration Plugin, Custom Multiple Addresses Plugin, SupportBoard

# Guest Order Support Implementation

## Overview

This document describes the implementation of guest order support for the SAP B1-WooCommerce integration. Previously, the system could only sync orders from registered customers who had an associated SAP B1 CardCode. Now, the system can handle guest orders by automatically creating SAP B1 customers when needed.

## Features Implemented

### 1. **Guest Order Processing**
- Automatically detects guest orders (customer_id = 0)
- Creates SAP B1 customers for guest orders using billing information
- Supports both individual customers and company customers

### 2. **Email-Based Customer Lookup**
- Searches for existing SAP B1 customers by email address before creating new ones
- Prevents duplicate customer creation
- Works for both guest orders and registered customers without CardCode

### 3. **Dynamic Customer Creation**
- Creates SAP B1 Business Partners with complete billing and shipping information
- Handles company vs. individual customer scenarios
- Uses WooCommerce currency settings
- Sanitizes all input data

### 4. **CardCode Management**
- Stores CardCode in user meta for registered customers
- Enables future orders from the same customer to use existing CardCode
- Maintains backward compatibility with existing customers

## Technical Implementation

### New API Client Methods

#### `get_customer_by_email($email)`
- Searches SAP B1 for customers by email address
- Returns customer data if found, false otherwise
- Includes proper email validation and sanitization

#### `create_business_partner($customer_data)`
- Creates new SAP B1 Business Partner (customer)
- Handles authentication and session management
- Returns created customer data with CardCode
- Includes retry logic for session expiration

### Enhanced Order Sync Logic

#### Modified `push_order_to_sapb1($order)`
- Detects guest vs. registered customers
- Calls new customer lookup/creation logic when CardCode is missing
- Stores CardCode for registered customers for future use

#### New `get_or_create_customer_by_email($order)`
- Orchestrates the customer lookup and creation process
- Handles both existing customer lookup and new customer creation
- Includes fallback logic for edge cases

#### New `prepare_customer_data_from_order($order)`
- Extracts customer data from WooCommerce order
- Handles company vs. individual customer scenarios
- Prepares data in SAP B1 format
- Includes proper data sanitization

## Usage Scenarios

### Scenario 1: New Guest Customer
1. Guest places order with email `<EMAIL>`
2. System searches SAP B1 for existing customer with this email
3. No customer found, so system creates new SAP B1 customer
4. Order is synced using the new CardCode

### Scenario 2: Returning Guest Customer
1. Guest places order with email `<EMAIL>`
2. System finds existing SAP B1 customer with this email
3. Order is synced using the existing CardCode

### Scenario 3: Registered Customer Without CardCode
1. Registered customer places order but has no stored CardCode
2. System searches SAP B1 by email address
3. If found, uses existing CardCode and stores it in user meta
4. If not found, creates new customer and stores CardCode

### Scenario 4: Company Guest Order
1. Guest provides company information in billing details
2. System creates SAP B1 customer with company name as CardName
3. Individual name is stored as ContactPerson
4. Order is synced normally

## Data Mapping

### WooCommerce → SAP B1 Customer Fields
- `billing_company` (if provided) → `CardName`
- `billing_first_name + billing_last_name` → `CardName` (if no company)
- `billing_first_name + billing_last_name` → `ContactPerson` (if company provided)
- `billing_email` → `EmailAddress`
- `billing_phone` → `Phone1`
- Billing address fields → `BillTo*` fields
- Shipping address fields → `ShipTo*` fields (fallback to billing if empty)
- WooCommerce currency → `Currency`

## Error Handling

### Validation
- Email address validation before API calls
- Required field validation (CardName, EmailAddress)
- Data sanitization for all fields

### API Failures
- Session expiration handling with automatic retry
- Graceful failure when customer creation fails
- Comprehensive logging for troubleshooting

### Edge Cases
- Empty customer names (fallback to "Guest Customer")
- Missing shipping address (uses billing address)
- Duplicate email handling
- Network connectivity issues

## Testing

### Test Coverage
- ✅ New guest customer creation
- ✅ Existing customer lookup
- ✅ Registered customer without CardCode
- ✅ Company guest orders
- ✅ Error handling scenarios
- ✅ Data validation
- ✅ API failure scenarios

### Test Files
- `test-guest-order-sync.php` - Comprehensive guest order testing
- `test-order-sync.php` - Updated with guest order support
- `tests/test-class-order-sync.php` - Unit tests for new functionality

## Configuration

### Requirements
- SAP B1 Service Layer API access
- Proper authentication credentials
- Network connectivity to SAP B1 server

### Settings
- No additional configuration required
- Uses existing WooCommerce currency settings
- Leverages existing SAP B1 API connection settings

## Logging

### Log Messages
- Guest order detection
- Customer search attempts
- Customer creation success/failure
- CardCode storage for registered users
- API communication details
- Error conditions and troubleshooting information

### Log Locations
- Standard SBIW_Logger output
- Detailed transaction logging
- Error logging with context

## Backward Compatibility

### Existing Functionality
- ✅ Registered customers with CardCode work unchanged
- ✅ Existing order sync logic preserved
- ✅ No breaking changes to API
- ✅ Existing test cases still pass

### Migration
- No migration required
- Existing customers continue to work
- New functionality activates automatically for orders without CardCode

## Performance Considerations

### Optimization
- Email-based lookup prevents duplicate customer creation
- CardCode caching for registered users
- Efficient API calls with proper session management

### Scalability
- Handles high-volume guest orders
- Minimal additional API calls per order
- Proper error handling prevents system overload

## Security

### Data Protection
- Input sanitization for all customer data
- Email validation before API calls
- Secure API communication
- No sensitive data in logs

### Access Control
- Uses existing SAP B1 authentication
- Respects SAP B1 user permissions
- No additional security requirements

## Future Enhancements

### Potential Improvements
- Customer deduplication by phone number
- Advanced customer matching algorithms
- Bulk customer creation for performance
- Customer data synchronization from SAP B1 to WooCommerce

### Integration Opportunities
- WooCommerce customer registration integration
- Email marketing platform synchronization
- Customer portal integration
- Advanced reporting and analytics

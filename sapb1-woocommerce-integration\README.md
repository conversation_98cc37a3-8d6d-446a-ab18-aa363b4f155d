# SAP B1 WooCommerce Integration Plugin

**Version:** 1.0.0
**Author:** <PERSON> (AI Agent)

## Description

This plugin integrates WooCommerce with an SAP Business One (SAP B1) server. It pulls product information from the SAP B1 API and creates corresponding products in WooCommerce if they do not already exist (matched by SKU). The synchronization process is scheduled to run automatically every 5 minutes.

**Note:** This plugin currently uses hardcoded credentials and API endpoints for SAP B1. The SAP B1 server is assumed to be operational for live data synchronization; current development uses simulated data for API responses.

## Features

*   Connects to SAP B1 API (credentials are hardcoded).
*   Fetches product list from SAP B1.
*   Creates new products in WooCommerce if they don't exist (based on SKU matching ItemCode from SAP B1).
*   Does **not** update existing products in WooCommerce.
*   Does **not** push any data from WooCommerce to SAP B1.
*   Scheduled synchronization every 5 minutes using WP-Cron.
*   Detailed logging of synchronization activities and errors.

## Installation

1.  Download the plugin files.
2.  Upload the `sapb1-woocommerce-integration` directory to your WordPress `wp-content/plugins/` directory.
3.  Activate the plugin through the 'Plugins' menu in WordPress.

## How It Works

1.  **Authentication:** The plugin first attempts to log in to the SAP B1 API server at `https://b1.primalcom.com/b1s/v1/Login` using hardcoded credentials (`manager`/`Abc123.!@!@`) and a hardcoded CompanyDB.
2.  **Product Fetching:** Upon successful login, it fetches a list of items (products) from the SAP B1 API (simulated for now).
3.  **Product Creation:** For each product retrieved from SAP B1, the plugin checks if a product with the same SKU (SAP B1 `ItemCode`) already exists in WooCommerce. If not, a new simple product is created in WooCommerce with details like name, SKU, price, and stock quantity mapped from the SAP B1 data.
4.  **Scheduling:** This synchronization process is automatically triggered every 5 minutes by a WP-Cron job that is scheduled upon plugin activation.
5.  **Logging:** All actions, including API communication attempts, product creation, and errors, are logged to `wp-content/plugins/sapb1-woocommerce-integration/logs/sync.log`.

## Configuration

Currently, all configuration details (API URL, credentials) are hardcoded within the plugin files:
*   **API URL:** `https://b1.primalcom.com/b1s/v1/` (in `includes/class-sapb1-api-client.php`)
*   **Username:** `manager` (in `includes/class-sapb1-api-client.php`)
*   **Password:** `Abc123.!@!@` (in `includes/class-sapb1-api-client.php`)
*   **CompanyDB:** `SBODEMOUS` (in `includes/class-sapb1-api-client.php`)

These would need to be modified directly in the code or, ideally, moved to a settings page in a future version.

## Important Notes

*   **SAP B1 Server Downtime:** The original issue stated the server is currently down. The plugin's API client (`SAPB1_API_Client`) has simulated responses for login and product fetching. To connect to the actual SAP B1 server, the commented-out cURL code within this class will need to be enabled and tested once the server is accessible.
*   **Error Handling:** Basic error handling for API calls and product creation is included. Check the log file for details.
*   **Security:** Hardcoding credentials is not recommended for production environments. This plugin is a proof-of-concept based on the issue requirements.

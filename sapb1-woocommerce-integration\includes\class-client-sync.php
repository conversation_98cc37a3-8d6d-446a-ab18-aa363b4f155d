<?php
/**
 * Client Synchronization
 *
 * @package SAPB1_WooCommerce_Integration
 * @version 1.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

// Include the SAPB1 API client class.
require_once plugin_dir_path( __FILE__ ) . 'class-sapb1-api-client.php';

/**
 * Class Client_Sync
 *
 * Handles the synchronization of clients (customers) between SAP B1 and WooCommerce.
 */
class Client_Sync {

	/**
	 * SAPB1 API Client instance.
	 *
	 * @var SAPB1_API_Client
	 */
	private $sapb1_api_client;

	/**
	 * Constructor.
	 *
	 * Initializes the SAPB1 API client.
	 */
	public function __construct() {
		$this->sapb1_api_client = new SAPB1_API_Client();
		// TODO: Add any necessary configuration for the API client,
		// e.g., API keys, endpoint URLs, from plugin settings.
	}

	/**
	 * Sync clients from woocommerce to SAP B1.
	 *
	 * This method will fetch client data from WooCommerce and then create or update
	 * corresponding customer accounts in SAP B1.
	 *
	 * @return void
	 */
	public function sync_woo_clients() {
	}

	/**
	 * Sync clients from SAP B1 to WooCommerce.
	 *
	 * This method will fetch client data from SAP B1 and then create or update
	 * corresponding customer accounts in WooCommerce.
	 *
	 * @return void
	 */
	public function sync_clients() {
		SBIW_Logger::log( 'Client Sync: Starting client synchronization process.' );

		$sap_clients = $this->sapb1_api_client->get_clients();

		if ( $sap_clients === false ) {
			SBIW_Logger::log( 'Client Sync Error: Failed to fetch clients from SAP B1. API client returned false.' );
			return;
		}

		if ( empty( $sap_clients ) ) {
			SBIW_Logger::log( 'Client Sync: No clients found in SAP B1 to synchronize.' );
			return;
		}

		SBIW_Logger::log( 'Client Sync: Fetched ' . count( $sap_clients ) . ' clients from SAP B1. Processing...' );

		foreach ( $sap_clients as $sap_client ) {
			// Validate essential data
			if ( empty( $sap_client['CardCode'] ) ) {
				SBIW_Logger::log( 'Client Sync Warning: Skipping client due to missing CardCode. Data: ' . wp_json_encode( $sap_client ) );
				continue;
			}
			if ( empty( $sap_client['EmailAddress'] ) ) {
				SBIW_Logger::log( 'Client Sync Warning: Skipping client ' . $sap_client['CardCode'] . ' due to missing EmailAddress.' );
				continue;
			}
			if ( empty( $sap_client['CardName'] ) ) {
				SBIW_Logger::log( 'Client Sync Warning: Skipping client ' . $sap_client['CardCode'] . ' due to missing CardName.' );
				continue;
			}

			// Check if client is marked as inactive/frozen in SAP B1
			if ( isset( $sap_client['Frozen'] ) && ( $sap_client['Frozen'] === 'tYES' || $sap_client['Frozen'] === true ) ) {
				SBIW_Logger::log( 'Client Sync: Skipping client ' . $sap_client['CardCode'] . ' because they are marked as frozen/on-hold in SAP B1.' );
				// Optionally, you could add logic here to disable the WordPress user account or mark them as inactive.
				continue;
			}
			if ( isset( $sap_client['Valid'] ) && ( $sap_client['Valid'] === 'tNO' || $sap_client['Valid'] === false ) ) {
				SBIW_Logger::log( 'Client Sync: Skipping client ' . $sap_client['CardCode'] . ' because they are marked as not valid/inactive in SAP B1.' );
				// Optionally, you could add logic here to disable the WordPress user account or mark them as inactive.
				continue;
			}


			$user_id = email_exists( $sap_client['EmailAddress'] );
			$customer = null;

			if ( $user_id ) {
				SBIW_Logger::log( 'Client Sync: Found existing user by email (' . $sap_client['EmailAddress'] . ') for CardCode ' . $sap_client['CardCode'] . '. User ID: ' . $user_id );
				try {
					$customer = new WC_Customer( $user_id );
				} catch ( Exception $e ) {
					SBIW_Logger::log( 'Client Sync Error: Could not instantiate WC_Customer for existing User ID ' . $user_id . '. Error: ' . $e->getMessage() );
					continue;
				}
			} else {
				$user_id = $this->get_customer_id_by_sap_cardcode( $sap_client['CardCode'] );
				if ( $user_id ) {
					SBIW_Logger::log( 'Client Sync: Found existing user by SAP CardCode meta (' . $sap_client['CardCode'] . '). User ID: ' . $user_id );
					try {
						$customer = new WC_Customer( $user_id );
						// Verify email, update if SAP B1 has a different one for the same CardCode
						if ( strtolower( $customer->get_email() ) !== strtolower( $sap_client['EmailAddress'] ) ) {
							SBIW_Logger::log( 'Client Sync: Email mismatch for CardCode ' . $sap_client['CardCode'] . '. WC Email: ' . $customer->get_email() . ', SAP Email: ' . $sap_client['EmailAddress'] . '. Updating email.' );
							// Note: Changing email for an existing user might have implications.
							// Consider if this should create a new user or if updating is the desired behavior.
							// For now, we assume CardCode is the primary key and update email if different.
							// wp_update_user also updates the user_login if it was the same as the old email.
							// It's safer to update the customer object and let WC handle user email updates.
							$customer->set_email( sanitize_email( $sap_client['EmailAddress'] ) );
							// The username is not changed here, only the email for login and contact.
						}
					} catch ( Exception $e ) {
						SBIW_Logger::log( 'Client Sync Error: Could not instantiate WC_Customer for existing User ID ' . $user_id . ' (found by CardCode). Error: ' . $e->getMessage() );
						continue;
					}
				}
			}

			if ( $customer && $customer->get_id() > 0 ) {
				// Update existing customer
				SBIW_Logger::log( 'Client Sync: Updating existing customer for CardCode ' . $sap_client['CardCode'] . ', User ID ' . $customer->get_id() );
				$this->update_customer_data( $customer, $sap_client );
			} else {
				// Create new customer
				SBIW_Logger::log( 'Client Sync: Creating new customer for CardCode ' . $sap_client['CardCode'] );
				$this->create_new_customer( $sap_client );
			}
		}
		SBIW_Logger::log( 'Client Sync: Finished client synchronization process.' );
	}

	/**
	 * Retrieves a WordPress user ID based on SAP B1 CardCode stored in user meta.
	 *
	 * @param string $card_code The SAP B1 CardCode.
	 * @return int|false The user ID if found, false otherwise.
	 */
	private function get_customer_id_by_sap_cardcode( $card_code ) {
		$users = get_users( [
			'meta_key'   => '_sapb1_cardcode',
			'meta_value' => $card_code,
			'number'     => 1,
			'fields'     => 'ID',
		] );
		return ! empty( $users ) ? (int) $users[0] : false;
	}

	/**
	 * Prepares a unique WordPress username from SAP client data.
	 *
	 * @param string $card_name SAP Client Name.
	 * @param string $card_code SAP Client Code.
	 * @return string Unique username.
	 */
	private function prepare_username_from_sap( $card_name, $card_code ) {
		$base_username = sanitize_user( str_replace( ' ', '_', strtolower( $card_name ) ), true );
		if ( empty( $base_username ) || strlen( $base_username ) > 60 ) { // Max username length
			$base_username = sanitize_user( 'sapuser_' . $card_code, true );
		}
		
		$username = $base_username;
		$count = 1;
		while ( username_exists( $username ) ) {
			$username = $base_username . '_' . $count;
			$count++;
			if ($count > 100) { // Safety break for extreme cases
				$username = sanitize_user('sapuser_' . $card_code . '_' . uniqid(), true);
				break;
			}
		}
		return $username;
	}

	/**
	 * Populates WooCommerce customer address data from SAP client data.
	 *
	 * DEPRECATED: This method is kept for backward compatibility but the new
	 * create_customer_addresses() method should be used for new customers.
	 *
	 * @param WC_Customer $customer The WooCommerce customer object (passed by reference).
	 * @param array       $sap_client_data The client data from SAP B1.
	 */
	private function set_customer_address_data( &$customer, $sap_client_data ) {

		// Initialize variables for address data
		$billing_data = [];
		$shipping_data = [];

		// Check if we have BPAddresses structure (new format)
		if (isset($sap_client_data['BPAddresses']) && is_array($sap_client_data['BPAddresses'])) {
			// Process BPAddresses array
			foreach ($sap_client_data['BPAddresses'] as $address) {
				if (isset($address['AddressType'])) {
					if ($address['AddressType'] === 'bo_BillTo') {
						$billing_data = $address;
					} elseif ($address['AddressType'] === 'bo_ShipTo') {
						$shipping_data = $address;
					}
				}
			}
		}

		// Fallback to old flat structure if BPAddresses not available
		if (empty($billing_data) && empty($shipping_data)) {
			// Use old structure for backward compatibility
			$billing_data = [
				'Street' => isset($sap_client_data['BillToStreet']) ? $sap_client_data['BillToStreet'] : '',
				'Block' => isset($sap_client_data['BillToBlock']) ? $sap_client_data['BillToBlock'] : '',
				'City' => isset($sap_client_data['BillToCity']) ? $sap_client_data['BillToCity'] : '',
				'County' => isset($sap_client_data['BillToState']) ? $sap_client_data['BillToState'] : '',
				'ZipCode' => isset($sap_client_data['BillToZipCode']) ? $sap_client_data['BillToZipCode'] : '',
				'Country' => isset($sap_client_data['BillToCountry']) ? $sap_client_data['BillToCountry'] : '',
			];
			$shipping_data = [
				'Street' => isset($sap_client_data['ShipToStreet']) ? $sap_client_data['ShipToStreet'] : '',
				'Block' => isset($sap_client_data['ShipToBlock']) ? $sap_client_data['ShipToBlock'] : '',
				'City' => isset($sap_client_data['ShipToCity']) ? $sap_client_data['ShipToCity'] : '',
				'County' => isset($sap_client_data['ShipToState']) ? $sap_client_data['ShipToState'] : '',
				'ZipCode' => isset($sap_client_data['ShipToZipCode']) ? $sap_client_data['ShipToZipCode'] : '',
				'Country' => isset($sap_client_data['ShipToCountry']) ? $sap_client_data['ShipToCountry'] : '',
			];
		}

		// Log incoming SAP client address data
		$address_fields_to_log = [
			'CardCode' => isset($sap_client_data['CardCode']) ? $sap_client_data['CardCode'] : 'N/A',
			'BillingData' => $billing_data,
			'ShippingData' => $shipping_data,
			'Phone1' => isset($sap_client_data['Phone1']) ? $sap_client_data['Phone1'] : '',
			'Cellular' => isset($sap_client_data['Cellular']) ? $sap_client_data['Cellular'] : '',
		];
		SBIW_Logger::log( 'Client Sync Address Data for CardCode ' . $address_fields_to_log['CardCode'] . ': SAP Data: ' . wp_json_encode( $address_fields_to_log ) );

		// Billing Address
		$billing_address = [
			'billing_first_name' => $customer->get_billing_first_name() ?: $customer->get_first_name(),
			'billing_last_name'  => $customer->get_billing_last_name() ?: $customer->get_last_name(),
			'billing_company'    => $customer->get_billing_company() ?: (isset($sap_client_data['CardName']) ? $sap_client_data['CardName'] : ''),
			'billing_address_1'  => isset($billing_data['Street']) ? $billing_data['Street'] : '',
			'billing_address_2'  => isset($billing_data['Block']) ? $billing_data['Block'] : '',
			'billing_city'       => isset($billing_data['City']) ? $billing_data['City'] : '',
			'billing_state'      => isset($billing_data['County']) ? $billing_data['County'] : '',
			'billing_postcode'   => isset($billing_data['ZipCode']) ? $billing_data['ZipCode'] : '',
			'billing_country'    => isset($billing_data['Country']) ? $billing_data['Country'] : '',
			'billing_email'      => $customer->get_email(), // Already set on customer
			'billing_phone'      => isset($sap_client_data['Phone1']) ? $sap_client_data['Phone1'] : (isset($sap_client_data['Cellular']) ? $sap_client_data['Cellular'] : ''),
		];
		SBIW_Logger::log( 'Client Sync Address Data for CardCode ' . (isset($sap_client_data['CardCode']) ? $sap_client_data['CardCode'] : 'N/A') . ': Preparing to set billing address: ' . wp_json_encode( $billing_address ) );
		$customer->set_props(array_filter($billing_address));

		// Shipping Address
		// Default to billing if shipping specific fields are not present or empty
		$ship_to_street = isset($shipping_data['Street']) && !empty($shipping_data['Street']) ? $shipping_data['Street'] : $billing_address['billing_address_1'];
		$ship_to_block = isset($shipping_data['Block']) && !empty($shipping_data['Block']) ? $shipping_data['Block'] : $billing_address['billing_address_2'];
		$ship_to_city = isset($shipping_data['City']) && !empty($shipping_data['City']) ? $shipping_data['City'] : $billing_address['billing_city'];
		$ship_to_state = isset($shipping_data['County']) && !empty($shipping_data['County']) ? $shipping_data['County'] : $billing_address['billing_state'];
		$ship_to_zipcode = isset($shipping_data['ZipCode']) && !empty($shipping_data['ZipCode']) ? $shipping_data['ZipCode'] : $billing_address['billing_postcode'];
		$ship_to_country = isset($shipping_data['Country']) && !empty($shipping_data['Country']) ? $shipping_data['Country'] : $billing_address['billing_country'];

		$shipping_address = [
			'shipping_first_name' => $customer->get_shipping_first_name() ?: $customer->get_first_name(),
			'shipping_last_name'  => $customer->get_shipping_last_name() ?: $customer->get_last_name(),
			'shipping_company'    => $customer->get_shipping_company() ?: (isset($sap_client_data['CardName']) ? $sap_client_data['CardName'] : ''),
			'shipping_address_1'  => $ship_to_street,
			'shipping_address_2'  => $ship_to_block,
			'shipping_city'       => $ship_to_city,
			'shipping_state'      => $ship_to_state,
			'shipping_postcode'   => $ship_to_zipcode,
			'shipping_country'    => $ship_to_country,
		];
		SBIW_Logger::log( 'Client Sync Address Data for CardCode ' . (isset($sap_client_data['CardCode']) ? $sap_client_data['CardCode'] : 'N/A') . ': Preparing to set shipping address: ' . wp_json_encode( $shipping_address ) );
		$customer->set_props(array_filter($shipping_address));
	}

	/**
	 * Updates an existing WooCommerce customer with data from SAP B1.
	 *
	 * @param WC_Customer $customer The WooCommerce customer object (passed by reference).
	 * @param array       $sap_client_data The client data from SAP B1.
	 */
	private function update_customer_data( &$customer, $sap_client_data ) {
		// Fix: Ensure existing users also have correct 'customer' role
		$user_id = $customer->get_id();
		$user = new WP_User( $user_id );
		$current_roles = $user->roles;

		if ( ! in_array( 'customer', $current_roles ) ) {
			$user->set_role( 'customer' );
			SBIW_Logger::log( 'Client Sync: Updated user role to "customer" for existing User ID: ' . $user_id . '. Previous roles: ' . implode( ', ', $current_roles ) );
		}

		// Update basic details
		$card_name_parts = explode( ' ', $sap_client_data['CardName'], 2 );
		$first_name = $card_name_parts[0];
		$last_name = isset( $card_name_parts[1] ) ? $card_name_parts[1] : '';
		if (empty($last_name) && !empty($first_name)) { // If CardName is a single word, use it as FirstName
		    $last_name = $customer->get_last_name(); // Keep existing last name if new one is empty
		}


		$customer->set_first_name( sanitize_text_field( $first_name ) );
		$customer->set_last_name( sanitize_text_field( $last_name ) );
		// Email is typically updated when identified by CardCode before this function is called,
		// or it's the primary key used for matching.
		// If email was changed in SAP, it would be handled in the main sync_clients logic.

		// Update addresses using new method to ensure proper storage
		$this->set_customer_address_data( $customer, $sap_client_data );

		// Sync additional addresses to Custom Multiple Addresses plugin
		$this->sync_sap_addresses_to_cma( $customer->get_id(), $sap_client_data );

		// Update additional meta if needed, e.g. phone for main user record
		if (isset($sap_client_data['Phone1']) && !empty($sap_client_data['Phone1'])) {
		    update_user_meta($customer->get_id(), 'billing_phone', sanitize_text_field($sap_client_data['Phone1']));
		} elseif (isset($sap_client_data['Cellular']) && !empty($sap_client_data['Cellular'])) {
		    update_user_meta($customer->get_id(), 'billing_phone', sanitize_text_field($sap_client_data['Cellular']));
		}


		// Update SAP B1 metadata for existing customers
		update_user_meta( $customer->get_id(), '_sapb1_cardcode', sanitize_text_field( $sap_client_data['CardCode'] ) );
		update_user_meta( $customer->get_id(), '_sapb1_sync_date', current_time( 'mysql' ) );

		try {
			$customer->save();
			SBIW_Logger::log( 'Client Sync: Successfully updated customer User ID: ' . $customer->get_id() . ' (CardCode: ' . $sap_client_data['CardCode'] . ') with SAP B1 metadata' );
		} catch ( Exception $e ) {
			SBIW_Logger::log( 'Client Sync Error: Failed to save updated customer User ID: ' . $customer->get_id() . '. Error: ' . $e->getMessage() );
		}
	}

	/**
	 * Creates a new WooCommerce customer from SAP B1 client data.
	 *
	 * @param array $sap_client_data The client data from SAP B1.
	 */
	private function create_new_customer( $sap_client_data ) {
		$email = sanitize_email( $sap_client_data['EmailAddress'] );
		$username = $this->prepare_username_from_sap( $sap_client_data['CardName'], $sap_client_data['CardCode'] );
		$password = wp_generate_password();
		$role = (strpos($sap_client_data['CardType'], 'c') !== false) ? 'customer' : 'subscriber';
		$description = 'users from SAPB1: '.$sap_client_data['CardCode'];

		$user_data = array(
    		'user_login'    => $username,
    		'user_pass'     => $password,       
    		'user_email'    => $email,
    		// 'first_name'    => 'John',
    		// 'last_name'     => 'Doe',
    		'role'          => $role, 
			'description'   => $description,
		);

		$user_id = wp_insert_user( $user_data );

		if ( is_wp_error( $user_id ) ) {
			SBIW_Logger::log( 'Client Sync Error: Failed to create WordPress user for CardCode ' . $sap_client_data['CardCode'] . '. Errors: ' . $user_id->get_error_message() );
			return;
		}

		SBIW_Logger::log( 'Client Sync: Successfully created WordPress user ID: ' . $user_id . ' for CardCode ' . $sap_client_data['CardCode']. ' Password: '.$password );

		try {
			SBIW_Logger::log( 'Client Sync: Set user role to "customer" for User ID: ' . $user_id );

			$customer = new WC_Customer( $user_id );
			$card_name_parts = explode( ' ', $sap_client_data['CardName'], 2 );
			$first_name = $card_name_parts[0];
			$last_name = isset( $card_name_parts[1] ) ? $card_name_parts[1] : '';
            if (empty($last_name) && !empty($first_name)) { // If CardName is a single word, use it as FirstName
                // $last_name can remain empty or use a placeholder like '.' if required by WC.
                // For new customers, an empty last name is usually fine.
            }


			$customer->set_first_name( sanitize_text_field( $first_name ) );
			$customer->set_last_name( sanitize_text_field( $last_name ) );

			// Use new address creation method to ensure addresses are properly stored
			$this->set_customer_address_data( $customer, $sap_client_data);
			$customer->save();

			// Sync additional addresses to Custom Multiple Addresses plugin
			$this->sync_sap_addresses_to_cma( $user_id, $sap_client_data );

			// Store SAP B1 metadata
			update_user_meta( $user_id, '_sapb1_cardcode', sanitize_text_field( $sap_client_data['CardCode'] ) );
			update_user_meta( $user_id, '_sapb1_sync_date', current_time( 'mysql' ) );

			// Store additional SAP fields if necessary
			// update_user_meta( $user_id, '_sapb1_currency', sanitize_text_field( $sap_client_data['Currency'] ) );
			// update_user_meta( $user_id, '_sapb1_sales_person_code', sanitize_text_field( $sap_client_data['SalesPersonCode'] ) );


			SBIW_Logger::log( 'Client Sync: Successfully created WooCommerce customer for User ID: ' . $user_id . ' (CardCode: ' . $sap_client_data['CardCode'] . ') with SAP B1 metadata || Password: '.$password  );
			
			// Optionally, send new user notification
			// wp_send_new_user_notifications( $user_id, 'user', 'both' ); // 'user' for user, 'admin' for admin, 'both' for both

		} catch ( Exception $e ) {
			SBIW_Logger::log( 'Client Sync Error: Failed to create/save WooCommerce customer for User ID: ' . $user_id . '. Error: ' . $e->getMessage() );
			// Consider deleting the created WP user if WC_Customer creation fails fundamentally
			// wp_delete_user($user_id);
		}
	}

	/**
	 * Sync SAP B1 BPAddresses to Custom Multiple Addresses plugin
	 *
	 * This function processes the BPAddresses array from SAP B1 and syncs additional addresses
	 * to the Custom Multiple Addresses plugin's meta keys (cma_billing_addresses, cma_shipping_addresses).
	 * The first address of each type becomes the WooCommerce default (handled by set_customer_address_data),
	 * while additional addresses are stored in the CMA plugin format.
	 *
	 * @param int $user_id WooCommerce user ID
	 * @param array $sap_client_data SAP B1 client data with BPAddresses
	 */
	private function sync_sap_addresses_to_cma( $user_id, $sap_client_data ) {
		// Check if Custom Multiple Addresses plugin is active
		if ( ! $this->is_cma_plugin_active() ) {
			SBIW_Logger::log( 'CMA Sync: Custom Multiple Addresses plugin not active, skipping additional address sync for User ID: ' . $user_id );
			return;
		}

		// Check if we have BPAddresses data
		if ( ! isset( $sap_client_data['BPAddresses'] ) || ! is_array( $sap_client_data['BPAddresses'] ) ) {
			SBIW_Logger::log( 'CMA Sync: No BPAddresses data found for User ID: ' . $user_id . ', CardCode: ' . ( isset( $sap_client_data['CardCode'] ) ? $sap_client_data['CardCode'] : 'N/A' ) );
			return;
		}

		SBIW_Logger::log( 'CMA Sync: Starting additional address sync for User ID: ' . $user_id . ', CardCode: ' . $sap_client_data['CardCode'] );

		// Separate addresses by type
		$billing_addresses = array();
		$shipping_addresses = array();

		foreach ( $sap_client_data['BPAddresses'] as $address ) {
			if ( ! isset( $address['AddressType'] ) ) {
				continue;
			}

			$converted_address = $this->convert_sap_address_to_cma_format( $address, $sap_client_data );

			if ( $address['AddressType'] === 'bo_BillTo' ) {
				$billing_addresses[] = $converted_address;
			} elseif ( $address['AddressType'] === 'bo_ShipTo' ) {
				$shipping_addresses[] = $converted_address;
			}
		}

		// Sync billing addresses (skip first one as it's already the WooCommerce default)
		if ( count( $billing_addresses ) > 1 ) {
			$additional_billing = array_slice( $billing_addresses, 1 ); // Skip first address
			$this->sync_addresses_to_cma_meta( $user_id, 'billing', $additional_billing );
			SBIW_Logger::log( 'CMA Sync: Synced ' . count( $additional_billing ) . ' additional billing addresses for User ID: ' . $user_id );
		}

		// Sync billing addresses (skip first one as it's already the WooCommerce default)
		if ( count( $billing_addresses ) > 1 ) {
			$additional_billing = array_slice( $billing_addresses, 1 ); // Skip first address
			$this->sync_addresses_to_cma_meta( $user_id, 'billing', $additional_billing );
			SBIW_Logger::log( 'CMA Sync: Synced ' . count( $additional_billing ) . ' additional billing addresses for User ID: ' . $user_id );
		}

		// Sync shipping addresses (skip first one as it's already the WooCommerce default)
		if ( count( $shipping_addresses ) > 1 ) {
			$additional_shipping = array_slice( $shipping_addresses, 1 ); // Skip first address
			$this->sync_addresses_to_cma_meta( $user_id, 'shipping', $additional_shipping );
			SBIW_Logger::log( 'CMA Sync: Synced ' . count( $additional_shipping ) . ' additional shipping addresses for User ID: ' . $user_id );
		}

		// Note: Single addresses are NOT stored in CMA - they remain only as WooCommerce defaults
		// This ensures CMA is used only for additional/multiple addresses as intended

		SBIW_Logger::log( 'CMA Sync: Completed additional address sync for User ID: ' . $user_id );
	}

	/**
	 * Convert SAP B1 address format to Custom Multiple Addresses format
	 *
	 * @param array $sap_address SAP B1 address data
	 * @param array $sap_client_data Full SAP client data for name/phone fallback
	 * @return array CMA formatted address
	 */
	private function convert_sap_address_to_cma_format( $sap_address, $sap_client_data ) {
		// Extract name from CardName
		$card_name_parts = explode( ' ', $sap_client_data['CardName'], 2 );
		$first_name = $card_name_parts[0];
		$last_name = isset( $card_name_parts[1] ) ? $card_name_parts[1] : '';

		// Determine address type for ID generation
		$address_type = '';
		if ( isset( $sap_address['AddressType'] ) ) {
			$address_type = $sap_address['AddressType'] === 'bo_BillTo' ? 'billing' : 'shipping';
		}

		return array(
			'first_name' => sanitize_text_field( $first_name ),
			'last_name' => sanitize_text_field( $last_name ),
			'company' => isset( $sap_client_data['CardName'] ) ? sanitize_text_field( $sap_client_data['CardName'] ) : '',
			'address_1' => isset( $sap_address['Street'] ) ? sanitize_text_field( $sap_address['Street'] ) : '',
			'address_2' => isset( $sap_address['Block'] ) ? sanitize_text_field( $sap_address['Block'] ) : '',
			'city' => isset( $sap_address['City'] ) ? sanitize_text_field( $sap_address['City'] ) : '',
			'state' => isset( $sap_address['County'] ) ? sanitize_text_field( $sap_address['County'] ) : '',
			'postcode' => isset( $sap_address['ZipCode'] ) ? sanitize_text_field( $sap_address['ZipCode'] ) : '',
			'country' => isset( $sap_address['Country'] ) ? sanitize_text_field( $sap_address['Country'] ) : '',
			'is_default' => false, // Will be set to true for first address of each type
			'sap_source' => true, // Mark as SAP-sourced for identification
			'sap_address_name' => isset( $sap_address['AddressName'] ) ? sanitize_text_field( $sap_address['AddressName'] ) : '',
			'sap_address_type' => $address_type, // Store for ID generation
			'sap_cardcode' => isset( $sap_client_data['CardCode'] ) ? sanitize_text_field( $sap_client_data['CardCode'] ) : '',
			'sap_sync_date' => current_time( 'mysql' )
		);
	}

	/**
	 * Generate unique address ID using CardCode + AddressName + AddressType format
	 *
	 * @param array $address Address data with sap_cardcode, sap_address_name, sap_address_type
	 * @return string Unique address ID
	 */
	private function generate_sap_address_id( $address ) {
		$cardcode = isset( $address['sap_cardcode'] ) ? $address['sap_cardcode'] : 'UNKNOWN';
		$address_name = isset( $address['sap_address_name'] ) ? $address['sap_address_name'] : 'Default';
		$address_type = isset( $address['sap_address_type'] ) ? $address['sap_address_type'] : 'unknown';

		// Replace spaces with underscores in address name
		$address_name = str_replace( ' ', '_', $address_name );

		// Generate ID: CardCode_AddressName_AddressType
		$address_id = $cardcode . '_' . $address_name . '_' . $address_type;

		// Sanitize the ID to ensure it's safe for use as array key
		$address_id = sanitize_key( $address_id );

		return $address_id;
	}

	/**
	 * Sync addresses to Custom Multiple Addresses plugin meta keys
	 *
	 * @param int $user_id User ID
	 * @param string $address_type 'billing' or 'shipping'
	 * @param array $addresses Array of addresses to sync
	 */
	private function sync_addresses_to_cma_meta( $user_id, $address_type, $addresses ) {
		if ( empty( $addresses ) ) {
			return;
		}

		// Get existing CMA addresses
		$existing_addresses = get_user_meta( $user_id, 'cma_' . $address_type . '_addresses', true );
		if ( ! is_array( $existing_addresses ) ) {
			$existing_addresses = array();
		}

		// Remove existing SAP-sourced addresses to avoid duplicates
		$filtered_addresses = array();
		foreach ( $existing_addresses as $addr_id => $address ) {
			if ( ! isset( $address['sap_source'] ) || $address['sap_source'] !== true ) {
				$filtered_addresses[ $addr_id ] = $address;
			}
		}

		// Add new SAP addresses with unique IDs using CardCode + AddressName + AddressType format
		foreach ( $addresses as $address ) {
			$address_id = $this->generate_sap_address_id( $address );
			$filtered_addresses[ $address_id ] = $address;
		}

		// Use CMA plugin's safe update method if available, otherwise use standard update_user_meta
		if ( $this->use_cma_safe_update( $user_id, 'cma_' . $address_type . '_addresses', $filtered_addresses ) ) {
			SBIW_Logger::log( 'CMA Sync: Used CMA safe update for ' . $address_type . ' addresses, User ID: ' . $user_id );
		} else {
			update_user_meta( $user_id, 'cma_' . $address_type . '_addresses', $filtered_addresses );
			SBIW_Logger::log( 'CMA Sync: Used standard update_user_meta for ' . $address_type . ' addresses, User ID: ' . $user_id );
		}
	}

	/**
	 * Check if Custom Multiple Addresses plugin is active
	 *
	 * @return bool
	 */
	private function is_cma_plugin_active() {
		return class_exists( 'Custom_Multiple_Addresses' ) ||
			   in_array( 'custom-multiple-addresses/custom-multiple-addresses.php', apply_filters( 'active_plugins', get_option( 'active_plugins' ) ) );
	}

	/**
	 * Attempt to use CMA plugin's safe update method to avoid SupportBoard conflicts
	 *
	 * @param int $user_id User ID
	 * @param string $meta_key Meta key
	 * @param mixed $meta_value Meta value
	 * @return bool True if CMA safe method was used, false if fallback was used
	 */
	private function use_cma_safe_update( $user_id, $meta_key, $meta_value ) {
		// Look for CMA plugin instance in WordPress hooks or globals
		if ( isset( $GLOBALS['custom_multiple_addresses'] ) &&
			 method_exists( $GLOBALS['custom_multiple_addresses'], 'safe_update_user_meta' ) ) {

			// Use CMA's safe update method
			$GLOBALS['custom_multiple_addresses']->safe_update_user_meta( $user_id, $meta_key, $meta_value );
			return true;
		}

		// Fallback: Use standard WordPress function
		return false;
	}

	/**
	 * Queue address change for reverse sync to SAP B1
	 * This method is called when WooCommerce addresses are updated
	 *
	 * @param int $user_id WordPress user ID
	 * @param string $address_type 'billing' or 'shipping'
	 * @param array $address_data Updated address data
	 */
	public function queue_address_change_for_reverse_sync( $user_id, $address_type, $address_data ) {
		// Only sync users that came from SAP B1 (have CardCode)
		$cardcode = get_user_meta( $user_id, '_sapb1_cardcode', true );
		if ( empty( $cardcode ) ) {
			SBIW_Logger::log( 'Reverse Sync: Skipping user ID ' . $user_id . ' - no SAP B1 CardCode found' );
			return;
		}

		// Get existing sync queue
		$sync_queue = get_option( 'sapb1_reverse_sync_queue', array() );

		// Create queue entry
		$queue_entry = array(
			'user_id' => $user_id,
			'cardcode' => $cardcode,
			'address_type' => $address_type,
			'address_data' => $address_data,
			'queued_at' => current_time( 'mysql' ),
			'status' => 'pending'
		);

		// Add to queue with unique key to prevent duplicates
		$queue_key = $user_id . '_' . $address_type . '_' . time();
		$sync_queue[ $queue_key ] = $queue_entry;

		// Save queue
		update_option( 'sapb1_reverse_sync_queue', $sync_queue );

		SBIW_Logger::log( 'Reverse Sync: Queued address change for User ID ' . $user_id . ', CardCode: ' . $cardcode . ', Type: ' . $address_type );
	}

	/**
	 * Process reverse sync queue (called by cron)
	 * Syncs WooCommerce address changes back to SAP B1
	 */
	public function process_reverse_sync_queue() {
		$sync_queue = get_option( 'sapb1_reverse_sync_queue', array() );

		if ( empty( $sync_queue ) ) {
			SBIW_Logger::log( 'Reverse Sync: No items in queue' );
			return;
		}

		SBIW_Logger::log( 'Reverse Sync: Processing ' . count( $sync_queue ) . ' queued items' );

		$processed_items = array();

		foreach ( $sync_queue as $queue_key => $queue_entry ) {
			if ( $queue_entry['status'] !== 'pending' ) {
				continue;
			}

			$success = $this->sync_address_to_sapb1(
				$queue_entry['cardcode'],
				$queue_entry['address_type'],
				$queue_entry['address_data']
			);

			if ( $success ) {
				$queue_entry['status'] = 'completed';
				$queue_entry['processed_at'] = current_time( 'mysql' );
				SBIW_Logger::log( 'Reverse Sync: Successfully synced address for CardCode: ' . $queue_entry['cardcode'] );
			} else {
				$queue_entry['status'] = 'failed';
				$queue_entry['failed_at'] = current_time( 'mysql' );
				SBIW_Logger::log( 'Reverse Sync: Failed to sync address for CardCode: ' . $queue_entry['cardcode'] );
			}

			$processed_items[ $queue_key ] = $queue_entry;
		}

		// Update queue with processed items
		$updated_queue = array_merge( $sync_queue, $processed_items );

		// Clean up old completed/failed items (older than 7 days)
		$cutoff_date = date( 'Y-m-d H:i:s', strtotime( '-7 days' ) );
		foreach ( $updated_queue as $key => $item ) {
			if ( $item['status'] !== 'pending' &&
				 isset( $item['processed_at'] ) &&
				 $item['processed_at'] < $cutoff_date ) {
				unset( $updated_queue[ $key ] );
			}
		}

		update_option( 'sapb1_reverse_sync_queue', $updated_queue );
		SBIW_Logger::log( 'Reverse Sync: Queue processing completed' );
	}

	/**
	 * Sync address data to SAP B1
	 *
	 * @param string $cardcode SAP B1 CardCode
	 * @param string $address_type 'billing' or 'shipping'
	 * @param array $address_data Address data to sync
	 * @return bool Success status
	 */
	private function sync_address_to_sapb1( $cardcode, $address_type, $address_data ) {
		try {
			// Convert WooCommerce address format to SAP B1 format
			$sap_address_data = $this->convert_wc_address_to_sap_format( $address_data, $address_type );

			// Use SAP B1 API to update address
			$result = $this->sapb1_api_client->update_customer_address( $cardcode, $sap_address_data );

			return $result !== false;

		} catch ( Exception $e ) {
			SBIW_Logger::log( 'Reverse Sync Error: Failed to sync address for CardCode ' . $cardcode . ': ' . $e->getMessage() );
			return false;
		}
	}

	/**
	 * Convert WooCommerce address format to SAP B1 format
	 *
	 * @param array $wc_address WooCommerce address data
	 * @param string $address_type 'billing' or 'shipping'
	 * @return array SAP B1 formatted address
	 */
	private function convert_wc_address_to_sap_format( $wc_address, $address_type ) {
		// Determine SAP B1 address type
		$sap_address_type = $address_type === 'billing' ? 'bo_BillTo' : 'bo_ShipTo';

		return array(
			'AddressType' => $sap_address_type,
			'Street' => isset( $wc_address['address_1'] ) ? $wc_address['address_1'] : '',
			'Block' => isset( $wc_address['address_2'] ) ? $wc_address['address_2'] : '',
			'City' => isset( $wc_address['city'] ) ? $wc_address['city'] : '',
			'County' => isset( $wc_address['state'] ) ? $wc_address['state'] : '',
			'ZipCode' => isset( $wc_address['postcode'] ) ? $wc_address['postcode'] : '',
			'Country' => isset( $wc_address['country'] ) ? $wc_address['country'] : '',
			'AddressName' => $address_type === 'billing' ? 'Billing Address' : 'Shipping Address'
		);
	}
}
?>

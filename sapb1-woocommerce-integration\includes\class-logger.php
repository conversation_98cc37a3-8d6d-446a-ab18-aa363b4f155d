<?php
/**
 * SBIW_Logger class file.
 *
 * @package SAPB1WooCommerceIntegration
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

if ( ! class_exists( 'SBIW_Logger' ) ) {
    /**
     * SBIW_Logger Class
     *
     * Handles logging for the SAP B1 WooCommerce Integration plugin.
     */
    class SBIW_Logger {

        /**
         * Path to the log file.
         *
         * @var string
         */
        private static $log_file = '';

        /**
         * Initializes the logger.
         *
         * Sets the log file path and ensures the log directory exists.
         */
        public static function init() {
            $upload_dir = wp_upload_dir();
            $log_dir_path = $upload_dir['basedir'] . '/sapb1-woo-integration-logs'; // More standard location

            if ( ! defined( 'WP_PLUGIN_DIR' ) ) {
                 // Fallback if WP_PLUGIN_DIR is not defined, though it should be in a WP context.
                self::$log_file = plugin_dir_path( __FILE__ ) . '../logs/sync.log';
                $log_dir_path = plugin_dir_path( __FILE__ ) . '../logs';
            } else {
                 self::$log_file = WP_PLUGIN_DIR . '/sapb1-woocommerce-integration/logs/sync.log';
                 $log_dir_path = WP_PLUGIN_DIR . '/sapb1-woocommerce-integration/logs';
            }


            if ( ! is_dir( $log_dir_path ) ) {
                wp_mkdir_p( $log_dir_path );
            }

            // Check if the .gitkeep file exists and remove it, as we will be writing logs now.
            $gitkeep_file = $log_dir_path . '/.gitkeep';
            if ( file_exists( $gitkeep_file ) ) {
                unlink( $gitkeep_file );
            }
        }

        /**
         * In-memory log storage for testing purposes.
         *
         * @var array
         */
        private static $test_logs = [];

        /**
         * Logs a message to the log file and memory (for testing).
         *
         * @param string $message The message to log.
         */
        public static function log( $message ) {
            // Store in memory for testing
            self::$test_logs[] = $message;

            if ( empty( self::$log_file ) ) {
                self::init(); // Ensure logger is initialized
            }

            $timestamp = date( 'Y-m-d H:i:s' );
            $log_message = "[{$timestamp}] - {$message}" . PHP_EOL;

            if ( false === file_put_contents( self::$log_file, $log_message, FILE_APPEND ) ) {
                // Fallback to error_log if file writing fails
                error_log( "SBIW_Logger: Failed to write to log file: " . self::$log_file . ". Message: " . $message );
            }
        }

        /**
         * Logs an inventory transaction to the log file.
         *
         * @param string $sku The product SKU.
         * @param int    $old_quantity The old quantity.
         * @param int    $new_quantity The new quantity.
         * @param string $product_name Optional. The name of the product.
         */
        public static function log_inventory_transaction( $sku, $old_quantity, $new_quantity, $product_name = '' ) {
            if ( empty( self::$log_file ) ) {
                self::init(); // Ensure logger is initialized
            }

            $timestamp = date( 'Y-m-d H:i:s' );
            $product_info = ! empty( $product_name ) ? "Product: {$product_name}, " : '';
            $message_detail = "INVENTORY_TRANSACTION - SKU: {$sku}, {$product_info}Old Qty: {$old_quantity}, New Qty: {$new_quantity}";
            $log_message = "[{$timestamp}] - {$message_detail}" . PHP_EOL;

            if ( false === file_put_contents( self::$log_file, $log_message, FILE_APPEND ) ) {
                // Fallback to error_log if file writing fails
                error_log( "SBIW_Logger: Failed to write inventory transaction to log file: " . self::$log_file . ". Message: " . $message_detail );
            }
        }

        /**
         * Gets all logged messages for testing.
         *
         * @return array Array of log messages.
         */
        public static function get_logs() {
            return self::$test_logs;
        }

        /**
         * Clears all logged messages for testing.
         */
        public static function clear_logs() {
            self::$test_logs = [];
        }
    }
}
?>

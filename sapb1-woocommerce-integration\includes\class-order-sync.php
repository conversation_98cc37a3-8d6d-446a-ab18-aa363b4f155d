<?php
/**
 * Order_Sync class file.
 *
 * @package SAPB1WooCommerceIntegration
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

// Ensure SAPB1_API_Client and SBIW_Logger classes are available.
// It's assumed S<PERSON><PERSON>_Logger is loaded elsewhere or will be required in the main plugin file.
require_once plugin_dir_path( __FILE__ ) . 'class-sapb1-api-client.php';

if ( ! class_exists( 'Order_Sync' ) ) {
    /**
     * Order_Sync Class
     *
     * Handles synchronization of WooCommerce orders to SAP B1.
     */
    class Order_Sync {

        /**
         * Instance of SAPB1_API_Client.
         *
         * @var SAPB1_API_Client
         */
        private $api_client;

        /**
         * Constructor.
         *
         * Initializes the API client.
         */
        public function __construct() {
            $this->api_client = new SAPB1_API_Client();
        }

        /**
         * Pushes a WooCommerce order to SAP B1.
         *
         * @param WC_Order|object $order The WooCommerce order object.
         * @return bool True on success, false on failure.
         */
        public function push_order_to_sapb1( $order ) {
            if ( ! $order ) {
                SBIW_Logger::log( 'Order Sync Error: Invalid WC_Order object provided.' );
                return false;
            }

            SBIW_Logger::log( 'Order Sync: Starting to process order ID: ' . $order->get_id() );

            // 1. Get Customer CardCode
            $customer_id = $order->get_customer_id();
            $card_code = '';

            if ( $customer_id ) {
                // Registered customer - get CardCode from user meta
                $card_code = get_user_meta( $customer_id, '_sapb1_cardcode', true );
                SBIW_Logger::log( 'Order Sync: Order ID: ' . $order->get_id() . ' is from registered customer ID: ' . $customer_id );
            } else {
                // Guest order - handle via email lookup and customer creation
                SBIW_Logger::log( 'Order Sync: Order ID: ' . $order->get_id() . ' is a guest order. Processing guest customer...' );
            }

            if ( empty( $card_code ) ) {
                // Attempt to find or create CardCode by email for registered users if meta is missing, or for guests
                $billing_email = $order->get_billing_email();
                if ( ! empty( $billing_email ) ) {
                    SBIW_Logger::log( 'Order Sync: CardCode not found for order ID: ' . $order->get_id() . '. Attempting to find or create SAP B1 customer by email: ' . $billing_email );

                    $card_code = $this->get_or_create_customer_by_email( $order );

                    if ( empty( $card_code ) ) {
                        SBIW_Logger::log( 'Order Sync Error: Could not determine or create CardCode for order ID: ' . $order->get_id() . ' (Email: ' . $billing_email . '). Order not sent to SAP B1.' );
                        return false;
                    }

                    // If this was a registered customer without CardCode, store it for future use
                    if ( $customer_id ) {
                        update_user_meta( $customer_id, '_sapb1_cardcode', $card_code );
                        SBIW_Logger::log( 'Order Sync: Stored CardCode ' . $card_code . ' for registered customer ID: ' . $customer_id );
                    }
                } else {
                    SBIW_Logger::log( 'Order Sync Error: CardCode is empty and no billing email available for order ID: ' . $order->get_id() . '. Order not sent to SAP B1.' );
                    return false;
                }
            }

            // 2. Prepare Document Lines (Order Items)
            $document_lines = [];
            foreach ( $order->get_items() as $item_id => $item ) {
                $product = $item->get_product();
                $sku = $product ? $product->get_sku() : '';

                if ( empty( $sku ) ) {
                    SBIW_Logger::log( 'Order Sync Warning: Order ID: ' . $order->get_id() . ' - Item ID: ' . $item_id . ' has no SKU. Skipping item.' );
                    continue;
                }
                $document_lines[] = [
                    'ItemCode'  => $sku,
                    'Quantity'  => $item->get_quantity(),
                    'UnitPrice' => $order->get_item_subtotal( $item, false, false ), // Price per unit before discounts
                    // 'WarehouseCode' => 'YOUR_DEFAULT_WAREHOUSE_CODE', // Optional: Specify warehouse if needed
                ];
            }

            if ( empty( $document_lines ) ) {
                SBIW_Logger::log( 'Order Sync Error: Order ID: ' . $order->get_id() . ' has no valid items with SKUs to send to SAP B1.' );
                return false;
            }

            // 3. Construct Order Data Payload (basic example, refer to Step 1 for more fields)
            $order_data = [
                'CardCode'      => $card_code,
                'DocDueDate'    => date( 'Y-m-d', strtotime( '+7 days' ) ), // Example: Due in 7 days
                'DocDate'       => $order->get_date_created()->format( 'Y-m-d' ),
                'Comments'      => 'WooCommerce Order ID: ' . $order->get_id() . ". " . $order->get_customer_note(),
                'DocumentLines' => $document_lines,
                // Add Address information - this is a simplified example
                // More detailed mapping would be needed for full addresses.
                'ShipToStreet'  => $order->get_shipping_address_1(),
                'ShipToBlock'   => $order->get_shipping_address_2(),
                'ShipToCity'    => $order->get_shipping_city(),
                'ShipToZipCode' => $order->get_shipping_postcode(),
                'ShipToState'   => $order->get_shipping_state(),
                'ShipToCountry' => $order->get_shipping_country(),

                'BillToStreet'  => $order->get_billing_address_1(),
                'BillToBlock'   => $order->get_billing_address_2(),
                'BillToCity'    => $order->get_billing_city(),
                'BillToZipCode' => $order->get_billing_postcode(),
                'BillToState'   => $order->get_billing_state(),
                'BillToCountry' => $order->get_billing_country(),
            ];

            // Optional: Add UDFs (User Defined Fields) if SAP B1 is configured for them
            // For example: $order_data['U_WC_Order_ID'] = $order->get_id();

            // 4. Send to SAP B1
            SBIW_Logger::log( 'Order Sync: Sending order data to SAP B1 for order ID: ' . $order->get_id() . '. Data: ' . wp_json_encode($order_data) );
            $sap_response = $this->api_client->create_sales_order( $order_data );

            if ( $sap_response ) {
                // Assuming sap_response might contain the SAP DocEntry or DocNum
                $sap_doc_num = isset($sap_response['DocNum']) ? $sap_response['DocNum'] : 'N/A';
                SBIW_Logger::log( 'Order Sync: Successfully pushed order ID: ' . $order->get_id() . ' to SAP B1. SAP DocNum: ' . $sap_doc_num );
                // Optionally, store SAP order ID in order meta:
                // $order->update_meta_data( '_sapb1_order_id', $sap_doc_num );
                // $order->save_meta_data();
                return true;
            } else {
                SBIW_Logger::log( 'Order Sync Error: Failed to push order ID: ' . $order->get_id() . ' to SAP B1.' );
                return false;
            }
        }

        /**
         * Gets or creates a customer in SAP B1 based on order billing information.
         *
         * @param WC_Order|object $order The WooCommerce order object.
         * @return string|false CardCode if successful, false on failure.
         */
        private function get_or_create_customer_by_email( $order ) {
            $billing_email = $order->get_billing_email();

            if ( empty( $billing_email ) ) {
                SBIW_Logger::log( 'Order Sync Error: Cannot create customer - no billing email provided.' );
                return false;
            }

            // First, try to find existing customer by email
            SBIW_Logger::log( 'Order Sync: Searching for existing SAP B1 customer with email: ' . $billing_email );
            $existing_customer = $this->api_client->get_customer_by_email( $billing_email );

            if ( $existing_customer && ! empty( $existing_customer['CardCode'] ) ) {
                SBIW_Logger::log( 'Order Sync: Found existing SAP B1 customer with CardCode: ' . $existing_customer['CardCode'] );
                return $existing_customer['CardCode'];
            }

            // Customer not found, create new one
            SBIW_Logger::log( 'Order Sync: No existing customer found. Creating new SAP B1 customer for email: ' . $billing_email );

            $customer_data = $this->prepare_customer_data_from_order( $order );
            $created_customer = $this->api_client->create_business_partner( $customer_data );

            if ( $created_customer && ! empty( $created_customer['CardCode'] ) ) {
                SBIW_Logger::log( 'Order Sync: Successfully created new SAP B1 customer with CardCode: ' . $created_customer['CardCode'] );
                return $created_customer['CardCode'];
            } elseif ( $created_customer === true ) {
                // Fallback case where creation was successful but we couldn't parse the CardCode
                // Try to find the customer again by email
                SBIW_Logger::log( 'Order Sync: Customer creation successful but CardCode not returned. Attempting to find newly created customer by email.' );
                $retry_customer = $this->api_client->get_customer_by_email( $billing_email );
                if ( $retry_customer && ! empty( $retry_customer['CardCode'] ) ) {
                    SBIW_Logger::log( 'Order Sync: Found newly created customer with CardCode: ' . $retry_customer['CardCode'] );
                    return $retry_customer['CardCode'];
                }
            }

            SBIW_Logger::log( 'Order Sync Error: Failed to create or retrieve CardCode for customer with email: ' . $billing_email );
            return false;
        }

        /**
         * Prepares customer data from WooCommerce order for SAP B1 business partner creation.
         *
         * @param WC_Order|object $order The WooCommerce order object.
         * @return array Customer data array for SAP B1.
         */
        private function prepare_customer_data_from_order( $order ) {
            $billing_first_name = $order->get_billing_first_name();
            $billing_last_name = $order->get_billing_last_name();
            $billing_company = $order->get_billing_company();

            // Determine CardName (customer name)
            if ( ! empty( $billing_company ) ) {
                $card_name = $billing_company;
            } else {
                $card_name = trim( $billing_first_name . ' ' . $billing_last_name );
            }

            // Fallback if no name is available
            if ( empty( $card_name ) ) {
                $card_name = 'Guest Customer';
            }

            $customer_data = [
                'CardName' => sanitize_text_field( $card_name ),
                'EmailAddress' => sanitize_email( $order->get_billing_email() ),
                'Phone1' => sanitize_text_field( $order->get_billing_phone() ),
                'CardType' => 'C', // Customer
                'Valid' => 'Y',
                'Frozen' => 'N',
                'Currency' => get_woocommerce_currency(),

                // Billing Address
                'BillToStreet' => sanitize_text_field( $order->get_billing_address_1() ),
                'BillToBlock' => sanitize_text_field( $order->get_billing_address_2() ),
                'BillToCity' => sanitize_text_field( $order->get_billing_city() ),
                'BillToZipCode' => sanitize_text_field( $order->get_billing_postcode() ),
                'BillToState' => sanitize_text_field( $order->get_billing_state() ),
                'BillToCountry' => sanitize_text_field( $order->get_billing_country() ),

                // Shipping Address (use billing if shipping is empty)
                'ShipToStreet' => sanitize_text_field( $order->get_shipping_address_1() ?: $order->get_billing_address_1() ),
                'ShipToBlock' => sanitize_text_field( $order->get_shipping_address_2() ?: $order->get_billing_address_2() ),
                'ShipToCity' => sanitize_text_field( $order->get_shipping_city() ?: $order->get_billing_city() ),
                'ShipToZipCode' => sanitize_text_field( $order->get_shipping_postcode() ?: $order->get_billing_postcode() ),
                'ShipToState' => sanitize_text_field( $order->get_shipping_state() ?: $order->get_billing_state() ),
                'ShipToCountry' => sanitize_text_field( $order->get_shipping_country() ?: $order->get_billing_country() ),
            ];

            // Add company-specific fields if company name is provided
            if ( ! empty( $billing_company ) ) {
                $customer_data['ContactPerson'] = sanitize_text_field( trim( $billing_first_name . ' ' . $billing_last_name ) );
            }

            // Remove empty values to avoid SAP B1 validation issues
            $customer_data = array_filter( $customer_data, function( $value ) {
                return ! empty( $value );
            });

            SBIW_Logger::log( 'Order Sync: Prepared customer data for SAP B1: ' . wp_json_encode( $customer_data ) );

            return $customer_data;
        }
    }
}
?>

<?php
/**
 * Product_Sync class file.
 *
 * @package SAPB1WooCommerceIntegration
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

// Ensure SAPB1_API_Client class is available.
require_once plugin_dir_path( __FILE__ ) . 'class-sapb1-api-client.php';

if ( ! class_exists( 'Product_Sync' ) ) {
    /**
     * Product_Sync Class
     *
     * Handles synchronization of products from SAP B1 to WooCommerce.
     */
    class Product_Sync {

        /**
         * Instance of SAPB1_API_Client.
         *
         * @var SAPB1_API_Client
         */
        private $api_client;

        /**
         * Constructor.
         *
         * Initializes the API client.
         */
        public function __construct() {
            $this->api_client = new SAPB1_API_Client();
        }

        /**
         * Synchronizes products from SAP B1 to WooCommerce.
         *
         * Fetches products from SAP B1 and creates them in WooCommerce if they don't already exist.
         * Existing products are currently skipped.
         */
        public function sync_products() {
            $sap_products = $this->api_client->get_products();

            if ( ! is_array( $sap_products ) ) {
                SBIW_Logger::log( 'SAP B1 Product Sync: Failed to fetch products from SAP B1 or no products returned. Synchronization aborted.' );
                return;
            }

            if ( empty( $sap_products ) ) {
                SBIW_Logger::log( 'SAP B1 Product Sync: No products to synchronize from SAP B1.' );
                return;
            }

            foreach ( $sap_products as $sap_product ) {
                if ( ! isset( $sap_product['ItemCode'], $sap_product['ItemName'], $sap_product['ItemPrice'], $sap_product['QuantityOnStock'] ) ) {
                    SBIW_Logger::log( 'SAP B1 Product Sync: Skipping product due to missing required data. Product data: ' . print_r( $sap_product, true ) );
                    continue;
                }

                $sku = $sap_product['ItemCode'];
                $product_id = wc_get_product_id_by_sku( $sku );

                if ( $product_id === 0 ) {
                    // Product does not exist, create it.
                    $product = new WC_Product_Simple();
                    $product->set_sku( $sku );
                    $product->set_name( $sap_product['ItemName'] );
                    $product->set_regular_price( (string) $sap_product['ItemPrice'] ); // Price should be a string
                    $product->set_manage_stock( true );
                    $product->set_stock_quantity( $sap_product['QuantityOnStock'] );
                    $product->set_status( 'publish' );

                    try {
                        $new_product_id = $product->save();
                        if ( $new_product_id ) {
                            SBIW_Logger::log( "SAP B1 Product Sync: Created new product: {$sap_product['ItemName']} (SKU: {$sku}), ID: {$new_product_id}" );
                        } else {
                            SBIW_Logger::log( "SAP B1 Product Sync: Failed to create product: {$sap_product['ItemName']} (SKU: {$sku}). WooCommerce save() returned 0." );
                        }
                    } catch ( WC_Data_Exception $e ) {
                        SBIW_Logger::log( "SAP B1 Product Sync: Error creating product {$sap_product['ItemName']} (SKU: {$sku}): " . $e->getMessage() );
                    }
                } else {
                    // Product exists, check and update stock if necessary.
                    $product = wc_get_product( $product_id );

                    if ( ! $product ) {
                        SBIW_Logger::log( "SAP B1 Product Sync: Could not retrieve product with ID {$product_id} for SKU {$sku}. Skipping update." );
                        continue;
                    }

                    $sap_product_stock_quantity = (int) $sap_product['QuantityOnStock'];
                    $wc_stock_quantity = $product->get_stock_quantity();

                    if ( $sap_product_stock_quantity !== $wc_stock_quantity ) {
                        $product->set_manage_stock( true );
                        $product->set_stock_quantity( $sap_product_stock_quantity );
                        try {
                            $product->save();
                            SBIW_Logger::log_inventory_transaction( $sku, $wc_stock_quantity, $sap_product_stock_quantity, $product->get_name() );
                        } catch ( WC_Data_Exception $e ) {
                            SBIW_Logger::log( "SAP B1 Product Sync: Error updating stock for product {$sap_product['ItemName']} (SKU: {$sku}): " . $e->getMessage() );
                        }
                    } else {
                        SBIW_Logger::log( "SAP B1 Product Sync: Stock for SKU {$sku} (ID: {$product_id}) is already synchronized. Quantity: {$wc_stock_quantity}. Skipping." );
                    }
                }
            }
            SBIW_Logger::log( 'SAP B1 Product Sync: Synchronization process completed.' );
        }
    }
}
?>

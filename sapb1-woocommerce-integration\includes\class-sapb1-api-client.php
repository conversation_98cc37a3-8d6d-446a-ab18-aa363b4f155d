<?php
/**
 * SAPB1_API_Client class file.
 *
 * @package SAPB1WooCommerceIntegration
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

if ( ! class_exists( 'SAPB1_API_Client' ) ) {
    /**
     * SAPB1_API_Client Class
     *
     * Handles communication with the SAP B1 Service Layer API.
     */
    class SAPB1_API_Client {

        /**
         * Base URL for the SAP B1 Service Layer API.
         *
         * @var string
         */
        private $base_url = 'https://sid-hdb/b1s/v1/';

        /**
         * Username for SAP B1 API authentication.
         *
         * @var string
         */
        private $username = 'manager'; // Try: administrator, manager, or other SAP user

        /**
         * Password for SAP B1 API authentication.
         *
         * @var string
         */
        private $password = 'Abc123.!@!@'; // Try: manager, admin, or actual password

        /**
         * Session cookie obtained after successful login.
         *
         * @var string
         */
        private $session_cookie = '';
        private $session_id = ''; // The actual SessionId from login response

        /**
         * Company database name for SAP B1.
         *
         * @var string
         */
        private $company_db = 'SBODEMOGB'; // Try: SBODemoUS, SBODEMO, or actual company DB

        /**
         * Constructor.
         */
        public function __construct() {
            // Initialization code can go here if needed in the future.
        }

        /**
         * Validates and fixes URL format for cURL compatibility
         */
        private function fix_url($url) {
            // Simple URL fix - just replace spaces with %20
            // The URL encoding is already handled in the query building
            $fixed_url = str_replace(' ', '%20', $url);
            return $fixed_url;
        }

        /**
         * Logs in to the SAP B1 API.
         *
         * As the server is currently down, this method simulates a successful login.
         * It includes commented-out code for the actual cURL request.
         *
         * @return bool True on successful login (or simulated success), false otherwise.
         */
        public function login() {
            // Actual cURL request for login
            $login_url = $this->base_url . 'Login';
            $login_data = [
                'UserName'  => $this->username,
                'Password'  => $this->password,
                'CompanyDB' => $this->company_db,
            ];

            $ch = curl_init( $login_url );
            curl_setopt( $ch, CURLOPT_RETURNTRANSFER, true );
            curl_setopt( $ch, CURLOPT_POST, true );
            curl_setopt( $ch, CURLOPT_POSTFIELDS, json_encode( $login_data ) );
            curl_setopt( $ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
            ] );
            curl_setopt( $ch, CURLOPT_HEADER, true ); // To capture Set-Cookie header

            // For development/testing with self-signed certificates, you might need:
            // curl_setopt( $ch, CURLOPT_SSL_VERIFYPEER, false );
            // curl_setopt( $ch, CURLOPT_SSL_VERIFYHOST, false );

            $response = curl_exec( $ch );
            $http_code = curl_getinfo( $ch, CURLINFO_HTTP_CODE );
            $header_size = curl_getinfo( $ch, CURLINFO_HEADER_SIZE );
            $header_str = substr( $response, 0, $header_size );
            $body_str = substr( $response, $header_size );

            if ( curl_errno( $ch ) ) {
                SBIW_Logger::log( 'SAPB1 Login Error: cURL error - ' . curl_error( $ch ) );
                curl_close( $ch );
                return false;
            }

            curl_close( $ch );

            if ( $http_code === 200 ) {
                $cookies = [];
                if ( preg_match_all( '/^Set-Cookie:\s*([^;]*)/mi', $header_str, $matches ) ) {
                    foreach ($matches[1] as $match) {
                        // Only grab B1SESSION and ROUTEID
                        if (strpos($match, 'SESSION=') === 0 || strpos($match, 'ROUTEID=') === 0) {
                            $cookies[] = $match;
                        }
                    }
                }

                if ( !empty($cookies) ) {
                    $login_data = json_decode( $body_str, true );
                    $this->session_id = $login_data['SessionId'];
                    $this->session_cookie = 'B1SESSION=' . $this->session_id;
                    SBIW_Logger::log( 'SAPB1 Login: Successful. Session cookies stored: ' . $this->session_cookie );
                    // SBIW_Logger::log( 'SAPB1 Login: Successful. Session ID stored: ' . $this->session_id );
                    return true;
                } else {
                    SBIW_Logger::log( 'SAPB1 Login Error: Session cookies (B1SESSION or ROUTEID) not found in response headers. Response: ' . $header_str );
                    return false;
                }
            } else {
                SBIW_Logger::log( "SAPB1 Login Error: HTTP Status - $http_code. Response: " . $body_str );
                return false;
            }
        }

        /**
         * Fetches products from the SAP B1 API.
         *
         * COMMENTED OUT - Not used for client sync, but kept for future product integration.
         * If not already logged in, it will attempt to log in first.
         *
         * @return array|false Array of products on success, false on failure.
         */
        public function get_products() {
            if ( empty( $this->session_cookie ) ) {
                if ( ! $this->login() ) {
                    SBIW_Logger::log( 'SAPB1 GetProducts Error: Cannot get products, login failed.' );
                    return false;
                }
            }

            // Actual cURL request for fetching items
            // Using AvgStdPrice for unit price and QuantityOnStock for stock (corrected field names)
            $items_url = $this->base_url . 'Items?$select=ItemCode,ItemName,AvgStdPrice,QuantityOnStock,ItemType';

            $ch = curl_init( $items_url );
            curl_setopt( $ch, CURLOPT_RETURNTRANSFER, true );
            curl_setopt( $ch, CURLOPT_HTTPHEADER, [
                sprintf('Cookie: %s', $this->session_cookie),
                'Content-Type: application/json',
            ] );

            // For development/testing with self-signed certificates:
            // curl_setopt( $ch, CURLOPT_SSL_VERIFYPEER, false );
            // curl_setopt( $ch, CURLOPT_SSL_VERIFYHOST, false );

            $response_body = curl_exec( $ch );
            $http_code = curl_getinfo( $ch, CURLINFO_HTTP_CODE );

            if ( curl_errno( $ch ) ) {
                SBIW_Logger::log( 'SAPB1 GetProducts Error: cURL error - ' . curl_error( $ch ) );
                curl_close( $ch );
                return false;
            }

            curl_close( $ch );

            if ( $http_code === 200 ) {
                $data = json_decode( $response_body, true );
                if ( json_last_error() !== JSON_ERROR_NONE ) {
                    SBIW_Logger::log( 'SAPB1 GetProducts Error: JSON decode error - ' . json_last_error_msg() . '. Response: ' . substr($response_body, 0, 200) );
                    return false;
                }

                if ( isset( $data['value'] ) && is_array( $data['value'] ) ) {
                    SBIW_Logger::log( 'SAPB1 GetProducts: Successfully fetched ' . count( $data['value'] ) . ' products.' );
                    return $data['value'];
                } else {
                    SBIW_Logger::log( 'SAPB1 GetProducts Error: Unexpected data structure or "value" field missing. Response: ' . substr($response_body, 0, 200) );
                    return false;
                }
            } elseif ( $http_code === 401 ) { // Unauthorized - session might have expired
                 SBIW_Logger::log( 'SAPB1 GetProducts Error: Unauthorized (401). Session might have expired. Attempting re-login.' );
                $this->session_cookie = ''; // Clear potentially invalid cookie
                if ( $this->login() ) { // Attempt to re-login
                    return $this->get_products(); // Retry fetching products
                }
                return false;
            } else {
                SBIW_Logger::log( "SAPB1 GetProducts Error: HTTP Status - $http_code. Response: " . substr($response_body, 0, 200) );
                return false;
            }
        }

        /**
         * Fetches clients (Business Partners) from the SAP B1 API.
         *
         * Filters for customers (CardType 'C').
         * If not already logged in, it will attempt to log in first.
         * Retries login once if the session has expired (401 error).
         *
         * @param array $params Optional parameters to filter clients.
         * @return array|false Array of clients on success, false on failure.
         */
        public function get_clients( $params = [] ) {
            // Check if dummy data mode is enabled
            $use_dummy_data = get_option( 'sapb1_use_dummy_data', false );

            if ( $use_dummy_data ) {
                SBIW_Logger::log( 'SAPB1 GetClients: Using dummy data mode (configured in settings).' );
                return $this->get_fallback_clients_data();
            }

            if ( empty( $this->session_cookie ) ) {
                if ( ! $this->login() ) {
                    SBIW_Logger::log( 'SAPB1 GetClients Error: Cannot get clients, login failed.' );
                    return false;
                }
            }

            // Define fields to select for Business Partners (Clients)
            // Using only basic fields that are guaranteed to exist in SAP B1 BusinessPartner
            $select_fields = [
                'CardCode' ,
                'CardName' ,
                'CardType' ,
                'GroupCode' ,
                'Address' ,
                'ZipCode' ,
                'MailAddress' ,
                'MailZipCode' ,
                'Phone1' ,
                'Phone2' ,
                'Fax' ,
                'ContactPerson' ,
                'FederalTaxID' ,
                'FreeText' ,
                'SalesPersonCode' ,
                'Currency' ,
                'RateDiffAccount' ,
                'City' ,
                'County' ,
                'Country' ,
                'MailCity' ,
                'MailCounty' ,
                'MailCountry' ,
                'EmailAddress' ,
                'Picture' ,
                'DefaultAccount' ,
                'DefaultBranch' ,
                'DefaultBankCode' ,
                'FatherType' ,
                'VatGroup' ,
                'CreditCardCode' ,
                'CreditCardNum' ,
                'CreditCardExpiration' ,
                'BillToState' ,
                'ShipToState' ,
                'Priority' ,
                'BankCountry' ,
                'HouseBank' ,
                'HouseBankCountry' ,
                'HouseBankAccount' ,
                'ShipToDefault' ,
                'BilltoDefault' ,
                'Website' ,
                'CompanyPrivate' ,
                'UpdateDate' ,
                'UpdateTime' ,
                'CreateDate' ,
                'CreateTime' ,
                'BPAddresses' ,
                'ContactEmployees' ,
                'BPPaymentMethods' 
            ];

            $select_query = '$select=' . implode( ',', $select_fields );


            // Filter for customers ('C') by default, can be overridden by $params
            $filter_query = '$filter=CardType eq \'C\''; // Space encoded + quotes encoded // 'C' for Customer
            if ( ! empty( $params ) ) {
                $param_filters = [];
                foreach ( $params as $key => $value ) {
                    $param_filters[] = "{$key} eq {$value}"; // No quotes, space encoded
                }
                $filter_query = '$filter=' . implode( ' and ', $param_filters );
            }

            $clients_url = $this->base_url . 'BusinessPartners?' . $select_query . '&' . $filter_query;

            // Apply URL fix for cURL compatibility
            $fixed_clients_url = $this->fix_url($clients_url);
            
            SBIW_Logger::log( 'SAPB1 GetClients: Requesting URL: ' . $fixed_clients_url );

            $ch = curl_init( $fixed_clients_url );
            curl_setopt( $ch, CURLOPT_RETURNTRANSFER, true );
            curl_setopt( $ch, CURLOPT_HTTPHEADER, [
                sprintf( 'Cookie: %s', $this->session_cookie ),
                'Content-Type: application/json',
                'Prefer: odata.maxpagesize=100' // Requesting paged results, adjust size as needed
            ] );

            // For development/testing with self-signed certificates:
            // curl_setopt( $ch, CURLOPT_SSL_VERIFYPEER, false );
            // curl_setopt( $ch, CURLOPT_SSL_VERIFYHOST, false );

            // Enhanced URL handling options
            curl_setopt( $ch, CURLOPT_TIMEOUT, 30 );
            curl_setopt( $ch, CURLOPT_CONNECTTIMEOUT, 10 );
            curl_setopt( $ch, CURLOPT_FOLLOWLOCATION, true );
            curl_setopt( $ch, CURLOPT_MAXREDIRS, 3 );
            curl_setopt( $ch, CURLOPT_USERAGENT, 'SAPB1-WooCommerce-Integration/1.0' );
            curl_setopt( $ch, CURLOPT_ENCODING, '' ); // Accept any encoding



            $response_body = curl_exec( $ch );
            $http_code = curl_getinfo( $ch, CURLINFO_HTTP_CODE );

            if ( curl_errno( $ch ) ) {
                $curl_error = curl_error( $ch );
                SBIW_Logger::log( 'SAPB1 GetClients Error: cURL error - ' . $curl_error );
                curl_close( $ch );
                return false;
            }

            curl_close( $ch );

            if ( $http_code === 200 ) {
                $data = json_decode( $response_body, true );
                if ( json_last_error() !== JSON_ERROR_NONE ) {
                    SBIW_Logger::log( 'SAPB1 GetClients Error: JSON decode error - ' . json_last_error_msg() . '. Response: ' . substr( $response_body, 0, 200 ) );
                    return false;
                }

                if ( isset( $data['value'] ) && is_array( $data['value'] ) ) {
                    SBIW_Logger::log( 'SAPB1 GetClients: Successfully fetched ' . count( $data['value'] ) . ' clients.' );
                    // TODO: Handle pagination if 'odata.nextLink' is present in $data
                    return $data['value'];
                } else {
                    SBIW_Logger::log( 'SAPB1 GetClients Error: Unexpected data structure or "value" field missing. Response: ' . substr( $response_body, 0, 200 ) );
                    return false;
                }
            } elseif ( $http_code === 401 ) { // Unauthorized - session might have expired
                SBIW_Logger::log( 'SAPB1 GetClients Error: Unauthorized (401). Session might have expired. Attempting re-login.' );
                $this->session_cookie = ''; // Clear potentially invalid cookie
                if ( $this->login() ) { // Attempt to re-login
                    // Using a static variable to prevent infinite retry loops.
                    static $retry_count = 0;
                    if ( $retry_count < 1 ) {
                        $retry_count++;
                        return $this->get_clients( $params ); // Retry fetching clients
                    } else {
                        SBIW_Logger::log( 'SAPB1 GetClients Error: Re-login successful, but GetClients failed on retry. Preventing further retries.' );
                        return false;
                    }
                }
                SBIW_Logger::log( 'SAPB1 GetClients Error: Re-login failed.' );
                return false;
            } else {
                SBIW_Logger::log( "SAPB1 GetClients Error: HTTP Status - $http_code. Response: " . substr( $response_body, 0, 200 ) );
                return false;
            }
        }

        /**
         * Fetches detailed address information for a specific business partner.
         *
         * This method retrieves comprehensive address details including billing and shipping
         * addresses from the BPAddresses collection for a given business partner.
         * If not already logged in, it will attempt to log in first.
         * Retries login once if the session has expired (401 error).
         *
         * @param string $card_code The CardCode of the business partner.
         * @return array|false Array of address details on success, false on failure.
         */
        public function get_clients_address( $card_code ) {
            if ( empty( $this->session_cookie ) ) {
                if ( ! $this->login() ) {
                    SBIW_Logger::log( 'SAPB1 GetClientsAddress Error: Cannot get client addresses, login failed.' );
                    return false;
                }
            }

            if ( empty( $card_code ) ) {
                SBIW_Logger::log( 'SAPB1 GetClientsAddress Error: CardCode parameter is required.' );
                return false;
            }

            // Sanitize CardCode for URL
            $sanitized_card_code = urlencode( trim( $card_code ) );

            // Use expand to get BPAddresses collection with the business partner
            // This approach gets both BP basic info and all addresses in one call
           $address_url = $this->base_url . "BusinessPartners('{$sanitized_card_code}')?\$select=CardCode,CardName,BPAddresses";

            // Apply URL fix for cURL compatibility
            $fixed_address_url = $this->fix_url( $address_url );

            $ch = curl_init( $fixed_address_url );
            curl_setopt( $ch, CURLOPT_RETURNTRANSFER, true );
            curl_setopt( $ch, CURLOPT_HTTPHEADER, [
                sprintf( 'Cookie: %s', $this->session_cookie ),
                'Content-Type: application/json',
                'Accept: application/json'
            ] );

            // For development/testing with self-signed certificates:
            curl_setopt( $ch, CURLOPT_SSL_VERIFYPEER, false );
            curl_setopt( $ch, CURLOPT_SSL_VERIFYHOST, false );

            // Enhanced URL handling options
            curl_setopt( $ch, CURLOPT_TIMEOUT, 30 );
            curl_setopt( $ch, CURLOPT_CONNECTTIMEOUT, 10 );
            curl_setopt( $ch, CURLOPT_FOLLOWLOCATION, true );
            curl_setopt( $ch, CURLOPT_MAXREDIRS, 3 );
            curl_setopt( $ch, CURLOPT_USERAGENT, 'SAPB1-WooCommerce-Integration/1.0' );
            curl_setopt( $ch, CURLOPT_ENCODING, '' ); // Accept any encoding

            $response_body = curl_exec( $ch );
            $http_code = curl_getinfo( $ch, CURLINFO_HTTP_CODE );

            if ( curl_errno( $ch ) ) {
                $curl_error = curl_error( $ch );
                SBIW_Logger::log( 'SAPB1 GetClientsAddress Error: cURL error - ' . $curl_error );
                curl_close( $ch );
                return false;
            }

            curl_close( $ch );

            if ( $http_code === 200 ) {
                $data = json_decode( $response_body, true );
                if ( json_last_error() !== JSON_ERROR_NONE ) {
                    SBIW_Logger::log( 'SAPB1 GetClientsAddress Error: JSON decode error - ' . json_last_error_msg() . '. Response: ' . substr( $response_body, 0, 200 ) );
                    return false;
                }

                // Check if we have the business partner data with addresses
                if ( isset( $data['BPAddresses'] ) ) {
                    $result = [
                        'CardCode' => $data['CardCode'],
                        'CardName' => isset( $data['CardName'] ) ? $data['CardName'] : '',
                        'BPAddresses' => isset( $data['BPAddresses'] ) ? $data['BPAddresses'] : []
                    ];

                    SBIW_Logger::log( 'SAPB1 GetClientsAddress: Successfully fetched addresses for CardCode: ' . $card_code . '. Found ' . count( $result['BPAddresses'] ) . ' addresses.' );
                    return $result;
                } else {
                    SBIW_Logger::log( 'SAPB1 GetClientsAddress Error: Business partner not found or unexpected data structure. CardCode: ' . $card_code . '. Response: ' . substr( $response_body, 0, 200 ) );
                    return false;
                }
            } elseif ( $http_code === 401 ) { // Unauthorized - session might have expired
                SBIW_Logger::log( 'SAPB1 GetClientsAddress Error: Unauthorized (401). Session might have expired. Attempting re-login.' );
                $this->session_cookie = ''; // Clear potentially invalid cookie

                static $retry_count = 0;
                if ( $retry_count < 1 ) {
                    $retry_count++;
                    if ( $this->login() ) { // Attempt to re-login
                        SBIW_Logger::log( 'SAPB1 GetClientsAddress: Re-login successful. Retrying address fetch.' );
                        return $this->get_clients_address( $card_code ); // Retry fetching addresses
                    } else {
                        SBIW_Logger::log( 'SAPB1 GetClientsAddress Error: Re-login failed. Cannot get addresses.' );
                        return false;
                    }
                } else {
                    SBIW_Logger::log( 'SAPB1 GetClientsAddress Error: Re-login successful, but GetClientsAddress failed on retry. Preventing further retries.' );
                    return false;
                }
            } elseif ( $http_code === 404 ) { // Not Found - CardCode doesn't exist
                SBIW_Logger::log( 'SAPB1 GetClientsAddress Error: Business partner not found (404). CardCode: ' . $card_code );
                return false;
            } else {
                SBIW_Logger::log( "SAPB1 GetClientsAddress Error: HTTP Status - $http_code. CardCode: $card_code. Response: " . substr( $response_body, 0, 200 ) );
                return false;
            }
        }

        /**
         * Fetches detailed business partner properties.
         *
         * 
         * 
         * 
         *
         *
         * @return array|false Array of address details on success, false on failure.
         */
        public function get_businesspartner_properties( $card_code = '' ) {
            if ( empty( $this->session_cookie ) ) {
                if ( ! $this->login() ) {
                    SBIW_Logger::log( 'SAPB1 GetClientsAddress Error: Cannot get client addresses, login failed.' );
                    return false;
                }
            }

            // For BusinessPartnerPropertiesService, we get the general properties list
            // This endpoint doesn't require a specific CardCode
            SBIW_Logger::log( 'SAPB1 GetBusinessPartnerProperties: Fetching business partner properties list.' );

            // Use the correct endpoint for business partner properties
            $properties_url = $this->base_url . "BusinessPartnerPropertiesService_GetBusinessPartnerPropertyList";

            // Apply URL fix for cURL compatibility
            $fixed_properties_url = $this->fix_url( $properties_url );

            $ch = curl_init( $fixed_properties_url );
            curl_setopt( $ch, CURLOPT_RETURNTRANSFER, true );
            curl_setopt( $ch, CURLOPT_HTTPHEADER, [
                sprintf( 'Cookie: %s', $this->session_cookie ),
                'Content-Type: application/json',
                'Accept: application/json'
            ] );

            // For development/testing with self-signed certificates:
            curl_setopt( $ch, CURLOPT_SSL_VERIFYPEER, false );
            curl_setopt( $ch, CURLOPT_SSL_VERIFYHOST, false );

            // Enhanced URL handling options
            curl_setopt( $ch, CURLOPT_TIMEOUT, 30 );
            curl_setopt( $ch, CURLOPT_CONNECTTIMEOUT, 10 );
            curl_setopt( $ch, CURLOPT_FOLLOWLOCATION, true );
            curl_setopt( $ch, CURLOPT_MAXREDIRS, 3 );
            curl_setopt( $ch, CURLOPT_USERAGENT, 'SAPB1-WooCommerce-Integration/1.0' );
            curl_setopt( $ch, CURLOPT_ENCODING, '' ); // Accept any encoding

            $response_body = curl_exec( $ch );
            $http_code = curl_getinfo( $ch, CURLINFO_HTTP_CODE );

            if ( curl_errno( $ch ) ) {
                $curl_error = curl_error( $ch );
                SBIW_Logger::log( 'SAPB1 GetClientsAddress Error: cURL error - ' . $curl_error );
                curl_close( $ch );
                return false;
            }

            curl_close( $ch );

            if ( $http_code === 200 ) {
                $data = json_decode( $response_body, true );
                if ( json_last_error() !== JSON_ERROR_NONE ) {
                    SBIW_Logger::log( 'SAPB1 GetClientsAddress Error: JSON decode error - ' . json_last_error_msg() . '. Response: ' . substr( $response_body, 0, 200 ) );
                    return false;
                }

                // Check if we have business partner properties data
                if ( isset( $data['value'] ) && is_array( $data['value'] ) ) {
                    SBIW_Logger::log( 'SAPB1 GetBusinessPartnerProperties: Successfully fetched ' . count( $data['value'] ) . ' business partner properties.' );
                    return $data['value'];
                } else {
                    SBIW_Logger::log( 'SAPB1 GetBusinessPartnerProperties Error: Unexpected data structure or "value" field missing. Response: ' . substr( $response_body, 0, 200 ) );
                    return false;
                }
            } elseif ( $http_code === 401 ) { // Unauthorized - session might have expired
                SBIW_Logger::log( 'SAPB1 GetClientsAddress Error: Unauthorized (401). Session might have expired. Attempting re-login.' );
                $this->session_cookie = ''; // Clear potentially invalid cookie

                static $retry_count = 0;
                if ( $retry_count < 1 ) {
                    $retry_count++;
                    if ( $this->login() ) { // Attempt to re-login
                        SBIW_Logger::log( 'SAPB1 GetClientsAddress: Re-login successful. Retrying address fetch.' );
                        return $this->get_businesspartner_properties( $card_code ); // Retry fetching properties
                    } else {
                        SBIW_Logger::log( 'SAPB1 GetClientsAddress Error: Re-login failed. Cannot get addresses.' );
                        return false;
                    }
                } else {
                    SBIW_Logger::log( 'SAPB1 GetClientsAddress Error: Re-login successful, but GetClientsAddress failed on retry. Preventing further retries.' );
                    return false;
                }
            } elseif ( $http_code === 404 ) { // Not Found - CardCode doesn't exist
                SBIW_Logger::log( 'SAPB1 GetClientsAddress Error: Business partner not found (404)');
                return false;
            } else {
                SBIW_Logger::log( "SAPB1 GetClientsAddress Error: HTTP Status - $http_code. Response: " . substr( $response_body, 0, 200 ) );
                return false;
            }
        }

        /**
         * Creates a sales order in SAP B1.
         *
         * If not already logged in, it will attempt to log in first.
         * Retries login once if the session has expired (401 error).
         *
         * @param array $order_data Associative array representing the order.
         * @return mixed SAP B1 response on success (decoded JSON if possible, otherwise true), or false on failure.
         */
        public function create_sales_order( $order_data ) {
            if ( empty( $this->session_cookie ) ) {
                if ( ! $this->login() ) {
                    SBIW_Logger::log( 'SAPB1 CreateSalesOrder Error: Cannot create order, login failed.' );
                    return false;
                }
            }

            $order_url = $this->base_url . 'Orders';

            $ch = curl_init( $order_url );
            curl_setopt( $ch, CURLOPT_RETURNTRANSFER, true );
            curl_setopt( $ch, CURLOPT_POST, true );
            curl_setopt( $ch, CURLOPT_POSTFIELDS, json_encode( $order_data ) );
            curl_setopt( $ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                sprintf( 'Cookie: %s', $this->session_cookie ),
            ] );

            // For development/testing with self-signed certificates:
            // curl_setopt( $ch, CURLOPT_SSL_VERIFYPEER, false );
            // curl_setopt( $ch, CURLOPT_SSL_VERIFYHOST, false );

            $response_body = curl_exec( $ch );
            $http_code = curl_getinfo( $ch, CURLINFO_HTTP_CODE );

            if ( curl_errno( $ch ) ) {
                SBIW_Logger::log( 'SAPB1 CreateSalesOrder Error: cURL error - ' . curl_error( $ch ) );
                curl_close( $ch );
                return false;
            }

            curl_close( $ch );

            if ( $http_code === 201 ) { // Created
                SBIW_Logger::log( 'SAPB1 CreateSalesOrder: Successfully created order. HTTP Status: ' . $http_code );
                $decoded_response = json_decode( $response_body, true );
                if ( json_last_error() === JSON_ERROR_NONE ) {
                    return $decoded_response;
                }
                return true; // Return true if response is not JSON or empty
            } elseif ( $http_code === 401 ) { // Unauthorized - session might have expired
                SBIW_Logger::log( 'SAPB1 CreateSalesOrder Error: Unauthorized (401). Session might have expired. Attempting re-login.' );
                $this->session_cookie = ''; // Clear potentially invalid cookie


                static $retry_count = 0;
                if ( $retry_count < 1 ) {
                    $retry_count++;
                    if ( $this->login() ) { // Attempt to re-login
                        SBIW_Logger::log( 'SAPB1 CreateSalesOrder: Re-login successful. Retrying order creation.' );
                        return $this->create_sales_order( $order_data ); // Retry creating order
                    } else {
                        SBIW_Logger::log( 'SAPB1 CreateSalesOrder Error: Re-login failed. Cannot create order.' );
                        return false;
                    }
                } else {
                    SBIW_Logger::log( 'SAPB1 CreateSalesOrder Error: Re-login successful, but CreateSalesOrder failed on retry. Preventing further retries.' );
                    return false;
                }
            } else { // Other errors (400, 500, etc.)
                SBIW_Logger::log( "SAPB1 CreateSalesOrder Error: HTTP Status - $http_code. Response: " . substr( $response_body, 0, 500 ) ); // Log more of the response for errors
                return false;
            }
        }

        /**
         * Updates customer address in SAP B1.
         *
         * This method updates the address information for a specific business partner
         * in SAP B1 using the PATCH method on the BusinessPartners endpoint.
         *
         * @param string $cardcode SAP B1 CardCode of the business partner
         * @param array $address_data Address data in SAP B1 format
         * @return bool True on success, false on failure
         */
        public function update_customer_address( $cardcode, $address_data ) {
            if ( empty( $this->session_cookie ) ) {
                if ( ! $this->login() ) {
                    SBIW_Logger::log( 'SAPB1 UpdateCustomerAddress Error: Cannot update address, login failed.' );
                    return false;
                }
            }

            if ( empty( $cardcode ) ) {
                SBIW_Logger::log( 'SAPB1 UpdateCustomerAddress Error: CardCode parameter is required.' );
                return false;
            }

            // Sanitize CardCode for URL
            $sanitized_card_code = urlencode( trim( $cardcode ) );

            // Prepare the update data - we'll update the business partner with new address info
            $update_data = array(
                'BPAddresses' => array( $address_data )
            );

            $update_url = $this->base_url . "BusinessPartners('{$sanitized_card_code}')";

            $ch = curl_init( $update_url );
            curl_setopt( $ch, CURLOPT_RETURNTRANSFER, true );
            curl_setopt( $ch, CURLOPT_CUSTOMREQUEST, 'PATCH' );
            curl_setopt( $ch, CURLOPT_POSTFIELDS, json_encode( $update_data ) );
            curl_setopt( $ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                sprintf( 'Cookie: %s', $this->session_cookie ),
            ] );

            // For development/testing with self-signed certificates:
            curl_setopt( $ch, CURLOPT_SSL_VERIFYPEER, false );
            curl_setopt( $ch, CURLOPT_SSL_VERIFYHOST, false );

            // Enhanced URL handling options
            curl_setopt( $ch, CURLOPT_TIMEOUT, 30 );
            curl_setopt( $ch, CURLOPT_CONNECTTIMEOUT, 10 );

            $response_body = curl_exec( $ch );
            $http_code = curl_getinfo( $ch, CURLINFO_HTTP_CODE );

            if ( curl_errno( $ch ) ) {
                SBIW_Logger::log( 'SAPB1 UpdateCustomerAddress Error: cURL error - ' . curl_error( $ch ) );
                curl_close( $ch );
                return false;
            }

            curl_close( $ch );

            if ( $http_code === 204 || $http_code === 200 ) { // No Content or OK
                SBIW_Logger::log( 'SAPB1 UpdateCustomerAddress: Successfully updated address for CardCode: ' . $cardcode );
                return true;
            } elseif ( $http_code === 401 ) { // Unauthorized - session might have expired
                SBIW_Logger::log( 'SAPB1 UpdateCustomerAddress Error: Unauthorized (401). Session might have expired. Attempting re-login.' );
                $this->session_cookie = ''; // Clear potentially invalid cookie

                static $retry_count = 0;
                if ( $retry_count < 1 ) {
                    $retry_count++;
                    if ( $this->login() ) { // Attempt to re-login
                        SBIW_Logger::log( 'SAPB1 UpdateCustomerAddress: Re-login successful. Retrying address update.' );
                        return $this->update_customer_address( $cardcode, $address_data ); // Retry updating address
                    } else {
                        SBIW_Logger::log( 'SAPB1 UpdateCustomerAddress Error: Re-login failed. Cannot update address.' );
                        return false;
                    }
                } else {
                    SBIW_Logger::log( 'SAPB1 UpdateCustomerAddress Error: Re-login successful, but UpdateCustomerAddress failed on retry. Preventing further retries.' );
                    return false;
                }
            } elseif ( $http_code === 404 ) { // Not Found - CardCode doesn't exist
                SBIW_Logger::log( 'SAPB1 UpdateCustomerAddress Error: Business partner not found (404). CardCode: ' . $cardcode );
                return false;
            } else { // Other errors (400, 500, etc.)
                SBIW_Logger::log( "SAPB1 UpdateCustomerAddress Error: HTTP Status - $http_code. CardCode: $cardcode. Response: " . substr( $response_body, 0, 500 ) );
                return false;
            }
        }
    }
}
?>

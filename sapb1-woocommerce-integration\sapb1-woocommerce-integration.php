<?php
/**
 * Plugin Name: SAP B1 WooCommerce Integration
 * Description: Pulls products from SAP B1 server to WooCommerce. Synchronizes every 5 minutes.
 * Version: 1.0.0
 * Author: <PERSON> (AI Agent)
 * Text Domain: sapb1-woo-integration
 * License: GPLv2 or later
 * License URI: http://www.gnu.org/licenses/gpl-2.0.html
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

// Include the logger class and initialize it.
require_once plugin_dir_path( __FILE__ ) . 'includes/class-logger.php';
if ( class_exists( 'SBIW_Logger' ) ) {
    SBIW_Logger::init();
    SBIW_Logger::log( 'SAP B1 WooCommerce Integration Plugin: Logger initialized.' );
} else {
    error_log('SAP B1 WooCommerce Integration Plugin: SBIW_Logger class not found after include.');
}

// Define a custom cron interval
add_filter( 'cron_schedules', 'sbiw_add_custom_cron_interval' );
function sbiw_add_custom_cron_interval( $schedules ) {
    $schedules['every_five_minutes'] = array(
        'interval' => 300, // 5 minutes in seconds
        'display'  => esc_html__( 'Every Five Minutes', 'sapb1-woo-integration' ),
    );
    return $schedules;
}

// Define the function to be executed by the cron hook
add_action( 'sbiw_product_sync_cron', 'sbiw_run_product_sync' );
function sbiw_run_product_sync() {
    SBIW_Logger::log( 'SB1W Cron: sbiw_product_sync_cron action triggered.' );

    // Ensure the Product_Sync class file is included
    $product_sync_class_file = plugin_dir_path( __FILE__ ) . 'includes/class-product-sync.php';

    if ( ! file_exists( $product_sync_class_file ) ) {
        SBIW_Logger::log( 'SB1W Cron Error: class-product-sync.php not found at ' . $product_sync_class_file );
        return;
    }
    require_once $product_sync_class_file;

    // The class-sapb1-api-client.php is required by class-product-sync.php,
    // so it should be loaded as part of that require_once.

    if ( class_exists( 'Product_Sync' ) ) {
        SBIW_Logger::log( 'SB1W Cron: Product_Sync class exists. Initializing sync.' );
        $product_sync = new Product_Sync();
        $product_sync->sync_products();
        SBIW_Logger::log( 'SB1W Cron: Product_Sync->sync_products() executed.' );
    } else {
        SBIW_Logger::log( 'SB1W Cron Error: Product_Sync class does not exist after include.' );
    }
}

// Define the function to be executed by the client sync cron hook
add_action( 'sbiw_client_sync_cron', 'sbiw_run_client_sync' );
function sbiw_run_client_sync() {
    SBIW_Logger::log( 'SB1W Cron: sbiw_client_sync_cron action triggered.' );

    // Ensure the Client_Sync class file is included
    $client_sync_class_file = plugin_dir_path( __FILE__ ) . 'includes/class-client-sync.php';

    if ( ! file_exists( $client_sync_class_file ) ) {
        SBIW_Logger::log( 'SB1W Cron Error: class-client-sync.php not found at ' . $client_sync_class_file );
        return;
    }
    require_once $client_sync_class_file;

    // The class-sapb1-api-client.php is required by class-client-sync.php,
    // so it should be loaded as part of that require_once.

    if ( class_exists( 'Client_Sync' ) ) {
        SBIW_Logger::log( 'SB1W Cron: Client_Sync class exists. Initializing sync.' );
        $client_sync = new Client_Sync();
        $client_sync->sync_clients(); // This is a placeholder method for now
        SBIW_Logger::log( 'SB1W Cron: Client_Sync->sync_clients() executed.' );
    } else {
        SBIW_Logger::log( 'SB1W Cron Error: Client_Sync class does not exist after include.' );
    }
}

// Function to schedule the cron events
function sbiw_schedule_sync_cron() {
    // Schedule Product Sync
    if ( ! wp_next_scheduled( 'sbiw_product_sync_cron' ) ) {
        wp_schedule_event( time(), 'every_five_minutes', 'sbiw_product_sync_cron' );
        SBIW_Logger::log( 'SAP B1 Sync Cron: Event scheduled for sbiw_product_sync_cron.' );
    } else {
        SBIW_Logger::log( 'SAP B1 Sync Cron: Event already scheduled for sbiw_product_sync_cron.' );
    }

    // Schedule Client Sync
    if ( ! wp_next_scheduled( 'sbiw_client_sync_cron' ) ) {
        wp_schedule_event( time(), 'every_five_minutes', 'sbiw_client_sync_cron' );
        SBIW_Logger::log( 'SAP B1 Sync Cron: Event scheduled for sbiw_client_sync_cron.' );
    } else {
        SBIW_Logger::log( 'SAP B1 Sync Cron: Event already scheduled for sbiw_client_sync_cron.' );
    }

    // Schedule Reverse Sync (WooCommerce → SAP B1)
    if ( ! wp_next_scheduled( 'sbiw_reverse_sync_cron' ) ) {
        wp_schedule_event( time(), 'every_five_minutes', 'sbiw_reverse_sync_cron' );
        SBIW_Logger::log( 'SAP B1 Sync Cron: Event scheduled for sbiw_reverse_sync_cron.' );
    } else {
        SBIW_Logger::log( 'SAP B1 Sync Cron: Event already scheduled for sbiw_reverse_sync_cron.' );
    }
}

// Function to unschedule the cron events
function sbiw_unschedule_sync_cron() {
    // Unschedule Product Sync
    wp_clear_scheduled_hook( 'sbiw_product_sync_cron' );
    SBIW_Logger::log( 'SAP B1 Sync Cron: Event unscheduled for sbiw_product_sync_cron.' );

    // Unschedule Client Sync
    wp_clear_scheduled_hook( 'sbiw_client_sync_cron' );
    SBIW_Logger::log( 'SAP B1 Sync Cron: Event unscheduled for sbiw_client_sync_cron.' );

    // Unschedule Reverse Sync
    wp_clear_scheduled_hook( 'sbiw_reverse_sync_cron' );
    SBIW_Logger::log( 'SAP B1 Sync Cron: Event unscheduled for sbiw_reverse_sync_cron.' );
}

// Register activation and deactivation hooks
register_activation_hook( __FILE__, 'sbiw_schedule_sync_cron' );
register_deactivation_hook( __FILE__, 'sbiw_unschedule_sync_cron' );

// Initialize reverse sync hooks
add_action( 'init', 'sbiw_init_reverse_sync_hooks' );

// Include the Order_Sync class
require_once plugin_dir_path( __FILE__ ) . 'includes/class-order-sync.php';

/**
 * Pushes an order to SAP B1 when order status changes to processing.
 *
 * @param int      $order_id The ID of the order.
 * @param WC_Order $order    The WooCommerce order object (optional, will be retrieved if not provided).
 */
function sbiw_push_order_to_sapb1( $order_id, $order = null ) {
    if ( ! $order_id ) {
        return;
    }

    // Optional: Check if plugin is configured to sync orders (if a setting exists)
    // For now, assume it's always active if the plugin is active.

    SBIW_Logger::log( 'WooCommerce Hook: Order status changed to processing for Order ID: ' . $order_id );

    // Ensure the Order_Sync class is available
    // Note: The require_once for class-order-sync.php is already at the top level of this file.
    // We still check class_exists in case the file is present but the class definition is missing or incorrect.
    if ( ! class_exists( 'Order_Sync' ) ) {
        SBIW_Logger::log( 'Order Sync Error: Order_Sync class not found. Cannot push order ID: ' . $order_id );
        if ( $order ) {
            $order->add_order_note( 'Critical Error: Order_Sync class not found. Order NOT sent to SAP B1.', true );
        }
        return;
    }

    // Retrieve the order object if not provided
    if ( ! $order ) {
        $order = wc_get_order( $order_id );
        if ( ! $order ) {
            SBIW_Logger::log( 'Order Sync Error: Could not retrieve WC_Order object for order ID: ' . $order_id );
            return;
        }
    }

    // Verify the order status is actually 'processing'
    if ( $order->get_status() !== 'processing' ) {
        SBIW_Logger::log( 'Order Sync: Skipping order ID: ' . $order_id . ' - Status is "' . $order->get_status() . '", not "processing"' );
        return;
    }

    // Check if order has already been synced to prevent duplicates
    $already_synced = $order->get_meta( '_sapb1_synced', true );
    if ( $already_synced ) {
        SBIW_Logger::log( 'Order Sync: Order ID: ' . $order_id . ' has already been synced to SAP B1. Skipping.' );
        return;
    }

    $order_sync = new Order_Sync();
    $success = $order_sync->push_order_to_sapb1( $order );

    if ( $success ) {
        // Mark order as synced to prevent duplicate attempts
        $order->update_meta_data( '_sapb1_synced', true );
        $order->update_meta_data( '_sapb1_sync_date', current_time( 'mysql' ) );
        $order->save_meta_data();

        $order->add_order_note( __( 'Order successfully sent to SAP B1 when status changed to processing.', 'sapb1-woo-integration' ) );
        SBIW_Logger::log( 'Order Sync: Successfully processed and sent order ID: ' . $order_id . ' to SAP B1 when status changed to processing.' );
    } else {
        // Mark sync attempt to prevent repeated failures
        $order->update_meta_data( '_sapb1_sync_failed', true );
        $order->update_meta_data( '_sapb1_sync_failed_date', current_time( 'mysql' ) );
        $order->save_meta_data();

        // The true parameter makes the note visible to the customer.
        $order->add_order_note( __( 'Failed to send order to SAP B1 when status changed to processing. Please contact support if this issue persists. Check SAP B1 integration logs for details.', 'sapb1-woo-integration' ), true );
        SBIW_Logger::log( 'Order Sync Error: Failed to send order ID: ' . $order_id . ' to SAP B1 when status changed to processing.' );
    }
}
/**
 * Hook into order status change to 'processing'
 *
 * This ensures orders are only synced to SAP B1 after payment is confirmed
 * and the order status changes to 'processing'. This prevents syncing orders
 * that may fail payment or remain in pending status.
 *
 * Previous hook: woocommerce_checkout_order_processed (fired before payment)
 * Current hook: woocommerce_order_status_processing (fires after payment confirmation)
 */
add_action( 'woocommerce_order_status_processing', 'sbiw_push_order_to_sapb1', 10, 1 );

/**
 * Initialize reverse sync hooks for WooCommerce address changes
 */
function sbiw_init_reverse_sync_hooks() {
    // Hook into WooCommerce address save events
    add_action( 'woocommerce_customer_save_address', 'sbiw_on_address_save', 10, 2 );

    // Hook into user profile updates (for admin-side changes)
    add_action( 'profile_update', 'sbiw_on_profile_update', 10, 1 );

    // Hook into user meta updates (for programmatic changes)
    add_action( 'updated_user_meta', 'sbiw_on_user_meta_update', 10, 4 );
}

/**
 * Handle WooCommerce address save events
 *
 * @param int $user_id User ID
 * @param string $address_type Address type ('billing' or 'shipping')
 */
function sbiw_on_address_save( $user_id, $address_type ) {
    // Only process billing and shipping addresses
    if ( ! in_array( $address_type, array( 'billing', 'shipping' ) ) ) {
        return;
    }

    // Get the updated address data
    $address_data = array();
    $address_fields = array( 'first_name', 'last_name', 'company', 'address_1', 'address_2', 'city', 'state', 'postcode', 'country', 'email', 'phone' );

    foreach ( $address_fields as $field ) {
        $meta_key = $address_type . '_' . $field;
        $address_data[ $field ] = get_user_meta( $user_id, $meta_key, true );
    }

    // Queue for reverse sync
    if ( class_exists( 'Client_Sync' ) ) {
        $client_sync = new Client_Sync();
        $client_sync->queue_address_change_for_reverse_sync( $user_id, $address_type, $address_data );
    }
}

/**
 * Handle user profile updates
 *
 * @param int $user_id User ID
 */
function sbiw_on_profile_update( $user_id ) {
    // Check if this user has SAP B1 CardCode (only sync SAP-sourced users)
    $cardcode = get_user_meta( $user_id, '_sapb1_cardcode', true );
    if ( empty( $cardcode ) ) {
        return;
    }

    // Get user data for name/phone changes
    $user_data = get_userdata( $user_id );
    if ( ! $user_data ) {
        return;
    }

    // Queue name changes for both billing and shipping
    $name_data = array(
        'first_name' => $user_data->first_name,
        'last_name' => $user_data->last_name
    );

    if ( class_exists( 'Client_Sync' ) ) {
        $client_sync = new Client_Sync();
        $client_sync->queue_address_change_for_reverse_sync( $user_id, 'billing', $name_data );
        $client_sync->queue_address_change_for_reverse_sync( $user_id, 'shipping', $name_data );
    }
}

/**
 * Handle user meta updates for address fields
 *
 * @param int $meta_id Meta ID
 * @param int $user_id User ID
 * @param string $meta_key Meta key
 * @param mixed $meta_value Meta value
 */
function sbiw_on_user_meta_update( $meta_id, $user_id, $meta_key, $meta_value ) {
    // Only process address-related meta keys
    if ( ! preg_match( '/^(billing|shipping)_/', $meta_key ) ) {
        return;
    }

    // Extract address type from meta key
    $address_type = strpos( $meta_key, 'billing_' ) === 0 ? 'billing' : 'shipping';

    // Check if this user has SAP B1 CardCode
    $cardcode = get_user_meta( $user_id, '_sapb1_cardcode', true );
    if ( empty( $cardcode ) ) {
        return;
    }

    // Get full address data
    $address_data = array();
    $address_fields = array( 'first_name', 'last_name', 'company', 'address_1', 'address_2', 'city', 'state', 'postcode', 'country', 'email', 'phone' );

    foreach ( $address_fields as $field ) {
        $field_meta_key = $address_type . '_' . $field;
        $address_data[ $field ] = get_user_meta( $user_id, $field_meta_key, true );
    }

    // Queue for reverse sync
    if ( class_exists( 'Client_Sync' ) ) {
        $client_sync = new Client_Sync();
        $client_sync->queue_address_change_for_reverse_sync( $user_id, $address_type, $address_data );
    }
}

/**
 * Process reverse sync queue (called by cron)
 */
function sbiw_process_reverse_sync_cron() {
    SBIW_Logger::log( 'SB1W Reverse Sync Cron: Starting reverse sync queue processing.' );

    // Include the Client_Sync class
    $client_sync_class_file = plugin_dir_path( __FILE__ ) . 'includes/class-client-sync.php';
    if ( ! file_exists( $client_sync_class_file ) ) {
        SBIW_Logger::log( 'SB1W Reverse Sync Cron Error: class-client-sync.php file not found.' );
        return;
    }

    require_once $client_sync_class_file;

    if ( class_exists( 'Client_Sync' ) ) {
        SBIW_Logger::log( 'SB1W Reverse Sync Cron: Client_Sync class exists. Processing reverse sync queue.' );
        $client_sync = new Client_Sync();
        $client_sync->process_reverse_sync_queue();
        SBIW_Logger::log( 'SB1W Reverse Sync Cron: Reverse sync queue processing completed.' );
    } else {
        SBIW_Logger::log( 'SB1W Reverse Sync Cron Error: Client_Sync class does not exist after include.' );
    }
}

// Hook the reverse sync cron function
add_action( 'sbiw_reverse_sync_cron', 'sbiw_process_reverse_sync_cron' );

?>

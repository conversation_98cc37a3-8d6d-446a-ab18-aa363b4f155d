<?php
/**
 * PHPUnit bootstrap file.
 */

// Define ABSPATH if not already defined
if ( ! defined( 'ABSPATH' ) ) {
    define( 'ABSPATH', dirname( __DIR__ ) . '/' ); // Points to the plugin root directory
}

// Define WP_DEBUG for logging inside Client_Sync (optional, based on its usage)
if ( ! defined( 'WP_DEBUG' ) ) {
    define( 'WP_DEBUG', true );
}
if ( ! defined( 'WP_PLUGIN_DIR' ) ) {
    define( 'WP_PLUGIN_DIR', dirname( ABSPATH ) );
}


// Mock SBIW_Logger before any plugin files are included, if necessary
// However, we will use PHPUnit's mocking for static methods if possible,
// or we can define a simple version of it here.
if ( ! class_exists( 'SBIW_Logger' ) ) {
    class SBIW_Logger {
        public static $logs = [];
        public static function init() { self::$logs = []; /* Initialize or re-initialize */ }
        public static function log( $message ) {
            self::$logs[] = $message;
            // echo "LOG: $message\n"; // For debugging tests
        }
        public static function get_logs() { return self::$logs; }
        public static function clear_logs() { self::$logs = []; }
        public static function log_inventory_transaction($sku, $old_quantity, $new_quantity, $product_name = '') {
            $product_info = !empty($product_name) ? "Product: {$product_name}, " : '';
            $message = "INVENTORY_TRANSACTION - SKU: {$sku}, {$product_info}Old Qty: {$old_quantity}, New Qty: {$new_quantity}";
            self::log($message);
        }
    }
    // Initialize logger for tests
    SBIW_Logger::init();
}

// --- Mock WordPress & WooCommerce Functions & Classes ---

// Mock WordPress core functions (must be defined before including classes)
if (!function_exists('plugin_dir_path')) {
    function plugin_dir_path($file) {
        return dirname($file) . '/';
    }
}

if (!function_exists('wp_upload_dir')) {
    function wp_upload_dir() {
        return [
            'basedir' => ABSPATH . 'wp-content/uploads',
            'baseurl' => 'http://example.com/wp-content/uploads'
        ];
    }
}

if (!function_exists('wp_mkdir_p')) {
    function wp_mkdir_p($target) {
        if (!is_dir($target)) {
            return mkdir($target, 0755, true);
        }
        return true;
    }
}

// Include actual classes
require_once ABSPATH . 'includes/class-sapb1-api-client.php';
require_once ABSPATH . 'includes/class-client-sync.php';
require_once ABSPATH . 'includes/class-product-sync.php';
require_once ABSPATH . 'includes/class-order-sync.php';

// WP_Error class mock
if ( ! class_exists( 'WP_Error' ) ) {
    class WP_Error {
        private $errors = [];
        private $error_data = [];

        public function __construct( $code = '', $message = '', $data = '' ) {
            if ( ! empty( $code ) ) {
                $this->add( $code, $message, $data );
            }
        }

        public function add( $code, $message, $data = '' ) {
            $this->errors[$code][] = $message;
            if ( ! empty( $data ) ) {
                $this->error_data[$code] = $data;
            }
        }

        public function get_error_codes() {
            return array_keys( $this->errors );
        }

        public function get_error_message( $code = '' ) {
            if ( empty( $code ) ) {
                $code = $this->get_error_codes()[0] ?? '';
            }
            return isset( $this->errors[$code][0] ) ? $this->errors[$code][0] : '';
        }
        public function get_error_messages() {
            $messages = [];
            foreach($this->errors as $code_errors) {
                $messages = array_merge($messages, $code_errors);
            }
            return $messages;
        }
    }
}

// Mock WordPress functions
// These are highly simplified and will need to be controlled by test expectations or static properties.
if ( ! function_exists( 'is_wp_error' ) ) {
    function is_wp_error( $thing ) {
        return $thing instanceof WP_Error;
    }
}

if ( ! function_exists( 'email_exists' ) ) {
    global $mock_email_exists_return;
    $mock_email_exists_return = false; // Default: email does not exist
    function email_exists( $email ) {
        global $mock_email_exists_return;
        // Allow specific emails to be configured for existence
        if (is_array($mock_email_exists_return) && isset($mock_email_exists_return[$email])) {
            return $mock_email_exists_return[$email];
        }
        return $mock_email_exists_return;
    }
}

if ( ! function_exists( 'username_exists' ) ) {
    global $mock_username_exists_return;
    $mock_username_exists_return = false;
    function username_exists( $username ) {
        global $mock_username_exists_return;
         if (is_array($mock_username_exists_return) && isset($mock_username_exists_return[$username])) {
            return $mock_username_exists_return[$username];
        }
        return $mock_username_exists_return;
    }
}

if ( ! function_exists( 'wp_create_user' ) ) {
    global $mock_wp_create_user_return; // Can be user ID or WP_Error
    $mock_wp_create_user_return = 1; // Default: success, returns user ID 1
    function wp_create_user( $username, $password, $email ) {
        global $mock_wp_create_user_return;
        if (isset($GLOBALS['wp_create_user_callback'])) { // More advanced control
            return call_user_func($GLOBALS['wp_create_user_callback'], $username, $password, $email);
        }
        return $mock_wp_create_user_return;
    }
}

if ( ! function_exists( 'update_user_meta' ) ) {
    global $mock_user_meta;
    $mock_user_meta = [];
    function update_user_meta( $user_id, $meta_key, $meta_value, $prev_value = '' ) {
        global $mock_user_meta;
        if (!isset($mock_user_meta[$user_id])) {
            $mock_user_meta[$user_id] = [];
        }
        $mock_user_meta[$user_id][$meta_key] = $meta_value;
        return true;
    }
}

if ( ! function_exists( 'get_user_meta' ) ) { // For verification
    global $mock_user_meta;
    function get_user_meta( $user_id, $meta_key = '', $single = false ) {
        global $mock_user_meta;
        if ( ! isset( $mock_user_meta[$user_id] ) ) {
            return $single ? '' : [];
        }
        if ( empty( $meta_key ) ) {
            return $mock_user_meta[$user_id];
        }
        if ( isset( $mock_user_meta[$user_id][$meta_key] ) ) {
            return $single ? $mock_user_meta[$user_id][$meta_key] : [$mock_user_meta[$user_id][$meta_key]];
        }
        return $single ? '' : [];
    }
}


if ( ! function_exists( 'get_users' ) ) {
    global $mock_get_users_return;
    $mock_get_users_return = []; // Default: no users found
    function get_users( $args ) {
        global $mock_get_users_return;
        // Simple mock: return pre-set value. Could be enhanced to check $args.
        if (isset($GLOBALS['get_users_callback'])) {
             return call_user_func($GLOBALS['get_users_callback'], $args);
        }
        return $mock_get_users_return;
    }
}

if ( ! function_exists( 'sanitize_email' ) ) {
    function sanitize_email( $email ) { return trim( $email ); } // Simple stub
}
if ( ! function_exists( 'is_email' ) ) {
    function is_email( $email ) { return filter_var( $email, FILTER_VALIDATE_EMAIL ) !== false; }
}
if ( ! function_exists( 'get_woocommerce_currency' ) ) {
    function get_woocommerce_currency() { return 'USD'; } // Default currency for testing
}
if ( ! function_exists( 'sanitize_user' ) ) {
    function sanitize_user( $username, $strict = false ) { return preg_replace( '/[^a-zA-Z0-9_.\-@]/', '', $username ); } // Simple stub
}
if ( ! function_exists( 'wp_generate_password' ) ) {
    function wp_generate_password( $length = 12, $special_chars = true, $extra_special_chars = false ) { return 'testpassword123'; } // Simple stub
}
if ( ! function_exists( 'sanitize_text_field' ) ) {
    function sanitize_text_field( $str ) { return trim( (string) $str ); } // Simple stub
}
if ( ! function_exists( 'wp_json_encode' ) ) {
    function wp_json_encode( $data, $options = 0, $depth = 512 ) { return json_encode( $data, $options, $depth ); } // Use actual json_encode
}
if ( ! function_exists( 'plugin_dir_path' ) ) {
    function plugin_dir_path( $file ) {
        return ABSPATH . 'includes/'; // Simplified for this plugin structure
    }
}


// Mock WooCommerce WC_Customer
if ( ! class_exists( 'WC_Customer' ) ) {
    class WC_Customer {
        public $id;
        private $data = [];
        public $is_valid = true; // Control for testing save failures
        public static $save_should_throw_exception = false;
        public static $save_exception_message = 'WC_Customer save failed.';
        public static $constructor_should_throw_exception = false;
        public static $constructor_exception_message = 'WC_Customer constructor failed.';


        public function __construct( $user_id = 0 ) {
            if (self::$constructor_should_throw_exception) {
                throw new Exception(self::$constructor_exception_message);
            }
            $this->id = $user_id;
            $this->data = [
                'id' => $user_id,
                'email' => '',
                'first_name' => '',
                'last_name' => '',
                'billing' => [],
                'shipping' => [],
                'role' => 'customer',
            ];
            // Pre-populate with some mock meta if user_id exists in our mock meta store
            global $mock_user_meta;
            if ($user_id && isset($mock_user_meta[$user_id])) {
                $this->data['email'] = $mock_user_meta[$user_id]['billing_email'] ?? "user{$user_id}@example.com";
                $this->data['first_name'] = $mock_user_meta[$user_id]['first_name'] ?? '';
                $this->data['last_name'] = $mock_user_meta[$user_id]['last_name'] ?? '';
            }
        }
        public function get_id() { return $this->id; }
        public function get_email() { return $this->data['email'] ?? ''; }
        public function set_email( $email ) { $this->data['email'] = sanitize_email( $email ); }
        public function get_first_name() { return $this->data['first_name'] ?? ''; }
        public function set_first_name( $name ) { $this->data['first_name'] = sanitize_text_field( $name ); }
        public function get_last_name() { return $this->data['last_name'] ?? ''; }
        public function set_last_name( $name ) { $this->data['last_name'] = sanitize_text_field( $name ); }

        public function set_props( $props ) {
            foreach ( $props as $key => $value ) {
                if ($key === 'billing' || $key === 'shipping') {
                    $this->data[$key] = is_array($value) ? array_map('sanitize_text_field', $value) : sanitize_text_field($value);
                } else {
                    $this->data[$key] = sanitize_text_field($value);
                }
            }
        }
        public function __call($name, $arguments) { // Generic getter/setter for billing_*, shipping_*
            $type = str_starts_with($name, 'get_') ? 'get' : (str_starts_with($name, 'set_') ? 'set' : null);
            if (!$type) return null;

            $key_parts = explode('_', substr($name, 4)); // remove get_/set_
            $address_type = $key_parts[0]; // billing or shipping
            $field = implode('_', array_slice($key_parts, 1));

            if ($address_type !== 'billing' && $address_type !== 'shipping') return null; // Not an address field

            if ($type === 'get') {
                return $this->data[$address_type][$field] ?? '';
            } else { // set
                $this->data[$address_type][$field] = $arguments[0];
            }
        }

        public function save() {
            if (self::$save_should_throw_exception) {
                throw new Exception(self::$save_exception_message);
            }
            if (!$this->is_valid) return new WP_Error('customer_save_error', 'Mocked save error');
            // Simulate saving by updating mock user meta
            global $mock_user_meta;
            if (!isset($mock_user_meta[$this->id])) $mock_user_meta[$this->id] = [];
            $mock_user_meta[$this->id]['first_name'] = $this->data['first_name'];
            $mock_user_meta[$this->id]['last_name'] = $this->data['last_name'];
            $mock_user_meta[$this->id]['billing_email'] = $this->data['email']; // WC stores email in billing usually
            // Persist billing/shipping arrays
            foreach(['billing', 'shipping'] as $type) {
                if (!empty($this->data[$type])) {
                    foreach($this->data[$type] as $key => $value) {
                        $mock_user_meta[$this->id]["{$type}_{$key}"] = $value;
                    }
                }
            }
            return $this->id;
        }
    }
}

// Mock WooCommerce Product functions and classes for Product_Sync tests
global $mock_products, $mock_product_id_counter;
$mock_products = [];
$mock_product_id_counter = 1000;

if (!function_exists('wc_get_product_id_by_sku')) {
    function wc_get_product_id_by_sku($sku) {
        global $mock_products;
        foreach ($mock_products as $product_id => $product_data) {
            if ($product_data['sku'] === $sku) {
                return $product_id;
            }
        }
        return 0; // Product not found
    }
}

if (!function_exists('wc_get_product')) {
    function wc_get_product($product_id) {
        global $mock_products;
        if (isset($mock_products[$product_id])) {
            return new WC_Product_Simple($product_id);
        }
        return false;
    }
}

// Mock WC_Product_Simple class
if (!class_exists('WC_Product_Simple')) {
    class WC_Product_Simple {
        private $id;
        private $data = [];
        public static $save_should_throw_exception = false;
        public static $save_exception_message = 'Product save failed.';

        public function __construct($product_id = 0) {
            global $mock_products;
            $this->id = $product_id;

            if ($product_id && isset($mock_products[$product_id])) {
                $this->data = $mock_products[$product_id];
            } else {
                $this->data = [
                    'sku' => '',
                    'name' => '',
                    'regular_price' => '',
                    'manage_stock' => false,
                    'stock_quantity' => null,
                    'status' => 'draft'
                ];
            }
        }

        public function get_id() { return $this->id; }
        public function set_sku($sku) { $this->data['sku'] = $sku; }
        public function get_sku() { return $this->data['sku']; }
        public function set_name($name) { $this->data['name'] = $name; }
        public function get_name() { return $this->data['name']; }
        public function set_regular_price($price) { $this->data['regular_price'] = $price; }
        public function get_regular_price() { return $this->data['regular_price']; }
        public function set_manage_stock($manage) { $this->data['manage_stock'] = $manage; }
        public function get_manage_stock() { return $this->data['manage_stock']; }
        public function set_stock_quantity($quantity) { $this->data['stock_quantity'] = $quantity; }
        public function get_stock_quantity() { return $this->data['stock_quantity']; }
        public function set_status($status) { $this->data['status'] = $status; }
        public function get_status() { return $this->data['status']; }

        public function save() {
            if (self::$save_should_throw_exception) {
                throw new WC_Data_Exception('product_save_error', self::$save_exception_message);
            }

            global $mock_products, $mock_product_id_counter;

            if (!$this->id) {
                $this->id = ++$mock_product_id_counter;
            }

            $mock_products[$this->id] = $this->data;
            return $this->id;
        }
    }
}

// Mock WC_Data_Exception
if (!class_exists('WC_Data_Exception')) {
    class WC_Data_Exception extends Exception {
        private $error_code;

        public function __construct($error_code, $message, $http_status_code = 400) {
            $this->error_code = $error_code;
            parent::__construct($message, $http_status_code);
        }

        public function getErrorCode() {
            return $this->error_code;
        }
    }
}

// Mock WooCommerce Order functions and classes for Order_Sync tests
global $mock_orders, $mock_order_id_counter, $mock_user_meta;
$mock_orders = [];
$mock_order_id_counter = 2000;

if (!function_exists('wc_get_order')) {
    function wc_get_order($order_id) {
        global $mock_orders;
        if (isset($mock_orders[$order_id])) {
            return new WC_Order($order_id);
        }
        return false;
    }
}

if (!function_exists('get_user_meta')) {
    function get_user_meta($user_id, $key, $single = false) {
        global $mock_user_meta;
        if (isset($mock_user_meta[$user_id][$key])) {
            return $single ? $mock_user_meta[$user_id][$key] : [$mock_user_meta[$user_id][$key]];
        }
        return $single ? '' : [];
    }
}

if (!function_exists('wp_json_encode')) {
    function wp_json_encode($data) {
        return json_encode($data);
    }
}

if (!function_exists('add_filter')) {
    function add_filter($hook, $callback, $priority = 10, $accepted_args = 1) {
        // Mock implementation - just return true
        return true;
    }
}

if (!function_exists('add_action')) {
    function add_action($hook, $callback, $priority = 10, $accepted_args = 1) {
        // Mock implementation - just return true
        return true;
    }
}

if (!function_exists('wp_next_scheduled')) {
    function wp_next_scheduled($hook, $args = []) {
        // Mock implementation - return false (not scheduled)
        return false;
    }
}

if (!function_exists('wp_schedule_event')) {
    function wp_schedule_event($timestamp, $recurrence, $hook, $args = []) {
        // Mock implementation - return true
        return true;
    }
}

if (!function_exists('wp_clear_scheduled_hook')) {
    function wp_clear_scheduled_hook($hook, $args = []) {
        // Mock implementation - return true
        return true;
    }
}

if (!function_exists('register_activation_hook')) {
    function register_activation_hook($file, $callback) {
        // Mock implementation - just return true
        return true;
    }
}

if (!function_exists('register_deactivation_hook')) {
    function register_deactivation_hook($file, $callback) {
        // Mock implementation - just return true
        return true;
    }
}

if (!function_exists('__')) {
    function __($text, $domain = 'default') {
        return $text;
    }
}

// Mock WC_Order class
if (!class_exists('WC_Order')) {
    class WC_Order {
        private $id;
        private $data = [];
        private $items = [];
        private $notes = [];

        public function __construct($order_id = 0) {
            global $mock_orders;
            $this->id = $order_id;

            if ($order_id && isset($mock_orders[$order_id])) {
                $this->data = $mock_orders[$order_id];
                $this->items = $this->data['items'] ?? [];
            } else {
                $this->data = [
                    'customer_id' => 0,
                    'billing_email' => '',
                    'billing_first_name' => '',
                    'billing_last_name' => '',
                    'billing_company' => '',
                    'billing_phone' => '',
                    'billing_address_1' => '',
                    'billing_address_2' => '',
                    'billing_city' => '',
                    'billing_postcode' => '',
                    'billing_state' => '',
                    'billing_country' => '',
                    'shipping_address_1' => '',
                    'shipping_address_2' => '',
                    'shipping_city' => '',
                    'shipping_postcode' => '',
                    'shipping_state' => '',
                    'shipping_country' => '',
                    'customer_note' => '',
                    'date_created' => new DateTime(),
                    'status' => 'pending',
                    'meta' => [],
                    'items' => []
                ];
            }
        }

        public function get_id() { return $this->id; }
        public function get_customer_id() { return $this->data['customer_id']; }
        public function get_billing_email() { return $this->data['billing_email']; }
        public function get_billing_first_name() { return $this->data['billing_first_name'] ?? ''; }
        public function get_billing_last_name() { return $this->data['billing_last_name'] ?? ''; }
        public function get_billing_company() { return $this->data['billing_company'] ?? ''; }
        public function get_billing_phone() { return $this->data['billing_phone'] ?? ''; }
        public function get_billing_address_1() { return $this->data['billing_address_1']; }
        public function get_billing_address_2() { return $this->data['billing_address_2']; }
        public function get_billing_city() { return $this->data['billing_city']; }
        public function get_billing_postcode() { return $this->data['billing_postcode']; }
        public function get_billing_state() { return $this->data['billing_state']; }
        public function get_billing_country() { return $this->data['billing_country']; }
        public function get_shipping_address_1() { return $this->data['shipping_address_1']; }
        public function get_shipping_address_2() { return $this->data['shipping_address_2']; }
        public function get_shipping_city() { return $this->data['shipping_city']; }
        public function get_shipping_postcode() { return $this->data['shipping_postcode']; }
        public function get_shipping_state() { return $this->data['shipping_state']; }
        public function get_shipping_country() { return $this->data['shipping_country']; }
        public function get_customer_note() { return $this->data['customer_note']; }
        public function get_date_created() { return $this->data['date_created']; }

        public function get_items() {
            $items = [];
            foreach ($this->items as $item_id => $item_data) {
                $items[$item_id] = new WC_Order_Item_Product($item_id, $item_data);
            }
            return $items;
        }

        public function get_item_subtotal($item, $inc_tax = false, $round = true) {
            return $item->get_subtotal() / $item->get_quantity();
        }

        public function add_order_note($note, $is_customer_note = false) {
            $this->notes[] = [
                'note' => $note,
                'customer_note' => $is_customer_note,
                'date' => date('Y-m-d H:i:s')
            ];
        }

        public function get_order_notes() {
            return $this->notes;
        }

        public function get_status() {
            return $this->data['status'] ?? 'pending';
        }

        public function update_meta_data($key, $value) {
            // Mock implementation for order meta
            if (!isset($this->data['meta'])) {
                $this->data['meta'] = [];
            }
            $this->data['meta'][$key] = $value;
        }

        public function save_meta_data() {
            // Mock implementation - just return true
            return true;
        }

        public function get_meta($key, $single = true) {
            $meta_value = $this->data['meta'][$key] ?? '';
            return $single ? $meta_value : [$meta_value];
        }
    }
}

// Mock WC_Order_Item_Product class
if (!class_exists('WC_Order_Item_Product')) {
    class WC_Order_Item_Product {
        private $id;
        private $data;

        public function __construct($item_id, $data = []) {
            $this->id = $item_id;
            $this->data = array_merge([
                'product_id' => 0,
                'quantity' => 1,
                'subtotal' => 0,
                'sku' => ''
            ], $data);
        }

        public function get_id() { return $this->id; }
        public function get_product_id() { return $this->data['product_id']; }
        public function get_quantity() { return $this->data['quantity']; }
        public function get_subtotal() { return $this->data['subtotal']; }

        public function get_product() {
            if ($this->data['product_id']) {
                return new WC_Product_Simple($this->data['product_id']);
            }
            return false;
        }
    }
}

// Include PHPUnit TestCase
// If wordpress-develop/tests/phpunit/includes/factory.php and WP_UnitTestCase are not available
// we'll need to use a basic PHPUnit_Framework_TestCase or PHPUnit\Framework\TestCase
if (class_exists('PHPUnit\Framework\TestCase')) {
    require_once __DIR__ . '/includes/class-base-test-case.php';
} else {
    // Fallback for older PHPUnit versions if necessary, though current contest env should support PHPUnit\Framework\TestCase
    // Or, if WP_UnitTestCase is part of the expected environment for such tests:
    // @include_once getenv('WP_TESTS_DIR') . '/includes/abstract-wc-unit-test-case.php';
}

// Clean up global mocks before each test run (can also be done in a base test class setUp)
function reset_all_mocks() {
    global $mock_email_exists_return, $mock_username_exists_return, $mock_wp_create_user_return, $mock_user_meta, $mock_get_users_return;
    global $mock_products, $mock_product_id_counter;
    global $mock_orders, $mock_order_id_counter;

    SBIW_Logger::clear_logs();
    $mock_email_exists_return = false;
    $mock_username_exists_return = false;
    $mock_wp_create_user_return = 1; // Default success
    $mock_user_meta = [];
    $mock_get_users_return = [];
    $mock_products = [];
    $mock_product_id_counter = 1000;
    $mock_orders = [];
    $mock_order_id_counter = 2000;

    WC_Customer::$save_should_throw_exception = false;
    WC_Customer::$constructor_should_throw_exception = false;
    WC_Product_Simple::$save_should_throw_exception = false;

    unset($GLOBALS['wp_create_user_callback']);
    unset($GLOBALS['get_users_callback']);

}
reset_all_mocks(); // Initial reset

?>

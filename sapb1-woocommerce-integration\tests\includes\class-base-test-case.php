<?php

use PHPUnit\Framework\TestCase;

/**
 * Base class for plugin tests.
 * Ensures mocks are reset before each test.
 */
class Base_Test_Case extends TestCase {

    protected function setUp() : void {
        parent::setUp();
        reset_all_mocks(); // Function defined in bootstrap.php
    }

    protected function tearDown() : void {
        // Clean up any resources if needed
        parent::tearDown();
    }

    /**
     * Asserts that a specific message is present in the SBIW_Logger logs.
     *
     * @param string $expected_message The message to search for.
     * @param string $message          PHPUnit assertion message.
     */
    public function assertLogContains( $expected_message, $message = '' ) {
        $logs = SBIW_Logger::get_logs();
        $found = false;
        foreach ( $logs as $log_entry ) {
            if ( strpos( $log_entry, $expected_message ) !== false ) {
                $found = true;
                break;
            }
        }
        $this->assertTrue( $found, $message ?: "Expected log message '{$expected_message}' not found." );
    }

    /**
     * Asserts that a specific message is NOT present in the SBIW_Logger logs.
     *
     * @param string $unexpected_message The message to search for.
     * @param string $message            PHPUnit assertion message.
     */
    public function assertLogNotContains( $unexpected_message, $message = '' ) {
        $logs = SBIW_Logger::get_logs();
        $found = false;
        foreach ( $logs as $log_entry ) {
            if ( strpos( $log_entry, $unexpected_message ) !== false ) {
                $found = true;
                break;
            }
        }
        $this->assertFalse( $found, $message ?: "Unexpected log message '{$unexpected_message}' found." );
    }
}
?>

<?php

use PHPUnit\Framework\TestCase; // Should be included via bootstrap or autoloader

/**
 * Tests for the Client_Sync class.
 *
 * @group client-sync
 */
class Test_Client_Sync extends Base_Test_Case {

    /**
     * @var Client_Sync
     */
    private $client_sync;

    /**
     * @var SAPB1_API_Client|\PHPUnit\Framework\MockObject\MockObject
     */
    private $mock_sap_api_client;

    protected function setUp() : void {
        parent::setUp(); // Calls reset_all_mocks()

        $this->mock_sap_api_client = $this->createMock( SAPB1_API_Client::class );
        
        // Instantiate Client_Sync with the mock SAPB1_API_Client
        // This requires Client_Sync's constructor to accept the client or a way to inject it.
        // For now, assuming constructor instantiates it, we might need to refactor Client_Sync or use a more advanced DI.
        // Let's assume Client_Sync can be instantiated and then its internal api client can be replaced.
        // This is a common challenge with hard dependencies in constructors.
        // For this exercise, we'll assume the `new SAPB1_API_Client()` inside Client_Sync will be what we mock,
        // or we modify Client_Sync for testability.
        // Given the current structure of Client_Sync, direct injection is not possible without modifying it.
        // Instead, we will rely on mocking the `get_clients` method on the $this->mock_sap_api_client
        // and then making sure Client_Sync uses this mock.
        // A simple way for this test setup without changing Client_Sync is to have a factory or getter in Client_Sync,
        // or pass it in constructor. Let's assume we refactored Client_Sync's constructor for tests:
        // Original: public function __construct() { $this->sapb1_api_client = new SAPB1_API_Client(); }
        // Refactored: public function __construct(SAPB1_API_Client $client = null) { $this->sapb1_api_client = $client ?: new SAPB1_API_Client(); }
        // For the purpose of this test, I will assume Client_Sync's constructor is modified to accept a SAPB1_API_Client instance.
        // If Client_Sync cannot be refactored, more complex mocking (like replacing the global `new` operator or using prophecy)
        // would be needed, which is beyond simple PHPUnit mocks.
        
        // We will create a new Client_Sync instance and then use reflection to set the sapb1_api_client property.
        $this->client_sync = new Client_Sync();
        $reflection = new ReflectionClass( $this->client_sync );
        $property = $reflection->getProperty( 'sapb1_api_client' );
        $property->setAccessible( true );
        $property->setValue( $this->client_sync, $this->mock_sap_api_client );

    }

    // Helper to assert address fields for a user ID in $mock_user_meta
    private function assertAddressFields( int $user_id, array $expected_billing, array $expected_shipping, string $message_prefix = '' ) {
        global $mock_user_meta;

        // Billing assertions
        foreach ( $expected_billing as $key => $value ) {
            if ($value === null) { // Assert not set or empty
                $this->assertTrue(
                    !isset( $mock_user_meta[$user_id]["billing_{$key}"] ) || empty( $mock_user_meta[$user_id]["billing_{$key}"] ),
                    "{$message_prefix}Billing field 'billing_{$key}' should be empty or not set."
                );
            } else {
                $this->assertEquals( $value, $mock_user_meta[$user_id]["billing_{$key}"], "{$message_prefix}Billing field 'billing_{$key}' mismatch." );
            }
        }

        // Shipping assertions
        foreach ( $expected_shipping as $key => $value ) {
             if ($value === null) { // Assert not set or empty
                $this->assertTrue(
                    !isset( $mock_user_meta[$user_id]["shipping_{$key}"] ) || empty( $mock_user_meta[$user_id]["shipping_{$key}"] ),
                    "{$message_prefix}Shipping field 'shipping_{$key}' should be empty or not set."
                );
            } else {
                $this->assertEquals( $value, $mock_user_meta[$user_id]["shipping_{$key}"], "{$message_prefix}Shipping field 'shipping_{$key}' mismatch." );
            }
        }
        
        // Check for first/last names based on customer's main details if not directly in address
        // This logic is based on how set_customer_address_data populates them.
        $expected_billing_first_name = $expected_billing['first_name'] ?? $mock_user_meta[$user_id]['first_name'];
        $expected_billing_last_name = $expected_billing['last_name'] ?? $mock_user_meta[$user_id]['last_name'];
        $expected_shipping_first_name = $expected_shipping['first_name'] ?? $mock_user_meta[$user_id]['first_name'];
        $expected_shipping_last_name = $expected_shipping['last_name'] ?? $mock_user_meta[$user_id]['last_name'];

        if (isset($expected_billing['first_name'])) {
            $this->assertEquals( $expected_billing_first_name, $mock_user_meta[$user_id]['billing_first_name'], "{$message_prefix}Billing first name mismatch." );
        }
        if (isset($expected_billing['last_name'])) {
            $this->assertEquals( $expected_billing_last_name, $mock_user_meta[$user_id]['billing_last_name'], "{$message_prefix}Billing last name mismatch." );
        }
        if (isset($expected_shipping['first_name'])) {
            $this->assertEquals( $expected_shipping_first_name, $mock_user_meta[$user_id]['shipping_first_name'], "{$message_prefix}Shipping first name mismatch." );
        }
        if (isset($expected_shipping['last_name'])) {
            $this->assertEquals( $expected_shipping_last_name, $mock_user_meta[$user_id]['shipping_last_name'], "{$message_prefix}Shipping last name mismatch." );
        }
    }


    private function get_sample_sap_client_data( $overrides = [] ) {
        return array_merge( [
            'CardCode' => 'C001',
            'CardName' => 'Test Client One',
            'EmailAddress' => '<EMAIL>',
            'Phone1' => '1234567890',
            'Cellular' => '0987654321',
            'BillToStreet' => '1 Billing St',
            'BillToBlock' => 'Billing Block',
            'BillToCity' => 'Billing City',
            'BillToZipCode' => 'B1Z IPC',
            'BillToState' => 'BS',
            'BillToCountry' => 'BC',
            'ShipToStreet' => '1 Shipping St',
            'ShipToBlock' => 'Shipping Block',
            'ShipToCity' => 'Shipping City',
            'ShipToZipCode' => 'S1Z IPC',
            'ShipToState' => 'SS',
            'ShipToCountry' => 'SC',
            'Frozen' => 'tNO', // Not frozen
            'Valid' => 'tYES',  // Valid
        ], $overrides );
    }

    public function test_create_new_client_success() {
        $sap_client_data = $this->get_sample_sap_client_data();
        $this->mock_sap_api_client->method( 'get_clients' )->willReturn( [ $sap_client_data ] );

        global $mock_email_exists_return, $mock_get_users_return, $mock_wp_create_user_return, $mock_user_meta, $mock_username_exists_return;
        $mock_email_exists_return = false; // Email does not exist
        $mock_get_users_return = [];     // No user found by CardCode
        $mock_username_exists_return = false; // Username does not exist
        $created_user_id = 10;
        $mock_wp_create_user_return = $created_user_id;

        $this->client_sync->sync_clients();

        $this->assertLogContains( 'Client Sync: Creating new customer for CardCode C001' );
        $this->assertLogContains( 'Successfully created WordPress user ID: ' . $created_user_id . ' for CardCode C001' );
        $this->assertLogContains( 'Successfully created WooCommerce customer for User ID: ' . $created_user_id . ' (CardCode: C001)' );
        
        $this->assertEquals( 'C001', $mock_user_meta[$created_user_id]['_sapb1_cardcode'] );
        $this->assertEquals( 'Test', $mock_user_meta[$created_user_id]['first_name'] );
        $this->assertEquals( 'Client One', $mock_user_meta[$created_user_id]['last_name'] );
        $this->assertEquals( '<EMAIL>', $mock_user_meta[$created_user_id]['billing_email'] );
        $this->assertEquals( '1 Billing St', $mock_user_meta[$created_user_id]['billing_address_1'] );
        $this->assertEquals( '1 Shipping St', $mock_user_meta[$created_user_id]['shipping_address_1'] );
    }

    public function test_update_existing_client_matched_by_email() {
        $sap_client_data = $this->get_sample_sap_client_data( ['CardName' => 'Updated Name', 'BillToStreet' => 'New Billing St'] );
        $this->mock_sap_api_client->method( 'get_clients' )->willReturn( [ $sap_client_data ] );

        global $mock_email_exists_return, $mock_user_meta;
        $existing_user_id = 5;
        $mock_email_exists_return = [$sap_client_data['EmailAddress'] => $existing_user_id];
        $mock_user_meta[$existing_user_id] = [ // Pre-existing meta
            '_sapb1_cardcode' => 'C001',
            'first_name' => 'Test',
            'last_name' => 'Client One',
            'billing_email' => $sap_client_data['EmailAddress'],
            'billing_address_1' => 'Old Billing St',
        ];
        
        $this->client_sync->sync_clients();

        $this->assertLogContains( 'Found existing user by email (<EMAIL>) for CardCode C001. User ID: ' . $existing_user_id );
        $this->assertLogContains( 'Updating existing customer for CardCode C001, User ID ' . $existing_user_id );
        $this->assertLogContains( 'Successfully updated customer User ID: ' . $existing_user_id . ' (CardCode: C001)' );

        $this->assertEquals( 'Updated', $mock_user_meta[$existing_user_id]['first_name'] );
        $this->assertEquals( 'Name', $mock_user_meta[$existing_user_id]['last_name'] );
        $this->assertEquals( 'New Billing St', $mock_user_meta[$existing_user_id]['billing_address_1'] );
    }

    public function test_update_existing_client_matched_by_cardcode_updates_email() {
        $sap_client_data = $this->get_sample_sap_client_data( ['EmailAddress' => '<EMAIL>'] );
        $this->mock_sap_api_client->method( 'get_clients' )->willReturn( [ $sap_client_data ] );

        global $mock_email_exists_return, $mock_get_users_return, $mock_user_meta;
        $existing_user_id = 7;
        $mock_email_exists_return = false; // Email '<EMAIL>' does not exist
                                          // and old email '<EMAIL>' also doesn't yield this user
        
        // Mock get_users to return the user by CardCode
        $GLOBALS['get_users_callback'] = function($args) use ($existing_user_id, $sap_client_data) {
            if ($args['meta_key'] === '_sapb1_cardcode' && $args['meta_value'] === $sap_client_data['CardCode']) {
                return [$existing_user_id];
            }
            return [];
        };

        $mock_user_meta[$existing_user_id] = [ // Pre-existing meta
            '_sapb1_cardcode' => 'C001',
            'first_name' => 'Test',
            'last_name' => 'Client One',
            'billing_email' => '<EMAIL>', // Old email
        ];

        $this->client_sync->sync_clients();
        
        $this->assertLogContains( 'Found existing user by SAP CardCode meta (C001). User ID: ' . $existing_user_id );
        $this->assertLogContains( 'Email mismatch for CardCode C001. WC Email: <EMAIL>, SAP Email: <EMAIL>. Updating email.' );
        $this->assertLogContains( 'Successfully updated customer User ID: ' . $existing_user_id );
        $this->assertEquals( '<EMAIL>', $mock_user_meta[$existing_user_id]['billing_email'] );
    }

    public function test_skip_frozen_client() {
        $sap_client_data = $this->get_sample_sap_client_data( ['Frozen' => 'tYES'] );
        $this->mock_sap_api_client->method( 'get_clients' )->willReturn( [ $sap_client_data ] );
        $this->client_sync->sync_clients();
        $this->assertLogContains( 'Skipping client C001 because they are marked as frozen/on-hold in SAP B1.' );
        $this->assertLogNotContains( 'Creating new customer for CardCode C001' );
        $this->assertLogNotContains( 'Updating existing customer for CardCode C001' );
    }

    public function test_skip_invalid_client() {
        $sap_client_data = $this->get_sample_sap_client_data( ['Valid' => 'tNO'] );
        $this->mock_sap_api_client->method( 'get_clients' )->willReturn( [ $sap_client_data ] );
        $this->client_sync->sync_clients();
        $this->assertLogContains( 'Skipping client C001 because they are marked as not valid/inactive in SAP B1.' );
    }

    public function test_skip_client_missing_cardcode() {
        $sap_client_data = $this->get_sample_sap_client_data( ['CardCode' => ''] );
        $this->mock_sap_api_client->method( 'get_clients' )->willReturn( [ $sap_client_data ] );
        $this->client_sync->sync_clients();
        $this->assertLogContains( 'Skipping client due to missing CardCode.' );
    }
    
    public function test_skip_client_missing_email() {
        $sap_client_data = $this->get_sample_sap_client_data( ['EmailAddress' => ''] );
        $this->mock_sap_api_client->method( 'get_clients' )->willReturn( [ $sap_client_data ] );
        $this->client_sync->sync_clients();
        $this->assertLogContains( 'Skipping client C001 due to missing EmailAddress.' );
    }

    public function test_skip_client_missing_cardname() {
        $sap_client_data = $this->get_sample_sap_client_data( ['CardName' => ''] );
        $this->mock_sap_api_client->method( 'get_clients' )->willReturn( [ $sap_client_data ] );
        $this->client_sync->sync_clients();
        $this->assertLogContains( 'Skipping client C001 due to missing CardName.' );
    }

    public function test_handle_get_clients_returns_false() {
        $this->mock_sap_api_client->method( 'get_clients' )->willReturn( false );
        $this->client_sync->sync_clients();
        $this->assertLogContains( 'Client Sync Error: Failed to fetch clients from SAP B1. API client returned false.' );
    }

    public function test_handle_get_clients_returns_empty_array() {
        $this->mock_sap_api_client->method( 'get_clients' )->willReturn( [] );
        $this->client_sync->sync_clients();
        $this->assertLogContains( 'Client Sync: No clients found in SAP B1 to synchronize.' );
    }
    
    public function test_handle_wp_create_user_failure() {
        $sap_client_data = $this->get_sample_sap_client_data();
        $this->mock_sap_api_client->method( 'get_clients' )->willReturn( [ $sap_client_data ] );

        global $mock_email_exists_return, $mock_get_users_return, $mock_wp_create_user_return;
        $mock_email_exists_return = false;
        $mock_get_users_return = [];
        $mock_wp_create_user_return = new WP_Error( 'create_user_failed', 'Mocked user creation failure.' );
        
        $this->client_sync->sync_clients();

        $this->assertLogContains( 'Client Sync Error: Failed to create WordPress user for CardCode C001. Errors: Mocked user creation failure.' );
        $this->assertLogNotContains( 'Successfully created WordPress user ID' );
    }

    public function test_handle_wc_customer_save_failure_on_create() {
        $sap_client_data = $this->get_sample_sap_client_data();
        $this->mock_sap_api_client->method( 'get_clients' )->willReturn( [ $sap_client_data ] );

        global $mock_email_exists_return, $mock_get_users_return, $mock_wp_create_user_return;
        $mock_email_exists_return = false;
        $mock_get_users_return = [];
        $mock_wp_create_user_return = 20; // Success

        WC_Customer::$save_should_throw_exception = true;
        WC_Customer::$save_exception_message = 'Simulated WC_Customer save error during create.';

        $this->client_sync->sync_clients();

        $this->assertLogContains( 'Successfully created WordPress user ID: 20 for CardCode C001' );
        $this->assertLogContains( 'Client Sync Error: Failed to create/save WooCommerce customer for User ID: 20. Error: Simulated WC_Customer save error during create.' );
    }
    
    public function test_handle_wc_customer_save_failure_on_update() {
        $sap_client_data = $this->get_sample_sap_client_data();
        $this->mock_sap_api_client->method( 'get_clients' )->willReturn( [ $sap_client_data ] );

        global $mock_email_exists_return;
        $existing_user_id = 21;
        $mock_email_exists_return = [$sap_client_data['EmailAddress'] => $existing_user_id];

        WC_Customer::$save_should_throw_exception = true;
        WC_Customer::$save_exception_message = 'Simulated WC_Customer save error during update.';

        $this->client_sync->sync_clients();

        $this->assertLogContains( 'Updating existing customer for CardCode C001, User ID ' . $existing_user_id );
        $this->assertLogContains( 'Client Sync Error: Failed to save updated customer User ID: ' . $existing_user_id . '. Error: Simulated WC_Customer save error during update.' );
    }

    public function test_handle_wc_customer_constructor_exception_on_existing_user_by_email() {
        $sap_client_data = $this->get_sample_sap_client_data();
        $this->mock_sap_api_client->method( 'get_clients' )->willReturn( [ $sap_client_data ] );

        global $mock_email_exists_return;
        $existing_user_id = 22;
        $mock_email_exists_return = [$sap_client_data['EmailAddress'] => $existing_user_id];
        
        WC_Customer::$constructor_should_throw_exception = true;
        WC_Customer::$constructor_exception_message = 'Simulated WC_Customer constructor error for existing user.';

        $this->client_sync->sync_clients();

        $this->assertLogContains( 'Client Sync Error: Could not instantiate WC_Customer for existing User ID ' . $existing_user_id . '. Error: Simulated WC_Customer constructor error for existing user.' );
        $this->assertLogNotContains( 'Updating existing customer' );
    }
    
    public function test_handle_wc_customer_constructor_exception_on_existing_user_by_cardcode() {
        $sap_client_data = $this->get_sample_sap_client_data();
        $this->mock_sap_api_client->method( 'get_clients' )->willReturn( [ $sap_client_data ] );

        global $mock_email_exists_return, $mock_get_users_return;
        $existing_user_id = 23;
        $mock_email_exists_return = false; // Not found by email
        $GLOBALS['get_users_callback'] = function($args) use ($existing_user_id, $sap_client_data) {
            if ($args['meta_key'] === '_sapb1_cardcode' && $args['meta_value'] === $sap_client_data['CardCode']) {
                return [$existing_user_id];
            }
            return [];
        };
        
        WC_Customer::$constructor_should_throw_exception = true;
        WC_Customer::$constructor_exception_message = 'Simulated WC_Customer constructor error for cardcode user.';

        $this->client_sync->sync_clients();

        $this->assertLogContains( 'Client Sync Error: Could not instantiate WC_Customer for existing User ID ' . $existing_user_id . ' (found by CardCode). Error: Simulated WC_Customer constructor error for cardcode user.' );
        $this->assertLogNotContains( 'Updating existing customer' );
    }


    public function test_handle_wc_customer_constructor_exception_on_new_user_after_wp_create() {
        $sap_client_data = $this->get_sample_sap_client_data();
        $this->mock_sap_api_client->method( 'get_clients' )->willReturn( [ $sap_client_data ] );

        global $mock_email_exists_return, $mock_get_users_return, $mock_wp_create_user_return;
        $created_user_id = 24;
        $mock_email_exists_return = false;
        $mock_get_users_return = [];
        $mock_wp_create_user_return = $created_user_id; // wp_create_user succeeds

        WC_Customer::$constructor_should_throw_exception = true;
        WC_Customer::$constructor_exception_message = 'Simulated WC_Customer constructor error after new user creation.';
        
        $this->client_sync->sync_clients();

        $this->assertLogContains( 'Successfully created WordPress user ID: ' . $created_user_id . ' for CardCode C001' );
        $this->assertLogContains( 'Client Sync Error: Failed to create/save WooCommerce customer for User ID: ' . $created_user_id . '. Error: Simulated WC_Customer constructor error after new user creation.' );
    }

    // New Address Synchronization Tests

    public function test_sync_client_with_full_billing_and_shipping_addresses() {
        $sap_data = $this->get_sample_sap_client_data( [
            'CardName' => 'Full Address Test', // Will become First: Full, Last: Address Test
            'BillToStreet' => '123 Billing Ave',
            'BillToBlock' => 'Suite B1',
            'BillToCity' => 'Billville',
            'BillToState' => 'BS',
            'BillToZipCode' => 'B1234',
            'BillToCountry' => 'BCN',
            'Phone1' => '555-0101', // Primary phone, used for billing phone
            'ShipToStreet' => '456 Shipping Rd',
            'ShipToBlock' => 'Unit S2',
            'ShipToCity' => 'Shipburg',
            'ShipToState' => 'SS',
            'ShipToZipCode' => 'S5678',
            'ShipToCountry' => 'SCN',
        ] );
        $this->mock_sap_api_client->method( 'get_clients' )->willReturn( [ $sap_data ] );

        global $mock_email_exists_return, $mock_get_users_return, $mock_wp_create_user_return, $mock_user_meta, $mock_username_exists_return;
        $mock_email_exists_return = false;
        $mock_get_users_return = [];
        $mock_username_exists_return = false;
        $user_id = 30;
        $mock_wp_create_user_return = $user_id;

        $this->client_sync->sync_clients();

        $this->assertLogContains( "Successfully created WooCommerce customer for User ID: {$user_id} (CardCode: {$sap_data['CardCode']})" );
        
        $expected_billing = [
            'first_name' => 'Full',
            'last_name' => 'Address Test',
            'company' => 'Full Address Test',
            'address_1' => '123 Billing Ave',
            'address_2' => 'Suite B1',
            'city' => 'Billville',
            'state' => 'BS',
            'postcode' => 'B1234',
            'country' => 'BCN',
            'phone' => '555-0101',
            'email' => $sap_data['EmailAddress'],
        ];
        $expected_shipping = [
            'first_name' => 'Full',
            'last_name' => 'Address Test',
            'company' => 'Full Address Test',
            'address_1' => '456 Shipping Rd',
            'address_2' => 'Unit S2',
            'city' => 'Shipburg',
            'state' => 'SS',
            'postcode' => 'S5678',
            'country' => 'SCN',
        ];
        $this->assertAddressFields( $user_id, $expected_billing, $expected_shipping );
    }

    public function test_sync_client_with_billing_only_shipping_defaults_to_billing() {
        $sap_data = $this->get_sample_sap_client_data( [
            'CardName' => 'Billing Only Test',
            'BillToStreet' => '789 Default Ave',
            'BillToCity' => 'Defaultville',
            'Phone1' => '555-0102',
            'ShipToStreet' => '', // Empty shipping street
            'ShipToCity' => null, // Null shipping city
            'ShipToZipCode' => '',
            'ShipToCountry' => '',
            'ShipToState' => '',
            'ShipToBlock' => '',
        ] );
        $this->mock_sap_api_client->method( 'get_clients' )->willReturn( [ $sap_data ] );

        global $mock_email_exists_return, $mock_get_users_return, $mock_wp_create_user_return, $mock_user_meta, $mock_username_exists_return;
        $mock_email_exists_return = false;
        $mock_get_users_return = [];
        $mock_username_exists_return = false;
        $user_id = 31;
        $mock_wp_create_user_return = $user_id;

        $this->client_sync->sync_clients();

        $this->assertLogContains( "Successfully created WooCommerce customer for User ID: {$user_id} (CardCode: {$sap_data['CardCode']})" );
        
        $expected_billing = [
            'first_name' => 'Billing', // from CardName
            'last_name' => 'Only Test', // from CardName
            'company' => 'Billing Only Test',
            'address_1' => '789 Default Ave',
            'city' => 'Defaultville',
            'phone' => '555-0102',
            'email' => $sap_data['EmailAddress'],
            // other billing fields from get_sample_sap_client_data if not overridden
            'address_2'  => $sap_data['BillToBlock'],
			'state'      => $sap_data['BillToState'],
			'postcode'   => $sap_data['BillToZipCode'],
			'country'    => $sap_data['BillToCountry'],
        ];
        // Shipping should default to billing
        $expected_shipping = $expected_billing; 
        // Shipping does not have email/phone fields by default in WC, so remove them for shipping assertion
        unset($expected_shipping['email']);
        unset($expected_shipping['phone']);


        $this->assertAddressFields( $user_id, $expected_billing, $expected_shipping );
    }

    public function test_sync_client_with_some_missing_address_fields() {
        $sap_data = $this->get_sample_sap_client_data( [
            'CardName' => 'Missing Fields Test',
            'BillToStreet' => '1 Main St',
            'BillToBlock' => '', // Missing billing address_2
            'BillToCity' => 'Anytown',
            'ShipToStreet' => '2 Sub St',
            'ShipToCity' => 'Otherplace',
            'ShipToState' => null, // Missing shipping state
        ] );
        $this->mock_sap_api_client->method( 'get_clients' )->willReturn( [ $sap_data ] );

        global $mock_email_exists_return, $mock_get_users_return, $mock_wp_create_user_return, $mock_user_meta, $mock_username_exists_return;
        $mock_email_exists_return = false;
        $mock_get_users_return = [];
        $mock_username_exists_return = false;
        $user_id = 32;
        $mock_wp_create_user_return = $user_id;

        $this->client_sync->sync_clients();

        $this->assertLogContains( "Successfully created WooCommerce customer for User ID: {$user_id} (CardCode: {$sap_data['CardCode']})" );
        
        $expected_billing = [
            'first_name' => 'Missing',
            'last_name' => 'Fields Test',
            'company' => 'Missing Fields Test',
            'address_1' => '1 Main St',
            'address_2' => null, // Should be empty/not set
            'city' => 'Anytown',
            'state'      => $sap_data['BillToState'],
			'postcode'   => $sap_data['BillToZipCode'],
			'country'    => $sap_data['BillToCountry'],
            'phone'      => $sap_data['Phone1'],
            'email'      => $sap_data['EmailAddress'],
        ];
        $expected_shipping = [
            'first_name' => 'Missing',
            'last_name' => 'Fields Test',
            'company' => 'Missing Fields Test',
            'address_1' => '2 Sub St',
            'address_2'  => $sap_data['ShipToBlock'], // from default sample
            'city' => 'Otherplace',
            'state' => null, // Should be empty/not set
            'postcode'   => $sap_data['ShipToZipCode'],
			'country'    => $sap_data['ShipToCountry'],
        ];
        $this->assertAddressFields( $user_id, $expected_billing, $expected_shipping );
    }

    public function test_sync_client_updates_existing_full_addresses() {
        $existing_user_id = 33;
        $initial_sap_card_code = 'CUST033';

        global $mock_email_exists_return, $mock_user_meta, $mock_get_users_return;
        $mock_email_exists_return = ['<EMAIL>' => $existing_user_id];
        $mock_user_meta[$existing_user_id] = [
            '_sapb1_cardcode' => $initial_sap_card_code,
            'first_name' => 'Old',
            'last_name' => 'Name',
            'billing_email' => '<EMAIL>',
            'billing_first_name' => 'Old', 'billing_last_name' => 'Name', 'billing_company' => 'Old Company',
            'billing_address_1' => '1 Old Bill St', 'billing_address_2' => 'OldB', 'billing_city' => 'OldBillCity', 
            'billing_state' => 'OB', 'billing_postcode' => 'OB1', 'billing_country' => 'OBC', 'billing_phone' => '111',
            'shipping_first_name' => 'Old', 'shipping_last_name' => 'Name', 'shipping_company' => 'Old Ship Company',
            'shipping_address_1' => '2 Old Ship St', 'shipping_address_2' => 'OldS', 'shipping_city' => 'OldShipCity',
            'shipping_state' => 'OS', 'shipping_postcode' => 'OS2', 'shipping_country' => 'OSC',
        ];
        // Ensure get_users also finds this user by cardcode if email lookup fails or email changes
        $GLOBALS['get_users_callback'] = function($args) use ($existing_user_id, $initial_sap_card_code) {
            if ($args['meta_key'] === '_sapb1_cardcode' && $args['meta_value'] === $initial_sap_card_code) {
                return [$existing_user_id];
            }
            return [];
        };


        $updated_sap_data = $this->get_sample_sap_client_data( [
            'CardCode' => $initial_sap_card_code, // Same CardCode
            'EmailAddress' => '<EMAIL>', // Same email for matching
            'CardName' => 'New Full Address', // New Name
            'BillToStreet' => '987 New Billing Ave', 'BillToBlock' => 'New B9', 'BillToCity' => 'NewBillville',
            'BillToState' => 'NB', 'BillToZipCode' => 'NB987', 'BillToCountry' => 'NBC', 'Phone1' => '555-0202',
            'ShipToStreet' => '654 New Shipping Rd', 'ShipToBlock' => 'New S6', 'ShipToCity' => 'NewShipburg',
            'ShipToState' => 'NS', 'ShipToZipCode' => 'NS654', 'ShipToCountry' => 'NSC',
        ] );
        $this->mock_sap_api_client->method( 'get_clients' )->willReturn( [ $updated_sap_data ] );
        
        $this->client_sync->sync_clients();

        $this->assertLogContains( "Successfully updated customer User ID: {$existing_user_id} (CardCode: {$updated_sap_data['CardCode']})" );
        
        // Assert names updated
        $this->assertEquals( 'New', $mock_user_meta[$existing_user_id]['first_name'] );
        $this->assertEquals( 'Full Address', $mock_user_meta[$existing_user_id]['last_name'] );

        $expected_billing = [
            'first_name' => 'New', 'last_name' => 'Full Address', 'company' => 'New Full Address',
            'address_1' => '987 New Billing Ave', 'address_2' => 'New B9', 'city' => 'NewBillville',
            'state' => 'NB', 'postcode' => 'NB987', 'country' => 'NBC', 'phone' => '555-0202',
            'email' => '<EMAIL>',
        ];
        $expected_shipping = [
            'first_name' => 'New', 'last_name' => 'Full Address', 'company' => 'New Full Address',
            'address_1' => '654 New Shipping Rd', 'address_2' => 'New S6', 'city' => 'NewShipburg',
            'state' => 'NS', 'postcode' => 'NS654', 'country' => 'NSC',
        ];
        $this->assertAddressFields( $existing_user_id, $expected_billing, $expected_shipping );
    }
    
    public function test_sync_client_sap_empty_shipping_defaults_to_billing_overwriting_existing_wc_shipping() {
        $existing_user_id = 34;
        $sap_card_code = 'CUST034';

        global $mock_email_exists_return, $mock_user_meta;
        $mock_email_exists_return = ['<EMAIL>' => $existing_user_id];
        $mock_user_meta[$existing_user_id] = [
            '_sapb1_cardcode' => $sap_card_code,
            'first_name' => 'User', 'last_name' => 'ThirtyFour', 'billing_email' => '<EMAIL>',
            'billing_first_name' => 'User', 'billing_last_name' => 'ThirtyFour', 'billing_company' => 'Company 34',
            'billing_address_1' => '1 Billing Way', 'billing_city' => 'Billtown', 'billing_phone' => '343434',
            'shipping_first_name' => 'User', 'shipping_last_name' => 'ThirtyFour', 'shipping_company' => 'Shipping Co 34',
            'shipping_address_1' => '99 Shipping Lane', 'shipping_city' => 'Shipton', // Distinct shipping
        ];

        $sap_data = $this->get_sample_sap_client_data( [
            'CardCode' => $sap_card_code,
            'EmailAddress' => '<EMAIL>',
            'CardName' => 'User ThirtyFour Updated', // Name update to see if base names change
            'BillToStreet' => '123 Updated Bill St', // New Billing
            'BillToCity' => 'Billville New',
            'BillToState' => 'BN',
            'Phone1' => '343434-new',
            // All ShipTo fields are from get_sample_sap_client_data, but we override them to be empty
            'ShipToStreet' => '', 'ShipToBlock' => '', 'ShipToCity' => '',
            'ShipToZipCode' => '', 'ShipToState' => '', 'ShipToCountry' => '',
        ] );
        $this->mock_sap_api_client->method( 'get_clients' )->willReturn( [ $sap_data ] );

        $this->client_sync->sync_clients();

        $this->assertLogContains( "Successfully updated customer User ID: {$existing_user_id} (CardCode: {$sap_card_code})" );
        
        $this->assertEquals( 'User', $mock_user_meta[$existing_user_id]['first_name'] ); // First name part of CardName
        $this->assertEquals( 'ThirtyFour Updated', $mock_user_meta[$existing_user_id]['last_name'] ); // Rest of CardName

        $expected_billing = [
            'first_name' => 'User', 'last_name' => 'ThirtyFour Updated', 'company' => 'User ThirtyFour Updated',
            'address_1' => '123 Updated Bill St',
            'address_2' => $sap_data['BillToBlock'], // from sample, as not overridden to empty for billing
            'city' => 'Billville New',
            'state' => 'BN',
            'postcode' => $sap_data['BillToZipCode'], // from sample
            'country' => $sap_data['BillToCountry'],  // from sample
            'phone' => '343434-new',
            'email' => '<EMAIL>',
        ];
        // Shipping should default to the new billing address
        $expected_shipping = $expected_billing; 
        unset($expected_shipping['email']);
        unset($expected_shipping['phone']);
        
        $this->assertAddressFields( $existing_user_id, $expected_billing, $expected_shipping, "Test Case: Empty SAP Shipping defaults to Billing. " );
    }

    public function test_sync_client_sap_empty_billing_address_clears_wc_billing() {
        $existing_user_id = 35;
        $sap_card_code = 'CUST035';

        global $mock_email_exists_return, $mock_user_meta;
        $mock_email_exists_return = ['<EMAIL>' => $existing_user_id];
        $mock_user_meta[$existing_user_id] = [
            '_sapb1_cardcode' => $sap_card_code,
            'first_name' => 'User', 'last_name' => 'ThirtyFive', 'billing_email' => '<EMAIL>',
            'billing_first_name' => 'User', 'billing_last_name' => 'ThirtyFive', 'billing_company' => 'Company 35',
            'billing_address_1' => '1 Billing Way', 'billing_address_2' => 'Suite B', 'billing_city' => 'Billtown', 
            'billing_state' => 'BS', 'billing_postcode' => 'B35', 'billing_country' => 'BC', 'billing_phone' => '353535',
            // Keep shipping distinct to ensure it's not affected when billing is cleared
            'shipping_first_name' => 'User', 'shipping_last_name' => 'ThirtyFive', 'shipping_company' => 'Shipping Co 35',
            'shipping_address_1' => '99 Shipping Lane', 'shipping_address_2' => 'Suite S', 'shipping_city' => 'Shipton',
            'shipping_state' => 'SS', 'shipping_postcode' => 'S35', 'shipping_country' => 'SC',
        ];

        $sap_data = $this->get_sample_sap_client_data( [
            'CardCode' => $sap_card_code,
            'EmailAddress' => '<EMAIL>',
            'CardName' => 'User ThirtyFive', // Keep name same
            // All BillTo fields are empty
            'BillToStreet' => '', 'BillToBlock' => '', 'BillToCity' => '',
            'BillToZipCode' => '', 'BillToState' => '', 'BillToCountry' => '',
            'Phone1' => '', // Also clear phone
            // Shipping fields remain as per get_sample_sap_client_data (which has values)
            // This is to ensure shipping is NOT cleared, only billing.
            'ShipToStreet' => 'Unchanged Ship St', 'ShipToCity' => 'Unchanged Ship City',
        ] );
        $this->mock_sap_api_client->method( 'get_clients' )->willReturn( [ $sap_data ] );

        $this->client_sync->sync_clients();

        $this->assertLogContains( "Successfully updated customer User ID: {$existing_user_id} (CardCode: {$sap_card_code})" );
        
        $expected_cleared_billing = [
            // first_name, last_name, company will still be derived from CardName
            'first_name' => 'User', 'last_name' => 'ThirtyFive', 'company' => 'User ThirtyFive',
            'address_1' => null, 'address_2' => null, 'city' => null,
            'state' => null, 'postcode' => null, 'country' => null, 'phone' => null,
            'email' => '<EMAIL>', // Email is preserved
        ];
        
        // Shipping should remain as per SAP data (which is distinct from original and not empty)
        $expected_shipping = [
            'first_name' => 'User', 'last_name' => 'ThirtyFive', 'company' => 'User ThirtyFive',
            'address_1'  => 'Unchanged Ship St',
			'address_2'  => $sap_data['ShipToBlock'], // from sample
			'city'       => 'Unchanged Ship City',
			'state'      => $sap_data['ShipToState'], // from sample
			'postcode'   => $sap_data['ShipToZipCode'], // from sample
			'country'    => $sap_data['ShipToCountry'], // from sample
        ];
        
        $this->assertAddressFields( $existing_user_id, $expected_cleared_billing, $expected_shipping, "Test Case: Empty SAP Billing clears WC Billing. " );
    }

}

?>

<?php

use PHPUnit\Framework\TestCase;

/**
 * Tests for the Order_Sync class.
 *
 * @group order-sync
 */
class Test_Order_Sync extends Base_Test_Case {

    /**
     * @var Order_Sync
     */
    private $order_sync;

    /**
     * @var SAPB1_API_Client|\PHPUnit\Framework\MockObject\MockObject
     */
    private $mock_sap_api_client;

    protected function setUp(): void {
        parent::setUp(); // Calls reset_all_mocks()

        $this->mock_sap_api_client = $this->createMock(SAPB1_API_Client::class);

        // Create Order_Sync instance and inject mock API client using reflection
        $this->order_sync = new Order_Sync();
        $reflection = new ReflectionClass($this->order_sync);
        $property = $reflection->getProperty('api_client');
        $property->setAccessible(true);
        $property->setValue($this->order_sync, $this->mock_sap_api_client);
    }

    /**
     * Helper method to create a mock WooCommerce order.
     */
    private function create_mock_order($order_data = []) {
        global $mock_orders, $mock_order_id_counter, $mock_products;

        $order_id = ++$mock_order_id_counter;

        $default_data = [
            'customer_id' => 123,
            'billing_email' => '<EMAIL>',
            'billing_address_1' => '123 Main St',
            'billing_address_2' => 'Apt 4B',
            'billing_city' => 'New York',
            'billing_postcode' => '10001',
            'billing_state' => 'NY',
            'billing_country' => 'US',
            'shipping_address_1' => '123 Main St',
            'shipping_address_2' => 'Apt 4B',
            'shipping_city' => 'New York',
            'shipping_postcode' => '10001',
            'shipping_state' => 'NY',
            'shipping_country' => 'US',
            'customer_note' => 'Please deliver after 5 PM',
            'date_created' => new DateTime('2024-01-15'),
            'items' => [
                1 => [
                    'product_id' => 1001,
                    'quantity' => 2,
                    'subtotal' => 199.98,
                    'sku' => 'TEST001'
                ],
                2 => [
                    'product_id' => 1002,
                    'quantity' => 1,
                    'subtotal' => 49.99,
                    'sku' => 'TEST002'
                ]
            ]
        ];

        $order_data = array_merge($default_data, $order_data);
        $mock_orders[$order_id] = $order_data;

        // Create corresponding products in mock store
        foreach ($order_data['items'] as $item) {
            if (!isset($mock_products[$item['product_id']])) {
                $mock_products[$item['product_id']] = [
                    'sku' => $item['sku'],
                    'name' => 'Product ' . $item['sku'],
                    'regular_price' => $item['subtotal'] / $item['quantity'],
                    'manage_stock' => true,
                    'stock_quantity' => 10,
                    'status' => 'publish'
                ];
            }
        }

        return new WC_Order($order_id);
    }

    /**
     * Test successful order sync with registered customer.
     */
    public function test_successful_order_sync_registered_customer() {
        // Setup customer with SAP B1 CardCode
        global $mock_user_meta;
        $mock_user_meta[123]['_sapb1_cardcode'] = 'C001';

        // Create mock order
        $order = $this->create_mock_order();

        // Mock successful SAP B1 response
        $this->mock_sap_api_client->method('create_sales_order')->willReturn([
            'DocNum' => 'SO-12345',
            'DocEntry' => 12345
        ]);

        // Execute order sync
        $result = $this->order_sync->push_order_to_sapb1($order);

        // Verify success
        $this->assertTrue($result);

        // Verify logs
        $this->assertLogContains('Order Sync: Starting to process order ID: ' . $order->get_id());
        $this->assertLogContains('Order Sync: Successfully pushed order ID: ' . $order->get_id() . ' to SAP B1. SAP DocNum: SO-12345');

        // Verify order note was added
        $notes = $order->get_order_notes();
        $this->assertCount(1, $notes);
        $this->assertStringContainsString('Order successfully sent to SAP B1', $notes[0]['note']);
    }

    /**
     * Test order sync failure when customer has no CardCode.
     */
    public function test_order_sync_failure_no_cardcode() {
        // Create mock order without CardCode in user meta
        $order = $this->create_mock_order();

        // Execute order sync
        $result = $this->order_sync->push_order_to_sapb1($order);

        // Verify failure
        $this->assertFalse($result);

        // Verify error logs
        $this->assertLogContains('Order Sync Error: Could not determine CardCode for order ID: ' . $order->get_id());
    }

    /**
     * Test order sync with guest customer (no customer_id) - should create new customer.
     */
    public function test_order_sync_guest_customer_new() {
        // Mock API client to simulate customer creation
        $this->mock_sap_api_client->method('get_customer_by_email')
            ->willReturn(false); // No existing customer

        $this->mock_sap_api_client->method('create_business_partner')
            ->willReturn(['CardCode' => 'C001234']);

        $this->mock_sap_api_client->method('create_sales_order')
            ->willReturn(['DocNum' => 'SO-12345']);

        // Create guest order
        $order = $this->create_mock_order(['customer_id' => 0, 'billing_email' => '<EMAIL>']);

        // Execute order sync
        $result = $this->order_sync->push_order_to_sapb1($order);

        // Verify success (guest orders now supported with customer creation)
        $this->assertTrue($result);

        // Verify logs
        $this->assertLogContains('Order Sync: Order ID: ' . $order->get_id() . ' is a guest order');
        $this->assertLogContains('Order Sync: No existing customer found. Creating new SAP B1 customer');
        $this->assertLogContains('Order Sync: Successfully created new SAP B1 customer with CardCode: C001234');
    }

    /**
     * Test order sync with guest customer - existing customer found.
     */
    public function test_order_sync_guest_customer_existing() {
        // Mock API client to simulate finding existing customer
        $this->mock_sap_api_client->method('get_customer_by_email')
            ->willReturn(['CardCode' => 'C005678', 'CardName' => 'Existing Customer']);

        $this->mock_sap_api_client->method('create_sales_order')
            ->willReturn(['DocNum' => 'SO-12345']);

        // Create guest order
        $order = $this->create_mock_order(['customer_id' => 0, 'billing_email' => '<EMAIL>']);

        // Execute order sync
        $result = $this->order_sync->push_order_to_sapb1($order);

        // Verify success
        $this->assertTrue($result);

        // Verify logs
        $this->assertLogContains('Order Sync: Order ID: ' . $order->get_id() . ' is a guest order');
        $this->assertLogContains('Order Sync: Found existing SAP B1 customer with CardCode: C005678');
    }

    /**
     * Test order sync with registered customer without CardCode.
     */
    public function test_order_sync_registered_customer_no_cardcode() {
        // Mock user meta to return empty CardCode
        global $mock_user_meta;
        $mock_user_meta[123] = []; // No CardCode stored

        // Mock API client to simulate customer creation
        $this->mock_sap_api_client->method('get_customer_by_email')
            ->willReturn(false); // No existing customer

        $this->mock_sap_api_client->method('create_business_partner')
            ->willReturn(['CardCode' => 'C009999']);

        $this->mock_sap_api_client->method('create_sales_order')
            ->willReturn(['DocNum' => 'SO-12345']);

        // Create order for registered customer
        $order = $this->create_mock_order(['customer_id' => 123, 'billing_email' => '<EMAIL>']);

        // Execute order sync
        $result = $this->order_sync->push_order_to_sapb1($order);

        // Verify success
        $this->assertTrue($result);

        // Verify CardCode was stored in user meta
        $this->assertEquals('C009999', $mock_user_meta[123]['_sapb1_cardcode']);

        // Verify logs
        $this->assertLogContains('Order Sync: Stored CardCode C009999 for registered customer ID: 123');
    }

    /**
     * Test order sync with invalid order object.
     */
    public function test_order_sync_invalid_order() {
        // Execute order sync with null order
        $result = $this->order_sync->push_order_to_sapb1(null);

        // Verify failure
        $this->assertFalse($result);

        // Verify error log
        $this->assertLogContains('Order Sync Error: Invalid WC_Order object provided.');
    }

    /**
     * Test order sync with items missing SKUs.
     */
    public function test_order_sync_items_missing_sku() {
        global $mock_user_meta;
        $mock_user_meta[123]['_sapb1_cardcode'] = 'C001';

        // Create order with items that have no SKU
        $order = $this->create_mock_order([
            'items' => [
                1 => [
                    'product_id' => 1001,
                    'quantity' => 2,
                    'subtotal' => 199.98,
                    'sku' => '' // Empty SKU
                ]
            ]
        ]);

        // Execute order sync
        $result = $this->order_sync->push_order_to_sapb1($order);

        // Verify failure
        $this->assertFalse($result);

        // Verify logs
        $this->assertLogContains('Order Sync Warning: Order ID: ' . $order->get_id() . ' - Item ID: 1 has no SKU. Skipping item.');
        $this->assertLogContains('Order Sync Error: Order ID: ' . $order->get_id() . ' has no valid items with SKUs to send to SAP B1.');
    }

    /**
     * Test order sync when SAP B1 API fails.
     */
    public function test_order_sync_sap_api_failure() {
        global $mock_user_meta;
        $mock_user_meta[123]['_sapb1_cardcode'] = 'C001';

        // Create mock order
        $order = $this->create_mock_order();

        // Mock SAP B1 API failure
        $this->mock_sap_api_client->method('create_sales_order')->willReturn(false);

        // Execute order sync
        $result = $this->order_sync->push_order_to_sapb1($order);

        // Verify failure
        $this->assertFalse($result);

        // Verify error log
        $this->assertLogContains('Order Sync Error: Failed to push order ID: ' . $order->get_id() . ' to SAP B1.');
    }

    /**
     * Test order data structure sent to SAP B1.
     */
    public function test_order_data_structure() {
        global $mock_user_meta;
        $mock_user_meta[123]['_sapb1_cardcode'] = 'C001';

        // Create mock order
        $order = $this->create_mock_order();

        // Capture the order data sent to SAP B1
        $captured_order_data = null;
        $this->mock_sap_api_client->method('create_sales_order')
            ->willReturnCallback(function($order_data) use (&$captured_order_data) {
                $captured_order_data = $order_data;
                return ['DocNum' => 'SO-12345'];
            });

        // Execute order sync
        $this->order_sync->push_order_to_sapb1($order);

        // Verify order data structure
        $this->assertNotNull($captured_order_data);
        $this->assertEquals('C001', $captured_order_data['CardCode']);
        $this->assertEquals('2024-01-15', $captured_order_data['DocDate']);
        $this->assertStringContainsString('WooCommerce Order ID: ' . $order->get_id(), $captured_order_data['Comments']);

        // Verify document lines
        $this->assertCount(2, $captured_order_data['DocumentLines']);

        $line1 = $captured_order_data['DocumentLines'][0];
        $this->assertEquals('TEST001', $line1['ItemCode']);
        $this->assertEquals(2, $line1['Quantity']);
        $this->assertEquals(99.99, $line1['UnitPrice']);

        $line2 = $captured_order_data['DocumentLines'][1];
        $this->assertEquals('TEST002', $line2['ItemCode']);
        $this->assertEquals(1, $line2['Quantity']);
        $this->assertEquals(49.99, $line2['UnitPrice']);

        // Verify addresses
        $this->assertEquals('123 Main St', $captured_order_data['BillToStreet']);
        $this->assertEquals('New York', $captured_order_data['BillToCity']);
        $this->assertEquals('10001', $captured_order_data['BillToZipCode']);
        $this->assertEquals('NY', $captured_order_data['BillToState']);
        $this->assertEquals('US', $captured_order_data['BillToCountry']);
    }

    /**
     * Test order sync with mixed valid and invalid items.
     */
    public function test_order_sync_mixed_items() {
        global $mock_user_meta;
        $mock_user_meta[123]['_sapb1_cardcode'] = 'C001';

        // Create order with mix of valid and invalid items
        $order = $this->create_mock_order([
            'items' => [
                1 => [
                    'product_id' => 1001,
                    'quantity' => 2,
                    'subtotal' => 199.98,
                    'sku' => 'TEST001' // Valid SKU
                ],
                2 => [
                    'product_id' => 1002,
                    'quantity' => 1,
                    'subtotal' => 49.99,
                    'sku' => '' // Invalid SKU
                ],
                3 => [
                    'product_id' => 1003,
                    'quantity' => 3,
                    'subtotal' => 149.97,
                    'sku' => 'TEST003' // Valid SKU
                ]
            ]
        ]);

        // Mock successful SAP B1 response
        $captured_order_data = null;
        $this->mock_sap_api_client->method('create_sales_order')
            ->willReturnCallback(function($order_data) use (&$captured_order_data) {
                $captured_order_data = $order_data;
                return ['DocNum' => 'SO-12345'];
            });

        // Execute order sync
        $result = $this->order_sync->push_order_to_sapb1($order);

        // Verify success (should succeed with valid items)
        $this->assertTrue($result);

        // Verify only valid items were sent
        $this->assertCount(2, $captured_order_data['DocumentLines']);
        $this->assertEquals('TEST001', $captured_order_data['DocumentLines'][0]['ItemCode']);
        $this->assertEquals('TEST003', $captured_order_data['DocumentLines'][1]['ItemCode']);

        // Verify warning for invalid item
        $this->assertLogContains('Order Sync Warning: Order ID: ' . $order->get_id() . ' - Item ID: 2 has no SKU. Skipping item.');
    }

    /**
     * Test order sync logging details.
     */
    public function test_order_sync_logging() {
        global $mock_user_meta;
        $mock_user_meta[123]['_sapb1_cardcode'] = 'C001';

        // Create mock order
        $order = $this->create_mock_order();

        // Mock successful SAP B1 response
        $this->mock_sap_api_client->method('create_sales_order')->willReturn([
            'DocNum' => 'SO-12345'
        ]);

        // Clear logs before test
        SBIW_Logger::clear_logs();

        // Execute order sync
        $this->order_sync->push_order_to_sapb1($order);

        // Verify comprehensive logging
        $logs = SBIW_Logger::get_logs();
        $this->assertGreaterThan(2, count($logs));

        // Check specific log entries
        $this->assertLogContains('Order Sync: Starting to process order ID: ' . $order->get_id());
        $this->assertLogContains('Order Sync: Sending order data to SAP B1 for order ID: ' . $order->get_id());
        $this->assertLogContains('Order Sync: Successfully pushed order ID: ' . $order->get_id() . ' to SAP B1');
    }
}

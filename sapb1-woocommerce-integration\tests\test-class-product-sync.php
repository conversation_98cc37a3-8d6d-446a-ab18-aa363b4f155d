<?php

use PHPUnit\Framework\TestCase;

/**
 * Tests for the Product_Sync class.
 *
 * @group product-sync
 */
class Test_Product_Sync extends Base_Test_Case {

    /**
     * @var Product_Sync
     */
    private $product_sync;

    /**
     * @var SAPB1_API_Client|\PHPUnit\Framework\MockObject\MockObject
     */
    private $mock_sap_api_client;

    protected function setUp(): void {
        parent::setUp(); // Calls reset_all_mocks()

        $this->mock_sap_api_client = $this->createMock(SAPB1_API_Client::class);
        
        // Create Product_Sync instance and inject mock API client using reflection
        $this->product_sync = new Product_Sync();
        $reflection = new ReflectionClass($this->product_sync);
        $property = $reflection->getProperty('api_client');
        $property->setAccessible(true);
        $property->setValue($this->product_sync, $this->mock_sap_api_client);
    }

    /**
     * Helper method to get sample SAP product data.
     */
    private function get_sample_sap_product_data($overrides = []) {
        $default_data = [
            'ItemCode' => 'TEST001',
            'ItemName' => 'Test Product',
            'ItemPrice' => 99.99,
            'QuantityOnStock' => 10
        ];
        return array_merge($default_data, $overrides);
    }

    /**
     * Test successful product creation when product doesn't exist in WooCommerce.
     */
    public function test_create_new_product_success() {
        $sap_product_data = $this->get_sample_sap_product_data();
        $this->mock_sap_api_client->method('get_products')->willReturn([$sap_product_data]);

        $this->product_sync->sync_products();

        // Verify product was created
        global $mock_products;
        $this->assertCount(1, $mock_products);
        
        $created_product = reset($mock_products);
        $this->assertEquals('TEST001', $created_product['sku']);
        $this->assertEquals('Test Product', $created_product['name']);
        $this->assertEquals('99.99', $created_product['regular_price']);
        $this->assertEquals(10, $created_product['stock_quantity']);
        $this->assertTrue($created_product['manage_stock']);
        $this->assertEquals('publish', $created_product['status']);

        // Verify logs
        $this->assertLogContains('SAP B1 Product Sync: Created new product: Test Product (SKU: TEST001)');
        $this->assertLogContains('SAP B1 Product Sync: Synchronization process completed.');
    }

    /**
     * Test stock update for existing product.
     */
    public function test_update_existing_product_stock() {
        // Pre-create a product in mock store
        global $mock_products;
        $existing_product_id = 1001;
        $mock_products[$existing_product_id] = [
            'sku' => 'TEST001',
            'name' => 'Test Product',
            'regular_price' => '99.99',
            'manage_stock' => true,
            'stock_quantity' => 5, // Different from SAP
            'status' => 'publish'
        ];

        $sap_product_data = $this->get_sample_sap_product_data(['QuantityOnStock' => 15]);
        $this->mock_sap_api_client->method('get_products')->willReturn([$sap_product_data]);

        $this->product_sync->sync_products();

        // Verify stock was updated
        $this->assertEquals(15, $mock_products[$existing_product_id]['stock_quantity']);
        
        // Verify inventory transaction log
        $this->assertLogContains('INVENTORY_TRANSACTION - SKU: TEST001');
        $this->assertLogContains('Old Qty: 5, New Qty: 15');
    }

    /**
     * Test that existing product with same stock is skipped.
     */
    public function test_skip_product_with_same_stock() {
        // Pre-create a product with same stock as SAP
        global $mock_products;
        $existing_product_id = 1001;
        $mock_products[$existing_product_id] = [
            'sku' => 'TEST001',
            'name' => 'Test Product',
            'regular_price' => '99.99',
            'manage_stock' => true,
            'stock_quantity' => 10, // Same as SAP
            'status' => 'publish'
        ];

        $sap_product_data = $this->get_sample_sap_product_data();
        $this->mock_sap_api_client->method('get_products')->willReturn([$sap_product_data]);

        $this->product_sync->sync_products();

        // Verify stock wasn't changed
        $this->assertEquals(10, $mock_products[$existing_product_id]['stock_quantity']);
        
        // Verify skip log
        $this->assertLogContains('Stock for SKU TEST001 (ID: 1001) is already synchronized. Quantity: 10. Skipping.');
    }

    /**
     * Test handling of API failure (get_products returns false).
     */
    public function test_handle_api_failure() {
        $this->mock_sap_api_client->method('get_products')->willReturn(false);

        $this->product_sync->sync_products();

        // Verify error log
        $this->assertLogContains('SAP B1 Product Sync: Failed to fetch products from SAP B1 or no products returned. Synchronization aborted.');
        
        // Verify no products were created
        global $mock_products;
        $this->assertEmpty($mock_products);
    }

    /**
     * Test handling of empty product array from API.
     */
    public function test_handle_empty_products_array() {
        $this->mock_sap_api_client->method('get_products')->willReturn([]);

        $this->product_sync->sync_products();

        // Verify log
        $this->assertLogContains('SAP B1 Product Sync: No products to synchronize from SAP B1.');
        
        // Verify no products were created
        global $mock_products;
        $this->assertEmpty($mock_products);
    }

    /**
     * Test skipping product with missing required data.
     */
    public function test_skip_product_missing_required_data() {
        $incomplete_product = $this->get_sample_sap_product_data();
        unset($incomplete_product['ItemName']); // Remove required field

        $this->mock_sap_api_client->method('get_products')->willReturn([$incomplete_product]);

        $this->product_sync->sync_products();

        // Verify skip log
        $this->assertLogContains('SAP B1 Product Sync: Skipping product due to missing required data.');
        
        // Verify no products were created
        global $mock_products;
        $this->assertEmpty($mock_products);
    }

    /**
     * Test handling WC_Data_Exception during product creation.
     */
    public function test_handle_product_creation_exception() {
        $sap_product_data = $this->get_sample_sap_product_data();
        $this->mock_sap_api_client->method('get_products')->willReturn([$sap_product_data]);

        // Force WC_Product_Simple to throw exception on save
        WC_Product_Simple::$save_should_throw_exception = true;
        WC_Product_Simple::$save_exception_message = 'Test save error';

        $this->product_sync->sync_products();

        // Verify error log
        $this->assertLogContains('SAP B1 Product Sync: Error creating product Test Product (SKU: TEST001): Test save error');
    }

    /**
     * Test handling when product save returns 0 (failure).
     */
    public function test_handle_product_save_failure() {
        // This test would require modifying the mock to return 0 from save()
        // For now, we'll test the exception case above
        $this->assertTrue(true); // Placeholder
    }

    /**
     * Test handling when existing product cannot be retrieved.
     */
    public function test_handle_existing_product_retrieval_failure() {
        // Pre-create a product ID that exists in our lookup but not in mock store
        global $mock_products;
        $mock_products[1001] = [
            'sku' => 'TEST001',
            'name' => 'Test Product',
            'regular_price' => '99.99',
            'manage_stock' => true,
            'stock_quantity' => 5,
            'status' => 'publish'
        ];
        
        // Remove it to simulate retrieval failure
        unset($mock_products[1001]);

        $sap_product_data = $this->get_sample_sap_product_data();
        $this->mock_sap_api_client->method('get_products')->willReturn([$sap_product_data]);

        $this->product_sync->sync_products();

        // Since wc_get_product_id_by_sku won't find it, it should create new product
        $this->assertCount(1, $mock_products);
        $this->assertLogContains('SAP B1 Product Sync: Created new product: Test Product (SKU: TEST001)');
    }

    /**
     * Test processing multiple products.
     */
    public function test_process_multiple_products() {
        $sap_products = [
            $this->get_sample_sap_product_data(['ItemCode' => 'PROD001', 'ItemName' => 'Product 1']),
            $this->get_sample_sap_product_data(['ItemCode' => 'PROD002', 'ItemName' => 'Product 2']),
            $this->get_sample_sap_product_data(['ItemCode' => 'PROD003', 'ItemName' => 'Product 3'])
        ];
        
        $this->mock_sap_api_client->method('get_products')->willReturn($sap_products);

        $this->product_sync->sync_products();

        // Verify all products were created
        global $mock_products;
        $this->assertCount(3, $mock_products);
        
        // Verify logs for each product
        $this->assertLogContains('SAP B1 Product Sync: Created new product: Product 1 (SKU: PROD001)');
        $this->assertLogContains('SAP B1 Product Sync: Created new product: Product 2 (SKU: PROD002)');
        $this->assertLogContains('SAP B1 Product Sync: Created new product: Product 3 (SKU: PROD003)');
    }

    /**
     * Test handling WC_Data_Exception during stock update.
     */
    public function test_handle_stock_update_exception() {
        // Pre-create a product
        global $mock_products;
        $existing_product_id = 1001;
        $mock_products[$existing_product_id] = [
            'sku' => 'TEST001',
            'name' => 'Test Product',
            'regular_price' => '99.99',
            'manage_stock' => true,
            'stock_quantity' => 5,
            'status' => 'publish'
        ];

        $sap_product_data = $this->get_sample_sap_product_data(['QuantityOnStock' => 15]);
        $this->mock_sap_api_client->method('get_products')->willReturn([$sap_product_data]);

        // Force exception on save
        WC_Product_Simple::$save_should_throw_exception = true;
        WC_Product_Simple::$save_exception_message = 'Stock update failed';

        $this->product_sync->sync_products();

        // Verify error log
        $this->assertLogContains('SAP B1 Product Sync: Error updating stock for product Test Product (SKU: TEST001): Stock update failed');
    }
}

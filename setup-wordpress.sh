
#!/bin/bash

echo "Setting up WordPress for Replit..."

# Install Composer if not available
if ! command -v composer &> /dev/null; then
    echo "Installing Composer..."
    php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
    php composer-setup.php --install-dir=/usr/local/bin --filename=composer
    php -r "unlink('composer-setup.php');"
fi

# Create necessary directories with proper permissions
mkdir -p wp-content/uploads
mkdir -p wp-content/upgrade
mkdir -p wp-content/cache
chmod -R 755 wp-content/

# Set up database (SQLite for Replit)
if [ ! -f "wp-content/database/.ht.sqlite" ]; then
    mkdir -p wp-content/database
    touch wp-content/database/.ht.sqlite
    chmod 666 wp-content/database/.ht.sqlite
fi

# Install SQLite integration plugin for WordPress
if [ ! -d "wp-content/plugins/sqlite-database-integration" ]; then
    echo "Installing SQLite Database Integration plugin..."
    cd wp-content/plugins/
    wget -q https://downloads.wordpress.org/plugin/sqlite-database-integration.zip
    unzip -q sqlite-database-integration.zip
    rm sqlite-database-integration.zip
    cd ../../
fi

# Copy SQLite drop-in
if [ ! -f "wp-content/db.php" ]; then
    cp wp-content/plugins/sqlite-database-integration/db.copy wp-content/db.php
fi

echo "WordPress setup complete!"
echo "Run the server using the Run button or './start-server.sh'"


#!/bin/bash

echo "Setting up WordPress server environment..."

# Create necessary directories
mkdir -p /tmp/logs
mkdir -p /tmp/php-fpm
mkdir -p /tmp/apache2/run
mkdir -p /home/<USER>/workspace/wp-content/uploads

# Make sure log files exist and are writable
touch /tmp/logs/php-fpm.log
touch /tmp/logs/apache-error.log
touch /tmp/logs/apache-access.log

# Set proper permissions
chmod -R 755 /home/<USER>/workspace/wp-content/uploads 2>/dev/null || true

echo "Starting PHP-FPM..."
# Start PHP-FPM in background
php-fpm8.2 -y /home/<USER>/workspace/php-fpm.conf -F &
PHP_FPM_PID=$!

# Wait for PHP-FPM to start
sleep 3

echo "Starting Apache..."
# Start Apache in foreground
apache2 -f /home/<USER>/workspace/apache-wordpress.conf -D FOREGROUND &
APACHE_PID=$!

echo "WordPress server started successfully!"
echo "PHP-FPM PID: $PHP_FPM_PID"
echo "Apache PID: $APACHE_PID"
echo "Access your site at the Replit preview URL on port 5000"

# Function to handle shutdown
shutdown() {
    echo "Shutting down services..."
    kill $APACHE_PID 2>/dev/null
    kill $PHP_FPM_PID 2>/dev/null
    exit 0
}

# Trap signals for graceful shutdown
trap shutdown SIGTERM SIGINT

# Wait for processes
wait

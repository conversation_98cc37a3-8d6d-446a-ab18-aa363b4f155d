/* Custom Multiple Addresses Styles */

.cma-additional-addresses {
    margin-top: 30px;
    padding-top: 30px;
    border-top: 1px solid #e1e1e1;
}

.cma-billing-section,
.cma-shipping-section {
    margin-bottom: 30px;
}

.cma-billing-section h3,
.cma-shipping-section h3 {
    margin-bottom: 15px;
    color: #333;
    font-size: 1.2em;
}

.cma-address-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.cma-address-item {
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    background: #f9f9f9;
    transition: all 0.3s ease;
}

.cma-address-item:hover {
    border-color: #999;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.cma-address-item.cma-default {
    border-color: #0073aa;
    background: #f0f8ff;
}

.cma-default-badge {
    display: inline-block;
    background: #0073aa;
    color: white;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 0.8em;
    font-weight: bold;
    margin-bottom: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Disabled state for current default button */
.cma-set-default:disabled {
    background: #e0e0e0 !important;
    color: #999 !important;
    cursor: not-allowed !important;
    border-color: #ccc !important;
}

.cma-address-content {
    margin-bottom: 15px;
}

.cma-address-content address {
    font-style: normal;
    line-height: 1.5;
    color: #666;
}

.cma-address-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.cma-address-actions .button {
    font-size: 0.9em;
    padding: 5px 10px;
    height: auto;
    line-height: 1.4;
}

.cma-add-address {
    background: #0073aa;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 1em;
    transition: background 0.3s ease;
}

.cma-add-address:hover {
    background: #005a87;
}

/* Modal Styles */
.cma-modal {
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    overflow: auto;
}

.cma-modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 0;
    border: none;
    border-radius: 5px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.cma-modal-header {
    padding: 20px;
    background: #f1f1f1;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 5px 5px 0 0;
}

.cma-modal-header h3 {
    margin: 0;
    color: #333;
}

.cma-modal-close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.cma-modal-close:hover,
.cma-modal-close:focus {
    color: #000;
}

.cma-modal-body {
    padding: 20px;
}

.cma-modal-body .form-row {
    margin-bottom: 15px;
}

.cma-modal-body .form-row label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.cma-modal-body .form-row .required {
    color: #e74c3c;
}

.cma-modal-body .input-text,
.cma-modal-body select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 14px;
    box-sizing: border-box;
}

.cma-modal-body .input-text:focus,
.cma-modal-body select:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 5px rgba(0,115,170,0.3);
}

.cma-modal-body .form-row-first {
    width: 48%;
    float: left;
    clear: left;
}

.cma-modal-body .form-row-last {
    width: 48%;
    float: right;
    clear: right;
}

.cma-modal-body .form-row-wide {
    width: 100%;
    clear: both;
}

.cma-modal-body .form-row::after {
    content: "";
    display: table;
    clear: both;
}

.cma-modal-body button {
    margin-right: 10px;
    margin-top: 10px;
}

/* Checkout Selector Styles */
.cma-checkout-selector {
    margin-bottom: 20px;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 5px;
    clear: both;
}

.cma-checkout-selector h3 {
    margin: 0 0 15px 0;
    font-size: 1.1em;
    color: #333;
    font-weight: 600;
}

.cma-checkout-selector .form-row {
    margin-bottom: 0;
}

.cma-checkout-selector select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 14px;
    background: white;
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

.cma-checkout-selector select:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 5px rgba(0,115,170,0.3);
}

/* Highlight selected address */
.cma-checkout-selector.address-selected {
    background: #e8f4f8;
    border-color: #0073aa;
}

.cma-checkout-selector.address-selected h3 {
    color: #0073aa;
}

/* Responsive Design */
@media (max-width: 768px) {
    .cma-address-list {
        grid-template-columns: 1fr;
    }
    
    .cma-modal-content {
        width: 95%;
        margin: 2% auto;
    }
    
    .cma-modal-body .form-row-first,
    .cma-modal-body .form-row-last {
        width: 100%;
        float: none;
    }
    
    .cma-address-actions {
        flex-direction: column;
    }
    
    .cma-address-actions .button {
        width: 100%;
        text-align: center;
    }
}

/* Animation for address items */
.cma-address-item {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Loading states */
.cma-loading {
    opacity: 0.6;
    pointer-events: none;
}

.cma-loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Address update animation */
.cma-address-updated {
    animation: addressUpdate 2s ease-in-out;
}

.cma-address-updated address {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    padding: 10px;
    transition: all 0.3s ease;
}

@keyframes addressUpdate {
    0% {
        background: #fff3cd;
        border-color: #ffeaa7;
    }
    50% {
        background: #d1ecf1;
        border-color: #bee5eb;
    }
    100% {
        background: inherit;
        border-color: inherit;
    }
}

/* Notification styles */
.cma-notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    pointer-events: none;
}

.cma-notification {
    background: #28a745;
    color: white;
    padding: 12px 20px;
    border-radius: 4px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.5s ease;
    font-weight: 500;
    max-width: 300px;
    word-wrap: break-word;
}

.cma-notification.cma-show {
    transform: translateX(0);
    opacity: 1;
}

.cma-notification.cma-success {
    background: #28a745;
}

.cma-notification.cma-error {
    background: #dc3545;
}

/* WooCommerce form field compatibility */
.cma-modal-body .woocommerce-form-row {
    margin-bottom: 15px;
}

.cma-modal-body .woocommerce-form-row label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.cma-modal-body .woocommerce-form-row .required {
    color: #e74c3c;
}

.cma-modal-body .woocommerce-input-wrapper {
    position: relative;
}

.cma-modal-body .woocommerce-input-wrapper input,
.cma-modal-body .woocommerce-input-wrapper select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 14px;
    box-sizing: border-box;
}

.cma-modal-body .woocommerce-input-wrapper input:focus,
.cma-modal-body .woocommerce-input-wrapper select:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 5px rgba(0,115,170,0.3);
}

/* State field specific styling */
.cma-modal-body #cma_state_field {
    clear: none;
}

.cma-modal-body #cma_state {
    width: 100%;
}

/* Ensure proper form row layout */
.cma-modal-body .form-row-first,
.cma-modal-body .woocommerce-form-row--first {
    width: 48%;
    float: left;
    clear: left;
}

.cma-modal-body .form-row-last,
.cma-modal-body .woocommerce-form-row--last {
    width: 48%;
    float: right;
    clear: right;
}

.cma-modal-body .form-row-wide,
.cma-modal-body .woocommerce-form-row--wide {
    width: 100%;
    clear: both;
}

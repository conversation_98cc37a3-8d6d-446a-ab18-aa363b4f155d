jQuery(document).ready(function($) {
    'use strict';

    // Check if PrimalAddresses is available
    var usePrimalAddresses = (typeof window.PrimalAddresses !== 'undefined');

    if (usePrimalAddresses) {
        console.log('🏠 PrimalAddresses integration enabled');
        // Enable debug mode if needed
        if (window.location.search.includes('cma_debug=1')) {
            window.PrimalAddresses.enableDebug();
        }
    }

    // Modal functionality
    var modal = $('#cma-address-modal');
    var form = $('#cma-address-form');
    
    // Open modal for adding new address
    $('.cma-add-address').on('click', function() {
        var type = $(this).data('type');
        $('#cma-address-type').val(type);
        $('#cma-address-id').val('');
        $('#cma-modal-title').text(cma_ajax.add_address_text + ' (' + type.charAt(0).toUpperCase() + type.slice(1) + ')');
        form[0].reset();

        // Reset state field to ensure it's properly initialized
        $('#cma_state').empty().append('<option value="">' + 'Select a state / county...' + '</option>');

        modal.show();

        // Initialize WooCommerce country/state functionality after modal is shown
        initializeCountryStateFields();
    });
    
    // Open modal for editing address
    $(document).on('click', '.cma-edit-address', function() {
        var addressId = $(this).data('address-id');
        var type = $(this).data('type');

        $('#cma-address-type').val(type);
        $('#cma-address-id').val(addressId);
        $('#cma-modal-title').text('Edit ' + type.charAt(0).toUpperCase() + type.slice(1) + ' Address');

        // Get address data via AJAX
        $.ajax({
            url: cma_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'cma_get_address',
                nonce: cma_ajax.nonce,
                address_type: type,
                address_id: addressId
            },
            success: function(response) {
                if (response.success) {
                    // Populate form fields with existing data
                    populateAddressForm(response.data);
                    modal.show();

                    // Initialize country/state functionality after populating data
                    initializeCountryStateFields();
                } else {
                    alert('Error loading address: ' + response.data.message);
                }
            },
            error: function() {
                alert('Error loading address data');
            }
        });
    });
    
    // Close modal
    $('.cma-modal-close').on('click', function() {
        modal.hide();
    });
    
    // Close modal when clicking outside
    $(window).on('click', function(event) {
        if (event.target === modal[0]) {
            modal.hide();
        }
    });
    
    // Handle form submission
    form.on('submit', function(e) {
        e.preventDefault();
        
        var formData = {
            action: 'cma_add_address',
            nonce: cma_ajax.nonce,
            address_type: $('#cma-address-type').val(),
            address_id: $('#cma-address-id').val(),
            first_name: $('#cma_first_name').val(),
            last_name: $('#cma_last_name').val(),
            company: $('#cma_company').val(),
            address_1: $('#cma_address_1').val(),
            address_2: $('#cma_address_2').val(),
            city: $('#cma_city').val(),
            state: $('#cma_state').val(),
            postcode: $('#cma_postcode').val(),
            country: $('#cma_country').val()
        };
        
        $.ajax({
            url: cma_ajax.ajax_url,
            type: 'POST',
            data: formData,
            beforeSend: function() {
                form.find('button[type="submit"]').prop('disabled', true).text('Saving...');
            },
            success: function(response) {
                if (response.success) {
                    modal.hide();
                    location.reload(); // Reload to show updated addresses
                } else {
                    alert('Error: ' + response.data.message);
                }
            },
            error: function() {
                alert('An error occurred. Please try again.');
            },
            complete: function() {
                form.find('button[type="submit"]').prop('disabled', false).text('Save Address');
            }
        });
    });
    
    // Delete address
    $(document).on('click', '.cma-delete-address', function() {
        if (!confirm(cma_ajax.delete_confirm)) {
            return;
        }
        
        var addressId = $(this).data('address-id');
        var type = $(this).data('type');
        var addressItem = $(this).closest('.cma-address-item');
        
        $.ajax({
            url: cma_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'cma_delete_address',
                nonce: cma_ajax.nonce,
                address_type: type,
                address_id: addressId
            },
            beforeSend: function() {
                addressItem.css('opacity', '0.5');
            },
            success: function(response) {
                if (response.success) {
                    addressItem.fadeOut(300, function() {
                        $(this).remove();
                    });
                } else {
                    alert('Error: ' + response.data.message);
                    addressItem.css('opacity', '1');
                }
            },
            error: function() {
                alert('An error occurred. Please try again.');
                addressItem.css('opacity', '1');
            }
        });
    });
    
    // Set default address
    $(document).on('click', '.cma-set-default', function() {
        // Don't process if button is disabled (already default)
        if ($(this).prop('disabled')) {
            return false;
        }

        var addressId = $(this).data('address-id');
        var type = $(this).data('type');
        var button = $(this);
        var addressItem = button.closest('.cma-address-item');
        var sectionClass = type === 'billing' ? '.cma-billing-section' : '.cma-shipping-section';

        $.ajax({
            url: cma_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'cma_set_default',
                nonce: cma_ajax.nonce,
                address_type: type,
                address_id: addressId
            },
            beforeSend: function() {
                button.prop('disabled', true).text('Setting...');
            },
            success: function(response) {
                if (response.success) {
                    // Remove default status from all addresses of this type
                    $(sectionClass + ' .cma-address-item').each(function() {
                        $(this).removeClass('cma-default');
                        $(this).find('.cma-default-badge').remove();
                        $(this).find('.cma-set-default').prop('disabled', false).text('Set as Default');
                    });

                    // Add default status to this address
                    addressItem.addClass('cma-default');

                    // Add default badge if it doesn't exist
                    if (addressItem.find('.cma-default-badge').length === 0) {
                        addressItem.prepend('<div class="cma-default-badge">Default</div>');
                    }

                    // Update button to show current default state
                    button.prop('disabled', true).text('Current Default');

                    // Update WooCommerce default address fields immediately
                    if (response.data.address_data && response.data.address_type) {
                        updateWooCommerceDefaultFields(response.data.address_type, response.data.address_data);
                    }

                    // Show success message
                    showSuccessMessage('Default address updated successfully!');
                } else {
                    alert('Error: ' + response.data.message);
                    button.prop('disabled', false).text('Set as Default');
                }
            },
            error: function() {
                alert('An error occurred. Please try again.');
                button.prop('disabled', false).text('Set as Default');
            }
        });
    });
    
    // Checkout address selector functionality
    $('#cma_billing_address, #cma_shipping_address').on('change', function() {
        var $selector = $(this);
        var addressId = $selector.val();
        var type = $selector.attr('id').includes('billing') ? 'billing' : 'shipping';
        var $container = $selector.closest('.cma-checkout-selector');

        if (addressId) {
            // Add visual feedback
            $container.addClass('address-selected');

            // Get address details and populate form fields
            $.ajax({
                url: cma_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'cma_get_address',
                    nonce: cma_ajax.nonce,
                    address_type: type,
                    address_id: addressId
                },
                beforeSend: function() {
                    $selector.prop('disabled', true);
                },
                success: function(response) {
                    if (response.success) {
                        populateCheckoutFields(type, response.data);
                    } else {
                        console.log('Error: ' + response.data.message);
                    }
                },
                error: function() {
                    console.log('Error loading address data');
                },
                complete: function() {
                    $selector.prop('disabled', false);
                }
            });
        } else {
            // Remove visual feedback and restore default values
            $container.removeClass('address-selected');
            clearCheckoutFields(type);
        }
    });

    // Function to populate checkout form fields
    function populateCheckoutFields(type, addressData) {
        var prefix = type + '_';

        // Map of our address fields to WooCommerce checkout fields
        var fieldMapping = {
            'first_name': prefix + 'first_name',
            'last_name': prefix + 'last_name',
            'company': prefix + 'company',
            'address_1': prefix + 'address_1',
            'address_2': prefix + 'address_2',
            'city': prefix + 'city',
            'state': prefix + 'state',
            'postcode': prefix + 'postcode',
            'country': prefix + 'country'
        };

        console.log('Populating ' + type + ' fields with:', addressData);

        // Populate each field
        $.each(fieldMapping, function(ourField, wcField) {
            if (addressData[ourField]) {
                var $field = $('#' + wcField);
                if ($field.length) {
                    console.log('Setting field ' + wcField + ' to: ' + addressData[ourField]);
                    $field.val(addressData[ourField]);

                    // For select fields (like country/state), trigger change event
                    if ($field.is('select')) {
                        $field.trigger('change');
                    }
                } else {
                    console.log('Field not found: ' + wcField);
                }
            }
        });

        // Small delay before triggering checkout update to ensure all fields are populated
        setTimeout(function() {
            $('body').trigger('update_checkout');
        }, 100);
    }

    // Function to clear checkout fields (restore defaults)
    function clearCheckoutFields(type) {
        console.log('Clearing ' + type + ' fields, restoring defaults');

        // Trigger checkout update to restore default values
        // WooCommerce will automatically populate with user's default address
        $('body').trigger('update_checkout');

        // Alternative: You could also reload the page section
        // location.reload();
    }

    // Function to update WooCommerce default address display immediately
    function updateWooCommerceDefaultFields(type, addressData) {
        console.log('Updating WooCommerce default ' + type + ' address display:', addressData);

        // Update the address display in the default address section
        updateDefaultAddressDisplay(type, addressData);
    }

    // Function to update the default address display section
    function updateDefaultAddressDisplay(type, addressData) {
        console.log('Looking for ' + type + ' address display to update');

        // Find the specific WooCommerce address section for this type
        // The structure is: .woocommerce-Address containing h2 with the address title
        var $addressSections = $('.woocommerce-Address');
        var $targetSection = null;

        // Find the correct address section by looking for the title
        $addressSections.each(function() {
            var $section = $(this);
            var $title = $section.find('h2');
            var titleText = $title.text().toLowerCase();

            // Check if this section matches our address type
            if ((type === 'billing' && titleText.includes('billing')) ||
                (type === 'shipping' && titleText.includes('shipping'))) {
                $targetSection = $section;
                return false; // Break the loop
            }
        });

        if ($targetSection) {
            var $addressElement = $targetSection.find('address');

            if ($addressElement.length) {
                // Format the address for display (WooCommerce style)
                var formattedAddress = '';

                if (addressData.first_name || addressData.last_name) {
                    formattedAddress += (addressData.first_name + ' ' + addressData.last_name).trim() + '<br>';
                }
                if (addressData.company) {
                    formattedAddress += addressData.company + '<br>';
                }
                if (addressData.address_1) {
                    formattedAddress += addressData.address_1 + '<br>';
                }
                if (addressData.address_2) {
                    formattedAddress += addressData.address_2 + '<br>';
                }

                var cityLine = '';
                if (addressData.city) {
                    cityLine += addressData.city;
                }
                if (addressData.state) {
                    cityLine += (cityLine ? ', ' : '') + addressData.state;
                }
                if (addressData.postcode) {
                    cityLine += (cityLine ? ' ' : '') + addressData.postcode;
                }
                if (cityLine) {
                    formattedAddress += cityLine + '<br>';
                }

                if (addressData.country) {
                    // Try to get country name from WooCommerce countries list if available
                    var countryName = addressData.country;
                    if (typeof wc_country_select_params !== 'undefined' &&
                        wc_country_select_params.countries &&
                        wc_country_select_params.countries[addressData.country]) {
                        countryName = wc_country_select_params.countries[addressData.country];
                    }
                    formattedAddress += countryName;
                }

                // Update the address display
                if (formattedAddress) {
                    console.log('Updating ' + type + ' address display with:', formattedAddress);
                    $addressElement.html(formattedAddress);

                    // Add a visual indicator that the address was updated
                    $targetSection.addClass('cma-address-updated');
                    setTimeout(function() {
                        $targetSection.removeClass('cma-address-updated');
                    }, 2000);
                } else {
                    console.log('No formatted address content to display');
                }
            } else {
                console.log('Address element not found in target section');
            }
        } else {
            console.log('Target address section not found for type: ' + type);
        }
    }

    // Function to show a non-intrusive success message
    function showSuccessMessage(message) {
        // Check if notification container exists, create if not
        var $notificationContainer = $('.cma-notification-container');
        if (!$notificationContainer.length) {
            $notificationContainer = $('<div class="cma-notification-container"></div>');
            $('body').append($notificationContainer);
        }

        // Create notification element
        var $notification = $('<div class="cma-notification cma-success"></div>');
        $notification.text(message);

        // Add to container
        $notificationContainer.append($notification);

        // Show with animation
        setTimeout(function() {
            $notification.addClass('cma-show');
        }, 10);

        // Auto-remove after delay
        setTimeout(function() {
            $notification.removeClass('cma-show');
            setTimeout(function() {
                $notification.remove();
            }, 500);
        }, 3000);
    }

    // Function to populate address form with existing data
    function populateAddressForm(addressData) {
        $('#cma_first_name').val(addressData.first_name || '');
        $('#cma_last_name').val(addressData.last_name || '');
        $('#cma_company').val(addressData.company || '');
        $('#cma_address_1').val(addressData.address_1 || '');
        $('#cma_address_2').val(addressData.address_2 || '');
        $('#cma_city').val(addressData.city || '');
        $('#cma_postcode').val(addressData.postcode || '');
        $('#cma_country').val(addressData.country || '');

        // Set state after country is set and state field is updated
        setTimeout(function() {
            $('#cma_state').val(addressData.state || '');
        }, 100);
    }

    // Function to initialize WooCommerce country/state functionality
    function initializeCountryStateFields() {
        var $countryField = $('#cma_country');
        var $stateField = $('#cma_state');

        // Handle country change to update states
        $countryField.on('change', function() {
            var country = $(this).val();
            updateStateField(country, $stateField);
        });

        // Initialize with current country selection
        if ($countryField.val()) {
            updateStateField($countryField.val(), $stateField);
        }
    }

    // Function to update state field based on selected country
    function updateStateField(country, $stateField) {
        if (!country) {
            $stateField.empty().append('<option value="">' + 'Select a state / county...' + '</option>');
            return;
        }

        // Get states for the selected country from WooCommerce
        $.ajax({
            url: cma_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'cma_get_states',
                nonce: cma_ajax.nonce,
                country: country
            },
            success: function(response) {
                if (response.success && response.data) {
                    var states = response.data;

                    // Clear existing options
                    $stateField.empty();
                    $stateField.append('<option value="">' + 'Select a state / county...' + '</option>');

                    // Add state options
                    $.each(states, function(code, name) {
                        $stateField.append('<option value="' + code + '">' + name + '</option>');
                    });
                } else {
                    // No states available, show default option
                    $stateField.empty();
                    $stateField.append('<option value="">' + 'Select a state / county...' + '</option>');
                }
            },
            error: function() {
                // Fallback - show default option
                $stateField.empty();
                $stateField.append('<option value="">' + 'Select a state / county...' + '</option>');
            }
        });
    }

    // Expose functions globally for PrimalAddresses integration
    window.populateCheckoutFields = populateCheckoutFields;
    window.updateWooCommerceDefaultFields = updateWooCommerceDefaultFields;
    window.updateDefaultAddressDisplay = updateDefaultAddressDisplay;
    window.clearCheckoutFields = clearCheckoutFields;
});

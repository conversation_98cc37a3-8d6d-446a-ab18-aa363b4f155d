/**
 * PrimalAddresses - Global Address Management Library
 * Provides REST API access to address management functionality
 * Extracted from PrimalComCart for dedicated address operations
 */

(function() {
    'use strict';

    const PrimalAddresses = {

        // API endpoints
        endpoints: {
            addresses: (typeof primal_addresses_settings !== 'undefined' ? primal_addresses_settings.api_url : '/wp-json/primalcom/v1/') + 'addresses'
        },

        // Configuration options
        config: {
            debugMode: false
        },

        // Debug logging
        log: function(message, data) {
            if (this.config.debugMode && console && console.log) {
                console.log('🏠 PrimalAddresses:', message, data || '');
            }
        },

        // Check if user is logged in
        isUserLoggedIn: function() {
            return document.body.classList.contains('logged-in');
        },

        // Generic API request method
        apiRequest: function(method, endpoint, data) {
            const self = this;
            
            const requestOptions = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': (typeof primal_addresses_settings !== 'undefined' ? primal_addresses_settings.nonce : '')
                }
            };

            if (data && (method === 'POST' || method === 'PUT')) {
                requestOptions.body = JSON.stringify(data);
            }

            self.log('API Request', { method, endpoint, data });

            return fetch(endpoint, requestOptions)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(result => {
                    self.log('API Response', result);
                    
                    if (result.success === false) {
                        throw new Error(result.error || 'API request failed');
                    }
                    
                    return result;
                })
                .catch(error => {
                    self.log('API Error', error);
                    throw error;
                });
        },

        // Get all addresses for current user
        getAddresses: function() {
            if (!this.isUserLoggedIn()) {
                return Promise.reject(new Error('User must be logged in to get addresses'));
            }

            return this.apiRequest('GET', this.endpoints.addresses)
                .then(response => {
                    if (response.success && response.data) {
                        // Convert objects to arrays without address_id
                        const billingArray = Object.values(response.data.billing || {});
                        const shippingArray = Object.values(response.data.shipping || {});

                        return {
                            success: true,
                            billing: billingArray,
                            shipping: shippingArray
                        };
                    }

                    return response;
                });
        },

        // Get billing addresses only (returns array)
        getBillingAddresses: function() {
            if (!this.isUserLoggedIn()) {
                return Promise.reject(new Error('User must be logged in to get billing addresses'));
            }

            this.log('Getting billing addresses');

            return this.getAddresses()
                .then(response => {
                    if (response.success && response.billing) {
                        return response.billing;
                    } else {
                        return [];
                    }
                });
        },

        // Get shipping addresses only (returns array)
        getShippingAddresses: function() {
            if (!this.isUserLoggedIn()) {
                return Promise.reject(new Error('User must be logged in to get shipping addresses'));
            }

            this.log('Getting shipping addresses');

            return this.getAddresses()
                .then(response => {
                    if (response.success && response.shipping) {
                        return response.shipping;
                    } else {
                        return [];
                    }
                });
        },

        // Add new address (requires login)
        addAddress: function(type, addressData) {
            if (!this.isUserLoggedIn()) {
                return Promise.reject(new Error('User must be logged in to add addresses'));
            }

            const data = {
                type: type,
                address_data: addressData
            };

            return this.apiRequest('POST', this.endpoints.addresses, data);
        },

        // Update existing address (requires login)
        updateAddress: function(type, addressId, addressData) {
            if (!this.isUserLoggedIn()) {
                return Promise.reject(new Error('User must be logged in to update addresses'));
            }

            const endpoint = this.endpoints.addresses + '/' + type + '/' + addressId;
            const data = {
                address_data: addressData
            };

            return this.apiRequest('PUT', endpoint, data);
        },

        // Delete address (requires login)
        deleteAddress: function(type, addressId) {
            if (!this.isUserLoggedIn()) {
                return Promise.reject(new Error('User must be logged in to delete addresses'));
            }

            const endpoint = this.endpoints.addresses + '/' + type + '/' + addressId;
            return this.apiRequest('DELETE', endpoint);
        },

        // Set default address (requires login)
        setDefaultAddress: function(type, addressId) {
            if (!this.isUserLoggedIn()) {
                return Promise.reject(new Error('User must be logged in to set default address'));
            }

            const endpoint = this.endpoints.addresses + '/' + type + '/' + addressId + '/default';
            return this.apiRequest('POST', endpoint);
        },

        // Utility: Format address for display
        formatAddress: function(address) {
            let formatted = '';
            
            if (address.first_name || address.last_name) {
                formatted += (address.first_name + ' ' + address.last_name).trim() + '\n';
            }
            
            if (address.company) {
                formatted += address.company + '\n';
            }
            
            if (address.address_1) {
                formatted += address.address_1 + '\n';
            }
            
            if (address.address_2) {
                formatted += address.address_2 + '\n';
            }
            
            if (address.city) {
                formatted += address.city;
                if (address.state) {
                    formatted += ', ' + address.state;
                }
                if (address.postcode) {
                    formatted += ' ' + address.postcode;
                }
                formatted += '\n';
            }
            
            if (address.country) {
                formatted += address.country;
            }
            
            return formatted.trim();
        },

        // Utility: Get address summary (one line)
        getAddressSummary: function(address) {
            let summary = '';
            
            if (address.first_name || address.last_name) {
                summary += (address.first_name + ' ' + address.last_name).trim();
            }
            
            if (address.address_1) {
                if (summary) summary += ' - ';
                summary += address.address_1;
            }
            
            if (address.city) {
                if (summary) summary += ', ';
                summary += address.city;
            }
            
            return summary || 'Address';
        },

        // Utility: Validate address data
        validateAddress: function(addressData, type) {
            const required = ['first_name', 'last_name', 'address_1', 'city', 'postcode', 'country'];
            
            // Email is required for billing addresses
            if (type === 'billing') {
                required.push('email');
            }
            
            for (let field of required) {
                if (!addressData[field] || addressData[field].trim() === '') {
                    return {
                        valid: false,
                        message: `Field ${field} is required`
                    };
                }
            }
            
            // Validate email format if provided
            if (addressData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(addressData.email)) {
                return {
                    valid: false,
                    message: 'Invalid email format'
                };
            }
            
            return { valid: true };
        },

        // Enable debug mode
        enableDebug: function() {
            this.config.debugMode = true;
            this.log('Debug mode enabled');
        },

        // Disable debug mode
        disableDebug: function() {
            this.config.debugMode = false;
        },

        // ===== SHIPPING CHECKBOX UTILITIES =====

        // Toggle "Ship to different address" checkbox
        toggleShipToDifferentAddress: function(enable) {
            if (typeof window.CMAShipping !== 'undefined') {
                return window.CMAShipping.toggleShipToDifferentAddress(enable);
            }

            // Fallback direct manipulation
            const checkbox = document.getElementById('ship-to-different-address-checkbox');
            if (checkbox) {
                checkbox.checked = enable;
                checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                this.log('Ship to different address toggled:', enable);
                return true;
            }

            this.log('Ship to different address checkbox not found', null, true);
            return false;
        },

        // Check if shipping to different address is enabled
        isShippingToDifferentAddress: function() {
            if (typeof window.CMAShipping !== 'undefined') {
                return window.CMAShipping.isShippingToDifferentAddress();
            }

            // Fallback direct check
            const checkbox = document.getElementById('ship-to-different-address-checkbox');
            return checkbox ? checkbox.checked : false;
        },

        // Enable shipping and select a specific address
        enableShippingAndSelectAddress: function(addressId) {
            if (typeof window.CMAShipping !== 'undefined') {
                window.CMAShipping.enableAndSelectShippingAddress(addressId);
                this.log('Enabled shipping and selected address:', addressId);
                return true;
            }

            // Fallback: just enable the checkbox
            this.log('CMAShipping not available, falling back to checkbox toggle');
            return this.toggleShipToDifferentAddress(true);
        },

        // Disable shipping to different address
        disableShippingToDifferentAddress: function() {
            if (typeof window.CMAShipping !== 'undefined') {
                window.CMAShipping.disableShippingToDifferentAddress();
                this.log('Disabled shipping to different address');
                return true;
            }

            // Fallback: just disable the checkbox
            return this.toggleShipToDifferentAddress(false);
        },

        // Smart billing address selection
        selectBillingAddress: function(addressIdentifier) {
            if (!this.isUserLoggedIn()) {
                return Promise.reject(new Error('User must be logged in to select billing address'));
            }

            this.log('Smart billing address selection:', addressIdentifier);

            return this.getBillingAddresses()
                .then(addresses => {
                    // Smart matching: find address by ID, name, or index
                    const selectedAddress = this.findAddressByIdentifier(addresses, addressIdentifier);

                    if (!selectedAddress) {
                        throw new Error(`Billing address not found: ${addressIdentifier}`);
                    }

                    // Find the index of the selected address
                    const addressIndex = addresses.indexOf(selectedAddress);

                    // Use a different approach - directly update the address via REST API
                    return this.setDefaultAddressByIndex('billing', addressIndex, selectedAddress)
                        .then(response => {
                            // Return both response and index for UI update
                            return { ...response, addressIndex: addressIndex };
                        });
                })
                .then(response => {
                    this.log('✅ Billing address selected successfully');

                    // Update the UI using existing dropdown handler (smart approach)
                    this.refreshAddressUI('billing', response.address, response.addressIndex);

                    return {
                        success: true,
                        message: 'Billing address selected',
                        type: 'billing',
                        address: response.address
                    };
                })
                .catch(error => {
                    this.log('❌ Billing address selection failed:', error.message);
                    throw error;
                });
        },

        // Smart shipping address selection with automatic toggle
        selectShippingAddress: function(addressIdentifier) {
            if (!this.isUserLoggedIn()) {
                return Promise.reject(new Error('User must be logged in to select shipping address'));
            }

            this.log('Smart shipping address selection:', addressIdentifier);

            return this.getShippingAddresses()
                .then(addresses => {
                    // Smart matching: find address by ID, name, or index
                    const selectedAddress = this.findAddressByIdentifier(addresses, addressIdentifier);

                    if (!selectedAddress) {
                        throw new Error(`Shipping address not found: ${addressIdentifier}`);
                    }

                    // Find the index of the selected address
                    const addressIndex = addresses.indexOf(selectedAddress);

                    // Only enable shipping if it's not already enabled (prevents extra rendering)
                    if (!this.isShippingToDifferentAddress()) {
                        this.enableShippingAndSelectAddress(addressIndex);
                    }

                    // Set as default shipping address
                    return this.setDefaultAddressByIndex('shipping', addressIndex, selectedAddress)
                        .then(response => {
                            // Return both response and index for UI update
                            return { ...response, addressIndex: addressIndex };
                        });
                })
                .then(response => {
                    this.log('✅ Shipping address selected successfully');

                    // Update the UI using existing dropdown handler (smart approach)
                    this.refreshAddressUI('shipping', response.address, response.addressIndex);

                    return {
                        success: true,
                        message: 'Shipping address selected and enabled',
                        type: 'shipping',
                        address: response.address,
                        shipping_enabled: true
                    };
                })
                .catch(error => {
                    this.log('❌ Shipping address selection failed:', error.message);
                    throw error;
                });
        },

        // Helper function for smart address matching
        findAddressByIdentifier: function(addresses, identifier) {
            if (!addresses || addresses.length === 0) {
                return null;
            }

            // Method 1: Match by address_id or id
            if (typeof identifier === 'string') {
                let found = addresses.find(addr =>
                    (addr.address_id && addr.address_id === identifier) ||
                    (addr.id && addr.id === identifier)
                );
                if (found) return found;

                // Method 2: Match by partial name (first_name + last_name)
                const searchTerm = identifier.toLowerCase();
                found = addresses.find(addr => {
                    const fullName = `${addr.first_name || ''} ${addr.last_name || ''}`.toLowerCase().trim();
                    return fullName.includes(searchTerm) || searchTerm.includes(fullName);
                });
                if (found) return found;

                // Method 3: Match by address line
                found = addresses.find(addr => {
                    const address1 = (addr.address_1 || '').toLowerCase();
                    return address1.includes(searchTerm) || searchTerm.includes(address1);
                });
                if (found) return found;
            }

            // Method 4: Match by array index
            if (typeof identifier === 'number' && identifier >= 0 && identifier < addresses.length) {
                return addresses[identifier];
            }

            // Method 5: Match by string number ("0", "1", "2")
            if (typeof identifier === 'string' && /^\d+$/.test(identifier)) {
                const index = parseInt(identifier, 10);
                if (index >= 0 && index < addresses.length) {
                    return addresses[index];
                }
            }

            // Method 6: Special keywords
            if (typeof identifier === 'string') {
                const keyword = identifier.toLowerCase();
                if (keyword === 'default' || keyword === 'first') {
                    return addresses.find(addr => addr.is_default) || addresses[0];
                }
                if (keyword === 'last') {
                    return addresses[addresses.length - 1];
                }
            }

            return null;
        },

        // Set default address by array index (works without address IDs)
        setDefaultAddressByIndex: function(type, addressIndex, addressData) {
            if (!this.isUserLoggedIn()) {
                return Promise.reject(new Error('User must be logged in to set default address'));
            }

            this.log(`Setting default ${type} address by index:`, addressIndex);

            // Create a new REST endpoint call that works with address data directly
            const endpoint = this.endpoints.addresses + '/set-default-by-data';

            return this.apiRequest('POST', endpoint, {
                type: type,
                address_index: addressIndex,
                address_data: addressData
            })
            .then(response => {
                if (response.success) {
                    this.log(`✅ Default ${type} address set successfully`);
                    return response;
                } else {
                    throw new Error(response.error || `Failed to set default ${type} address`);
                }
            });
        },

        // Refresh the address UI after changes (direct function call - no dropdown trigger)
        refreshAddressUI: function(type, selectedAddress, addressIndex) {
            this.log(`Refreshing ${type} address UI directly...`);

            try {
                // Instead of triggering dropdown (which causes page refresh),
                // directly call the same function that the dropdown handler calls
                if (typeof window.populateCheckoutFields === 'function' && selectedAddress) {
                    this.log(`Calling populateCheckoutFields directly for ${type}...`);
                    window.populateCheckoutFields(type, selectedAddress);
                    this.log('✅ Address UI refresh completed - direct function call');
                } else {
                    this.log('❌ populateCheckoutFields function not available');
                }

            } catch (error) {
                this.log('❌ Error refreshing address UI:', error.message);
            }
        },

        // SpeechAI Integration: Voice-activated address selection
        selectAddressByVoice: function(voiceQuery, type = 'auto') {
            this.log('🎤 Voice address selection:', voiceQuery, 'Type:', type);

            if (!this.isUserLoggedIn()) {
                return Promise.reject(new Error('User must be logged in for voice address selection'));
            }

            try {
                // Clean and normalize the voice query
                const cleanQuery = this.normalizeVoiceQuery(voiceQuery);
                this.log('Normalized voice query:', cleanQuery);

                // Auto-detect address type if not specified
                if (type === 'auto') {
                    type = this.detectAddressTypeFromVoice(cleanQuery);
                    this.log('Auto-detected address type:', type);
                }

                // Validate address type
                if (!['billing', 'shipping'].includes(type)) {
                    throw new Error(`Invalid address type: ${type}. Must be 'billing', 'shipping', or 'auto'.`);
                }

                // Use the existing smart selection with the voice query
                if (type === 'billing') {
                    return this.selectBillingAddress(cleanQuery);
                } else {
                    return this.selectShippingAddress(cleanQuery);
                }

            } catch (error) {
                this.log('❌ Voice address selection failed:', error.message);
                return Promise.reject(error);
            }
        },

        // Normalize voice input for better matching
        normalizeVoiceQuery: function(voiceQuery) {
            if (!voiceQuery || typeof voiceQuery !== 'string') {
                return '';
            }

            return voiceQuery
                .toLowerCase()
                .trim()
                // Remove common voice command prefixes
                .replace(/^(select|choose|use|set|pick)\s+/i, '')
                .replace(/^(my|the)\s+/i, '')
                .replace(/\s+(address|location)$/i, '')
                // Normalize ordinal numbers
                .replace(/\b(1st|first)\b/gi, 'first')
                .replace(/\b(2nd|second)\b/gi, 'second')
                .replace(/\b(3rd|third)\b/gi, 'third')
                .replace(/\b(4th|fourth)\b/gi, 'fourth')
                .replace(/\b(5th|fifth)\b/gi, 'fifth')
                // Normalize common address terms
                .replace(/\b(home|house|residential)\b/gi, 'home')
                .replace(/\b(work|office|business)\b/gi, 'office')
                .replace(/\b(default|main|primary)\b/gi, 'default')
                .trim();
        },

        // Auto-detect address type from voice query
        detectAddressTypeFromVoice: function(query) {
            const lowerQuery = query.toLowerCase();

            // Shipping indicators
            const shippingKeywords = [
                'shipping', 'ship to', 'deliver to', 'delivery', 'send to',
                'different address', 'another address', 'alternate address'
            ];

            // Billing indicators
            const billingKeywords = [
                'billing', 'bill to', 'payment', 'invoice', 'charge to'
            ];

            // Check for explicit shipping keywords
            for (const keyword of shippingKeywords) {
                if (lowerQuery.includes(keyword)) {
                    return 'shipping';
                }
            }

            // Check for explicit billing keywords
            for (const keyword of billingKeywords) {
                if (lowerQuery.includes(keyword)) {
                    return 'billing';
                }
            }

            // Default to billing if no clear indicators
            return 'billing';
        },

        // SpeechAI Helper: Get voice command examples
        getVoiceCommandExamples: function() {
            return {
                billing: [
                    "select my home address for billing",
                    "use my office address",
                    "choose the first billing address",
                    "set default billing address",
                    "pick my main address"
                ],
                shipping: [
                    "ship to my office address",
                    "deliver to my home address",
                    "use different shipping address",
                    "send to my second address",
                    "ship to John's address"
                ],
                auto: [
                    "use my home address",
                    "select office location",
                    "choose the first address",
                    "pick my default address"
                ]
            };
        },

        // SpeechAI Helper: Test voice query matching
        testVoiceQuery: function(voiceQuery, type = 'auto') {
            this.log('🧪 Testing voice query:', voiceQuery);

            const cleanQuery = this.normalizeVoiceQuery(voiceQuery);
            const detectedType = type === 'auto' ? this.detectAddressTypeFromVoice(cleanQuery) : type;

            return {
                original: voiceQuery,
                normalized: cleanQuery,
                detectedType: detectedType,
                willMatch: cleanQuery.length > 0
            };
        },

        // SpeechAI Integration: Batch voice commands (for testing)
        processVoiceCommands: function(commands) {
            this.log('🎤 Processing batch voice commands:', commands);

            const results = [];
            const promises = commands.map((command, index) => {
                return new Promise((resolve) => {
                    // Add delay between commands to avoid overwhelming the system
                    setTimeout(() => {
                        this.selectAddressByVoice(command.query, command.type || 'auto')
                            .then(response => {
                                results[index] = { success: true, command, response };
                                resolve(results[index]);
                            })
                            .catch(error => {
                                results[index] = { success: false, command, error: error.message };
                                resolve(results[index]);
                            });
                    }, index * 500); // 500ms delay between commands
                });
            });

            return Promise.all(promises).then(() => results);
        },

        // REST API: Toggle shipping to different address
        toggleShippingAPI: function(enable, addressId = null) {
            if (!this.isUserLoggedIn()) {
                return Promise.reject(new Error('User must be logged in to toggle shipping'));
            }

            const data = {
                enable: enable
            };

            if (addressId) {
                data.address_id = addressId;
            }

            const endpoint = this.endpoints.addresses.replace('/addresses', '/shipping/toggle');

            return this.apiRequest('POST', endpoint, data)
                .then(response => {
                    this.log('Shipping toggle API response:', response);

                    // Also update the frontend checkbox if available
                    if (typeof window.CMAShipping !== 'undefined') {
                        window.CMAShipping.toggleShipToDifferentAddress(enable);
                        if (enable && addressId) {
                            setTimeout(() => {
                                const selector = document.getElementById('cma_shipping_address');
                                if (selector) {
                                    selector.value = addressId;
                                    selector.dispatchEvent(new Event('change', { bubbles: true }));
                                }
                            }, 350);
                        }
                    }

                    return response;
                });
        }
    };

    // Make PrimalAddresses globally available
    if (typeof window !== 'undefined') {
        window.PrimalAddresses = PrimalAddresses;
        
        // Also make it available as globalThis for modern environments
        if (typeof globalThis !== 'undefined') {
            globalThis.PrimalAddresses = PrimalAddresses;
        }
    }

    // Log initialization
    PrimalAddresses.log('PrimalAddresses library initialized');

    // Auto-enable debug mode if URL contains debug parameter
    if (typeof window !== 'undefined' && window.location && window.location.search.includes('primal_debug=1')) {
        PrimalAddresses.enableDebug();
    }

})();

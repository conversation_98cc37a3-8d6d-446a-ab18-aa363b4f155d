<?php
/**
 * Plugin Name: Custom Multiple Addresses for WooCommerce
 * Plugin URI: https://localhost/
 * Description: Unlimited multiple billing and shipping addresses for WooCommerce with bidirectional sync and no telemetry.
 * Version: 1.1.4
 * Author: Custom Development
 * Text Domain: custom-multiple-addresses
 * Domain Path: /languages
 * WC requires at least: 3.0.0
 * WC tested up to: 9.4
 * Requires Plugins: woocommerce
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Check if WooCommerce is active
if (!in_array('woocommerce/woocommerce.php', apply_filters('active_plugins', get_option('active_plugins')))) {
    return;
}

define('CMA_VERSION', '1.1.4');
define('CMA_PLUGIN_FILE', __FILE__);
define('CMA_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('CMA_PLUGIN_URL', plugin_dir_url(__FILE__));

class Custom_Multiple_Addresses {

    /**
     * Track hook state for safe user meta updates
     * @var bool
     */
    private $hooks_disabled = false;

    public function __construct() {
        add_action('init', array($this, 'init'));

        // Declare HPOS compatibility
        add_action('before_woocommerce_init', array($this, 'declare_hpos_compatibility'));
    }

    /**
     * Declare compatibility with WooCommerce High-Performance Order Storage (HPOS)
     * This plugin only manages user address data and doesn't interact with orders directly,
     * making it fully compatible with HPOS.
     */
    public function declare_hpos_compatibility() {
        if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
            \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('custom_order_tables', __FILE__, true);
        }
    }

    public function init() {
        // Hook into WooCommerce my account
        add_action('woocommerce_account_addresses_endpoint', array($this, 'display_multiple_addresses'));
        add_filter('woocommerce_locate_template', array($this, 'locate_template'), 10, 3);

        // AJAX handlers
        add_action('wp_ajax_cma_add_address', array($this, 'ajax_add_address'));
        add_action('wp_ajax_cma_delete_address', array($this, 'ajax_delete_address'));
        add_action('wp_ajax_cma_set_default', array($this, 'ajax_set_default'));
        add_action('wp_ajax_cma_get_address', array($this, 'ajax_get_address'));
        add_action('wp_ajax_nopriv_cma_get_address', array($this, 'ajax_get_address'));
        add_action('wp_ajax_cma_get_states', array($this, 'ajax_get_states'));
        add_action('wp_ajax_nopriv_cma_get_states', array($this, 'ajax_get_states'));

        // REST API endpoints
        add_action('rest_api_init', array($this, 'register_rest_routes'));

        // Enqueue scripts
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));

        // Add CSS to hide default address sections
        add_action('wp_head', array($this, 'add_custom_css'));

        // Checkout integration
        add_action('woocommerce_before_checkout_billing_form', array($this, 'checkout_address_selector'));
        add_action('woocommerce_before_checkout_shipping_form', array($this, 'checkout_shipping_selector'));

        // Ship to different address integration
        add_action('wp_footer', array($this, 'add_shipping_checkbox_handler'));

        // Hook to sync default flags when WooCommerce default addresses are changed
        add_action('woocommerce_customer_save_address', array($this, 'sync_default_address_flags'), 10, 2);
    }
    
    public function locate_template($template, $template_name, $template_path) {
        if ($template_name === 'myaccount/my-address.php') {
            $custom_template = CMA_PLUGIN_PATH . 'templates/my-address.php';
            if (file_exists($custom_template)) {
                return $custom_template;
            }
        }
        return $template;
    }
    
    public function enqueue_scripts() {
        if (is_wc_endpoint_url('edit-address') || is_checkout()) {
            wp_enqueue_script('cma-script', CMA_PLUGIN_URL . 'assets/js/custom-addresses.js', array('jquery'), CMA_VERSION, true);
            wp_enqueue_style('cma-style', CMA_PLUGIN_URL . 'assets/css/custom-addresses.css', array(), CMA_VERSION);

            // Enqueue PrimalAddresses library
            wp_enqueue_script('primal-addresses', CMA_PLUGIN_URL . 'assets/js/primal-addresses.js', array('jquery'), CMA_VERSION, true);

            wp_localize_script('cma-script', 'cma_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('cma_nonce'),
                'add_address_text' => __('Add New Address', 'custom-multiple-addresses'),
                'delete_confirm' => __('Are you sure you want to delete this address?', 'custom-multiple-addresses')
            ));

            // Localize PrimalAddresses settings
            wp_localize_script('primal-addresses', 'primal_addresses_settings', array(
                'nonce' => wp_create_nonce('wp_rest'),
                'api_url' => rest_url('primalcom/v1/'),
                'messages' => array(
                    'address_added' => __('Address added successfully!', 'custom-multiple-addresses'),
                    'address_updated' => __('Address updated successfully!', 'custom-multiple-addresses'),
                    'address_deleted' => __('Address deleted successfully!', 'custom-multiple-addresses'),
                    'default_set' => __('Default address updated!', 'custom-multiple-addresses'),
                    'error' => __('An error occurred. Please try again.', 'custom-multiple-addresses')
                )
            ));
        }
    }
    
    public function display_multiple_addresses() {
        $user_id = get_current_user_id();
        if (!$user_id) return;

        echo '<div class="cma-additional-addresses">';
        
        // Billing addresses
        echo '<div class="cma-billing-section">';
        echo '<h3>' . __('Billing Addresses', 'custom-multiple-addresses') . '</h3>';
        $this->render_address_list($user_id, 'billing');
        echo '<button type="button" class="button cma-add-address" data-type="billing">' . __('Add Billing Address', 'custom-multiple-addresses') . '</button>';
        echo '</div>';
        
        // Shipping addresses
        if (!wc_ship_to_billing_address_only() && wc_shipping_enabled()) {
            echo '<div class="cma-shipping-section">';
            echo '<h3>' . __('Shipping Addresses', 'custom-multiple-addresses') . '</h3>';
            $this->render_address_list($user_id, 'shipping');
            echo '<button type="button" class="button cma-add-address" data-type="shipping">' . __('Add Shipping Address', 'custom-multiple-addresses') . '</button>';
            echo '</div>';
        }
        
        echo '</div>';
        
        // Add address form modal
        $this->render_address_form_modal();
    }
    
    private function render_address_list($user_id, $type) {
        $addresses = $this->get_user_addresses($user_id, $type);

        if (empty($addresses)) {
            echo '<p>' . __('No additional addresses saved yet.', 'custom-multiple-addresses') . '</p>';
            return;
        }

        echo '<div class="cma-address-list">';
        foreach ($addresses as $address_id => $address) {
            // Check if this address is marked as default
            $is_default = isset($address['is_default']) && $address['is_default'] === true;
            $item_class = $is_default ? 'cma-address-item cma-default' : 'cma-address-item';

            echo '<div class="' . esc_attr($item_class) . '" data-address-id="' . esc_attr($address_id) . '">';

            // Add default badge if this is the default address
            if ($is_default) {
                echo '<div class="cma-default-badge">Default</div>';
            }

            echo '<div class="cma-address-content">';
            echo '<address>' . $this->format_address($address) . '</address>';
            echo '</div>';
            echo '<div class="cma-address-actions">';
            echo '<button type="button" class="button cma-edit-address" data-address-id="' . esc_attr($address_id) . '" data-type="' . esc_attr($type) . '">' . __('Edit', 'custom-multiple-addresses') . '</button>';
            echo '<button type="button" class="button cma-delete-address" data-address-id="' . esc_attr($address_id) . '" data-type="' . esc_attr($type) . '">' . __('Delete', 'custom-multiple-addresses') . '</button>';

            // Set button text and state based on default status
            if ($is_default) {
                echo '<button type="button" class="button cma-set-default" data-address-id="' . esc_attr($address_id) . '" data-type="' . esc_attr($type) . '" disabled>' . __('Current Default', 'custom-multiple-addresses') . '</button>';
            } else {
                echo '<button type="button" class="button cma-set-default" data-address-id="' . esc_attr($address_id) . '" data-type="' . esc_attr($type) . '">' . __('Set as Default', 'custom-multiple-addresses') . '</button>';
            }

            echo '</div>';
            echo '</div>';
        }
        echo '</div>';
    }
    
    private function render_address_form_modal() {
        ?>
        <div id="cma-address-modal" class="cma-modal" style="display: none;">
            <div class="cma-modal-content">
                <div class="cma-modal-header">
                    <h3 id="cma-modal-title"><?php _e('Add Address', 'custom-multiple-addresses'); ?></h3>
                    <span class="cma-modal-close">&times;</span>
                </div>
                <div class="cma-modal-body">
                    <form id="cma-address-form">
                        <input type="hidden" id="cma-address-type" name="address_type" value="">
                        <input type="hidden" id="cma-address-id" name="address_id" value="">

                        <p class="form-row form-row-first">
                            <label for="cma_first_name"><?php _e('First name', 'custom-multiple-addresses'); ?> <span class="required">*</span></label>
                            <input type="text" class="input-text" name="first_name" id="cma_first_name" required>
                        </p>

                        <p class="form-row form-row-last">
                            <label for="cma_last_name"><?php _e('Last name', 'custom-multiple-addresses'); ?> <span class="required">*</span></label>
                            <input type="text" class="input-text" name="last_name" id="cma_last_name" required>
                        </p>

                        <p class="form-row form-row-wide">
                            <label for="cma_company"><?php _e('Company name', 'custom-multiple-addresses'); ?></label>
                            <input type="text" class="input-text" name="company" id="cma_company">
                        </p>

                        <p class="form-row form-row-wide">
                            <label for="cma_address_1"><?php _e('Street address', 'custom-multiple-addresses'); ?> <span class="required">*</span></label>
                            <input type="text" class="input-text" name="address_1" id="cma_address_1" placeholder="<?php _e('House number and street name', 'custom-multiple-addresses'); ?>" required>
                        </p>

                        <p class="form-row form-row-wide">
                            <label for="cma_address_2"><?php _e('Apartment, suite, unit, etc.', 'custom-multiple-addresses'); ?></label>
                            <input type="text" class="input-text" name="address_2" id="cma_address_2">
                        </p>

                        <p class="form-row form-row-wide">
                            <label for="cma_city"><?php _e('Town / City', 'custom-multiple-addresses'); ?> <span class="required">*</span></label>
                            <input type="text" class="input-text" name="city" id="cma_city" required>
                        </p>

                        <p class="form-row form-row-wide">
                            <label for="cma_country"><?php _e('Country / Region', 'custom-multiple-addresses'); ?> <span class="required">*</span></label>
                            <select name="country" id="cma_country" class="country_to_state" required>
                                <option value=""><?php _e('Select a country / region&hellip;', 'custom-multiple-addresses'); ?></option>
                                <?php
                                foreach (WC()->countries->get_allowed_countries() as $key => $value) {
                                    echo '<option value="' . esc_attr($key) . '">' . esc_html($value) . '</option>';
                                }
                                ?>
                            </select>
                        </p>

                        <p class="form-row form-row-first">
                            <label for="cma_state"><?php _e('State / County', 'custom-multiple-addresses'); ?> <span class="required">*</span></label>
                            <select name="state" id="cma_state" required>
                                <option value=""><?php _e('Select a state / county&hellip;', 'custom-multiple-addresses'); ?></option>
                            </select>
                        </p>

                        <p class="form-row form-row-last">
                            <label for="cma_postcode"><?php _e('Postcode / ZIP', 'custom-multiple-addresses'); ?> <span class="required">*</span></label>
                            <input type="text" class="input-text" name="postcode" id="cma_postcode" required>
                        </p>

                        <p class="form-row">
                            <button type="submit" class="button alt"><?php _e('Save Address', 'custom-multiple-addresses'); ?></button>
                            <button type="button" class="button cma-modal-close"><?php _e('Cancel', 'custom-multiple-addresses'); ?></button>
                        </p>
                    </form>
                </div>
            </div>
        </div>
        <?php
    }
    
    public function get_user_addresses($user_id, $type) {
        $addresses = array();

        // Get default WooCommerce address first
        $default_address = $this->get_default_woocommerce_address($user_id, $type);
        if (!empty($default_address)) {
            $default_address['is_default'] = true;
            $addresses['default_' . $type] = $default_address;
        }

        // Get additional addresses from custom meta
        $additional_addresses = get_user_meta($user_id, 'cma_' . $type . '_addresses', true) ?: array();

        // Add additional addresses (mark as non-default)
        foreach ($additional_addresses as $address_id => $address) {
            $address['is_default'] = false;
            $addresses[$address_id] = $address;
        }

        return $addresses;
    }

    /**
     * Get default WooCommerce address for a user
     */
    private function get_default_woocommerce_address($user_id, $type) {
        $address_fields = array(
            'first_name',
            'last_name',
            'company',
            'address_1',
            'address_2',
            'city',
            'state',
            'postcode',
            'country',
            'phone'
        );

        // Add email for billing addresses
        if ($type === 'billing') {
            $address_fields[] = 'email';
        }

        $address = array();
        $has_data = false;

        foreach ($address_fields as $field) {
            $meta_key = $type . '_' . $field;
            $value = get_user_meta($user_id, $meta_key, true);

            if (!empty($value)) {
                $address[$field] = $value;
                $has_data = true;
            }
        }

        return $has_data ? $address : array();
    }
    
    public function save_user_address($user_id, $type, $address_data, $address_id = null) {
        $addresses = $this->get_user_addresses($user_id, $type);

        if ($address_id === null) {
            $address_id = 'addr_' . time() . '_' . rand(1000, 9999);
        }

        $addresses[$address_id] = $address_data;

        // Use safe user meta update to avoid SupportBoard conflicts
        $this->safe_update_user_meta($user_id, 'cma_' . $type . '_addresses', $addresses);

        return $address_id;
    }
    
    public function delete_user_address($user_id, $type, $address_id) {
        $addresses = $this->get_user_addresses($user_id, $type);
        unset($addresses[$address_id]);

        // Use safe user meta update to avoid SupportBoard conflicts
        $this->safe_update_user_meta($user_id, 'cma_' . $type . '_addresses', $addresses);
    }
    
    private function format_address($address) {
        $formatted = '';
        if (!empty($address['first_name']) || !empty($address['last_name'])) {
            $formatted .= trim($address['first_name'] . ' ' . $address['last_name']) . '<br>';
        }
        if (!empty($address['company'])) {
            $formatted .= $address['company'] . '<br>';
        }
        if (!empty($address['address_1'])) {
            $formatted .= $address['address_1'] . '<br>';
        }
        if (!empty($address['address_2'])) {
            $formatted .= $address['address_2'] . '<br>';
        }
        if (!empty($address['city'])) {
            $formatted .= $address['city'];
        }
        if (!empty($address['state'])) {
            $formatted .= ', ' . $address['state'];
        }
        if (!empty($address['postcode'])) {
            $formatted .= ' ' . $address['postcode'] . '<br>';
        }
        if (!empty($address['country'])) {
            $countries = WC()->countries->get_countries();
            $formatted .= isset($countries[$address['country']]) ? $countries[$address['country']] : $address['country'];
        }
        
        return $formatted;
    }
    
    // AJAX handlers will be added in the next part
    public function ajax_add_address() {
        check_ajax_referer('cma_nonce', 'nonce');
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_die('Unauthorized');
        }
        
        $type = sanitize_text_field($_POST['address_type']);
        $address_id = !empty($_POST['address_id']) ? sanitize_text_field($_POST['address_id']) : null;
        
        $address_data = array(
            'first_name' => sanitize_text_field($_POST['first_name']),
            'last_name' => sanitize_text_field($_POST['last_name']),
            'company' => sanitize_text_field($_POST['company']),
            'address_1' => sanitize_text_field($_POST['address_1']),
            'address_2' => sanitize_text_field($_POST['address_2']),
            'city' => sanitize_text_field($_POST['city']),
            'state' => sanitize_text_field($_POST['state']),
            'postcode' => sanitize_text_field($_POST['postcode']),
            'country' => sanitize_text_field($_POST['country']),
            'is_default' => false  // New addresses default to non-default
        );
        
        $saved_id = $this->save_user_address($user_id, $type, $address_data, $address_id);
        
        wp_send_json_success(array(
            'message' => __('Address saved successfully', 'custom-multiple-addresses'),
            'address_id' => $saved_id
        ));
    }
    
    public function ajax_delete_address() {
        check_ajax_referer('cma_nonce', 'nonce');
        
        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_die('Unauthorized');
        }
        
        $type = sanitize_text_field($_POST['address_type']);
        $address_id = sanitize_text_field($_POST['address_id']);
        
        $this->delete_user_address($user_id, $type, $address_id);
        
        wp_send_json_success(array(
            'message' => __('Address deleted successfully', 'custom-multiple-addresses')
        ));
    }
    
    public function ajax_set_default() {
        check_ajax_referer('cma_nonce', 'nonce');

        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_die('Unauthorized');
        }

        $type = sanitize_text_field($_POST['address_type']);
        $address_id = sanitize_text_field($_POST['address_id']);

        $addresses = $this->get_user_addresses($user_id, $type);
        if (isset($addresses[$address_id])) {
            $address_data = $addresses[$address_id];

            // Step 1: Update WooCommerce default address
            $wc_update_success = $this->update_woocommerce_address_safely($user_id, $type, $address_data);

            if ($wc_update_success) {
                // Step 2: Update is_default flags in additional addresses
                foreach ($addresses as $addr_id => &$addr) {
                    $addr['is_default'] = ($addr_id === $address_id);
                }

                // Step 3: Save updated addresses with is_default flags
                $this->safe_update_user_meta($user_id, 'cma_' . $type . '_addresses', $addresses);

                wp_send_json_success(array(
                    'message' => __('Default address updated successfully', 'custom-multiple-addresses'),
                    'address_data' => $address_data,
                    'address_type' => $type
                ));
            } else {
                wp_send_json_error(array(
                    'message' => __('Failed to update WooCommerce default address', 'custom-multiple-addresses')
                ));
            }
        } else {
            wp_send_json_error(array(
                'message' => __('Address not found', 'custom-multiple-addresses')
            ));
        }
    }

    public function ajax_get_address() {
        check_ajax_referer('cma_nonce', 'nonce');

        $user_id = get_current_user_id();
        if (!$user_id) {
            wp_die('Unauthorized');
        }

        $type = sanitize_text_field($_POST['address_type']);
        $address_id = sanitize_text_field($_POST['address_id']);

        $addresses = $this->get_user_addresses($user_id, $type);
        if (isset($addresses[$address_id])) {
            wp_send_json_success($addresses[$address_id]);
        } else {
            wp_send_json_error(array('message' => __('Address not found', 'custom-multiple-addresses')));
        }
    }

    public function ajax_get_states() {
        check_ajax_referer('cma_nonce', 'nonce');

        $country = sanitize_text_field($_POST['country']);

        if (empty($country)) {
            wp_send_json_error(array('message' => __('Country is required', 'custom-multiple-addresses')));
        }

        // Get states for the country from WooCommerce
        $states = WC()->countries->get_states($country);

        if (!empty($states) && is_array($states)) {
            wp_send_json_success($states);
        } else {
            // Return empty array if no states available
            wp_send_json_success(array());
        }
    }

    /**
     * Sync default address flags when WooCommerce default addresses are changed
     * This ensures that when a user edits their default address via WooCommerce (not through our plugin),
     * all additional addresses are marked as is_default=false to maintain consistency
     *
     * @param int $user_id User ID whose address was saved
     * @param string $address_type Type of address ('billing' or 'shipping')
     */
    public function sync_default_address_flags($user_id, $address_type) {
        // Only process billing and shipping address types
        if (!in_array($address_type, array('billing', 'shipping'))) {
            return;
        }

        // Get all additional addresses for this type
        $addresses = $this->get_user_addresses($user_id, $address_type);

        // If no additional addresses exist, nothing to sync
        if (empty($addresses)) {
            return;
        }

        // Set all additional addresses to is_default=false since WooCommerce default was changed
        $addresses_updated = false;
        foreach ($addresses as $address_id => &$address) {
            if (isset($address['is_default']) && $address['is_default'] === true) {
                $address['is_default'] = false;
                $addresses_updated = true;
            }
        }

        // Save updated addresses if any changes were made
        if ($addresses_updated) {
            $this->safe_update_user_meta($user_id, 'cma_' . $address_type . '_addresses', $addresses);

            // Log the sync action for debugging
            error_log("CMA: Synced default flags for user {$user_id} {$address_type} addresses - set all additional addresses to is_default=false");
        }
    }
    
    public function checkout_address_selector($checkout) {
        $user_id = get_current_user_id();
        if (!$user_id) return;

        $addresses = $this->get_user_addresses($user_id, 'billing');
        if (empty($addresses)) return;

        echo '<div class="cma-checkout-selector cma-billing-selector">';
        echo '<h3>' . __('Select Billing Address', 'custom-multiple-addresses') . '</h3>';
        echo '<p class="form-row form-row-wide">';
        echo '<select name="cma_billing_address" id="cma_billing_address" class="select">';
        echo '<option value="">' . __('Use default billing address', 'custom-multiple-addresses') . '</option>';
        foreach ($addresses as $address_id => $address) {
            $label = trim($address['first_name'] . ' ' . $address['last_name']);
            if (!empty($address['address_1'])) {
                $label .= ' - ' . $address['address_1'];
            }
            if (!empty($address['city'])) {
                $label .= ', ' . $address['city'];
            }
            echo '<option value="' . esc_attr($address_id) . '">' . esc_html($label) . '</option>';
        }
        echo '</select>';
        echo '</p>';
        echo '</div>';
    }
    
    public function checkout_shipping_selector($checkout) {
        $user_id = get_current_user_id();
        if (!$user_id) return;

        $addresses = $this->get_user_addresses($user_id, 'shipping');
        if (empty($addresses)) return;

        echo '<div class="cma-checkout-selector cma-shipping-selector">';
        echo '<h3>' . __('Select Shipping Address', 'custom-multiple-addresses') . '</h3>';
        echo '<p class="form-row form-row-wide">';
        echo '<select name="cma_shipping_address" id="cma_shipping_address" class="select">';
        echo '<option value="">' . __('Use default shipping address', 'custom-multiple-addresses') . '</option>';
        foreach ($addresses as $address_id => $address) {
            $label = trim($address['first_name'] . ' ' . $address['last_name']);
            if (!empty($address['address_1'])) {
                $label .= ' - ' . $address['address_1'];
            }
            if (!empty($address['city'])) {
                $label .= ', ' . $address['city'];
            }
            echo '<option value="' . esc_attr($address_id) . '">' . esc_html($label) . '</option>';
        }
        echo '</select>';
        echo '</p>';
        echo '</div>';
    }

    /**
     * Add JavaScript handler for "Ship to different address" checkbox
     */
    public function add_shipping_checkbox_handler() {
        // Only add on checkout page
        if (!is_checkout()) {
            return;
        }
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            'use strict';

            // Ship to different address checkbox handler
            function handleShipToDifferentAddress() {
                const checkbox = $('#ship-to-different-address-checkbox');
                const shippingFields = $('.woocommerce-shipping-fields');
                const cmaShippingSelector = $('#cma_shipping_address');

                if (checkbox.length && shippingFields.length) {
                    // Function to toggle shipping fields
                    function toggleShippingFields() {
                        const isChecked = checkbox.is(':checked');

                        if (isChecked) {
                            shippingFields.slideDown(300);
                            // If CMA shipping selector exists, show it
                            if (cmaShippingSelector.length) {
                                cmaShippingSelector.closest('.form-row').show();
                            }
                            console.log('🚚 Ship to different address: ENABLED');
                        } else {
                            shippingFields.slideUp(300);
                            // Clear shipping address selection
                            if (cmaShippingSelector.length) {
                                cmaShippingSelector.val('').trigger('change');
                                cmaShippingSelector.closest('.form-row').hide();
                            }
                            console.log('🚚 Ship to different address: DISABLED');
                        }
                    }

                    // Initial state
                    toggleShippingFields();

                    // Handle checkbox change
                    checkbox.on('change', function() {
                        toggleShippingFields();

                        // Trigger checkout update
                        $('body').trigger('update_checkout');
                    });

                    // Handle programmatic checkbox changes
                    checkbox.on('cma_toggle', function(e, shouldCheck) {
                        if (shouldCheck !== undefined) {
                            checkbox.prop('checked', shouldCheck).trigger('change');
                        }
                    });
                }
            }

            // Initialize on page load
            handleShipToDifferentAddress();

            // Re-initialize after checkout updates
            $(document.body).on('updated_checkout', function() {
                setTimeout(handleShipToDifferentAddress, 100);
            });

            // Make functions globally available for PrimalAddresses integration
            if (typeof window.CMAShipping === 'undefined') {
                window.CMAShipping = {
                    // Toggle the ship to different address checkbox
                    toggleShipToDifferentAddress: function(enable) {
                        const checkbox = $('#ship-to-different-address-checkbox');
                        if (checkbox.length) {
                            checkbox.trigger('cma_toggle', [enable]);
                            return true;
                        }
                        return false;
                    },

                    // Check if shipping to different address is enabled
                    isShippingToDifferentAddress: function() {
                        const checkbox = $('#ship-to-different-address-checkbox');
                        return checkbox.length ? checkbox.is(':checked') : false;
                    },

                    // Enable shipping to different address and select an address
                    enableAndSelectShippingAddress: function(addressId) {
                        this.toggleShipToDifferentAddress(true);

                        setTimeout(() => {
                            const selector = $('#cma_shipping_address');
                            if (selector.length && addressId) {
                                selector.val(addressId).trigger('change');
                                console.log('🚚 Selected shipping address:', addressId);
                            }
                        }, 350); // Wait for slide animation
                    },

                    // Disable shipping to different address
                    disableShippingToDifferentAddress: function() {
                        this.toggleShipToDifferentAddress(false);
                    }
                };

                console.log('🚚 CMAShipping utilities loaded');
            }
        });
        </script>
        <?php
    }

    // Option F: Removed automatic sync functions to eliminate SupportBoard conflicts

    // Option F: Removed sync_address_type function - no automatic syncing

    /**
     * Safely update user meta to avoid SupportBoard conflicts
     * Temporarily disables hooks that might interfere with SupportBoard
     *
     * @param int $user_id User ID
     * @param string $meta_key Meta key
     * @param mixed $meta_value Meta value
     */
    private function safe_update_user_meta($user_id, $meta_key, $meta_value) {
        // Temporarily disable hooks that might interfere with SupportBoard
        $this->disable_conflicting_hooks();

        // Perform the user meta update
        $result = update_user_meta($user_id, $meta_key, $meta_value);

        // Re-enable hooks
        $this->enable_conflicting_hooks();

        return $result;
    }

    /**
     * Disable hooks that might conflict with SupportBoard during user meta updates
     */
    private function disable_conflicting_hooks() {
        // Store current hook state for restoration
        $this->hooks_disabled = true;

        // Temporarily remove hooks that might interfere
        // (Add specific hooks here if we identify them)
    }

    /**
     * Re-enable hooks after safe user meta update
     */
    private function enable_conflicting_hooks() {
        // Restore hooks if they were disabled
        if (isset($this->hooks_disabled) && $this->hooks_disabled) {
            $this->hooks_disabled = false;
            // Restore specific hooks here
        }
    }

    /**
     * Safely update WooCommerce address without breaking SupportBoard
     * Uses WooCommerce's proper customer data methods instead of direct user meta
     *
     * @param int $user_id User ID
     * @param string $type Address type (billing or shipping)
     * @param array $address_data Address data to update
     */
    private function update_woocommerce_address_safely($user_id, $type, $address_data) {
        // Method 1: Use WooCommerce Customer object (safest)
        if (class_exists('WC_Customer')) {
            try {
                $customer = new WC_Customer($user_id);

                // Update address using WooCommerce's methods
                if ($type === 'billing') {
                    $customer->set_billing_first_name($address_data['first_name'] ?? '');
                    $customer->set_billing_last_name($address_data['last_name'] ?? '');
                    $customer->set_billing_company($address_data['company'] ?? '');
                    $customer->set_billing_address_1($address_data['address_1'] ?? '');
                    $customer->set_billing_address_2($address_data['address_2'] ?? '');
                    $customer->set_billing_city($address_data['city'] ?? '');
                    $customer->set_billing_state($address_data['state'] ?? '');
                    $customer->set_billing_postcode($address_data['postcode'] ?? '');
                    $customer->set_billing_country($address_data['country'] ?? '');
                } else {
                    $customer->set_shipping_first_name($address_data['first_name'] ?? '');
                    $customer->set_shipping_last_name($address_data['last_name'] ?? '');
                    $customer->set_shipping_company($address_data['company'] ?? '');
                    $customer->set_shipping_address_1($address_data['address_1'] ?? '');
                    $customer->set_shipping_address_2($address_data['address_2'] ?? '');
                    $customer->set_shipping_city($address_data['city'] ?? '');
                    $customer->set_shipping_state($address_data['state'] ?? '');
                    $customer->set_shipping_postcode($address_data['postcode'] ?? '');
                    $customer->set_shipping_country($address_data['country'] ?? '');
                }

                // Save using WooCommerce's method (this handles hooks properly)
                $customer->save();

                return true;

            } catch (Exception $e) {
                error_log('CMA: WooCommerce Customer update failed: ' . $e->getMessage());
                // Fall back to direct user meta if WooCommerce method fails
                return $this->update_user_meta_fallback($user_id, $type, $address_data);
            }
        }

        // Fallback: Direct user meta (original method)
        return $this->update_user_meta_fallback($user_id, $type, $address_data);
    }

    /**
     * Fallback method for direct user meta updates
     *
     * @param int $user_id User ID
     * @param string $type Address type
     * @param array $address_data Address data
     */
    private function update_user_meta_fallback($user_id, $type, $address_data) {
        foreach ($address_data as $key => $value) {
            update_user_meta($user_id, $type . '_' . $key, $value);
        }
        return true;
    }

    // ===== REST API METHODS =====

    /**
     * Register REST API routes for address management
     */
    public function register_rest_routes() {
        // Get all addresses
        register_rest_route('primalcom/v1', '/addresses', array(
            'methods' => 'GET',
            'callback' => array($this, 'rest_get_addresses'),
            'permission_callback' => array($this, 'check_user_logged_in')
        ));

        // Add new address
        register_rest_route('primalcom/v1', '/addresses', array(
            'methods' => 'POST',
            'callback' => array($this, 'rest_add_address'),
            'permission_callback' => array($this, 'check_user_logged_in'),
            'args' => array(
                'type' => array('required' => true, 'type' => 'string'),
                'address_data' => array('required' => true, 'type' => 'object')
            )
        ));

        // Update address
        register_rest_route('primalcom/v1', '/addresses/(?P<type>billing|shipping)/(?P<address_id>[a-zA-Z0-9_]+)', array(
            'methods' => 'PUT',
            'callback' => array($this, 'rest_update_address'),
            'permission_callback' => array($this, 'check_user_logged_in'),
            'args' => array(
                'type' => array('required' => true, 'type' => 'string'),
                'address_id' => array('required' => true, 'type' => 'string'),
                'address_data' => array('required' => true, 'type' => 'object')
            )
        ));

        // Delete address
        register_rest_route('primalcom/v1', '/addresses/(?P<type>billing|shipping)/(?P<address_id>[a-zA-Z0-9_]+)', array(
            'methods' => 'DELETE',
            'callback' => array($this, 'rest_delete_address'),
            'permission_callback' => array($this, 'check_user_logged_in'),
            'args' => array(
                'type' => array('required' => true, 'type' => 'string'),
                'address_id' => array('required' => true, 'type' => 'string')
            )
        ));

        // Set default address
        register_rest_route('primalcom/v1', '/addresses/(?P<type>billing|shipping)/(?P<address_id>[a-zA-Z0-9_]+)/default', array(
            'methods' => 'POST',
            'callback' => array($this, 'rest_set_default_address'),
            'permission_callback' => array($this, 'check_user_logged_in'),
            'args' => array(
                'type' => array('required' => true, 'type' => 'string'),
                'address_id' => array('required' => true, 'type' => 'string')
            )
        ));

        // Toggle ship to different address
        register_rest_route('primalcom/v1', '/shipping/toggle', array(
            'methods' => 'POST',
            'callback' => array($this, 'rest_toggle_shipping'),
            'permission_callback' => array($this, 'check_user_logged_in'),
            'args' => array(
                'enable' => array('required' => true, 'type' => 'boolean'),
                'address_id' => array('required' => false, 'type' => 'string')
            )
        ));

        // Set default address by data (works without address IDs)
        register_rest_route('primalcom/v1', '/addresses/set-default-by-data', array(
            'methods' => 'POST',
            'callback' => array($this, 'rest_set_default_by_data'),
            'permission_callback' => array($this, 'check_user_logged_in'),
            'args' => array(
                'type' => array('required' => true, 'type' => 'string'),
                'address_index' => array('required' => true, 'type' => 'integer'),
                'address_data' => array('required' => true, 'type' => 'object')
            )
        ));
    }

    /**
     * Check if user is logged in (permission callback)
     */
    public function check_user_logged_in() {
        return is_user_logged_in();
    }

    /**
     * REST API: Get all addresses for current user
     */
    public function rest_get_addresses(WP_REST_Request $request) {
        $user_id = get_current_user_id();
        if (!$user_id) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => 'User not logged in'
            ), 401);
        }

        try {
            $billing_addresses = $this->get_user_addresses($user_id, 'billing');
            $shipping_addresses = $this->get_user_addresses($user_id, 'shipping');

            return new WP_REST_Response(array(
                'success' => true,
                'data' => array(
                    'billing' => $billing_addresses,
                    'shipping' => $shipping_addresses
                )
            ), 200);
        } catch (Exception $e) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => $e->getMessage()
            ), 500);
        }
    }

    /**
     * REST API: Add new address
     */
    public function rest_add_address(WP_REST_Request $request) {
        $user_id = get_current_user_id();
        if (!$user_id) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => 'User not logged in'
            ), 401);
        }

        try {
            $type = $request->get_param('type');
            $address_data = $request->get_param('address_data');

            // Validate type
            if (!in_array($type, array('billing', 'shipping'))) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'Invalid address type. Must be billing or shipping.'
                ), 400);
            }

            // Sanitize address data
            $sanitized_data = $this->sanitize_address_data($address_data);

            // Validate required fields
            $validation_result = $this->validate_address_data($sanitized_data, $type);
            if (!$validation_result['valid']) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => $validation_result['message']
                ), 400);
            }

            $address_id = $this->save_user_address($user_id, $type, $sanitized_data);

            return new WP_REST_Response(array(
                'success' => true,
                'message' => ucfirst($type) . ' address added successfully',
                'data' => array(
                    'address_id' => $address_id,
                    'address_data' => $sanitized_data
                )
            ), 201);
        } catch (Exception $e) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => $e->getMessage()
            ), 500);
        }
    }

    /**
     * REST API: Update existing address
     */
    public function rest_update_address(WP_REST_Request $request) {
        $user_id = get_current_user_id();
        if (!$user_id) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => 'User not logged in'
            ), 401);
        }

        try {
            $type = $request->get_param('type');
            $address_id = $request->get_param('address_id');
            $address_data = $request->get_param('address_data');

            // Validate type
            if (!in_array($type, array('billing', 'shipping'))) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'Invalid address type. Must be billing or shipping.'
                ), 400);
            }

            // Check if address exists
            $addresses = $this->get_user_addresses($user_id, $type);
            if (!isset($addresses[$address_id])) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'Address not found'
                ), 404);
            }

            // Sanitize address data
            $sanitized_data = $this->sanitize_address_data($address_data);

            // Validate required fields
            $validation_result = $this->validate_address_data($sanitized_data, $type);
            if (!$validation_result['valid']) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => $validation_result['message']
                ), 400);
            }

            $this->save_user_address($user_id, $type, $sanitized_data, $address_id);

            return new WP_REST_Response(array(
                'success' => true,
                'message' => ucfirst($type) . ' address updated successfully',
                'data' => array(
                    'address_id' => $address_id,
                    'address_data' => $sanitized_data
                )
            ), 200);
        } catch (Exception $e) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => $e->getMessage()
            ), 500);
        }
    }

    /**
     * REST API: Delete address
     */
    public function rest_delete_address(WP_REST_Request $request) {
        $user_id = get_current_user_id();
        if (!$user_id) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => 'User not logged in'
            ), 401);
        }

        try {
            $type = $request->get_param('type');
            $address_id = $request->get_param('address_id');

            // Validate type
            if (!in_array($type, array('billing', 'shipping'))) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'Invalid address type. Must be billing or shipping.'
                ), 400);
            }

            // Check if address exists
            $addresses = $this->get_user_addresses($user_id, $type);
            if (!isset($addresses[$address_id])) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'Address not found'
                ), 404);
            }

            $this->delete_user_address($user_id, $type, $address_id);

            return new WP_REST_Response(array(
                'success' => true,
                'message' => ucfirst($type) . ' address deleted successfully'
            ), 200);
        } catch (Exception $e) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => $e->getMessage()
            ), 500);
        }
    }

    /**
     * REST API: Set default address
     */
    public function rest_set_default_address(WP_REST_Request $request) {
        $user_id = get_current_user_id();
        if (!$user_id) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => 'User not logged in'
            ), 401);
        }

        try {
            $type = $request->get_param('type');
            $address_id = $request->get_param('address_id');

            // Validate type
            if (!in_array($type, array('billing', 'shipping'))) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'Invalid address type. Must be billing or shipping.'
                ), 400);
            }

            // Check if address exists
            $addresses = $this->get_user_addresses($user_id, $type);
            if (!isset($addresses[$address_id])) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'Address not found'
                ), 404);
            }

            $address_data = $addresses[$address_id];

            // Update WooCommerce default address
            foreach ($address_data as $key => $value) {
                update_user_meta($user_id, $type . '_' . $key, $value);
            }

            return new WP_REST_Response(array(
                'success' => true,
                'message' => ucfirst($type) . ' default address updated successfully',
                'data' => array(
                    'address_id' => $address_id,
                    'address_data' => $address_data
                )
            ), 200);
        } catch (Exception $e) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => $e->getMessage()
            ), 500);
        }
    }

    // ===== HELPER METHODS =====

    /**
     * Sanitize address data
     */
    private function sanitize_address_data($address_data) {
        $sanitized = array();

        $allowed_fields = array(
            'first_name', 'last_name', 'company', 'address_1', 'address_2',
            'city', 'state', 'postcode', 'country', 'phone', 'email'
        );

        foreach ($allowed_fields as $field) {
            if (isset($address_data[$field])) {
                $sanitized[$field] = sanitize_text_field($address_data[$field]);
            }
        }

        return $sanitized;
    }

    /**
     * Validate address data
     */
    private function validate_address_data($address_data, $type) {
        $required_fields = array('first_name', 'last_name', 'address_1', 'city', 'postcode', 'country');

        // Email is required for billing addresses
        if ($type === 'billing') {
            $required_fields[] = 'email';
        }

        foreach ($required_fields as $field) {
            if (empty($address_data[$field])) {
                return array(
                    'valid' => false,
                    'message' => sprintf(__('Field %s is required', 'custom-multiple-addresses'), $field)
                );
            }
        }

        // Validate email format if provided
        if (!empty($address_data['email']) && !is_email($address_data['email'])) {
            return array(
                'valid' => false,
                'message' => __('Invalid email format', 'custom-multiple-addresses')
            );
        }

        return array('valid' => true);
    }

    /**
     * REST API: Toggle ship to different address
     */
    public function rest_toggle_shipping(WP_REST_Request $request) {
        $user_id = get_current_user_id();
        if (!$user_id) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => 'User not logged in'
            ), 401);
        }

        try {
            $enable = $request->get_param('enable');
            $address_id = $request->get_param('address_id');

            // Store the shipping preference in session/user meta
            if (isset($_SESSION)) {
                $_SESSION['cma_ship_to_different_address'] = $enable;
                if ($address_id) {
                    $_SESSION['cma_selected_shipping_address'] = $address_id;
                }
            }

            // Also store in user meta for persistence
            update_user_meta($user_id, 'cma_ship_to_different_address', $enable);
            if ($address_id) {
                update_user_meta($user_id, 'cma_selected_shipping_address', $address_id);
            }

            $response_data = array(
                'success' => true,
                'message' => $enable ? 'Ship to different address enabled' : 'Ship to different address disabled',
                'shipping_enabled' => $enable
            );

            if ($address_id) {
                $response_data['selected_address_id'] = $address_id;
            }

            return new WP_REST_Response($response_data, 200);

        } catch (Exception $e) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => $e->getMessage()
            ), 500);
        }
    }

    /**
     * Add custom CSS to hide default address sections
     */
    public function add_custom_css() {
        // Only add CSS on account pages where addresses are displayed
        if (is_account_page() || is_wc_endpoint_url('edit-address')) {
            ?>
            <style type="text/css">
                /* Hide default WooCommerce address sections since they're included in our arrays */
                .woocommerce-address .woocommerce-Address {
                    display: none !important;
                }

                /* Hide the default address display boxes */
                .woocommerce-MyAccount-content .woocommerce-Addresses .woocommerce-Address {
                    display: none !important;
                }

                /* Hide individual address sections by title */
                .woocommerce-MyAccount-content .woocommerce-Addresses .woocommerce-Address-title:contains("Billing address"),
                .woocommerce-MyAccount-content .woocommerce-Addresses .woocommerce-Address-title:contains("Shipping address") {
                    display: none !important;
                }

                /* Alternative: Hide by address content structure */
                .woocommerce-MyAccount-content .woocommerce-Addresses .col-1,
                .woocommerce-MyAccount-content .woocommerce-Addresses .col-2 {
                    display: none !important;
                }

                /* Show our custom address sections */
                .cma-addresses-container {
                    display: block !important;
                }
            </style>
            <?php
        }
    }

    /**
     * REST API: Set default address by data (works without address IDs)
     */
    public function rest_set_default_by_data(WP_REST_Request $request) {
        $user_id = get_current_user_id();
        if (!$user_id) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => 'User not logged in'
            ), 401);
        }

        try {
            $type = $request->get_param('type');
            $address_index = $request->get_param('address_index');
            $address_data = $request->get_param('address_data');

            // Validate type
            if (!in_array($type, array('billing', 'shipping'))) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'Invalid address type. Must be billing or shipping.'
                ), 400);
            }

            // Get all addresses for this type
            $addresses = $this->get_user_addresses($user_id, $type);
            $addresses_array = array_values($addresses); // Convert to indexed array

            // Validate index
            if ($address_index < 0 || $address_index >= count($addresses_array)) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'Invalid address index'
                ), 400);
            }

            // Get the selected address
            $selected_address = $addresses_array[$address_index];

            // If this is already the default address, no need to change
            if (isset($selected_address['is_default']) && $selected_address['is_default']) {
                return new WP_REST_Response(array(
                    'success' => true,
                    'message' => 'Address is already the default',
                    'address' => $selected_address
                ), 200);
            }

            // Update WooCommerce default address fields
            $address_fields = array(
                'first_name', 'last_name', 'company', 'address_1', 'address_2',
                'city', 'state', 'postcode', 'country', 'phone'
            );

            if ($type === 'billing') {
                $address_fields[] = 'email';
            }

            foreach ($address_fields as $field) {
                if (isset($selected_address[$field])) {
                    update_user_meta($user_id, $type . '_' . $field, $selected_address[$field]);
                }
            }

            // Update is_default flags in stored addresses
            $updated_addresses = array();
            foreach ($addresses as $addr_id => $addr) {
                $addr['is_default'] = false; // Set all to false first
                $updated_addresses[$addr_id] = $addr;
            }

            // Set the selected address as default
            $selected_key = array_keys($addresses)[$address_index];
            if (isset($updated_addresses[$selected_key])) {
                $updated_addresses[$selected_key]['is_default'] = true;
            }

            // Save updated addresses
            update_user_meta($user_id, 'cma_' . $type . '_addresses', $updated_addresses);

            return new WP_REST_Response(array(
                'success' => true,
                'message' => 'Default address updated successfully',
                'address' => $selected_address
            ), 200);

        } catch (Exception $e) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => $e->getMessage()
            ), 500);
        }
    }
}

// Initialize the plugin
new Custom_Multiple_Addresses();

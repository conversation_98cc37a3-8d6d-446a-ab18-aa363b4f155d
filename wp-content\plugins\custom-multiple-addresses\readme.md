# Custom Multiple Addresses for WooCommerce

## Overview
The Custom Multiple Addresses plugin enhances WooCommerce by allowing customers to save and manage unlimited shipping and billing addresses with seamless integration to WooCommerce's default address system.

## 🆕 Latest Features

- **🎯 Smart Address Selection**: Select addresses by name, index, keywords, or partial matches
- **📦 Simple Array Responses**: Clean API responses without wrapper objects
- **🔗 Default Address Integration**: WooCommerce default addresses included in responses
- **🎨 Cleaner UI**: Hidden duplicate default address sections
- **⚡ Auto-Shipping Toggle**: Shipping addresses automatically enable "ship to different address"

## Features
- **Unlimited Addresses**: Save unlimited shipping and billing addresses per customer
- **Smart Address Selection**: Intelligent address matching by ID, name, address, index, or keywords
- **Default Address Management**: Set any additional address as WooCommerce default with visual indicators
- **Simple Array Responses**: Clean API responses with default addresses included (no wrapper objects)
- **Checkout Integration**: Select from saved addresses during checkout process
- **SupportBoard Integration**: Full chatbot support for address management
- **Real-time Updates**: Immediate synchronization with WooCommerce default addresses
- **Visual Feedback**: Blue styling and badges for default addresses
- **Address Validation**: Built-in validation and formatting
- **REST API Endpoints**: Full REST API support for programmatic access
- **Global PrimalAddresses Library**: JavaScript library with smart selection functions
- **Hybrid Support**: Both AJAX and REST API approaches supported

## Installation
1. Upload the plugin files to the `/wp-content/plugins/custom-multiple-addresses` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Navigate to My Account > Addresses to start using the plugin

## Quick Start

```javascript
// Get all addresses (includes default WooCommerce addresses)
PrimalAddresses.getAddresses()
    .then(response => {
        console.log('Billing:', response.billing);    // Simple array
        console.log('Shipping:', response.shipping);  // Simple array
    });

// Smart address selection - multiple ways to select
PrimalAddresses.selectBillingAddress('John')        // By name
PrimalAddresses.selectBillingAddress(0)             // By index
PrimalAddresses.selectBillingAddress('default')     // By keyword
PrimalAddresses.selectShippingAddress('Office')     // Auto-enables shipping
```

## Data Storage

### Database Tables
The plugin stores data in WordPress user meta tables:

| Meta Key | Description | Data Type |
|----------|-------------|-----------|
| `cma_billing_addresses` | Additional billing addresses | Serialized Array |
| `cma_shipping_addresses` | Additional shipping addresses | Serialized Array |

### Address Data Structure
Each address is stored with the following fields:
```
- first_name
- last_name
- company
- address_1
- address_2
- city
- state
- postcode
- country
- is_default (boolean)
```

### Integration Points
- **WooCommerce User Meta**: Syncs with standard WooCommerce address fields
- **SupportBoard**: Integrates with `sb_woocommerce_get_users_with_multiple_addresses()`
- **WordPress Users**: Links to WordPress user accounts via user_id

## Usage

### Customer Interface
- **My Account Page**: Navigate to My Account > Addresses
- **Add Addresses**: Click "Add Billing Address" or "Add Shipping Address" buttons
- **Edit Addresses**: Click "Edit" button on any saved address
- **Delete Addresses**: Click "Delete" button to remove addresses
- **Set Default**: Click "Set as Default" to make an address the WooCommerce default
- **Visual Indicators**: Default addresses show blue styling and "Default" badge

### Checkout Process
- **Address Selection**: Dropdown menus to choose from saved addresses
- **Default Fallback**: Automatically uses WooCommerce default when no selection made
- **Real-time Population**: Selected addresses immediately populate checkout fields

### Admin Interface
- **User Management**: View customer addresses in WordPress admin under Users
- **Order Processing**: View selected address details in WooCommerce orders
- **Bulk Operations**: Manage multiple customer addresses efficiently

## SupportBoard Integration

### Available Functions
- `sb_woocommerce_get_users_with_multiple_addresses($type, $limit, $user_id)`
- `sb_woocommerce_get_single_user_addresses($user_id, $type)`
- `sb_woocommerce_get_cma_users_with_addresses($type, $user_id)`

### Chatbot Features
- Address retrieval and display in chat
- Default address detection and marking
- Rich message formatting for address lists
- User-specific address management

## Technical Details

### Default Address Synchronization
- **One-way Sync**: Additional addresses can be set as WooCommerce defaults
- **Visual Feedback**: Immediate UI updates without page refresh
- **Flag Management**: Proper `is_default` flag handling across all addresses
- **WooCommerce Integration**: Uses WC_Customer object for safe updates

### Performance Optimizations
- **Safe User Meta Updates**: Prevents conflicts with SupportBoard caching
- **Efficient Queries**: Optimized database queries for address retrieval
- **Minimal Cache Usage**: Designed to avoid caching conflicts

## Requirements
- **WordPress**: 5.0 or higher
- **WooCommerce**: 5.0 or higher
- **PHP**: 7.4 or higher
- **SupportBoard**: Optional, for chatbot integration

## PrimalAddresses JavaScript Library

This plugin includes the global **PrimalAddresses** library for programmatic access to address management:

### Usage Examples

```javascript
// Get all addresses for current user (includes default addresses)
PrimalAddresses.getAddresses()
    .then(response => {
        console.log('Billing addresses:', response.billing);
        console.log('Shipping addresses:', response.shipping);

        // Find default addresses
        const defaultBilling = response.billing.find(addr => addr.is_default);
        const defaultShipping = response.shipping.find(addr => addr.is_default);
        console.log('Default billing:', defaultBilling);
        console.log('Default shipping:', defaultShipping);
    });

// Get billing addresses only (returns simple array)
PrimalAddresses.getBillingAddresses()
    .then(billingAddresses => {
        console.log('Billing addresses:', billingAddresses);
        console.log('Count:', billingAddresses.length);

        // Find default address
        const defaultAddress = billingAddresses.find(addr => addr.is_default);
        console.log('Default address:', defaultAddress);

        // Loop through addresses
        billingAddresses.forEach((address, index) => {
            console.log(`Address ${index + 1}: ${address.first_name} ${address.last_name}`);
            console.log(`Default: ${address.is_default}`);
        });
    });

// Get shipping addresses only (returns simple array)
PrimalAddresses.getShippingAddresses()
    .then(shippingAddresses => {
        console.log('Shipping addresses:', shippingAddresses);
        console.log('Count:', shippingAddresses.length);

        // Find default address
        const defaultAddress = shippingAddresses.find(addr => addr.is_default);
        console.log('Default address:', defaultAddress);

        // Loop through addresses
        shippingAddresses.forEach((address, index) => {
            console.log(`Address ${index + 1}: ${address.address_1}, ${address.city}`);
            console.log(`Default: ${address.is_default}`);
        });
    });



// Add new address
PrimalAddresses.addAddress('billing', {
    first_name: 'John',
    last_name: 'Doe',
    address_1: '123 Main St',
    city: 'New York',
    postcode: '10001',
    country: 'US',
    email: '<EMAIL>'
})
.then(response => console.log('Address added:', response));

// Update existing address
PrimalAddresses.updateAddress('billing', 'addr_123', updatedData)
    .then(response => console.log('Address updated:', response));

// Delete address
PrimalAddresses.deleteAddress('billing', 'addr_123')
    .then(response => console.log('Address deleted:', response));

// Set as default address
PrimalAddresses.setDefaultAddress('billing', 'addr_123')
    .then(response => console.log('Default address set:', response));

// Smart address selection (NEW!)
PrimalAddresses.selectBillingAddress('default')     // Select default billing
    .then(response => console.log('Billing selected:', response));

PrimalAddresses.selectShippingAddress('work')       // Select by name match
    .then(response => console.log('Shipping selected:', response));

PrimalAddresses.selectBillingAddress(1)             // Select by index
    .then(response => console.log('Billing selected:', response));

PrimalAddresses.selectShippingAddress('addr_123')   // Select by ID
    .then(response => console.log('Shipping selected:', response));

// Toggle "Ship to different address" checkbox
PrimalAddresses.toggleShipToDifferentAddress(true);  // Enable
PrimalAddresses.toggleShipToDifferentAddress(false); // Disable

// Check if shipping to different address is enabled
const isShippingEnabled = PrimalAddresses.isShippingToDifferentAddress();

// Enable shipping and select a specific address
PrimalAddresses.enableShippingAndSelectAddress('addr_456');

// Smart shipping selection (enables checkbox and selects address)
PrimalAddresses.selectShippingAddressAndEnable('addr_456')
    .then(response => console.log('Shipping address selected:', response));

// API-based shipping toggle (persists on server)
PrimalAddresses.toggleShippingAPI(true, 'addr_456')
    .then(response => console.log('Shipping toggled via API:', response));

// Utility functions
const formatted = PrimalAddresses.formatAddress(addressData);
const summary = PrimalAddresses.getAddressSummary(addressData);
const validation = PrimalAddresses.validateAddress(addressData, 'billing');
```

### Address Array Structure

The address functions return simple arrays with default addresses included:

```javascript
// getAddresses() response structure
{
    success: true,
    billing: [
        {
            first_name: "CUst",
            last_name: "test",
            company: "Test Company",
            address_1: "Jalan Menggatal",
            city: "Menggatal",
            state: "SBH",
            postcode: "897000",
            country: "MY",
            is_default: true
        },
        {
            first_name: "Customer",
            last_name: "Test",
            company: "Test Company",
            address_1: "718",
            address_2: "Apartment",
            city: "Menggatal",
            state: "SBH",
            postcode: "897000",
            country: "MY",
            is_default: false
        }
    ],
    shipping: [
        {
            first_name: "CUst",
            last_name: "test",
            company: "Test Company",
            address_1: "Jalan Menggatal",
            city: "Menggatal",
            state: "SBH",
            postcode: "897000",
            country: "MY",
            is_default: true
        },
        {
            first_name: "Customer",
            last_name: "test",
            company: "Rental House",
            address_1: "Jalan Penampang",
            address_2: "Cyber City",
            city: "Kepayan",
            state: "SBH",
            postcode: "89708",
            country: "MY",
            is_default: false
        }
    ]
}

// getBillingAddresses() returns just the billing array:
[
    {
        first_name: "CUst",
        last_name: "test",
        company: "Test Company",
        address_1: "Jalan Menggatal",
        city: "Menggatal",
        state: "SBH",
        postcode: "897000",
        country: "MY",
        is_default: true
    },
    {
        first_name: "Customer",
        last_name: "Test",
        company: "Test Company",
        address_1: "718",
        address_2: "Apartment",
        city: "Menggatal",
        state: "SBH",
        postcode: "897000",
        country: "MY",
        is_default: false
    }
]

// Easy to loop through
billingAddresses.forEach(address => {
    console.log(`${address.first_name} ${address.last_name} - ${address.address_1}`);
    if (address.is_default) {
        console.log('This is the default address');
    }
});

// Find default address
const defaultAddress = billingAddresses.find(addr => addr.is_default);
if (defaultAddress) {
    console.log('Default address:', defaultAddress.first_name, defaultAddress.last_name);
} else {
    console.log('No default address set');
}
```

## Ship to Different Address Integration

This plugin provides seamless integration with WooCommerce's "Ship to a different address?" checkbox:

### JavaScript Control

```javascript
// Check current state
if (PrimalAddresses.isShippingToDifferentAddress()) {
    console.log('Shipping to different address is enabled');
}

// Enable and select shipping address in one action
PrimalAddresses.selectShippingAddressAndEnable('addr_456');

// Or control manually
PrimalAddresses.toggleShipToDifferentAddress(true);  // Enable
PrimalAddresses.toggleShipToDifferentAddress(false); // Disable

// Use the global CMAShipping utility (if available)
if (typeof CMAShipping !== 'undefined') {
    CMAShipping.enableAndSelectShippingAddress('addr_456');
    CMAShipping.disableShippingToDifferentAddress();
}
```

### Features

- **Automatic Integration**: Works with WooCommerce's native shipping checkbox
- **Smooth Animations**: Shipping fields slide in/out when toggled
- **Address Selection**: Automatically shows/hides shipping address selector
- **Persistent State**: Remembers shipping preference via REST API
- **Global Access**: Available through both `PrimalAddresses` and `CMAShipping` objects

### REST API Endpoints

All endpoints are available under `/wp-json/primalcom/v1/addresses` and require user authentication:

- `GET /addresses` - Get all addresses
- `POST /addresses` - Add new address
- `PUT /addresses/{type}/{address_id}` - Update address
- `DELETE /addresses/{type}/{address_id}` - Delete address
- `POST /addresses/{type}/{address_id}/default` - Set default address
- `POST /shipping/toggle` - Toggle "Ship to different address" checkbox

### Debug Mode

Enable debug mode by adding `?primal_debug=1` to the URL or:

```javascript
PrimalAddresses.enableDebug();
```

## Smart Address Selection

The plugin provides intelligent address selection functions that can match addresses by various criteria:

### selectBillingAddress(identifier)

Selects a billing address using smart matching and sets it as default.

```javascript
// Select by array index (0 = first address, 1 = second, etc.)
PrimalAddresses.selectBillingAddress(0)
    .then(response => {
        console.log('✅ First billing address selected:', response.address);
    });

// Select by array index (0 = first address, 1 = second, etc.)
PrimalAddresses.selectBillingAddress(0)
    .then(response => {
        console.log('✅ First billing address selected');
    });

// Select by name matching (partial match)
PrimalAddresses.selectBillingAddress('John')
    .then(response => {
        console.log('✅ Address matching "John" selected');
    });

// Select using keywords
PrimalAddresses.selectBillingAddress('default')  // Select default address
PrimalAddresses.selectBillingAddress('first')    // Select first address
PrimalAddresses.selectBillingAddress('last')     // Select last address
```

### selectShippingAddress(identifier)

Selects a shipping address, enables "ship to different address", and sets as default.

```javascript
// Select by array index (automatically enables shipping)
PrimalAddresses.selectShippingAddress(0)
    .then(response => {
        console.log('✅ First shipping address selected:', response.address);
        console.log('Shipping enabled:', response.shipping_enabled);
    });

// Select by index
PrimalAddresses.selectShippingAddress(1)
    .then(response => {
        console.log('✅ Second shipping address selected and enabled');
    });
```

### Smart Matching Rules

1. **Array Index Match**: `0`, `1`, `2` (first, second, third address)
2. **Name Match**: Partial match against `first_name + last_name`
3. **Address Match**: Partial match against `address_1`
4. **String Index Match**: `"0"`, `"1"`, `"2"` (string numbers)
5. **Keywords**: `"default"`, `"first"`, `"last"`

## SpeechAI Integration

The plugin includes voice-activated address selection functionality that integrates seamlessly with existing SpeechAI systems.

### Voice Address Selection

Use natural language voice commands to select addresses:

```javascript
// Voice-activated address selection with auto-detection
PrimalAddresses.selectAddressByVoice("select my home address")
    .then(response => {
        console.log('✅ Address selected via voice:', response.address);
    });

// Specify address type explicitly
PrimalAddresses.selectAddressByVoice("ship to my office", "shipping")
    .then(response => {
        console.log('✅ Shipping address set:', response.address);
        console.log('Shipping enabled:', response.shipping_enabled);
    });
```

### Voice Command Examples

**Billing Address Commands:**
- "select my home address for billing"
- "use my office address"
- "choose the first billing address"
- "set default billing address"

**Shipping Address Commands:**
- "ship to my office address"
- "deliver to my home address"
- "use different shipping address"
- "send to my second address"

**Auto-Detection Commands:**
- "use my home address"
- "select office location"
- "choose the first address"
- "pick my default address"

### Voice Query Testing

Test and debug voice queries before using them:

```javascript
// Test voice query normalization and type detection
const result = PrimalAddresses.testVoiceQuery("ship to my office address");
console.log('Normalized query:', result.normalized);
console.log('Detected type:', result.detectedType);
console.log('Will match:', result.willMatch);
```

### Batch Voice Commands

Process multiple voice commands for testing:

```javascript
const commands = [
    { query: "select my first address", type: "billing" },
    { query: "ship to my office", type: "shipping" },
    { query: "use default address", type: "auto" }
];

PrimalAddresses.processVoiceCommands(commands)
    .then(results => {
        console.log('Batch results:', results);
    });
```

### Voice Command Normalization

The system automatically normalizes voice input:

- **Removes prefixes**: "select", "choose", "use", "set", "pick"
- **Normalizes ordinals**: "1st/first", "2nd/second", "3rd/third"
- **Standardizes terms**: "home/house/residential" → "home"
- **Cleans input**: Trims whitespace, converts to lowercase

### Integration with Existing SpeechAI

The voice functions work alongside existing SpeechAI implementations without conflicts:

```javascript
// Example integration with SupportBoard SpeechAI
function handleVoiceCommand(transcribedText) {
    // Check if it's an address-related command
    if (transcribedText.includes('address') || transcribedText.includes('ship')) {
        return PrimalAddresses.selectAddressByVoice(transcribedText);
    }

    // Handle other voice commands with existing system
    return handleOtherVoiceCommands(transcribedText);
}
```

## Testing & Development

The plugin includes test files for development and debugging:

- **`test-address-api.html`**: Test basic address API functionality
- **`test-smart-selection.html`**: Test smart address selection features
- **`test-integration.html`**: Test WooCommerce integration
- **`test-speechai-integration.html`**: Test voice-activated address selection

Access these files directly in your browser to test functionality.

## Support & Documentation
- **Plugin Documentation**: Available in readme.md
- **SupportBoard Integration**: See MULTIPLE-ADDRESSES-INTEGRATION.md
- **Technical Updates**: Check updates.md for implementation details

## License
GPL v2 or later
# Floating Button Configuration

## 🎛️ How to Configure the Floating Support Button

### 📍 **Location of Configuration**

Edit the configuration in: `custom-support-ticket.php`

Look for the `get_floating_button_config()` function around **line 183**.

### ⚙️ **Configuration Options**

```php
private function get_floating_button_config() {
    return array(
        // 👁️ SHOW/HIDE BUTTON
        'enabled' => false, // Set to true to show floating button
        
        // 📍 POSITION SETTINGS
        'position' => 'bottom-right', // Options: 'bottom-right', 'bottom-left', 'top-right', 'top-left'
        
        // 🎯 CUSTOM POSITIONING (overrides position setting)
        'custom_position' => array(
            'bottom' => '24px',  // Distance from bottom
            'right' => '24px',   // Distance from right
            'top' => '',         // Distance from top (leave empty for bottom)
            'left' => '',        // Distance from left (leave empty for right)
        ),
        
        // 🎨 BUTTON TEXT AND STYLING
        'text' => 'Need Help?',
        'background_color' => '', // Leave empty for default gradient
        'text_color' => '#ffffff',
        
        // 📏 SIZE AND SPACING
        'padding' => '16px 24px',
        'font_size' => '14px',
        'border_radius' => '50px',
        
        // ✨ ANIMATION AND EFFECTS
        'hover_effect' => true,
        'shadow' => true,
        
        // 🔢 Z-INDEX (for layering)
        'z_index' => 1000,
        
        // 📱 MOBILE SETTINGS
        'mobile_enabled' => true,
        'mobile_position' => array(
            'bottom' => '16px',
            'right' => '16px',
            'padding' => '14px 20px',
            'font_size' => '13px',
        ),
    );
}
```

## 🚀 **Quick Setup Examples**

### **1. Enable Floating Button (Bottom Right)**
```php
'enabled' => true,
'position' => 'bottom-right',
'text' => 'Need Help?',
```

### **2. Bottom Left Position**
```php
'enabled' => true,
'position' => 'bottom-left',
'text' => '💬 Support',
```

### **3. Custom Position**
```php
'enabled' => true,
'custom_position' => array(
    'bottom' => '50px',
    'right' => '30px',
    'top' => '',
    'left' => '',
),
```

### **4. Custom Colors**
```php
'enabled' => true,
'background_color' => '#ff6b6b',
'text_color' => '#ffffff',
'text' => 'Get Help',
```

### **5. Disable Hover Effects**
```php
'enabled' => true,
'hover_effect' => false,
'shadow' => false,
```

## 🎯 **Position Options**

| Position | Description |
|----------|-------------|
| `bottom-right` | Bottom right corner (default) |
| `bottom-left` | Bottom left corner |
| `top-right` | Top right corner |
| `top-left` | Top left corner |

## 🎨 **Styling Options**

### **Colors**
- `background_color`: Any CSS color (`#ff0000`, `red`, `rgb(255,0,0)`)
- `text_color`: Text color (default: `#ffffff`)

### **Sizing**
- `padding`: Button padding (`16px 24px`)
- `font_size`: Text size (`14px`)
- `border_radius`: Corner roundness (`50px` for pill shape)

### **Effects**
- `hover_effect`: Enable/disable hover animation
- `shadow`: Enable/disable drop shadow
- `z_index`: Layer order (higher = on top)

## 📱 **Mobile Responsive**

The button automatically adjusts for mobile devices using the `mobile_position` settings.

## 🔧 **Advanced CSS Override**

For advanced styling, you can also add custom CSS to your theme:

```css
.floating-support-button {
    /* Your custom styles here */
    background: linear-gradient(45deg, #your-color1, #your-color2) !important;
    border: 2px solid #your-border-color !important;
}
```

## 🚫 **To Hide the Button**

Simply set:
```php
'enabled' => false,
```

## 💡 **Tips**

1. **Test on Mobile**: Always check how the button looks on mobile devices
2. **Z-Index**: If the button appears behind other elements, increase `z_index`
3. **Accessibility**: Keep text readable and button size adequate for touch
4. **Performance**: The button only loads when enabled

## 🔄 **After Making Changes**

1. Save the file
2. Clear any caching plugins
3. Refresh your website to see changes

# Custom Support Ticket Plugin

A WordPress plugin that provides a popup contact form for users to submit support tickets. The submissions are saved to the WordPress database and can be managed through a dedicated admin interface.

## ✨ Features

- 🎯 **Popup Contact Form** - Clean, responsive popup form with validation
- 💾 **Database Storage** - Saves submissions to `wp_posts` with `post_type='contact'`
- 🔧 **Admin Interface** - Dedicated admin page to view and manage tickets
- 🔍 **Search & Filter** - Search tickets by name or email
- 📱 **Responsive Design** - Works perfectly on all devices
- ♿ **Accessibility** - WCAG compliant with keyboard navigation
- 🛡️ **Security** - Nonce verification and data sanitization
- 🎨 **Customizable** - Easy to style and customize

## 📦 Installation

1. Upload the plugin files to `/wp-content/plugins/custom-support-ticket/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Use the shortcode `[support_ticket_button]` to display the contact button

## 🚀 Usage

### JavaScript API (Recommended)
```javascript
// Trigger popup from anywhere
SupportTicket.popUp();
```

### Shortcode (Optional)
```
[support_ticket_button]
```

### Customized Button
```
[support_ticket_button text="Get Help" class="my-custom-button"]
```

### JavaScript API Functions
- `SupportTicket.popUp()` - Open the popup
- `SupportTicket.close()` - Close the popup
- `SupportTicket.isOpen()` - Check if popup is open
- `SupportTicket.fillForm(data)` - Pre-fill form data
- `SupportTicket.resetForm()` - Reset the form
- `SupportTicket.submit()` - Submit form programmatically

### Shortcode Parameters
- `text` - Button text (default: "Contact Support")
- `class` - Additional CSS classes for the button

## 🎛️ Admin Interface

Access the admin interface through **Support Tickets** in your WordPress admin menu.

### Features:
- View all submitted tickets in a table format
- Search tickets by name or email
- View full ticket details in a modal popup
- Delete tickets with confirmation
- Pagination for large numbers of tickets
- Responsive design for mobile management

## 📊 Database Structure

The plugin stores contact submissions as WordPress posts with:
- **Post Type**: `contact`
- **Post Title**: "Support Ticket from [Name]"
- **Post Content**: The message content
- **Meta Fields**:
  - `contact_name` - Submitter's name
  - `contact_email` - Submitter's email
  - `contact_phone` - Submitter's phone (optional)
  - `contact_date` - Submission timestamp

## 🎨 Styling

The plugin includes default styles that work with most themes. You can customize the appearance by:

1. **Override CSS** - Add custom styles to your theme
2. **Custom Classes** - Use the `class` parameter in the shortcode
3. **Theme Integration** - Copy templates to your theme for full control

### CSS Classes
- `.cst-button` - The trigger button
- `.cst-popup-overlay` - The popup overlay
- `.cst-popup-container` - The popup container
- `.cst-form-group` - Form field groups

## 🔧 Customization

### Custom Button Styles
```css
.my-custom-button {
    background: #ff6b6b;
    color: white;
    border-radius: 25px;
    padding: 15px 30px;
}
```

### Custom Popup Styles
```css
.cst-popup-container {
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.4);
}
```

## 🛡️ Security Features

- **Nonce Verification** - All AJAX requests are verified
- **Data Sanitization** - All input is sanitized before storage
- **Email Validation** - Email addresses are validated
- **XSS Protection** - Output is properly escaped
- **CSRF Protection** - Forms include security tokens

## 🌐 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Internet Explorer 11+

## 📱 Mobile Support

The plugin is fully responsive and works on:
- Smartphones (iOS/Android)
- Tablets
- Desktop computers
- Touch devices

## ♿ Accessibility

- WCAG 2.1 AA compliant
- Keyboard navigation support
- Screen reader friendly
- High contrast mode support
- Focus management
- ARIA labels and roles

## 🔌 Hooks & Filters

### Actions
- `cst_before_ticket_save` - Before saving a ticket
- `cst_after_ticket_save` - After saving a ticket
- `cst_ticket_deleted` - When a ticket is deleted

### Filters
- `cst_form_fields` - Modify form fields
- `cst_admin_columns` - Customize admin table columns
- `cst_email_validation` - Custom email validation

## 🐛 Troubleshooting

### Common Issues

**Popup not showing?**
- Check if jQuery is loaded
- Verify the shortcode is placed correctly
- Check for JavaScript errors in browser console

**Form not submitting?**
- Verify AJAX URL is correct
- Check nonce verification
- Ensure required fields are filled

**Admin page not loading?**
- Check user permissions (requires `manage_options`)
- Verify plugin is activated
- Check for PHP errors in error log

## 📝 Changelog

### Version 1.0.0
- Initial release
- Popup contact form
- Admin interface
- Database storage
- Search functionality
- Responsive design

## 🤝 Support

For support and feature requests, please contact the plugin developer.

## 📄 License

This plugin is licensed under the GPLv2 or later.

## 🙏 Credits

Developed with ❤️ for WordPress community.

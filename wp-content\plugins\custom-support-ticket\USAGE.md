# Support Ticket System - Usage Guide

## 🎯 Simple Public API

### Show Support Form
```javascript
// Basic form
SupportTicket.form();

// Form with pre-filled data
SupportTicket.form({
    prefill: {
        name: '<PERSON>',
        email: '<EMAIL>',
        message: 'I need help with...'
    }
});
```

### Public API Functions
- `SupportTicket.form(options)` - Show the support form
- `SupportTicket.popUp(options)` - Legacy method (same as form)
- `SupportTicket.close()` - Close the form
- `SupportTicket.isOpen()` - Check if form is open
- `SupportTicket.fillForm(data)` - Pre-fill form data
- `SupportTicket.resetForm()` - Reset the form

## 🔧 Automatic Features

The plugin automatically adds:
- **Floating support button** (bottom-right corner)
- **Admin bar support link** (for logged-in users)  
- **Error page support buttons** (on 404 pages)

## 🎨 Custom Triggers

### Add Support Button to Any Element
```javascript
addSupportButton('.my-element', {
    text: 'Get Help',
    class: 'my-custom-class'
});
```

### Custom Floating Button
```javascript
addFloatingSupportButton({
    text: '🆘 Help',
    position: 'bottom-left',  // bottom-right, bottom-left, top-right, top-left
    color: '#e74c3c'
});
```

### Disable Auto-Triggers
```javascript
// Add this before the plugin loads
window.DISABLE_AUTO_SUPPORT_TRIGGERS = true;
```

## 📋 Admin Interface

- Access via **Support Tickets** in WordPress admin menu
- View all submitted tickets
- Search by name or email
- View full ticket details in modal
- Delete tickets with confirmation

## 🔒 Security Features

- Nonce verification for all AJAX requests
- Input sanitization and validation
- XSS protection with proper output escaping
- WordPress-compatible (works with or without jQuery)

## 🔧 Development & Debugging

### Debug Mode
Add `?debug=1` or `?cst_debug=1` to your URL to enable debug mode:
```
https://yoursite.com/page/?debug=1
```

In debug mode, internal objects are exposed for debugging:
- `SupportFormManager` - Internal form engine
- `SupportTriggers` - Trigger management system

### Clean Architecture
- **Public API**: Only `SupportTicket` object is globally exposed
- **Internal Objects**: `SupportFormManager` and `SupportTriggers` are private
- **Helper Functions**: `addSupportButton()` and `addFloatingSupportButton()` are public

## 🎯 Professional Implementation

The system is designed to be:
- **Clean API** - Only necessary functions exposed globally
- **Professional** - Production-ready code with proper encapsulation
- **WordPress Native** - Follows WordPress standards
- **Accessible** - WCAG compliant
- **Responsive** - Works on all devices
- **Secure** - Internal objects are private by default

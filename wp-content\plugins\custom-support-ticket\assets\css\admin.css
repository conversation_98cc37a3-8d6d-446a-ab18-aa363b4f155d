/**
 * Custom Support Ticket Admin Styles
 */

/* Admin Page Styles */
.cst-stats {
    background: #f9f9f9;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    padding: 15px;
    margin: 20px 0;
}

.cst-stats p {
    margin: 0;
    font-size: 14px;
}

/* Search Form */
.search-form {
    display: flex;
    align-items: center;
    gap: 8px;
}

.search-form input[type="search"] {
    min-width: 250px;
}

/* Table Enhancements */
.wp-list-table th,
.wp-list-table td {
    vertical-align: middle;
}

.wp-list-table td a {
    text-decoration: none;
}

.wp-list-table td a:hover {
    text-decoration: underline;
}

/* Action Buttons */
.wp-list-table .button {
    margin-right: 5px;
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
    line-height: 1.4;
}

/* Modal Styles */
.cst-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 999999;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
}

.cst-modal-content {
    background: white;
    border-radius: 4px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    animation: cstModalFadeIn 0.3s ease-out;
}

@keyframes cstModalFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.cst-modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e1e1e1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
}

.cst-modal-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.cst-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.cst-modal-close:hover {
    background: #e1e1e1;
    color: #333;
}

.cst-modal-body {
    padding: 24px;
}

.cst-modal-body .form-table {
    margin: 0;
}

.cst-modal-body .form-table th {
    width: 120px;
    font-weight: 600;
    color: #333;
    padding: 12px 0;
    vertical-align: top;
}

.cst-modal-body .form-table td {
    padding: 12px 0;
    word-wrap: break-word;
}

/* Responsive Design */
@media (max-width: 782px) {
    .search-form {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .search-form input[type="search"] {
        min-width: auto;
        width: 100%;
    }
    
    .wp-list-table .button {
        display: block;
        margin: 2px 0;
        text-align: center;
    }
    
    .cst-modal-content {
        margin: 10px;
        max-height: calc(100vh - 20px);
    }
    
    .cst-modal-header,
    .cst-modal-body {
        padding: 16px;
    }
}

/* Table Responsive Enhancements */
@media (max-width: 600px) {
    .wp-list-table {
        font-size: 12px;
    }
    
    .wp-list-table th,
    .wp-list-table td {
        padding: 8px 4px;
    }
    
    /* Hide less important columns on mobile */
    .wp-list-table th:nth-child(4),
    .wp-list-table td:nth-child(4) {
        display: none;
    }
}

/* Status Indicators */
.cst-status {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.cst-status-new {
    background: #e3f2fd;
    color: #1976d2;
}

.cst-status-read {
    background: #f3e5f5;
    color: #7b1fa2;
}

/* Loading States */
.cst-loading-row {
    text-align: center;
    padding: 40px;
    color: #666;
}

.cst-loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: cstSpin 1s linear infinite;
    margin-right: 8px;
}

@keyframes cstSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty State */
.cst-empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.cst-empty-state .dashicons {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 16px;
}

.cst-empty-state h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    color: #333;
}

.cst-empty-state p {
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
}

/* Pagination Enhancements */
.tablenav-pages {
    display: flex;
    align-items: center;
    gap: 10px;
}

.tablenav-pages .displaying-num {
    font-size: 13px;
    color: #666;
}

/* Accessibility Improvements */
.cst-modal:focus {
    outline: none;
}

.cst-modal-content:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .cst-modal-content {
        border: 2px solid #000;
    }
    
    .wp-list-table th,
    .wp-list-table td {
        border: 1px solid #000;
    }
}

/* Print Styles */
@media print {
    .cst-modal {
        display: none;
    }
    
    .wp-list-table .button {
        display: none;
    }
    
    .search-form {
        display: none;
    }
    
    .tablenav {
        display: none;
    }
}

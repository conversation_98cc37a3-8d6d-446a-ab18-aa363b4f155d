/**
 * Custom Support Ticket Frontend Styles
 */

/* Support Ticket Button */
.cst-button {
    background: #0073aa;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: background-color 0.3s ease;
    display: inline-block;
    text-decoration: none;
}

.cst-button:hover {
    background: #005a87;
    color: white;
    text-decoration: none;
}

.cst-button:focus {
    outline: 2px solid #005a87;
    outline-offset: 2px;
}

/* Popup Overlay */
.cst-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 999999;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
}

/* Popup Container */
.cst-popup-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    animation: cstFadeInUp 0.3s ease-out;
}

@keyframes cstFadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Popup Header */
.cst-popup-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e1e1e1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.cst-popup-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.cst-popup-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.cst-popup-close:hover {
    background: #e1e1e1;
    color: #333;
}

/* Popup Body */
.cst-popup-body {
    padding: 24px;
}

/* Form Styles */
.cst-form-group {
    margin-bottom: 20px;
}

.cst-form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.cst-form-group .required {
    color: #d63638;
}

.cst-form-group input,
.cst-form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    font-family: inherit;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    box-sizing: border-box;
}

.cst-form-group input:focus,
.cst-form-group textarea:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.1);
}

.cst-form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Form Actions */
.cst-form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid #e1e1e1;
}

/* Buttons */
.cst-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    min-width: 100px;
}

.cst-btn-primary {
    background: #0073aa;
    color: white;
}

.cst-btn-primary:hover {
    background: #005a87;
}

.cst-btn-primary:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.cst-btn-secondary {
    background: #f1f1f1;
    color: #333;
    border: 1px solid #ddd;
}

.cst-btn-secondary:hover {
    background: #e1e1e1;
}

/* Loading State */
.cst-loading {
    text-align: center;
    padding: 40px 20px;
}

.cst-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0073aa;
    border-radius: 50%;
    animation: cstSpin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes cstSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.cst-loading p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* Message States */
.cst-message {
    text-align: center;
    padding: 40px 20px;
}

.cst-message-icon {
    font-size: 48px;
    margin-bottom: 16px;
    font-weight: bold;
}

.cst-success .cst-message-icon {
    color: #46b450;
}

.cst-error .cst-message-icon {
    color: #d63638;
}

.cst-message h4 {
    margin: 0 0 12px 0;
    font-size: 18px;
    font-weight: 600;
}

.cst-success h4 {
    color: #46b450;
}

.cst-error h4 {
    color: #d63638;
}

.cst-message p {
    margin: 0 0 20px 0;
    color: #666;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 600px) {
    .cst-popup-container {
        margin: 10px;
        max-height: calc(100vh - 20px);
    }
    
    .cst-popup-header,
    .cst-popup-body {
        padding: 16px;
    }
    
    .cst-form-actions {
        flex-direction: column;
    }
    
    .cst-btn {
        width: 100%;
    }
}

/* Form Validation Styles */
.cst-form-group input.error,
.cst-form-group textarea.error {
    border-color: #d63638;
    box-shadow: 0 0 0 2px rgba(214, 54, 56, 0.1);
}

.cst-error-message {
    color: #d63638;
    font-size: 12px;
    margin-top: 4px;
    display: block;
}

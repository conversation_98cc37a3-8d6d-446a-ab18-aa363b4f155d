/**
 * Modern Support Ticket System - UI Styles
 */

/* Modern Support Button */
.cst-button,
.support-form-trigger {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 14px 28px;
    border-radius: 12px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-block;
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.cst-button::before,
.support-form-trigger::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.cst-button:hover::before,
.support-form-trigger:hover::before {
    left: 100%;
}

.cst-button:hover,
.support-form-trigger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
    text-decoration: none;
}

.cst-button:active,
.support-form-trigger:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.cst-button:focus,
.support-form-trigger:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.5);
}

/* Floating Support Button - Configurable */
.floating-support-button {
    position: fixed;
    z-index: 1000;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 24px;
    border-radius: 50px;
    cursor: pointer;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    user-select: none;
    border: none;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

    /* Default position - can be overridden by JavaScript */
    bottom: 24px;
    right: 24px;

    /* Shown by default - can be hidden via JavaScript if disabled */
    display: block;
}

.floating-support-button:hover {
    transform: scale(1.05) translateY(-2px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.5);
}

/* Position variants */
.floating-support-button.position-bottom-right {
    bottom: 24px;
    right: 24px;
}

.floating-support-button.position-bottom-left {
    bottom: 24px;
    left: 24px;
}

.floating-support-button.position-top-right {
    top: 24px;
    right: 24px;
}

.floating-support-button.position-top-left {
    top: 24px;
    left: 24px;
}

/* Floating button hover removed - button is hidden */

/* Modern Popup Overlay */
.support-form-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    z-index: 999999;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
    opacity: 0;
    animation: fadeIn 0.3s ease-out forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

/* Modern Popup Container */
.support-form-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    max-width: 520px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    position: relative;
    transform: scale(0.9) translateY(20px);
    animation: slideIn 0.3s ease-out 0.1s forwards;
}

@keyframes slideIn {
    to {
        transform: scale(1) translateY(0);
    }
}

/* Modern Header */
.support-form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 24px 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.support-form-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 50%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(255,255,255,0.1) 0%, transparent 50%);
    opacity: 0.6;
    pointer-events: none;
}

.support-form-header h3 {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    letter-spacing: -0.5px;
    position: relative;
    z-index: 1;
}

.support-form-close {
    padding:0;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: 300;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.support-form-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
}

/* Modern Form Body */
.support-form-body {
    padding: 32px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* Support Form Groups */
.cst-form-group {
    margin-bottom: 24px;
    position: relative;
}

.cst-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2d3748;
    font-size: 14px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    letter-spacing: 0.025em;
}

.cst-required {
    color: #e53e3e;
    margin-left: 2px;
}

/* Modern Support Input Fields */
.cst-form-group input,
.cst-form-group textarea {
    width: 100%;
    padding: 16px 20px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 16px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: white;
    box-sizing: border-box;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.cst-form-group input:focus,
.cst-form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.cst-form-group input::placeholder,
.cst-form-group textarea::placeholder {
    color: #a0aec0;
    font-weight: 400;
}

.cst-form-group textarea {
    resize: vertical;
    min-height: 120px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Support Form Error States */
.cst-form-group input.cst-error,
.cst-form-group textarea.cst-error {
    border-color: #e53e3e;
    box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
    animation: cst-shake 0.5s ease-in-out;
}

@keyframes cst-shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.cst-error-message {
    display: block;
    color: #e53e3e;
    font-size: 13px;
    font-weight: 500;
    margin-top: 6px;
    padding-left: 4px;
    animation: cst-slideDown 0.3s ease-out;
}

@keyframes cst-slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Support Form Actions */
.cst-form-actions,
.support-form-container .cst-form-actions {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    gap: 16px !important;
    justify-content: flex-end !important;
    align-items: center !important;
    margin-top: 32px !important;
    padding-top: 24px !important;
    border-top: 1px solid #e2e8f0 !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

.cst-btn,
.support-form-container .cst-btn,
.support-form-body .cst-btn {
    padding: 14px 24px !important;
    border: none !important;
    border-radius: 12px !important;
    cursor: pointer !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
    min-width: 120px !important;
    white-space: nowrap !important;
    flex-shrink: 0 !important;
}

.cst-btn-primary,
.support-form-container .cst-btn-primary,
.support-form-body .cst-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
    border: 2px solid transparent !important;
}

.cst-btn-primary:hover,
.support-form-container .cst-btn-primary:hover,
.support-form-body .cst-btn-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
    color: white !important;
}

.cst-btn-secondary,
.support-form-container .cst-btn-secondary,
.support-form-body .cst-btn-secondary {
    background: white !important;
    color: #4a5568 !important;
    border: 2px solid #e2e8f0 !important;
}

.cst-btn-secondary:hover,
.support-form-container .cst-btn-secondary:hover,
.support-form-body .cst-btn-secondary:hover {
    background: #f7fafc !important;
    border-color: #cbd5e0 !important;
    transform: translateY(-1px) !important;
    color: #4a5568 !important;
}

/* Support Form States */
.cst-form-state {
    text-align: center;
    padding: 48px 32px;
}

.cst-loading-spinner {
    width: 48px;
    height: 48px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: cst-spin 1s linear infinite;
    margin: 0 auto 24px;
}

@keyframes cst-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.cst-success-icon,
.cst-error-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 36px;
    font-weight: bold;
    margin: 0 auto 24px;
    animation: cst-bounceIn 0.6s ease-out;
}

@keyframes cst-bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.cst-success-icon {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
}

.cst-error-icon {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
}

.cst-form-state h4 {
    font-size: 24px;
    font-weight: 700;
    margin: 0 0 12px 0;
    color: #2d3748;
}

.cst-form-state p {
    font-size: 16px;
    color: #4a5568;
    margin: 0 0 24px 0;
    line-height: 1.5;
}

/* Prevent body scroll when form is open */
body.support-form-open {
    overflow: hidden;
}

/* Ensure form elements are properly styled */
.support-form-container input[type="text"],
.support-form-container input[type="email"],
.support-form-container input[type="tel"],
.support-form-container textarea {
    width: 100% !important;
    padding: 16px 20px !important;
    border: 2px solid #e2e8f0 !important;
    border-radius: 12px !important;
    font-size: 16px !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    background: white !important;
    box-sizing: border-box !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.support-form-container input:focus,
.support-form-container textarea:focus {
    outline: none !important;
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    transform: translateY(-1px) !important;
}

/* Mobile Responsive */
@media (max-width: 600px) {
    .support-form-container {
        margin: 10px;
        max-width: none;
        border-radius: 16px;
    }

    .support-form-header {
        padding: 20px 24px;
    }

    .support-form-header h3 {
        font-size: 20px;
    }

    .support-form-body {
        padding: 24px;
    }

    .cst-form-actions,
    .support-form-container .cst-form-actions {
        flex-direction: column !important;
    }

    .cst-btn,
    .support-form-container .cst-btn {
        width: 100% !important;
        justify-content: center !important;
        text-align: center !important;
    }

    .floating-support-button {
        bottom: 16px;
        right: 16px;
        padding: 14px 20px;
        font-size: 13px;
    }
}

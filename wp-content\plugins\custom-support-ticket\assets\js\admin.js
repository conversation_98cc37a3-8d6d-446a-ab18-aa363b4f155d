/**
 * Custom Support Ticket Admin JavaScript
 */

(function($) {
    'use strict';
    
    // Initialize when document is ready
    $(document).ready(function() {
        CSTAdmin.init();
    });
    
    // Main admin object
    window.CSTAdmin = {
        
        // Initialize the admin interface
        init: function() {
            this.bindEvents();
            this.initTooltips();
        },
        
        // Bind event handlers
        bindEvents: function() {
            // View ticket modal
            $(document).on('click', '.cst-view-ticket', this.openTicketModal);
            
            // Close modal
            $(document).on('click', '.cst-modal-close', this.closeModal);
            $(document).on('click', '.cst-modal', function(e) {
                if (e.target === this) {
                    CSTAdmin.closeModal();
                }
            });
            
            // Escape key to close modal
            $(document).on('keydown', function(e) {
                if (e.keyCode === 27 && $('.cst-modal').is(':visible')) {
                    CSTAdmin.closeModal();
                }
            });
            
            // Delete confirmation
            $(document).on('click', 'a[href*="action=delete"]', this.confirmDelete);
            
            // Search form enhancements
            $(document).on('input', 'input[name="s"]', this.debounceSearch);
            
            // Table row hover effects
            $(document).on('mouseenter', '.wp-list-table tbody tr', this.highlightRow);
            $(document).on('mouseleave', '.wp-list-table tbody tr', this.unhighlightRow);
        },
        
        // Initialize tooltips for truncated content
        initTooltips: function() {
            $('[title]').each(function() {
                var $this = $(this);
                var title = $this.attr('title');
                
                if (title && title.length > 50) {
                    $this.attr('data-tooltip', title);
                    $this.removeAttr('title');
                    
                    $this.hover(
                        function() {
                            CSTAdmin.showTooltip(this, $(this).attr('data-tooltip'));
                        },
                        function() {
                            CSTAdmin.hideTooltip();
                        }
                    );
                }
            });
        },
        
        // Show custom tooltip
        showTooltip: function(element, text) {
            var $tooltip = $('<div class="cst-tooltip">' + text + '</div>');
            $('body').append($tooltip);
            
            var $element = $(element);
            var offset = $element.offset();
            var elementHeight = $element.outerHeight();
            
            $tooltip.css({
                position: 'absolute',
                top: offset.top + elementHeight + 5,
                left: offset.left,
                background: '#333',
                color: '#fff',
                padding: '8px 12px',
                borderRadius: '4px',
                fontSize: '12px',
                maxWidth: '300px',
                wordWrap: 'break-word',
                zIndex: 999999,
                boxShadow: '0 2px 8px rgba(0,0,0,0.3)'
            });
            
            // Adjust position if tooltip goes off screen
            var tooltipWidth = $tooltip.outerWidth();
            var windowWidth = $(window).width();
            
            if (offset.left + tooltipWidth > windowWidth) {
                $tooltip.css('left', windowWidth - tooltipWidth - 10);
            }
        },
        
        // Hide custom tooltip
        hideTooltip: function() {
            $('.cst-tooltip').remove();
        },
        
        // Open ticket details modal
        openTicketModal: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var ticketData = {
                id: $button.data('ticket-id'),
                name: $button.data('name'),
                email: $button.data('email'),
                phone: $button.data('phone'),
                message: $button.data('message'),
                date: $button.data('date')
            };
            
            // Populate modal with ticket data
            $('#modal-ticket-id').text(ticketData.id);
            $('#modal-name').text(ticketData.name);
            $('#modal-email').html('<a href="mailto:' + ticketData.email + '">' + ticketData.email + '</a>');
            
            if (ticketData.phone) {
                $('#modal-phone').html('<a href="tel:' + ticketData.phone + '">' + ticketData.phone + '</a>');
            } else {
                $('#modal-phone').html('<span style="color: #999;">—</span>');
            }
            
            $('#modal-date').text(ticketData.date);
            $('#modal-message').text(ticketData.message);
            
            // Show modal
            $('#cst-ticket-modal').fadeIn(300);
            $('body').addClass('cst-modal-open');
            
            // Focus on modal for accessibility
            setTimeout(function() {
                $('#cst-ticket-modal .cst-modal-content').focus();
            }, 350);
        },
        
        // Close modal
        closeModal: function() {
            $('.cst-modal').fadeOut(300);
            $('body').removeClass('cst-modal-open');
        },
        
        // Confirm delete action
        confirmDelete: function(e) {
            var confirmed = confirm('Are you sure you want to delete this ticket? This action cannot be undone.');
            if (!confirmed) {
                e.preventDefault();
                return false;
            }
        },
        
        // Debounced search function
        debounceSearch: (function() {
            var timer;
            return function() {
                clearTimeout(timer);
                var $input = $(this);
                timer = setTimeout(function() {
                    // Auto-submit search after 1 second of no typing
                    if ($input.val().length >= 3 || $input.val().length === 0) {
                        $input.closest('form').submit();
                    }
                }, 1000);
            };
        })(),
        
        // Highlight table row on hover
        highlightRow: function() {
            $(this).css('background-color', '#f8f9fa');
        },
        
        // Remove highlight from table row
        unhighlightRow: function() {
            $(this).css('background-color', '');
        },
        
        // Utility function to format dates
        formatDate: function(dateString) {
            var date = new Date(dateString);
            var options = {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            };
            return date.toLocaleDateString('en-US', options);
        },
        
        // Utility function to truncate text
        truncateText: function(text, maxLength) {
            if (text.length <= maxLength) {
                return text;
            }
            return text.substr(0, maxLength) + '...';
        },
        
        // Export functionality (for future enhancement)
        exportTickets: function(format) {
            // This could be implemented to export tickets as CSV/PDF
            console.log('Export tickets as ' + format);
        }
    };
    
})(jQuery);

// Add CSS for modal and admin enhancements
(function() {
    var style = document.createElement('style');
    style.textContent = `
        body.cst-modal-open {
            overflow: hidden;
        }
        
        .cst-tooltip {
            pointer-events: none;
            animation: cstTooltipFadeIn 0.2s ease-out;
        }
        
        @keyframes cstTooltipFadeIn {
            from {
                opacity: 0;
                transform: translateY(-5px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Focus styles for accessibility */
        .cst-modal-content:focus {
            outline: 2px solid #0073aa;
            outline-offset: 2px;
        }
        
        .cst-view-ticket:focus,
        .button:focus {
            outline: 2px solid #0073aa;
            outline-offset: 1px;
        }
        
        /* Loading state for table */
        .wp-list-table.loading {
            opacity: 0.6;
            pointer-events: none;
        }
        
        /* Enhanced table styles */
        .wp-list-table tbody tr:hover {
            background-color: #f8f9fa !important;
        }
        
        .wp-list-table td {
            position: relative;
        }
        
        /* Status indicators */
        .cst-status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 6px;
        }
        
        .cst-status-new .cst-status-indicator {
            background-color: #46b450;
        }
        
        .cst-status-read .cst-status-indicator {
            background-color: #999;
        }
        
        /* Responsive table improvements */
        @media (max-width: 782px) {
            .wp-list-table {
                border: 0;
            }
            
            .wp-list-table thead {
                display: none;
            }
            
            .wp-list-table tbody tr {
                display: block;
                border: 1px solid #ccc;
                margin-bottom: 10px;
                padding: 10px;
                border-radius: 4px;
            }
            
            .wp-list-table tbody td {
                display: block;
                text-align: left;
                border: none;
                padding: 5px 0;
            }
            
            .wp-list-table tbody td:before {
                content: attr(data-label) ": ";
                font-weight: bold;
                display: inline-block;
                width: 80px;
            }
        }
    `;
    document.head.appendChild(style);
})();

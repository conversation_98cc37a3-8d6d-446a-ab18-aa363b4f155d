/**
 * Custom Support Ticket Frontend JavaScript
 */

(function($) {
    'use strict';
    
    // Initialize when document is ready
    $(document).ready(function() {
        CST.init();
    });
    
    // Main CST object
    window.CST = {

        // Initialize the plugin
        init: function() {
            this.bindEvents();
        },
        
        // Bind event handlers
        bindEvents: function() {
            // Open popup
            $(document).on('click', '#cst-open-popup', this.openPopup);
            
            // Close popup
            $(document).on('click', '#cst-close-popup, #cst-cancel-btn', this.closePopup);
            $(document).on('click', '.cst-popup-overlay', function(e) {
                if (e.target === this) {
                    CST.closePopup();
                }
            });
            
            // Form submission
            $(document).on('submit', '#cst-support-form', this.submitForm);
            
            // Success/Error close buttons
            $(document).on('click', '#cst-success-close', this.closePopup);
            $(document).on('click', '#cst-error-close', this.showForm);
            
            // Escape key to close popup
            $(document).on('keydown', function(e) {
                if (e.keyCode === 27 && $('#cst-popup-overlay').is(':visible')) {
                    CST.closePopup();
                }
            });
            
            // Real-time validation
            $(document).on('blur', '#cst-support-form input, #cst-support-form textarea', this.validateField);
        },
        
        // Open the popup
        openPopup: function(e) {
            if (e && e.preventDefault) {
                e.preventDefault();
            }
            $('#cst-popup-overlay').fadeIn(300);
            $('body').addClass('cst-popup-open');

            // Focus on first input
            setTimeout(function() {
                $('#cst-name').focus();
            }, 350);
        },
        
        // Close the popup
        closePopup: function() {
            $('#cst-popup-overlay').fadeOut(300);
            $('body').removeClass('cst-popup-open');
            
            // Reset form after animation
            setTimeout(function() {
                CST.resetForm();
            }, 300);
        },
        
        // Reset the form to initial state
        resetForm: function() {
            $('#cst-support-form')[0].reset();
            this.showForm();
            this.clearValidation();
        },
        
        // Show the form (hide loading/success/error states)
        showForm: function() {
            $('#cst-support-form').show();
            $('#cst-loading, #cst-success, #cst-error').hide();
        },
        
        // Show loading state
        showLoading: function() {
            $('#cst-support-form').hide();
            $('#cst-success, #cst-error').hide();
            $('#cst-loading').show();
        },
        
        // Show success state
        showSuccess: function(message) {
            $('#cst-support-form, #cst-loading, #cst-error').hide();
            $('#cst-success').show();
            
            if (message) {
                $('#cst-success p').text(message);
            }
        },
        
        // Show error state
        showError: function(message) {
            $('#cst-support-form, #cst-loading, #cst-success').hide();
            $('#cst-error').show();
            $('#cst-error-text').text(message || cst_ajax.messages.error);
        },
        
        // Validate individual field
        validateField: function() {
            var $field = $(this);
            var value = $field.val().trim();
            var isValid = true;
            var errorMessage = '';
            
            // Remove existing error state
            $field.removeClass('error');
            $field.siblings('.cst-error-message').remove();
            
            // Check if field is required
            if ($field.prop('required') && !value) {
                isValid = false;
                errorMessage = 'This field is required.';
            }
            
            // Email validation
            if ($field.attr('type') === 'email' && value) {
                var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    isValid = false;
                    errorMessage = 'Please enter a valid email address.';
                }
            }
            
            // Phone validation (basic)
            if ($field.attr('type') === 'tel' && value) {
                var phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
                if (!phoneRegex.test(value)) {
                    isValid = false;
                    errorMessage = 'Please enter a valid phone number.';
                }
            }
            
            // Show error if validation failed
            if (!isValid) {
                $field.addClass('error');
                $field.after('<span class="cst-error-message">' + errorMessage + '</span>');
            }
            
            return isValid;
        },
        
        // Validate entire form
        validateForm: function() {
            var isValid = true;
            
            // Validate each required field
            $('#cst-support-form input[required], #cst-support-form textarea[required]').each(function() {
                if (!CST.validateField.call(this)) {
                    isValid = false;
                }
            });
            
            // Validate email field even if not required but has value
            var $email = $('#cst-email');
            if ($email.val().trim()) {
                if (!CST.validateField.call($email[0])) {
                    isValid = false;
                }
            }
            
            return isValid;
        },
        
        // Clear all validation errors
        clearValidation: function() {
            $('#cst-support-form input, #cst-support-form textarea').removeClass('error');
            $('#cst-support-form .cst-error-message').remove();
        },
        
        // Submit the form
        submitForm: function(e) {
            e.preventDefault();
            
            // Validate form
            if (!CST.validateForm()) {
                return false;
            }
            
            // Get form data
            var formData = {
                action: 'cst_submit_ticket',
                nonce: cst_ajax.nonce,
                name: $('#cst-name').val().trim(),
                phone: $('#cst-phone').val().trim(),
                email: $('#cst-email').val().trim(),
                message: $('#cst-message').val().trim()
            };
            
            // Show loading state
            CST.showLoading();
            
            // Submit via AJAX
            $.ajax({
                url: cst_ajax.ajax_url,
                type: 'POST',
                data: formData,
                timeout: 30000, // 30 seconds timeout
                success: function(response) {
                    if (response.success) {
                        CST.showSuccess(response.data);
                    } else {
                        CST.showError(response.data || cst_ajax.messages.error);
                    }
                },
                error: function(xhr, status, error) {
                    var errorMessage = cst_ajax.messages.error;
                    
                    if (status === 'timeout') {
                        errorMessage = 'Request timed out. Please try again.';
                    } else if (xhr.status === 0) {
                        errorMessage = 'Network error. Please check your connection.';
                    } else if (xhr.status >= 500) {
                        errorMessage = 'Server error. Please try again later.';
                    }
                    
                    CST.showError(errorMessage);
                }
            });
            
            return false;
        }
    };
    
})(jQuery);

// Global SupportTicket object for external function calls
window.SupportTicket = {

    // Open the popup programmatically
    popUp: function() {
        // Create popup HTML if it doesn't exist
        if ($('#cst-popup-overlay').length === 0) {
            this.createPopupHTML();
        }

        // Trigger the popup
        CST.openPopup();
        return true;
    },

    // Create popup HTML dynamically
    createPopupHTML: function() {
        var popupHTML = `
            <div id="cst-popup-overlay" class="cst-popup-overlay" style="display: none;">
                <div class="cst-popup-container">
                    <div class="cst-popup-header">
                        <h3>Contact Support</h3>
                        <button type="button" class="cst-popup-close" id="cst-close-popup">&times;</button>
                    </div>

                    <div class="cst-popup-body">
                        <form id="cst-support-form" method="post">
                            <div class="cst-form-group">
                                <label for="cst-name">Name <span class="required">*</span></label>
                                <input type="text" id="cst-name" name="name" required>
                            </div>

                            <div class="cst-form-group">
                                <label for="cst-phone">Phone Number</label>
                                <input type="tel" id="cst-phone" name="phone">
                            </div>

                            <div class="cst-form-group">
                                <label for="cst-email">Email <span class="required">*</span></label>
                                <input type="email" id="cst-email" name="email" required>
                            </div>

                            <div class="cst-form-group">
                                <label for="cst-message">Message <span class="required">*</span></label>
                                <textarea id="cst-message" name="message" rows="5" required></textarea>
                            </div>

                            <div class="cst-form-actions">
                                <button type="button" class="cst-btn cst-btn-secondary" id="cst-cancel-btn">Cancel</button>
                                <button type="submit" class="cst-btn cst-btn-primary" id="cst-submit-btn">Send Message</button>
                            </div>
                        </form>

                        <div id="cst-loading" class="cst-loading" style="display: none;">
                            <div class="cst-spinner"></div>
                            <p>Sending your message...</p>
                        </div>

                        <div id="cst-success" class="cst-message cst-success" style="display: none;">
                            <div class="cst-message-icon">✓</div>
                            <h4>Message Sent Successfully!</h4>
                            <p>Thank you for contacting us. We will get back to you soon.</p>
                            <button type="button" class="cst-btn cst-btn-primary" id="cst-success-close">Close</button>
                        </div>

                        <div id="cst-error" class="cst-message cst-error" style="display: none;">
                            <div class="cst-message-icon">✗</div>
                            <h4>Error Sending Message</h4>
                            <p id="cst-error-text"></p>
                            <button type="button" class="cst-btn cst-btn-primary" id="cst-error-close">Try Again</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        $('body').append(popupHTML);
    },

    // Close the popup programmatically
    close: function() {
        CST.closePopup();
        return true;
    },

    // Check if popup is open
    isOpen: function() {
        return $('#cst-popup-overlay').is(':visible');
    },

    // Pre-fill form data
    fillForm: function(data) {
        if (data.name) $('#cst-name').val(data.name);
        if (data.email) $('#cst-email').val(data.email);
        if (data.phone) $('#cst-phone').val(data.phone);
        if (data.message) $('#cst-message').val(data.message);
        return true;
    },

    // Reset form
    resetForm: function() {
        CST.resetForm();
        return true;
    },

    // Submit form programmatically
    submit: function() {
        $('#cst-support-form').trigger('submit');
        return true;
    }
};

// Add CSS to prevent body scroll when popup is open
(function() {
    var style = document.createElement('style');
    style.textContent = `
        body.cst-popup-open {
            overflow: hidden;
        }
        
        /* Accessibility improvements */
        .cst-popup-overlay:focus {
            outline: none;
        }
        
        .cst-popup-container {
            outline: none;
        }
        
        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .cst-button {
                border: 2px solid currentColor;
            }
            
            .cst-popup-container {
                border: 2px solid #000;
            }
            
            .cst-form-group input,
            .cst-form-group textarea {
                border: 2px solid #000;
            }
        }
        
        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .cst-popup-container {
                animation: none;
            }
            
            .cst-spinner {
                animation: none;
                border: 4px solid #ccc;
                border-top-color: #0073aa;
            }
            
            * {
                transition: none !important;
            }
        }
    `;
    document.head.appendChild(style);
})();

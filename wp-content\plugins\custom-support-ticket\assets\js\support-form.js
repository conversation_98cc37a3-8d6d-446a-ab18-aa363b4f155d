/**
 * Support Ticket Form Manager
 * Professional popup form system for support tickets
 */

(function() {
    'use strict';
    
    // WordPress-compatible DOM ready function
    function domReady(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', callback);
        } else {
            callback();
        }
    }
    
    // WordPress-compatible jQuery wrapper
    function withJQuery(callback) {
        if (typeof jQuery !== 'undefined') {
            callback(jQuery);
        } else {
            callback(null);
        }
    }
    
    // Main Support Form Manager
    var SupportFormManager = {
        
        isOpen: false,
        $: null,
        
        // Initialize the form manager
        init: function($) {
            this.$ = $ || null;
            this.bindEvents();
        },
        
        // Bind event handlers
        bindEvents: function() {
            var self = this;
            
            if (this.$) {
                // jQuery version
                this.$(document).on('click', '.support-form-trigger', function(e) {
                    e.preventDefault();
                    self.showForm();
                });
                
                this.$(document).on('click', '.support-form-close', function() {
                    self.hideForm();
                });

                this.$(document).on('click', '.cst-btn-cancel', function() {
                    self.hideForm();
                });

                this.$(document).on('click', '.support-form-overlay', function(e) {
                    if (e.target === this) {
                        self.hideForm();
                    }
                });
                
                this.$(document).on('submit', '#support-ticket-form', function(e) {
                    e.preventDefault();
                    self.submitForm();
                });
                
                // Escape key to close
                this.$(document).on('keydown', function(e) {
                    if (e.key === 'Escape' && self.isOpen) {
                        self.hideForm();
                    }
                });
            } else {
                // Vanilla JavaScript fallback
                document.addEventListener('click', function(e) {
                    if (e.target.classList.contains('support-form-trigger')) {
                        e.preventDefault();
                        self.showForm();
                    }
                    if (e.target.classList.contains('support-form-close')) {
                        self.hideForm();
                    }
                    if (e.target.classList.contains('cst-btn-cancel')) {
                        self.hideForm();
                    }
                });
                
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && self.isOpen) {
                        self.hideForm();
                    }
                });
            }
        },
        
        // Show the support form
        showForm: function(options) {
            options = options || {};
            
            if (!this.formExists()) {
                this.createForm();
            }
            
            // Pre-fill form if data provided
            if (options.prefill) {
                this.fillForm(options.prefill);
            }
            
            // Show the form
            this.isOpen = true;
            var overlay = document.getElementById('support-form-overlay');
            if (overlay) {
                overlay.style.display = 'flex';
                document.body.classList.add('support-form-open');
                
                // Focus on first input
                setTimeout(function() {
                    var firstInput = overlay.querySelector('input[type="text"], input[type="email"], textarea');
                    if (firstInput) {
                        firstInput.focus();
                    }
                }, 300);
            }
        },
        
        // Hide the support form
        hideForm: function() {
            this.isOpen = false;
            var overlay = document.getElementById('support-form-overlay');
            if (overlay) {
                overlay.style.display = 'none';
                document.body.classList.remove('support-form-open');
                this.resetForm();
            }
        },
        
        // Check if form exists in DOM
        formExists: function() {
            return document.getElementById('support-form-overlay') !== null;
        },
        
        // Reset form to initial state
        resetForm: function() {
            var form = document.getElementById('support-ticket-form');
            if (form) {
                form.reset();
                this.clearValidation();
                this.showFormState();
            }
        },
        
        // Show form state (hide loading/success/error)
        showFormState: function() {
            var form = document.getElementById('support-ticket-form');
            var loading = document.getElementById('support-form-loading');
            var success = document.getElementById('support-form-success');
            var error = document.getElementById('support-form-error');
            
            if (form) form.style.display = 'block';
            if (loading) loading.style.display = 'none';
            if (success) success.style.display = 'none';
            if (error) error.style.display = 'none';
        },
        
        // Show loading state
        showLoadingState: function() {
            var form = document.getElementById('support-ticket-form');
            var loading = document.getElementById('support-form-loading');
            
            if (form) form.style.display = 'none';
            if (loading) loading.style.display = 'block';
        },
        
        // Show success state with animation
        showSuccessState: function(message) {
            var form = document.getElementById('support-ticket-form');
            var loading = document.getElementById('support-form-loading');
            var success = document.getElementById('support-form-success');
            var successText = document.getElementById('support-success-text');

            if (form) form.style.display = 'none';
            if (loading) loading.style.display = 'none';
            if (success) {
                success.style.display = 'block';
                success.style.opacity = '0';
                success.style.transform = 'scale(0.9)';

                // Animate in
                setTimeout(function() {
                    success.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                    success.style.opacity = '1';
                    success.style.transform = 'scale(1)';
                }, 10);
            }

            if (successText && message) {
                successText.textContent = message;
            }

            // Add confetti effect (simple version)
            this.addConfettiEffect();
        },

        // Simple confetti effect
        addConfettiEffect: function() {
            var container = document.getElementById('support-form-success');
            if (!container) return;

            for (var i = 0; i < 20; i++) {
                setTimeout(function() {
                    var confetti = document.createElement('div');
                    confetti.style.cssText = `
                        position: absolute;
                        width: 6px;
                        height: 6px;
                        background: linear-gradient(45deg, #667eea, #764ba2);
                        border-radius: 50%;
                        pointer-events: none;
                        z-index: 1000;
                        animation: confetti 2s ease-out forwards;
                    `;

                    confetti.style.left = Math.random() * 100 + '%';
                    confetti.style.top = '20%';

                    container.appendChild(confetti);

                    setTimeout(function() {
                        if (confetti.parentNode) {
                            confetti.parentNode.removeChild(confetti);
                        }
                    }, 2000);
                }, i * 100);
            }

            // Add confetti animation to CSS if not exists
            if (!document.getElementById('confetti-styles')) {
                var style = document.createElement('style');
                style.id = 'confetti-styles';
                style.textContent = `
                    @keyframes confetti {
                        0% {
                            transform: translateY(0) rotate(0deg);
                            opacity: 1;
                        }
                        100% {
                            transform: translateY(200px) rotate(720deg);
                            opacity: 0;
                        }
                    }
                `;
                document.head.appendChild(style);
            }
        },
        
        // Show error state
        showErrorState: function(message) {
            var form = document.getElementById('support-ticket-form');
            var loading = document.getElementById('support-form-loading');
            var error = document.getElementById('support-form-error');
            var errorText = document.getElementById('support-error-text');
            
            if (form) form.style.display = 'none';
            if (loading) loading.style.display = 'none';
            if (error) error.style.display = 'block';
            if (errorText) errorText.textContent = message || 'An error occurred. Please try again.';
        },
        
        // Create the form HTML
        createForm: function() {
            var formHTML = `
                <div id="support-form-overlay" class="support-form-overlay" style="display: none;">
                    <div class="support-form-container">
                        <div class="support-form-header">
                            <h3>Contact Support</h3>
                            <button type="button" class="support-form-close">&times;</button>
                        </div>
                        
                        <div class="support-form-body">
                            <form id="support-ticket-form">
                                <div class="cst-form-group">
                                    <label for="support-name">Name <span class="cst-required">*</span></label>
                                    <input type="text" id="support-name" name="name" required>
                                </div>

                                <div class="cst-form-group">
                                    <label for="support-email">Email <span class="cst-required">*</span></label>
                                    <input type="email" id="support-email" name="email" required>
                                </div>

                                <div class="cst-form-group">
                                    <label for="support-phone">Phone</label>
                                    <input type="tel" id="support-phone" name="phone">
                                </div>

                                <div class="cst-form-group">
                                    <label for="support-message">Message <span class="cst-required">*</span></label>
                                    <textarea id="support-message" name="message" rows="5" required></textarea>
                                </div>

                                <div class="cst-form-actions">
                                    <button type="button" class="cst-btn cst-btn-secondary cst-btn-cancel">Cancel</button>
                                    <button type="submit" class="cst-btn cst-btn-primary">Send Message</button>
                                </div>
                            </form>
                            
                            <div id="support-form-loading" class="form-state" style="display: none;">
                                <div class="loading-spinner"></div>
                                <p>Sending your message...</p>
                            </div>
                            
                            <div id="support-form-success" class="cst-form-state" style="display: none;">
                                <div class="cst-success-icon">✓</div>
                                <h4>Message Sent Successfully!</h4>
                                <p id="support-success-text">Thank you for contacting us. We will get back to you soon.</p>
                                <button type="button" class="cst-btn cst-btn-primary support-form-close">Close</button>
                            </div>

                            <div id="support-form-error" class="cst-form-state" style="display: none;">
                                <div class="cst-error-icon">✗</div>
                                <h4>Error Sending Message</h4>
                                <p id="support-error-text">An error occurred. Please try again.</p>
                                <button type="button" class="cst-btn cst-btn-primary" onclick="SupportTicket.resetForm(); SupportTicket.form();">Try Again</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', formHTML);
        },

        // Fill form with data
        fillForm: function(data) {
            if (data.name) {
                var nameField = document.getElementById('support-name');
                if (nameField) nameField.value = data.name;
            }
            if (data.email) {
                var emailField = document.getElementById('support-email');
                if (emailField) emailField.value = data.email;
            }
            if (data.phone) {
                var phoneField = document.getElementById('support-phone');
                if (phoneField) phoneField.value = data.phone;
            }
            if (data.message) {
                var messageField = document.getElementById('support-message');
                if (messageField) messageField.value = data.message;
            }
        },

        // Validate form
        validateForm: function() {
            var isValid = true;
            this.clearValidation();

            // Get form fields
            var nameField = document.getElementById('support-name');
            var emailField = document.getElementById('support-email');
            var messageField = document.getElementById('support-message');

            // Validate name
            if (!nameField || !nameField.value.trim()) {
                this.showFieldError(nameField, 'Name is required');
                isValid = false;
            }

            // Validate email
            if (!emailField || !emailField.value.trim()) {
                this.showFieldError(emailField, 'Email is required');
                isValid = false;
            } else if (!this.isValidEmail(emailField.value)) {
                this.showFieldError(emailField, 'Please enter a valid email address');
                isValid = false;
            }

            // Validate message
            if (!messageField || !messageField.value.trim()) {
                this.showFieldError(messageField, 'Message is required');
                isValid = false;
            }

            return isValid;
        },

        // Show field error with animation
        showFieldError: function(field, message) {
            if (!field) return;

            field.classList.add('cst-error');

            // Add shake animation
            field.style.animation = 'cst-shake 0.5s ease-in-out';
            setTimeout(function() {
                field.style.animation = '';
            }, 500);

            // Remove existing error message
            var existingError = field.parentNode.querySelector('.cst-error-message');
            if (existingError) {
                existingError.remove();
            }

            // Add new error message with slide-down animation
            var errorElement = document.createElement('span');
            errorElement.className = 'cst-error-message';
            errorElement.textContent = message;
            errorElement.style.opacity = '0';
            errorElement.style.transform = 'translateY(-10px)';
            field.parentNode.appendChild(errorElement);

            // Animate in
            setTimeout(function() {
                errorElement.style.transition = 'all 0.3s ease-out';
                errorElement.style.opacity = '1';
                errorElement.style.transform = 'translateY(0)';
            }, 10);
        },

        // Clear validation errors
        clearValidation: function() {
            var form = document.getElementById('support-ticket-form');
            if (!form) return;

            // Remove error classes
            var errorFields = form.querySelectorAll('.cst-error');
            errorFields.forEach(function(field) {
                field.classList.remove('cst-error');
            });

            // Remove error messages
            var errorMessages = form.querySelectorAll('.cst-error-message');
            errorMessages.forEach(function(message) {
                message.remove();
            });
        },

        // Validate email format
        isValidEmail: function(email) {
            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },

        // Submit the form
        submitForm: function() {
            if (!this.validateForm()) {
                return false;
            }

            // Get form data
            var nameField = document.getElementById('support-name');
            var emailField = document.getElementById('support-email');
            var phoneField = document.getElementById('support-phone');
            var messageField = document.getElementById('support-message');

            var formData = {
                action: 'cst_submit_ticket',
                nonce: (typeof cst_ajax !== 'undefined' ? cst_ajax.nonce : ''),
                name: nameField ? nameField.value.trim() : '',
                email: emailField ? emailField.value.trim() : '',
                phone: phoneField ? phoneField.value.trim() : '',
                message: messageField ? messageField.value.trim() : ''
            };

            // Show loading state
            this.showLoadingState();

            // Submit via AJAX or fetch
            this.sendFormData(formData);

            return false;
        },

        // Send form data to server
        sendFormData: function(formData) {
            var self = this;

            if (this.$ && typeof cst_ajax !== 'undefined') {
                // jQuery AJAX version
                this.$.ajax({
                    url: cst_ajax.ajax_url,
                    type: 'POST',
                    data: formData,
                    timeout: 30000,
                    success: function(response) {
                        if (response.success) {
                            self.showSuccessState(response.data || 'Message sent successfully!');
                        } else {
                            self.showErrorState(response.data || 'Failed to send message. Please try again.');
                        }
                    },
                    error: function(xhr, status) {
                        var errorMessage = 'An error occurred. Please try again.';
                        if (status === 'timeout') {
                            errorMessage = 'Request timed out. Please try again.';
                        } else if (xhr.status === 0) {
                            errorMessage = 'Network error. Please check your connection.';
                        }
                        self.showErrorState(errorMessage);
                    }
                });
            } else {
                // Fetch API fallback
                var url = (typeof cst_ajax !== 'undefined' ? cst_ajax.ajax_url : '/wp-admin/admin-ajax.php');
                var formBody = Object.keys(formData).map(function(key) {
                    return encodeURIComponent(key) + '=' + encodeURIComponent(formData[key]);
                }).join('&');

                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: formBody
                })
                .then(function(response) {
                    return response.json();
                })
                .then(function(data) {
                    if (data.success) {
                        self.showSuccessState(data.data || 'Message sent successfully!');
                    } else {
                        self.showErrorState(data.data || 'Failed to send message. Please try again.');
                    }
                })
                .catch(function() {
                    self.showErrorState('Network error. Please try again.');
                });
            }
        }
    };

    // Global SupportTicket object for external function calls
    window.SupportTicket = {

        // Show support form
        form: function(options) {
            return SupportFormManager.showForm(options);
        },

        // Legacy method names for compatibility
        popUp: function(options) {
            return this.form(options);
        },

        // Close the form
        close: function() {
            SupportFormManager.hideForm();
            return true;
        },

        // Check if form is open
        isOpen: function() {
            return SupportFormManager.isOpen;
        },

        // Fill form with data
        fillForm: function(data) {
            SupportFormManager.fillForm(data);
            return true;
        },

        // Reset form
        resetForm: function() {
            SupportFormManager.resetForm();
            return true;
        }
    };

    // Only expose SupportFormManager in development mode
    if (window.location.search.includes('debug=1') || window.location.search.includes('cst_debug=1')) {
        window.SupportFormManager = SupportFormManager;
        console.log('🔧 SupportFormManager exposed for debugging');
    }

    // Initialize when DOM is ready
    domReady(function() {
        withJQuery(function($) {
            SupportFormManager.init($);
        });
    });

})();

/**
 * Support Form Triggers
 * Professional trigger buttons and integration examples
 */

(function() {
    'use strict';
    
    // WordPress-compatible DOM ready function
    function domReady(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', callback);
        } else {
            callback();
        }
    }
    
    // Support Form Triggers Manager
    var SupportTriggers = {
        
        // Add floating support button
        addFloatingButton: function(options) {
            options = options || {};

            console.log('CST: Adding floating button with options:', options);

            var buttonText = options.text || '💬 Need Help?';
            var buttonPosition = options.position || 'bottom-right';
            var buttonColor = options.color || '#0073aa';
            
            // Create floating button
            var floatingBtn = document.createElement('div');
            floatingBtn.id = 'floating-support-btn';
            floatingBtn.className = 'support-form-trigger floating-support-button';
            floatingBtn.innerHTML = buttonText;
            
            // Position styles
            var positions = {
                'bottom-right': { bottom: '20px', right: '20px' },
                'bottom-left': { bottom: '20px', left: '20px' },
                'top-right': { top: '20px', right: '20px' },
                'top-left': { top: '20px', left: '20px' }
            };
            
            var pos = positions[buttonPosition] || positions['bottom-right'];
            
            floatingBtn.style.cssText = `
                position: fixed;
                ${pos.bottom ? 'bottom: ' + pos.bottom : ''}
                ${pos.top ? 'top: ' + pos.top : ''}
                ${pos.left ? 'left: ' + pos.left : ''}
                ${pos.right ? 'right: ' + pos.right : ''}
                z-index: 1000;
                background: ${buttonColor};
                color: white;
                padding: 15px 20px;
                border-radius: 50px;
                cursor: pointer;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                font-weight: bold;
                font-size: 14px;
                transition: all 0.3s ease;
                user-select: none;
                display: block;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            `;
            
            // Hover effect
            floatingBtn.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
                this.style.boxShadow = '0 6px 16px rgba(0,0,0,0.4)';
            });
            
            floatingBtn.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = '0 4px 12px rgba(0,0,0,0.3)';
            });

            document.body.appendChild(floatingBtn);
            console.log('CST: Floating button added to page');

            return floatingBtn;
        },
        
        // Add support button to specific element
        addButtonToElement: function(selector, options) {
            options = options || {};
            
            var elements = document.querySelectorAll(selector);
            if (elements.length === 0) {
                console.warn('No elements found for selector:', selector);
                return;
            }
            
            var buttonText = options.text || 'Contact Support';
            var buttonClass = options.class || 'support-btn';
            
            elements.forEach(function(element) {
                var button = document.createElement('button');
                button.type = 'button';
                button.className = 'support-form-trigger ' + buttonClass;
                button.textContent = buttonText;
                button.style.cssText = `
                    background: #0073aa;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                    margin: 10px 0;
                    transition: background 0.3s ease;
                `;
                
                button.addEventListener('mouseenter', function() {
                    this.style.background = '#005a87';
                });
                
                button.addEventListener('mouseleave', function() {
                    this.style.background = '#0073aa';
                });
                
                element.appendChild(button);
            });
        },
        
        // Add support link to navigation menu
        addToNavigation: function(options) {
            options = options || {};
            
            var navSelectors = [
                '.main-navigation ul',
                '.primary-menu',
                '.nav-menu',
                'nav ul',
                '.menu ul'
            ];
            
            var navFound = false;
            
            for (var i = 0; i < navSelectors.length; i++) {
                var nav = document.querySelector(navSelectors[i]);
                if (nav) {
                    var listItem = document.createElement('li');
                    listItem.className = 'menu-item support-menu-item';
                    
                    var link = document.createElement('a');
                    link.href = '#';
                    link.className = 'support-form-trigger';
                    link.textContent = options.text || 'Support';
                    link.style.cssText = 'text-decoration: none;';
                    
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                    });
                    
                    listItem.appendChild(link);
                    nav.appendChild(listItem);
                    navFound = true;
                    break;
                }
            }
            
            if (!navFound) {
                console.warn('No navigation menu found to add support link');
            }
        },
        
        // Add support button to WordPress admin bar
        addToAdminBar: function() {
            var adminBar = document.getElementById('wpadminbar');
            if (!adminBar) return;
            
            var topSecondary = adminBar.querySelector('.ab-top-secondary');
            if (!topSecondary) return;
            
            var supportItem = document.createElement('li');
            supportItem.id = 'wp-admin-bar-support';
            
            var supportLink = document.createElement('a');
            supportLink.href = '#';
            supportLink.className = 'ab-item support-form-trigger';
            supportLink.style.cssText = 'color: #fff; text-decoration: none;';
            
            var icon = document.createElement('span');
            icon.className = 'ab-icon dashicons dashicons-sos';
            icon.style.cssText = 'margin-top: 2px;';
            
            var label = document.createElement('span');
            label.className = 'ab-label';
            label.textContent = 'Support';
            
            supportLink.appendChild(icon);
            supportLink.appendChild(label);
            supportItem.appendChild(supportLink);
            
            supportLink.addEventListener('click', function(e) {
                e.preventDefault();
            });
            
            topSecondary.insertBefore(supportItem, topSecondary.firstChild);
        },
        
        // Initialize common triggers
        init: function() {
            // Debug: Check if configuration is available
            if (typeof cstFloatingConfig !== 'undefined') {
                console.log('CST: Floating config found:', cstFloatingConfig);
                if (cstFloatingConfig.enabled) {
                    console.log('CST: Adding floating button');
                    this.addFloatingButton(cstFloatingConfig);
                } else {
                    console.log('CST: Floating button disabled in config');
                }
            } else {
                console.log('CST: No floating config found, adding default button');
                this.addFloatingButton();
            }

            // Add to admin bar if present
            this.addToAdminBar();
            
            // Add to common page elements
            this.addButtonToElement('.error-404, .not-found', {
                text: 'Need Help Finding Something?',
                class: 'error-support-btn'
            });
        }
    };
    
    // Only expose SupportTriggers in development mode
    if (window.location.search.includes('debug=1') || window.location.search.includes('cst_debug=1')) {
        window.SupportTriggers = SupportTriggers;
        console.log('🔧 SupportTriggers exposed for debugging');
    }

    // Auto-initialize when DOM is ready
    domReady(function() {
        // Only auto-init if not disabled
        if (!window.DISABLE_AUTO_SUPPORT_TRIGGERS) {
            SupportTriggers.init();
        }
    });

    // Public helper functions (these are useful for developers)
    window.addSupportButton = function(selector, options) {
        SupportTriggers.addButtonToElement(selector, options);
    };

    window.addFloatingSupportButton = function(options) {
        SupportTriggers.addFloatingButton(options);
    };

})();

<?php
/**
 * Plugin Name: Custom Support Ticket
 * Plugin URI: https://localhost
 * Description: A popup contact form plugin that saves submissions to wp_posts with post_type='contact' and provides admin interface for managing tickets
 * Version: 1.0.0
 * Author: Azirul A
 * Text Domain: custom-support-ticket
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.3
 * Requires PHP: 7.4
 * License: GPLv2 or later
 * License URI: http://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('CST_VERSION', '1.0.0');
define('CST_FILE', __FILE__);
define('CST_DIR', plugin_dir_path(__FILE__));
define('CST_URL', plugin_dir_url(__FILE__));

/**
 * Main Custom Support Ticket Plugin Class
 */
class CustomSupportTicket {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // AJAX handlers
        add_action('wp_ajax_cst_submit_ticket', array($this, 'handle_ticket_submission'));
        add_action('wp_ajax_nopriv_cst_submit_ticket', array($this, 'handle_ticket_submission'));
        
        // Plugin activation/deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        load_plugin_textdomain('custom-support-ticket', false, dirname(plugin_basename(CST_FILE)) . '/languages');

        // Register contact post type
        $this->register_contact_post_type();

        // Add shortcode
        add_shortcode('support_ticket_button', array($this, 'support_ticket_button_shortcode'));
    }
    
    /**
     * Enqueue frontend scripts and styles
     * Always enqueue so SupportTicket.popUp() is available globally
     */
    public function enqueue_scripts() {
        // Enqueue the new professional support form
        wp_enqueue_script('cst-support-form', CST_URL . 'assets/js/support-form.js', array('jquery'), CST_VERSION, true);
        wp_enqueue_script('cst-support-triggers', CST_URL . 'assets/js/support-triggers.js', array('cst-support-form'), CST_VERSION, true);

        // Enqueue modern CSS styles with higher priority
        wp_enqueue_style('cst-modern-form', CST_URL . 'assets/css/modern-support-form.css', array(), CST_VERSION);

        // Add floating button configuration
        $floating_config = $this->get_floating_button_config();
        wp_localize_script('cst-support-triggers', 'cstFloatingConfig', $floating_config);

        // Add inline CSS to ensure modern styles take precedence
        $modern_css_override = "
        /* Force modern support button styles */
        .support-form-container .cst-btn {
            padding: 14px 24px !important;
            border: none !important;
            border-radius: 12px !important;
            cursor: pointer !important;
            font-size: 16px !important;
            font-weight: 600 !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            text-decoration: none !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            position: relative !important;
            overflow: hidden !important;
            box-sizing: border-box !important;
            min-width: 120px !important;
            white-space: nowrap !important;
            flex-shrink: 0 !important;
        }

        .support-form-container .cst-btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
            border: 2px solid transparent !important;
        }

        .support-form-container .cst-btn-primary:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
        }

        .support-form-container .cst-btn-secondary {
            background: white !important;
            color: #4a5568 !important;
            border: 2px solid #e2e8f0 !important;
        }

        .support-form-container .cst-btn-secondary:hover {
            background: #f7fafc !important;
            border-color: #cbd5e0 !important;
            transform: translateY(-1px) !important;
        }

        .support-form-container .cst-form-actions {
            display: flex !important;
            flex-direction: row !important;
            flex-wrap: nowrap !important;
            gap: 16px !important;
            justify-content: flex-end !important;
            align-items: center !important;
            margin-top: 32px !important;
            padding-top: 24px !important;
            border-top: 1px solid #e2e8f0 !important;
            width: 100% !important;
            box-sizing: border-box !important;
        }
        ";

        wp_add_inline_style('cst-modern-form', $modern_css_override);

        // Localize script for AJAX
        wp_localize_script('cst-support-form', 'cst_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('cst_nonce'),
            'messages' => array(
                'success' => __('Thank you! Your message has been sent successfully.', 'custom-support-ticket'),
                'error' => __('Sorry, there was an error sending your message. Please try again.', 'custom-support-ticket'),
                'validation_error' => __('Please fill in all required fields.', 'custom-support-ticket')
            )
        ));
    }

    /**
     * Get floating button configuration
     */
    private function get_floating_button_config() {
        return array(
            // Show/Hide floating button
            'enabled' => true, // Set to true to show floating button

            // Position settings
            'position' => 'bottom-right', // Options: 'bottom-right', 'bottom-left', 'top-right', 'top-left'

            // Custom positioning (overrides position setting if set)
            'custom_position' => array(
                'bottom' => '24px',  // Distance from bottom
                'right' => '24px',   // Distance from right
                'top' => '',         // Distance from top (leave empty for bottom positioning)
                'left' => '',        // Distance from left (leave empty for right positioning)
            ),

            // Button text and styling
            'text' => 'Need Help?',
            'background_color' => '', // Leave empty for default gradient
            'text_color' => '#ffffff',

            // Size and spacing
            'padding' => '16px 24px',
            'font_size' => '14px',
            'border_radius' => '50px',

            // Animation and effects
            'hover_effect' => true,
            'shadow' => true,

            // Z-index (for layering)
            'z_index' => 1000,

            // Mobile settings
            'mobile_enabled' => true,
            'mobile_position' => array(
                'bottom' => '16px',
                'right' => '16px',
                'padding' => '14px 20px',
                'font_size' => '13px',
            ),
        );
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function admin_enqueue_scripts($hook) {
        if ('toplevel_page_custom-support-tickets' !== $hook) {
            return;
        }
        
        wp_enqueue_style('cst-admin', CST_URL . 'assets/css/admin.css', array(), CST_VERSION);
        wp_enqueue_script('cst-admin', CST_URL . 'assets/js/admin.js', array('jquery'), CST_VERSION, true);
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('Support Tickets', 'custom-support-ticket'),
            __('Support Tickets', 'custom-support-ticket'),
            'manage_options',
            'custom-support-tickets',
            array($this, 'admin_page'),
            'dashicons-email-alt',
            30
        );
    }
    
    /**
     * Admin page content
     */
    public function admin_page() {
        include CST_DIR . 'includes/admin-page.php';
    }
    
    /**
     * Handle ticket submission via AJAX
     */
    public function handle_ticket_submission() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cst_nonce')) {
            wp_die(__('Security check failed', 'custom-support-ticket'));
        }
        
        // Sanitize and validate input
        $name = sanitize_text_field($_POST['name']);
        $phone = sanitize_text_field($_POST['phone']);
        $email = sanitize_email($_POST['email']);
        $message = sanitize_textarea_field($_POST['message']);
        
        // Validation
        if (empty($name) || empty($email) || empty($message)) {
            wp_send_json_error(__('Please fill in all required fields.', 'custom-support-ticket'));
        }
        
        if (!is_email($email)) {
            wp_send_json_error(__('Please enter a valid email address.', 'custom-support-ticket'));
        }
        
        // Save to database
        $result = $this->save_ticket($name, $phone, $email, $message);
        
        if ($result) {
            wp_send_json_success(__('Thank you! Your message has been sent successfully.', 'custom-support-ticket'));
        } else {
            wp_send_json_error(__('Sorry, there was an error sending your message. Please try again.', 'custom-support-ticket'));
        }
    }
    
    /**
     * Save ticket to database
     */
    private function save_ticket($name, $phone, $email, $message) {
        $post_data = array(
            'post_title' => sprintf(__('Support Ticket from %s', 'custom-support-ticket'), $name),
            'post_content' => $message,
            'post_status' => 'publish',
            'post_type' => 'contact',
            'meta_input' => array(
                'contact_name' => $name,
                'contact_phone' => $phone,
                'contact_email' => $email,
                'contact_date' => current_time('mysql')
            )
        );
        
        $post_id = wp_insert_post($post_data);
        
        return $post_id ? $post_id : false;
    }
    
    /**
     * Support ticket button shortcode
     */
    public function support_ticket_button_shortcode($atts) {
        $atts = shortcode_atts(array(
            'text' => __('Contact Support', 'custom-support-ticket'),
            'class' => 'cst-button'
        ), $atts);
        
        ob_start();
        include CST_DIR . 'templates/button.php';
        return ob_get_clean();
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create custom post type for contact entries
        $this->register_contact_post_type();
        
        // Flush rewrite rules
        flush_rewrite_rules();
        
        error_log('Custom Support Ticket Plugin activated');
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
        
        error_log('Custom Support Ticket Plugin deactivated');
    }
    
    /**
     * Register contact post type
     */
    private function register_contact_post_type() {
        register_post_type('contact', array(
            'labels' => array(
                'name' => __('Contact Submissions', 'custom-support-ticket'),
                'singular_name' => __('Contact Submission', 'custom-support-ticket')
            ),
            'public' => false,
            'show_ui' => false,
            'show_in_menu' => false,
            'supports' => array('title', 'editor', 'custom-fields')
        ));
    }
}

// Initialize the plugin
CustomSupportTicket::get_instance();

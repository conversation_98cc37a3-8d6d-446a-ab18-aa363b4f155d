<?php
/**
 * Admin page for displaying support tickets
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current page for pagination
$current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$per_page = 20;
$offset = ($current_page - 1) * $per_page;

// Get search query
$search = isset($_GET['s']) ? sanitize_text_field($_GET['s']) : '';

// Build query arguments
$args = array(
    'post_type' => 'contact',
    'post_status' => 'publish',
    'posts_per_page' => $per_page,
    'offset' => $offset,
    'orderby' => 'date',
    'order' => 'DESC'
);

// Add search if provided
if (!empty($search)) {
    $args['meta_query'] = array(
        'relation' => 'OR',
        array(
            'key' => 'contact_name',
            'value' => $search,
            'compare' => 'LIKE'
        ),
        array(
            'key' => 'contact_email',
            'value' => $search,
            'compare' => 'LIKE'
        )
    );
}

// Get tickets
$tickets_query = new WP_Query($args);
$tickets = $tickets_query->posts;

// Get total count for pagination
$total_args = $args;
$total_args['posts_per_page'] = -1;
$total_args['fields'] = 'ids';
unset($total_args['offset']);
$total_query = new WP_Query($total_args);
$total_tickets = $total_query->found_posts;
$total_pages = ceil($total_tickets / $per_page);

// Handle ticket deletion
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['ticket_id'])) {
    $ticket_id = intval($_GET['ticket_id']);
    if (wp_verify_nonce($_GET['_wpnonce'], 'delete_ticket_' . $ticket_id)) {
        wp_delete_post($ticket_id, true);
        echo '<div class="notice notice-success"><p>' . __('Ticket deleted successfully.', 'custom-support-ticket') . '</p></div>';
        // Refresh the page to update the list
        echo '<script>window.location.href = "' . admin_url('admin.php?page=custom-support-tickets') . '";</script>';
    }
}
?>

<div class="wrap">
    <h1 class="wp-heading-inline"><?php _e('Support Tickets', 'custom-support-ticket'); ?></h1>
    
    <!-- Search Form -->
    <form method="get" class="search-form" style="float: right; margin-top: 10px;">
        <input type="hidden" name="page" value="custom-support-tickets">
        <input type="search" name="s" value="<?php echo esc_attr($search); ?>" placeholder="<?php _e('Search by name or email...', 'custom-support-ticket'); ?>">
        <input type="submit" class="button" value="<?php _e('Search', 'custom-support-ticket'); ?>">
        <?php if (!empty($search)): ?>
            <a href="<?php echo admin_url('admin.php?page=custom-support-tickets'); ?>" class="button"><?php _e('Clear', 'custom-support-ticket'); ?></a>
        <?php endif; ?>
    </form>
    
    <div style="clear: both;"></div>
    
    <!-- Statistics -->
    <div class="cst-stats" style="margin: 20px 0;">
        <p><strong><?php printf(__('Total Tickets: %d', 'custom-support-ticket'), $total_tickets); ?></strong></p>
        <?php if (!empty($search)): ?>
            <p><?php printf(__('Showing results for: "%s"', 'custom-support-ticket'), esc_html($search)); ?></p>
        <?php endif; ?>
    </div>
    
    <!-- Tickets Table -->
    <table class="wp-list-table widefat fixed striped">
        <thead>
            <tr>
                <th scope="col" style="width: 60px;"><?php _e('ID', 'custom-support-ticket'); ?></th>
                <th scope="col"><?php _e('Name', 'custom-support-ticket'); ?></th>
                <th scope="col"><?php _e('Email', 'custom-support-ticket'); ?></th>
                <th scope="col"><?php _e('Phone', 'custom-support-ticket'); ?></th>
                <th scope="col"><?php _e('Message', 'custom-support-ticket'); ?></th>
                <th scope="col"><?php _e('Date', 'custom-support-ticket'); ?></th>
                <th scope="col" style="width: 100px;"><?php _e('Actions', 'custom-support-ticket'); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($tickets)): ?>
                <tr>
                    <td colspan="7" style="text-align: center; padding: 40px;">
                        <?php if (!empty($search)): ?>
                            <?php _e('No tickets found matching your search.', 'custom-support-ticket'); ?>
                        <?php else: ?>
                            <?php _e('No support tickets found.', 'custom-support-ticket'); ?>
                        <?php endif; ?>
                    </td>
                </tr>
            <?php else: ?>
                <?php foreach ($tickets as $ticket): ?>
                    <?php
                    $contact_name = get_post_meta($ticket->ID, 'contact_name', true);
                    $contact_email = get_post_meta($ticket->ID, 'contact_email', true);
                    $contact_phone = get_post_meta($ticket->ID, 'contact_phone', true);
                    $contact_date = get_post_meta($ticket->ID, 'contact_date', true);
                    
                    // Format date
                    $formatted_date = $contact_date ? date('M j, Y g:i A', strtotime($contact_date)) : get_the_date('M j, Y g:i A', $ticket->ID);
                    
                    // Truncate message for display
                    $message = wp_trim_words($ticket->post_content, 15, '...');
                    ?>
                    <tr>
                        <td><?php echo esc_html($ticket->ID); ?></td>
                        <td><strong><?php echo esc_html($contact_name); ?></strong></td>
                        <td>
                            <a href="mailto:<?php echo esc_attr($contact_email); ?>">
                                <?php echo esc_html($contact_email); ?>
                            </a>
                        </td>
                        <td>
                            <?php if ($contact_phone): ?>
                                <a href="tel:<?php echo esc_attr($contact_phone); ?>">
                                    <?php echo esc_html($contact_phone); ?>
                                </a>
                            <?php else: ?>
                                <span style="color: #999;">—</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span title="<?php echo esc_attr($ticket->post_content); ?>">
                                <?php echo esc_html($message); ?>
                            </span>
                        </td>
                        <td><?php echo esc_html($formatted_date); ?></td>
                        <td>
                            <a href="#" class="cst-view-ticket button button-small" 
                               data-ticket-id="<?php echo esc_attr($ticket->ID); ?>"
                               data-name="<?php echo esc_attr($contact_name); ?>"
                               data-email="<?php echo esc_attr($contact_email); ?>"
                               data-phone="<?php echo esc_attr($contact_phone); ?>"
                               data-message="<?php echo esc_attr($ticket->post_content); ?>"
                               data-date="<?php echo esc_attr($formatted_date); ?>">
                                <?php _e('View', 'custom-support-ticket'); ?>
                            </a>
                            <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=custom-support-tickets&action=delete&ticket_id=' . $ticket->ID), 'delete_ticket_' . $ticket->ID); ?>" 
                               class="button button-small" 
                               onclick="return confirm('<?php _e('Are you sure you want to delete this ticket?', 'custom-support-ticket'); ?>')">
                                <?php _e('Delete', 'custom-support-ticket'); ?>
                            </a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>
    
    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
        <div class="tablenav">
            <div class="tablenav-pages">
                <span class="displaying-num"><?php printf(__('%s items', 'custom-support-ticket'), number_format($total_tickets)); ?></span>
                <?php
                $page_links = paginate_links(array(
                    'base' => add_query_arg('paged', '%#%'),
                    'format' => '',
                    'prev_text' => '&laquo;',
                    'next_text' => '&raquo;',
                    'total' => $total_pages,
                    'current' => $current_page,
                    'add_args' => array('s' => $search)
                ));
                if ($page_links) {
                    echo '<span class="pagination-links">' . $page_links . '</span>';
                }
                ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Modal for viewing ticket details -->
<div id="cst-ticket-modal" class="cst-modal" style="display: none;">
    <div class="cst-modal-content">
        <div class="cst-modal-header">
            <h2><?php _e('Ticket Details', 'custom-support-ticket'); ?></h2>
            <span class="cst-modal-close">&times;</span>
        </div>
        <div class="cst-modal-body">
            <table class="form-table">
                <tr>
                    <th><?php _e('Ticket ID:', 'custom-support-ticket'); ?></th>
                    <td id="modal-ticket-id"></td>
                </tr>
                <tr>
                    <th><?php _e('Name:', 'custom-support-ticket'); ?></th>
                    <td id="modal-name"></td>
                </tr>
                <tr>
                    <th><?php _e('Email:', 'custom-support-ticket'); ?></th>
                    <td id="modal-email"></td>
                </tr>
                <tr>
                    <th><?php _e('Phone:', 'custom-support-ticket'); ?></th>
                    <td id="modal-phone"></td>
                </tr>
                <tr>
                    <th><?php _e('Date:', 'custom-support-ticket'); ?></th>
                    <td id="modal-date"></td>
                </tr>
                <tr>
                    <th><?php _e('Message:', 'custom-support-ticket'); ?></th>
                    <td id="modal-message" style="white-space: pre-wrap;"></td>
                </tr>
            </table>
        </div>
    </div>
</div>

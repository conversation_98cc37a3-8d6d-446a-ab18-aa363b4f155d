<?php
/**
 * Support ticket button template
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<!-- Support Ticket Button -->
<button type="button" class="<?php echo esc_attr($atts['class']); ?>" id="cst-open-popup">
    <?php echo esc_html($atts['text']); ?>
</button>

<!-- Support Ticket Popup Modal -->
<div id="cst-popup-overlay" class="cst-popup-overlay" style="display: none;">
    <div class="cst-popup-container">
        <div class="cst-popup-header">
            <h3><?php _e('Contact Support', 'custom-support-ticket'); ?></h3>
            <button type="button" class="cst-popup-close" id="cst-close-popup">&times;</button>
        </div>
        
        <div class="cst-popup-body">
            <form id="cst-support-form" method="post">
                <div class="cst-form-group">
                    <label for="cst-name"><?php _e('Name', 'custom-support-ticket'); ?> <span class="required">*</span></label>
                    <input type="text" id="cst-name" name="name" required>
                </div>
                
                <div class="cst-form-group">
                    <label for="cst-phone"><?php _e('Phone Number', 'custom-support-ticket'); ?></label>
                    <input type="tel" id="cst-phone" name="phone">
                </div>
                
                <div class="cst-form-group">
                    <label for="cst-email"><?php _e('Email', 'custom-support-ticket'); ?> <span class="required">*</span></label>
                    <input type="email" id="cst-email" name="email" required>
                </div>
                
                <div class="cst-form-group">
                    <label for="cst-message"><?php _e('Message', 'custom-support-ticket'); ?> <span class="required">*</span></label>
                    <textarea id="cst-message" name="message" rows="5" required></textarea>
                </div>
                
                <div class="cst-form-actions">
                    <button type="button" class="cst-btn cst-btn-secondary" id="cst-cancel-btn">
                        <?php _e('Cancel', 'custom-support-ticket'); ?>
                    </button>
                    <button type="submit" class="cst-btn cst-btn-primary" id="cst-submit-btn">
                        <?php _e('Send Message', 'custom-support-ticket'); ?>
                    </button>
                </div>
            </form>
            
            <!-- Loading indicator -->
            <div id="cst-loading" class="cst-loading" style="display: none;">
                <div class="cst-spinner"></div>
                <p><?php _e('Sending your message...', 'custom-support-ticket'); ?></p>
            </div>
            
            <!-- Success message -->
            <div id="cst-success" class="cst-message cst-success" style="display: none;">
                <div class="cst-message-icon">✓</div>
                <h4><?php _e('Message Sent Successfully!', 'custom-support-ticket'); ?></h4>
                <p><?php _e('Thank you for contacting us. We will get back to you soon.', 'custom-support-ticket'); ?></p>
                <button type="button" class="cst-btn cst-btn-primary" id="cst-success-close">
                    <?php _e('Close', 'custom-support-ticket'); ?>
                </button>
            </div>
            
            <!-- Error message -->
            <div id="cst-error" class="cst-message cst-error" style="display: none;">
                <div class="cst-message-icon">✗</div>
                <h4><?php _e('Error Sending Message', 'custom-support-ticket'); ?></h4>
                <p id="cst-error-text"></p>
                <button type="button" class="cst-btn cst-btn-primary" id="cst-error-close">
                    <?php _e('Try Again', 'custom-support-ticket'); ?>
                </button>
            </div>
        </div>
    </div>
</div>

# PrimalCom Cart Manager for WooCommerce

A clean, API-first cart and stock management solution for WooCommerce. Perfect for custom frontends, mobile apps, and voice assistants.

## 🚀 Quick Start

```javascript
// Add product to cart
PrimalComCart.addToCart(123, 2)
    .then(response => console.log('Added to cart!', response));

// Check stock before adding
PrimalComCart.checkStock(123, 2)
    .then(stock => {
        if (stock.stock_data.has_enough_stock) {
            return PrimalComCart.addToCart(123, 2);
        } else {
            console.log('Not enough stock:', stock.stock_data.stock_message);
        }
    });

// Get cart contents
PrimalComCart.getCart()
    .then(cart => console.log('Cart:', cart));
```

## ✨ What This Plugin Does

- 🛒 **Cart Management** - Add, update, remove items via JavaScript or REST API
- 📦 **Stock Checking** - Check product availability before adding to cart
- 🔌 **Clean API** - Simple REST endpoints that work with any frontend
- 📱 **No Dependencies** - Pure JavaScript library, works everywhere

> **Note:** Looking for address management? That functionality has been moved to the **Custom Multiple Addresses** plugin with the global `PrimalAddresses` library.

## 📦 Installation

1. Upload to `/wp-content/plugins/primalcom-custom-plugins/`
2. Activate in WordPress admin
3. Use the JavaScript library or REST API directly

## 🛒 Cart Management

### JavaScript Library (Recommended)

```javascript
// Add product to cart
PrimalComCart.addToCart(123, 2)  // productId, quantity
    .then(response => console.log('Added!', response));

// Add variation to cart
PrimalComCart.addToCart(123, 1, 456)  // productId, quantity, variationId
    .then(response => console.log('Variation added!', response));

// Update cart item quantity
PrimalComCart.updateCartItem('cart_item_key_here', 3)
    .then(response => console.log('Updated!', response));

// Remove item from cart
PrimalComCart.removeFromCart('cart_item_key_here')
    .then(response => console.log('Removed!', response));

// Clear entire cart
PrimalComCart.clearCart()
    .then(response => console.log('Cart cleared!', response));

// Get current cart
PrimalComCart.getCart()
    .then(cart => {
        console.log('Items:', cart.cart_items);
        console.log('Total:', cart.cart_total);
        console.log('Count:', cart.cart_count);
    });
```

## 📦 Stock Checking

### Check Before Adding to Cart

```javascript
// Check if product is available
PrimalComCart.checkStock(123, 2)  // productId, quantity
    .then(stock => {
        if (stock.stock_data.has_enough_stock) {
            console.log('✅ In stock:', stock.stock_data.stock_message);
            // Safe to add to cart
            return PrimalComCart.addToCart(123, 2);
        } else {
            console.log('❌ Not available:', stock.stock_data.stock_message);
        }
    });

// Check multiple products at once
const products = [
    { product_id: 123, quantity: 2 },
    { product_id: 124, quantity: 1 },
    { product_id: 125, quantity: 3 }
];

PrimalComCart.checkBulkStock(products)
    .then(result => {
        console.log(`${result.summary.in_stock_count} products available`);
        console.log(`${result.summary.out_of_stock_count} products out of stock`);

        // Check each product result
        result.results.forEach(product => {
            if (product.stock_data.has_enough_stock) {
                console.log(`✅ ${product.product_info.name} - Available`);
            } else {
                console.log(`❌ ${product.product_info.name} - ${product.stock_data.stock_message}`);
            }
        });
    });

// Validate stock before adding (throws error if not available)
PrimalComCart.checkStockBeforeAdd(123, 2)
    .then(() => {
        console.log('✅ Stock validated, adding to cart...');
        return PrimalComCart.addToCart(123, 2);
    })
    .catch(error => {
        console.error('❌ Cannot add to cart:', error.message);
    });
```

## 🔌 REST API Reference

If you prefer to use the REST API directly instead of the JavaScript library:

### Cart Endpoints
- `POST /wp-json/primalcom/v1/cart/add` - Add product to cart
- `POST /wp-json/primalcom/v1/cart/update` - Update cart item
- `POST /wp-json/primalcom/v1/cart/remove` - Remove cart item
- `POST /wp-json/primalcom/v1/cart/clear` - Clear entire cart
- `GET /wp-json/primalcom/v1/cart` - Get cart contents

### Stock Endpoints
- `POST /wp-json/primalcom/v1/stock/check` - Check single product stock
- `POST /wp-json/primalcom/v1/stock/check-bulk` - Check multiple products

### Example REST API Call
```javascript
fetch('/wp-json/primalcom/v1/cart/add', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-WP-Nonce': pcm_settings.nonce  // WordPress nonce for security
    },
    body: JSON.stringify({
        product_id: 123,
        quantity: 2
    })
})
.then(response => response.json())
.then(data => console.log('Success:', data));
```

### Stock Endpoints

#### Check Stock
```
POST /wp-json/primalcom/v1/stock/check
```
**Parameters:**
- `product_id` (required): Product ID to check
- `quantity` (optional): Quantity to check (default: 1)
- `variation_id` (optional): Variation ID for variable products
- `variation` (optional): Variation attributes object

**Response:**
```json
{
  "success": true,
  "product_id": 123,
  "variation_id": 456,
  "requested_quantity": 2,
  "stock_data": {
    "is_in_stock": true,
    "has_enough_stock": true,
    "is_purchasable": true,
    "manages_stock": true,
    "stock_quantity": 10,
    "stock_status": "instock",
    "backorders_allowed": false,
    "max_quantity": 10,
    "stock_message": "10 in stock"
  },
  "product_info": {
    "name": "Product Name",
    "price": "19.99",
    "type": "simple"
  }
}
```

#### Bulk Check Stock
```
POST /wp-json/primalcom/v1/stock/check-bulk
```
**Parameters:**
- `products` (required): Array of product objects to check

**Product Object Structure:**
```json
{
  "product_id": 123,
  "quantity": 2,
  "variation_id": 456
}
```

**Response:**
```json
{
  "success": true,
  "overall_stock_available": true,
  "total_products_checked": 3,
  "results": [...],
  "summary": {
    "in_stock_count": 2,
    "out_of_stock_count": 0,
    "insufficient_stock_count": 1,
    "error_count": 0
  }
}
```



## JavaScript Usage

The plugin includes a lightweight JavaScript library (`PrimalComCart`) for easy API integration:

### Direct API Calls
```javascript
// Add product to cart
PrimalComCart.addToCart(123, 2)
    .then(response => console.log('Added to cart:', response))
    .catch(error => console.error('Error:', error));

// Update cart item
PrimalComCart.updateCartItem('cart_item_key', 3)
    .then(response => console.log('Cart updated:', response));

// Remove from cart
PrimalComCart.removeFromCart('cart_item_key')
    .then(response => console.log('Item removed:', response));

// Clear entire cart
PrimalComCart.clearCart()
    .then(response => console.log('Cart cleared:', response));
```

### Get Cart Contents
```javascript
PrimalComCart.getCart()
    .then(cart => console.log('Cart contents:', cart));
```

### Stock Management
```javascript
// Check stock for a single product
PrimalComCart.checkStock(123, 2)
    .then(stockData => {
        console.log('Stock check result:', stockData);
        if (stockData.stock_data.has_enough_stock) {
            console.log('✅ Product is available');
        } else {
            console.log('❌ Insufficient stock:', stockData.stock_data.stock_message);
        }
    });

// Check stock for a variation
PrimalComCart.checkStock(123, 1, 456, { attribute_pa_color: 'red', attribute_pa_size: 'large' })
    .then(stockData => console.log('Variation stock:', stockData));

// Bulk stock check
const products = [
    { product_id: 123, quantity: 2 },
    { product_id: 124, quantity: 1 },
    { product_id: 125, quantity: 3, variation_id: 456 }
];

PrimalComCart.checkBulkStock(products)
    .then(bulkResult => {
        console.log('Bulk stock check:', bulkResult);
        console.log(`${bulkResult.summary.in_stock_count} products available`);
        console.log(`${bulkResult.summary.out_of_stock_count} products out of stock`);
    });

// Check stock before adding to cart (with validation)
PrimalComCart.checkStockBeforeAdd(123, 2)
    .then(stockData => {
        console.log('✅ Stock validated, safe to add to cart');
        return PrimalComCart.addToCart(123, 2);
    })
    .catch(error => {
        console.error('❌ Stock validation failed:', error.message);
    });

// Get stock status summary for UI display
PrimalComCart.checkStock(123, 1)
    .then(stockData => {
        const summary = PrimalComCart.getStockStatusSummary(stockData);
        console.log('Stock status:', summary);
        // summary = { status: 'in-stock', message: '10 in stock', class: 'stock-in' }

        // Use in UI
        const stockElement = document.getElementById('stock-status');
        stockElement.textContent = summary.message;
        stockElement.className = summary.class;
    });
```

### Using Native Fetch API
```javascript
// Direct fetch calls without the library
fetch('/wp-json/primalcom/v1/cart/add', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        product_id: 123,
        quantity: 2
    })
})
.then(response => response.json())
.then(data => console.log(data));
```

## Integration Examples

### Custom Implementation
Since this is a pure REST API solution, you implement your own UI and call the endpoints directly:

```javascript
// Example: Custom add to cart button
document.getElementById('my-add-to-cart-btn').addEventListener('click', async function() {
    const productId = this.dataset.productId;
    const quantity = document.getElementById('quantity-input').value;

    try {
        const response = await PrimalComCart.addToCart(productId, quantity);
        alert('Product added to cart!');
        updateCartDisplay(response);
    } catch (error) {
        alert('Error: ' + error.message);
    }
});

// Example: Custom cart display update
function updateCartDisplay(cartData) {
    document.getElementById('cart-count').textContent = cartData.cart_count;
    document.getElementById('cart-total').textContent = cartData.cart_total;
}

// Example: Load and display cart
async function loadCart() {
    try {
        const cart = await PrimalComCart.getCart();
        displayCartItems(cart.cart_items);
    } catch (error) {
        console.error('Failed to load cart:', error);
    }
}
```

## 📍 Page Context & Detection

Get detailed information about the current page and available actions:

### Get Current Page Details

```javascript
// Get comprehensive page information
PrimalComCart.getCurrentPageDetails()
    .then(pageDetails => {
        console.log('Page type:', pageDetails.title);
        console.log('URL:', pageDetails.url);
        console.log('Path:', pageDetails.path);

        // Product page includes product data
        if (pageDetails.product) {
            console.log('Product:', pageDetails.product.name);
            console.log('Price:', pageDetails.product.price);
            console.log('In stock:', pageDetails.product.in_stock);
        }

        // Cart/checkout pages include cart data
        if (pageDetails.cart) {
            console.log('Cart items:', pageDetails.cart.cart_count);
            console.log('Cart total:', pageDetails.cart.cart_total);
        }
    });
```

### Get Page Context for AI/Voice Assistants

```javascript
// Get page context with available capabilities
PrimalComCart.getPageContext()
    .then(context => {
        console.log('Page type:', context.page.title);
        console.log('Available actions:', context.capabilities);

        // Use context for voice commands or AI
        if (context.capabilities.includes('add_to_cart')) {
            console.log('User can add products to cart on this page');
        }

        if (context.product_name) {
            console.log('Current product:', context.product_name);
        }
    });
```

### Page Detection Examples

The system automatically detects these page types:

- **Product Page** (`/product/...`): Includes product details and stock info
- **Cart Page** (`/cart`): Includes current cart contents
- **Checkout Page** (`/checkout`): Includes cart data for order context
- **Shop Page** (`/shop`, `/product-category/...`): Browse/search capabilities
- **Account Page** (`/my-account`): User account context
- **Home Page** (`/`, `/home`): General navigation capabilities

### Voice Assistant Integration

```javascript
// Example: Voice command handler
function handleVoiceCommand(command) {
    PrimalComCart.getPageContext()
        .then(context => {
            if (command.includes('add to cart') && context.capabilities.includes('add_to_cart')) {
                if (context.page.productId) {
                    return PrimalComCart.addToCart(context.page.productId, 1);
                }
            }

            if (command.includes('view cart') && context.capabilities.includes('view_cart')) {
                window.location.href = '/cart';
            }

            // Handle other commands based on context
        });
}
```

### React/Vue/Angular Integration
```javascript
// React example
const [cart, setCart] = useState(null);

useEffect(() => {
    PrimalComCart.getCart()
        .then(setCart)
        .catch(console.error);
}, []);

const addToCart = async (productId, quantity) => {
    try {
        await PrimalComCart.addToCart(productId, quantity);
        const updatedCart = await PrimalComCart.getCart();
        setCart(updatedCart);
    } catch (error) {
        console.error('Add to cart failed:', error);
    }
};
```

## Installation

1. Upload the plugin files to `/wp-content/plugins/primalcom-custom-plugins/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Ensure WooCommerce is installed and activated

## Requirements

- WordPress 5.0+
- WooCommerce 3.0+
- PHP 7.4+

## Security Features

- Nonce verification for all requests
- Input sanitization and validation
- Permission checks for authenticated operations
- Rate limiting protection
- Comprehensive error logging

## Logging

The plugin logs all operations for debugging purposes. Logs can be found in your WordPress debug log if `WP_DEBUG_LOG` is enabled.

## Troubleshooting

### Internal Server Error (500)

If you're getting internal server errors when calling the API endpoints, try these steps:

1. **Check Plugin Activation**: Make sure the plugin is activated in WordPress Admin → Plugins

2. **Verify WooCommerce**: Ensure WooCommerce is installed and activated

3. **Check WordPress Debug Log**: Enable WordPress debugging in `wp-config.php`:
   ```php
   define('WP_DEBUG', true);
   define('WP_DEBUG_LOG', true);
   ```
   Then check `/wp-content/debug.log` for error messages.

5. **Verify Permalinks**: Go to WordPress Admin → Settings → Permalinks and click "Save Changes" to flush rewrite rules.

6. **Check Server Requirements**: Ensure your server meets the requirements:
   - PHP 7.4+
   - WordPress 5.0+
   - WooCommerce 3.0+

### Common Issues

**"WooCommerce cart not available"**
- This happens when WooCommerce isn't properly initialized
- Try calling the endpoint after WooCommerce has loaded
- Check if you're calling it too early in the page load process

**"Product not found"**
- Verify the product ID exists in your WooCommerce store
- Check that the product is published and purchasable



## Support

For support and customization, contact PrimalCom Development.

## Changelog

### 1.0.0
- Initial release
- Cart management REST API endpoints
- Stock checking functionality
- Frontend JavaScript library
- Comprehensive validation and security

/**
 * PrimalCom Cart Manager - REST API Library
 * Provides direct access to cart and address management REST API endpoints
 */

(function() {
    'use strict';

    const PrimalComCart = {

        // API endpoints
        endpoints: {
            cart: (typeof pcm_settings !== 'undefined' ? pcm_settings.api_url : '/wp-json/primalcom/v1/') + 'cart',
            stock: (typeof pcm_settings !== 'undefined' ? pcm_settings.api_url : '/wp-json/primalcom/v1/') + 'stock'
        },

        // Cart change callback - called whenever cart changes
        onCartChange: null,

        // Product details cache
        productCache: new Map(),

        // Configuration options
        config: {
            enableMiniCartRefresh: false, // Disabled - use WooCommerce default
            miniCartDebugMode: false
        },

        // Add product to cart (Voice Assistant optimized - API only)
        addToCart: function(productId, quantity = 1, attributes = null, variationId = 0) {
            console.log('🛒 ADD TO CART - Starting with params:', { productId, quantity, attributes, variationId });

            // If attributes provided, find matching variation
            if (typeof attributes === 'object' && attributes !== null) {
                console.log('🔍 ADD TO CART - Attributes provided, finding matching variation');
                console.log('📋 ADD TO CART - Looking for attributes:', attributes);

                // Find matching variation using API
                return this.getProductDetails(productId)
                    .then(productData => {
                        console.log('📊 ADD TO CART - Product data received for matching');

                        if (productData.type === 'variable') {
                            console.log('🔄 ADD TO CART - Variable product, searching for matching variation');
                            console.log('📋 ADD TO CART - Available variations:', productData.variations.length);

                            // Log all available variations for debugging
                            productData.variations.forEach((v, index) => {
                                console.log(`📋 ADD TO CART - Variation ${index + 1}:`, {
                                    id: v.variation_id,
                                    attributes: v.attributes,
                                    in_stock: v.is_in_stock,
                                    price: v.price
                                });
                            });

                            const matchingVariation = productData.variations.find(v => {
                                console.log(`🔍 ADD TO CART - Checking variation ${v.variation_id} for match`);

                                const isMatch = Object.keys(attributes).every(attrKey => {
                                    const expectedValue = attributes[attrKey];
                                    const actualValue = v.attributes[attrKey];

                                    // Case-insensitive comparison
                                    const matches = actualValue && expectedValue ?
                                        actualValue.toLowerCase() === expectedValue.toLowerCase() :
                                        actualValue === expectedValue;

                                    console.log(`🔍 ADD TO CART - Attribute check: ${attrKey}`, {
                                        expected: expectedValue,
                                        actual: actualValue,
                                        matches: matches,
                                        caseInsensitive: true
                                    });

                                    return matches;
                                });

                                console.log(`🔍 ADD TO CART - Variation ${v.variation_id} match result:`, isMatch);
                                return isMatch;
                            });

                            if (matchingVariation) {
                                console.log('✅ ADD TO CART - Found matching variation:', {
                                    id: matchingVariation.variation_id,
                                    attributes: matchingVariation.attributes
                                });
                                return this.addToCartDirect(productId, quantity, matchingVariation.variation_id, attributes);
                            } else {
                                console.log('❌ ADD TO CART - No matching variation found');
                                console.log('📋 ADD TO CART - Searched for:', attributes);
                                console.log('📋 ADD TO CART - Available variations:', productData.variations.map(v => v.attributes));

                                return {
                                    success: false,
                                    error: 'No variation matches the specified attributes',
                                    message: 'No variation matches the specified attributes'
                                };
                            }
                        } else {
                            console.log('📦 ADD TO CART - Simple product - adding directly');
                            return this.addToCartDirect(productId, quantity);
                        }
                    });
            } else {
                // Direct mode: check if product requires variations first
                console.log('🔄 Direct mode: Checking if product requires variations...');

                return this.getProductDetails(productId)
                    .then(productData => {
                        if (productData.type === 'variable' && !variationId) {
                            console.log('❌ Variable product requires variation selection');

                            // Show available options for voice assistant
                            const availableOptions = [];
                            Object.keys(productData.variation_attributes || {}).forEach(attr => {
                                const attrName = attr.replace('pa_', '').replace('_', ' ');
                                const options = productData.variation_attributes[attr];
                                availableOptions.push(`${attrName}: ${options.join(', ')}`);
                            });

                            const optionsText = availableOptions.length > 0
                                ? ` Available options: ${availableOptions.join('; ')}`
                                : '';

                            return {
                                success: false,
                                error: `This product has variations. Please select color, size, or specify variation ID.${optionsText}`,
                                message: `This product has variations. Please select color, size, or specify variation ID.${optionsText}`
                            };
                        }

                        console.log('✅ Product validation passed, adding to cart');
                        return this.addToCartDirect(productId, quantity, variationId, attributes);
                    });
            }
        },

        // Direct add to cart (internal function)
        addToCartDirect: function(productId, quantity = 1, variationId = 0, variation = null) {
            console.log('🔄 Adding to cart:', { productId, quantity, variationId, color: variation?.attribute_pa_color, size: variation?.attribute_pa_size });

            // Use API validation only - no form dependency
            return this.validateVariationWithAPI(productId, variationId, variation)
                .then(validationResult => {
                    if (!validationResult.valid) {
                        console.log('❌ API validation failed:', validationResult.message);
                        return {
                            success: false,
                            error: validationResult.message,
                            message: validationResult.message
                        };
                    }

                    console.log('✅ API validation passed, proceeding to add to cart');

                    const data = {
                        product_id: productId,
                        quantity: quantity
                    };

                    if (variationId) {
                        data.variation_id = variationId;
                    }

                    if (variation) {
                        data.variation = variation;
                    }

                    return this.apiRequest('POST', this.endpoints.cart + '/add', data);
                })
                .then(response => {
                    // Trigger cart change events with the response data
                    if (response.success) {
                        console.log('🎉 Product added to cart successfully via API');
                        this.triggerCartChange(response);
                    }
                    return response;
                })
                .catch(error => {
                    console.error('🎤 Voice Assistant - Add to cart error:', error);
                    return {
                        success: false,
                        error: error.message || 'Failed to add product to cart',
                        message: error.message || 'Failed to add product to cart'
                    };
                });
        },

        // Get product details from API with caching
        getProductDetails: function(productId, useCache = true) {
            console.log('🔍 GET PRODUCT DETAILS - Starting for product ID:', productId, 'useCache:', useCache);

            // Check cache first
            if (useCache && this.productCache.has(productId)) {
                console.log('📦 GET PRODUCT DETAILS - Found in cache for product:', productId);
                const cachedData = this.productCache.get(productId);
                console.log('📦 GET PRODUCT DETAILS - Cached data:', {
                    id: cachedData.id,
                    name: cachedData.name,
                    type: cachedData.type,
                    variationsCount: cachedData.variations ? cachedData.variations.length : 0,
                    hasVariationAttributes: !!cachedData.variation_attributes
                });
                return Promise.resolve(cachedData);
            }

            const apiUrl = `${pcm_settings.api_url}product/${productId}`;
            console.log('🔗 GET PRODUCT DETAILS - Fetching from API:', apiUrl);

            return this.apiRequest('GET', apiUrl)
                .then(data => {
                    console.log('✅ GET PRODUCT DETAILS - API Response received for product:', productId);
                    console.log('📊 GET PRODUCT DETAILS - Product data summary:', {
                        id: data.id,
                        name: data.name,
                        type: data.type,
                        price: data.price,
                        in_stock: data.in_stock,
                        purchasable: data.purchasable,
                        variationsCount: data.variations ? data.variations.length : 0,
                        hasVariationAttributes: !!data.variation_attributes
                    });

                    if (data.type === 'variable') {
                        console.log('🔄 GET PRODUCT DETAILS - Variable product detected');
                        console.log('📋 GET PRODUCT DETAILS - Variation attributes:', data.variation_attributes);
                        console.log('📋 GET PRODUCT DETAILS - Available variations:', data.variations?.map(v => ({
                            id: v.variation_id,
                            attributes: v.attributes,
                            in_stock: v.is_in_stock,
                            price: v.price
                        })));
                    }

                    // Cache the result
                    this.productCache.set(productId, data);
                    console.log('💾 GET PRODUCT DETAILS - Data cached for product:', productId);

                    return data;
                })
                .catch(error => {
                    console.error('❌ GET PRODUCT DETAILS - Error fetching product details for:', productId, error);
                    throw error;
                });
        },

        // Validate variation using API product data
        validateVariationWithAPI: function(productId, variationId = 0, variation = null) {
            console.log('🚀 API VALIDATION START - validateVariationWithAPI()');
            console.log('📋 Validating:', { productId, variationId, color: variation?.attribute_pa_color, size: variation?.attribute_pa_size });

            return this.getProductDetails(productId)
                .then(productData => {
                    console.log('📊 API Product data received:', productData);

                    // Check if product is variable
                    if (productData.type === 'variable') {
                        console.log('🔄 Product is VARIABLE, validating variation...');
                        console.log('📊 Available variations:', productData.variations.length);

                        if (!variationId) {
                            console.log('❌ API VALIDATION FAILED: Variable product requires variation selection');

                            // Show available options for voice assistant
                            const availableOptions = [];
                            Object.keys(productData.variation_attributes || {}).forEach(attr => {
                                const attrName = attr.replace('pa_', '').replace('_', ' ');
                                const options = productData.variation_attributes[attr];
                                availableOptions.push(`${attrName}: ${options.join(', ')}`);
                            });

                            const optionsText = availableOptions.length > 0
                                ? ` Available options: ${availableOptions.join('; ')}`
                                : '';

                            return {
                                valid: false,
                                message: `This product has variations. Please select your options.${optionsText}`
                            };
                        }

                        // Find the variation in product data
                        console.log('🔍 VALIDATION - Searching for variation ID in API data:', variationId);
                        console.log('📊 VALIDATION - Available variation IDs:', productData.variations.map(v => v.variation_id));
                        console.log('📊 VALIDATION - Full available variations:', productData.variations.map(v => ({
                            id: v.variation_id,
                            attributes: v.attributes,
                            in_stock: v.is_in_stock,
                            purchasable: v.purchasable,
                            price: v.price
                        })));

                        const foundVariation = productData.variations.find(v => v.variation_id == variationId);
                        if (!foundVariation) {
                            console.log('❌ VALIDATION - Variation not found in API data');
                            console.log('❌ VALIDATION - Searched for ID:', variationId, 'Type:', typeof variationId);
                            console.log('❌ VALIDATION - Available IDs:', productData.variations.map(v => ({ id: v.variation_id, type: typeof v.variation_id })));
                            return {
                                valid: false,
                                message: 'Selected variation is not available.'
                            };
                        }

                        console.log('✅ VALIDATION - Variation found in API data:', {
                            id: foundVariation.variation_id,
                            attributes: foundVariation.attributes,
                            in_stock: foundVariation.is_in_stock,
                            purchasable: foundVariation.purchasable,
                            price: foundVariation.price
                        });

                        // Check if variation is in stock
                        if (!foundVariation.is_in_stock) {
                            console.log('❌ API VALIDATION FAILED: Variation out of stock');
                            return {
                                valid: false,
                                message: 'Selected variation is out of stock.'
                            };
                        }

                        // Check if variation is purchasable
                        if (!foundVariation.purchasable) {
                            console.log('❌ API VALIDATION FAILED: Variation not purchasable');
                            return {
                                valid: false,
                                message: 'This variation cannot be purchased.'
                            };
                        }

                        // Validate required attributes
                        if (variation && productData.variation_attributes) {
                            console.log('🔍 Validating variation attributes...');
                            console.log('📋 Provided attributes:', variation);
                            console.log('📋 Required attributes:', productData.variation_attributes);

                            const missingAttributes = [];

                            Object.keys(productData.variation_attributes).forEach(attrName => {
                                const attrKey = `attribute_${attrName}`;
                                const attrValues = productData.variation_attributes[attrName];

                                console.log(`🔍 Checking attribute ${attrName}:`, { attrKey, attrValues, provided: variation[attrKey] });

                                // If attribute has values (is required) and not provided
                                if (attrValues.length > 0 && (!variation[attrKey] || variation[attrKey] === '')) {
                                    missingAttributes.push(attrName.replace('pa_', '').replace('_', ' '));
                                }
                            });

                            if (missingAttributes.length > 0) {
                                console.log('❌ API VALIDATION FAILED: Missing attributes:', missingAttributes);
                                return {
                                    valid: false,
                                    message: `Please select: ${missingAttributes.join(', ')}`
                                };
                            }

                            console.log('✅ All attributes validated');
                        }
                    } else {
                        console.log('📦 Product is SIMPLE, validating basic properties...');

                        // Simple product - check if purchasable and in stock
                        if (!productData.purchasable) {
                            console.log('❌ API VALIDATION FAILED: Simple product not purchasable');
                            return {
                                valid: false,
                                message: 'This product cannot be purchased.'
                            };
                        }

                        if (!productData.in_stock) {
                            console.log('❌ API VALIDATION FAILED: Simple product out of stock');
                            return {
                                valid: false,
                                message: 'This product is out of stock.'
                            };
                        }

                        console.log('✅ Simple product validation passed');
                    }

                    console.log('🎉 API VALIDATION SUCCESS!');
                    return {
                        valid: true,
                        message: 'Product validation passed',
                        productData: productData
                    };
                })
                .catch(error => {
                    console.error('API validation error:', error);
                    // If API fails, return error instead of throwing
                    return {
                        valid: false,
                        message: 'Failed to validate product: ' + (error.message || 'Unknown error')
                    };
                });
        },

        // Update cart item (quantity, variations, or both)
        updateCartItem: function(cartItemKey, quantityOrUpdates, attributes = null, variationId = null) {
            console.log('🛒 UPDATE CART - Starting update for cart item key:', cartItemKey);
            console.log('🛒 UPDATE CART - quantityOrUpdates:', quantityOrUpdates);
            console.log('🛒 UPDATE CART - attributes:', attributes);
            console.log('🛒 UPDATE CART - variationId:', variationId);

            const data = {
                cart_item_key: cartItemKey
            };

            // Handle different parameter formats for flexibility
            if (typeof quantityOrUpdates === 'object' && quantityOrUpdates !== null) {
                console.log('🛒 UPDATE CART - Using object format');
                // Object format: updateCartItem(key, {quantity: 2, attributes: {...}, variation_id: 123})
                if (quantityOrUpdates.quantity !== undefined) {
                    data.quantity = quantityOrUpdates.quantity;
                }
                if (quantityOrUpdates.variation_id !== undefined) {
                    data.variation_id = quantityOrUpdates.variation_id;
                }
                if (quantityOrUpdates.attributes && typeof quantityOrUpdates.attributes === 'object') {
                    data.variation = quantityOrUpdates.attributes;
                }
            } else {
                console.log('🛒 UPDATE CART - Using simple format');
                // Simple format: updateCartItem(key, quantity, attributes, variationId)
                if (quantityOrUpdates !== undefined && quantityOrUpdates !== null) {
                    data.quantity = quantityOrUpdates;
                }
                if (attributes && typeof attributes === 'object') {
                    data.variation = attributes;
                }
                if (variationId !== undefined && variationId !== null) {
                    data.variation_id = variationId;
                }
            }

            console.log('🛒 UPDATE CART - Final request data:', data);

            return this.apiRequest('POST', this.endpoints.cart + '/update', data)
                .then(response => {
                    console.log('🛒 UPDATE CART - Response received:', response);
                    if (response.success) {
                        console.log('🛒 UPDATE CART - Success! Triggering cart change');
                        this.triggerCartChange(response);
                    } else {
                        console.error('🛒 UPDATE CART - Failed:', response.error || response);
                    }
                    return response;
                })
                .catch(error => {
                    console.error('🛒 UPDATE CART - Error:', error);
                    throw error;
                });
        },

        // Remove item from cart
        removeFromCart: function(cartItemKey) {
            console.log('🛒 REMOVE CART - Starting removal for cart item key:', cartItemKey);

            const data = {
                cart_item_key: cartItemKey
            };

            console.log('🛒 REMOVE CART - Request data:', data);

            return this.apiRequest('POST', this.endpoints.cart + '/remove', data)
                .then(response => {
                    console.log('🛒 REMOVE CART - Response received:', response);
                    // Trigger cart change events with the response data
                    if (response.success) {
                        console.log('🛒 REMOVE CART - Success! Triggering cart change');
                        this.triggerCartChange(response);
                    } else {
                        console.error('🛒 REMOVE CART - Failed:', response.error || response);
                    }
                    return response;
                })
                .catch(error => {
                    console.error('🛒 REMOVE CART - Error:', error);
                    throw error;
                });
        },

        // Clear entire cart
        clearCart: function() {
            return this.apiRequest('POST', this.endpoints.cart + '/clear', {})
                .then(response => {
                    // Trigger cart change events with the response data
                    if (response.success) {
                        this.triggerCartChange(response);
                    }
                    return response;
                });
        },

        // Get cart contents
        getCart: function() {
            return this.apiRequest('GET', this.endpoints.cart);
        },

        // Trigger cart change events and UI updates
        triggerCartChange: function(cartData) {
            // console.log('Cart changed:', cartData);

            // Update built-in UI elements
            this.updateCartUI(cartData);

            // Call cart change callback if set
            if (this.onCartChange && typeof this.onCartChange === 'function') {
                this.onCartChange(cartData);
            }

            // Trigger custom event for cart change
            if (typeof window.CustomEvent !== 'undefined') {
                const event = new CustomEvent('primalcom_cart_changed', {
                    detail: cartData
                });
                window.dispatchEvent(event);
            }
        },

        // Update cart elements in the DOM (based on WooCommerce standards)
        updateCartUI: function(cartData) {
            const itemCount = cartData.cart_count || 0;
            const isEmpty = itemCount === 0;
            const total = cartData.cart_total || '$0.00';

            // Update cart count elements (typically shown in header)
            const cartCountElements = document.querySelectorAll('.cart-count, .cart-contents-count, .count, .cart-items-count, .woocommerce-cart-count, [data-cart-count]');
            cartCountElements.forEach(element => {
                element.textContent = itemCount;

                // Toggle visibility based on empty status
                if (isEmpty) {
                    element.style.display = 'none';
                } else {
                    element.style.display = '';
                }
            });

            // Update price elements
            const cartTotalElements = document.querySelectorAll('.cart-total, .cart-contents .amount, .cart-subtotal .amount, .woocommerce-cart-total, [data-cart-total]');
            cartTotalElements.forEach(element => {
                // Check if the element has price formatting already
                if (element.innerHTML.includes('<span')) {
                    // If it has complex HTML structure, find the price part and update it
                    const priceSpan = element.querySelector('.amount');
                    if (priceSpan) {
                        priceSpan.innerHTML = total;
                    } else {
                        element.innerHTML = total;
                    }
                } else {
                    // Simple text update
                    element.innerHTML = total;
                }
            });

            // Toggle empty cart indicators
            const emptyCartElements = document.querySelectorAll('.empty-cart-message, .cart-empty, [data-cart-empty]');
            emptyCartElements.forEach(element => {
                element.style.display = isEmpty ? '' : 'none';
            });

            // Toggle cart content areas
            const cartContentElements = document.querySelectorAll('.cart-contents, .widget_shopping_cart_content, [data-cart-contents]');
            cartContentElements.forEach(element => {
                // Some themes add 'empty' class to empty carts
                if (isEmpty) {
                    element.classList.add('empty');
                } else {
                    element.classList.remove('empty');
                }
            });

            // Trigger WooCommerce update events for compatibility (use WooCommerce default UI)
            if (typeof jQuery !== 'undefined') {
                jQuery(document.body).trigger('wc_fragment_refresh');
                jQuery(document.body).trigger('cart_updated', [cartData]);
                jQuery(document.body).trigger('updated_wc_div');
            }

            // console.log('Cart UI updated - Count:', itemCount, 'Total:', total, 'Empty:', isEmpty);
        },

        // Refresh mini-cart contents
        refreshMiniCart: function(cartData) {
            // Be more specific about which elements to update - avoid cart buttons
            const miniCartElements = document.querySelectorAll(
                '.widget_shopping_cart_content:not(.cart-button):not([data-cart-button]), ' +
                '.mini-cart-content:not(.cart-button):not([data-cart-button]), ' +
                '.cart-dropdown:not(.cart-button):not([data-cart-button]), ' +
                '.woocommerce-mini-cart-contents, ' +
                '.mini-cart-wrapper .mini-cart-content'
            );

            if (this.config.miniCartDebugMode) {
                console.log('🛒 Refreshing mini-cart for', miniCartElements.length, 'elements');
            }

            miniCartElements.forEach(element => {
                // Skip if element is too small (likely a button or icon)
                const rect = element.getBoundingClientRect();
                if (rect.width < 100 && rect.height < 50) {
                    console.log('⏭️ Skipping small element (likely a button):', element.className);
                    return;
                }

                // Skip if element is inside a button
                const parentButton = element.closest('button, .button, [role="button"], .cart-button');
                if (parentButton) {
                    console.log('⏭️ Skipping element inside button:', element.className);
                    return;
                }

                // Skip if element contains cart buttons (to avoid overwriting them)
                const hasCartButtons = element.querySelector('.cart-button, .add-to-cart, .single_add_to_cart_button');
                if (hasCartButtons) {
                    console.log('⏭️ Skipping element containing cart buttons:', element.className);
                    return;
                }

                console.log('✅ Updating mini-cart element:', element.className);

                if (cartData.cart_items && cartData.cart_items.length > 0) {
                    // Build mini-cart HTML
                    let miniCartHTML = '<ul class="woocommerce-mini-cart cart_list product_list_widget">';

                    cartData.cart_items.forEach(item => {
                        miniCartHTML += `
                            <li class="woocommerce-mini-cart-item mini_cart_item">
                                <a href="#" class="remove remove_from_cart_button" data-cart-key="${item.key}">&times;</a>
                                ${item.image ? `<img src="${item.image}" alt="${item.name}" class="attachment-woocommerce_thumbnail">` : ''}
                                <a href="${item.permalink || '#'}">${item.name}</a>
                                <span class="quantity">${item.quantity} &times; <span class="woocommerce-Price-amount amount">${item.price}</span></span>
                            </li>
                        `;
                    });

                    miniCartHTML += '</ul>';
                    miniCartHTML += `
                        <p class="woocommerce-mini-cart__total total">
                            <strong>Subtotal: <span class="woocommerce-Price-amount amount">${cartData.cart_subtotal || cartData.cart_total}</span></strong>
                        </p>
                        <p class="woocommerce-mini-cart__buttons buttons">
                            <a href="/cart" class="button wc-forward">View cart</a>
                            <a href="/checkout" class="button checkout wc-forward">Checkout</a>
                        </p>
                    `;

                    element.innerHTML = miniCartHTML;

                    // Add event listeners for remove buttons
                    const self = this;
                    element.querySelectorAll('.remove_from_cart_button').forEach(button => {
                        button.addEventListener('click', (e) => {
                            e.preventDefault();
                            const cartKey = button.dataset.cartKey;
                            if (cartKey && confirm('Remove this item from cart?')) {
                                self.removeFromCart(cartKey);
                            }
                        });
                    });
                } else {
                    // Empty cart message
                    element.innerHTML = '<p class="woocommerce-mini-cart__empty-message">No products in the cart.</p>';
                }
            });
        },

        // Refresh cart - force update cart data
        refreshCart: function() {
            return this.apiRequest('GET', this.endpoints.cart + '/refresh')
                .then(data => {
                    // console.log('Cart refreshed:', data);
                    this.triggerCartChange(data);
                    return data;
                })
                .catch(error => {
                    console.error('Cart refresh error:', error);
                    throw error;
                });
        },

        // ===== STOCK CHECKING METHODS =====

        // Check stock for a single product
        checkStock: function(productId, quantity = 1, variationId = 0, variation = null) {
            console.log('📦 STOCK CHECK - Starting for product:', { productId, quantity, variationId, variation });

            const data = {
                product_id: productId,
                quantity: quantity
            };

            if (variationId) {
                data.variation_id = variationId;
            }

            if (variation) {
                data.variation = variation;
            }

            return fetch(this.endpoints.stock + '/check', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': (typeof pcm_settings !== 'undefined' ? pcm_settings.nonce : '')
                },
                body: JSON.stringify(data)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(result => {
                console.log('📦 STOCK CHECK - Response:', result);

                if (!result.success) {
                    throw new Error(result.error || 'Stock check failed');
                }

                return result;
            })
            .catch(error => {
                console.error('📦 STOCK CHECK - Error:', error);
                throw error;
            });
        },

        // Check stock for multiple products (bulk check)
        checkBulkStock: function(products) {
            console.log('📦 BULK STOCK CHECK - Starting for products:', products);

            if (!Array.isArray(products) || products.length === 0) {
                return Promise.reject(new Error('Products array is required and cannot be empty'));
            }

            const data = {
                products: products
            };

            return fetch(this.endpoints.stock + '/check-bulk', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': (typeof pcm_settings !== 'undefined' ? pcm_settings.nonce : '')
                },
                body: JSON.stringify(data)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(result => {
                console.log('📦 BULK STOCK CHECK - Response:', result);

                if (!result.success) {
                    throw new Error(result.error || 'Bulk stock check failed');
                }

                return result;
            })
            .catch(error => {
                console.error('📦 BULK STOCK CHECK - Error:', error);
                throw error;
            });
        },

        // Check if product has enough stock before adding to cart
        checkStockBeforeAdd: function(productId, quantity = 1, variationId = 0, variation = null) {
            console.log('📦 PRE-ADD STOCK CHECK - Checking stock before adding to cart');

            return this.checkStock(productId, quantity, variationId, variation)
                .then(stockResult => {
                    const stockData = stockResult.stock_data;

                    if (!stockData.is_in_stock) {
                        throw new Error(`${stockResult.product_info.name} is out of stock`);
                    }

                    if (!stockData.has_enough_stock) {
                        const message = stockData.backorders_allowed
                            ? `${stockResult.product_info.name}: ${stockData.stock_message} (backorders allowed)`
                            : `${stockResult.product_info.name}: ${stockData.stock_message}`;

                        if (!stockData.backorders_allowed) {
                            throw new Error(message);
                        } else {
                            console.warn('⚠️ STOCK WARNING:', message);
                        }
                    }

                    console.log('✅ STOCK CHECK PASSED - Product can be added to cart');
                    return stockResult;
                });
        },

        // Get stock status summary for display
        getStockStatusSummary: function(stockData) {
            if (!stockData || !stockData.stock_data) {
                return { status: 'unknown', message: 'Stock status unknown', class: 'stock-unknown' };
            }

            const stock = stockData.stock_data;

            if (!stock.is_in_stock) {
                return {
                    status: 'out-of-stock',
                    message: 'Out of stock',
                    class: 'stock-out'
                };
            }

            if (!stock.has_enough_stock && !stock.backorders_allowed) {
                return {
                    status: 'insufficient',
                    message: stock.stock_message,
                    class: 'stock-insufficient'
                };
            }

            if (stock.manages_stock && stock.stock_quantity !== null && stock.stock_quantity <= 5) {
                return {
                    status: 'low-stock',
                    message: stock.stock_message,
                    class: 'stock-low'
                };
            }

            return {
                status: 'in-stock',
                message: stock.stock_message || 'In stock',
                class: 'stock-in'
            };
        },

        // ===== UTILITY METHODS =====

        // Check if user is logged in
        isUserLoggedIn: function() {
            // Method 1: Check if WordPress user is logged in via body class
            if (document.body.classList.contains('logged-in')) {
                return true;
            }

            // Method 2: Check for WordPress admin bar
            if (document.getElementById('wpadminbar')) {
                return true;
            }

            // Method 3: Check for user ID in global variables
            if (typeof wp !== 'undefined' && wp.user && wp.user.id) {
                return true;
            }

            // Method 4: Check for WooCommerce user data
            if (typeof wc_checkout_params !== 'undefined' && wc_checkout_params.is_user_logged_in) {
                return true;
            }

            console.log('🔒 User is not logged in - address functions require authentication');
            return false;
        },

        // Get current page details and context
        getCurrentPageDetails: function() {
            console.log('🔍 GET PAGE DETAILS - Starting page analysis');

            const url = window.location.href;
            const path = window.location.pathname;

            // Basic page details object
            const pageDetails = {
                url: url,
                path: path,
                title: 'unknown',
                timestamp: new Date().toISOString()
            };

            console.log('🔍 GET PAGE DETAILS - Analyzing path:', path);

            // Detect page type and handle accordingly
            if (path.includes('/product/')) {
                return this.handleProductPage(pageDetails);
            } else if (path.includes('/cart')) {
                return this.handleCartPage(pageDetails);
            } else if (path.includes('/checkout')) {
                return this.handleCheckoutPage(pageDetails);
            } else if (path.includes('/shop') || path.includes('/product-category')) {
                return this.handleShopPage(pageDetails);
            } else if (path === '/' || path === '' || path === '/home') {
                return this.handleHomePage(pageDetails);
            } else if (path.includes('/my-account')) {
                return this.handleAccountPage(pageDetails);
            } else {
                console.log('✅ GET PAGE DETAILS - Unknown page type, returning basic details');
                return pageDetails;
            }
        },

        // Handle product page detection and data fetching
        handleProductPage: function(pageDetails) {
            console.log('📦 HANDLE PRODUCT PAGE - Processing product page');
            pageDetails.title = 'product';

            // Get product ID from global variable
            let productId = null;
            if (typeof globalThis.SB_WP_PAGE_ID !== 'undefined') {
                productId = parseInt(globalThis.SB_WP_PAGE_ID, 10);
                pageDetails.productId = productId;
                console.log('🆔 HANDLE PRODUCT PAGE - Found product ID:', productId);
            }

            // If we have a product ID, fetch the product details
            if (productId) {
                console.log('📊 HANDLE PRODUCT PAGE - Fetching product details for ID:', productId);
                return this.getProductDetails(productId)
                    .then(productDetails => {
                        console.log('✅ HANDLE PRODUCT PAGE - Product details fetched successfully');
                        return {
                            ...pageDetails,
                            product: productDetails
                        };
                    })
                    .catch(error => {
                        console.warn('⚠️ HANDLE PRODUCT PAGE - Failed to fetch product details:', error);
                        return pageDetails;
                    });
            }

            console.log('⚠️ HANDLE PRODUCT PAGE - No product ID found, returning basic page details');
            return pageDetails;
        },

        // Handle cart page detection and data fetching
        handleCartPage: function(pageDetails) {
            console.log('🛒 HANDLE CART PAGE - Processing cart page');
            pageDetails.title = 'cart';

            return this.getCart()
                .then(cartData => {
                    console.log('✅ HANDLE CART PAGE - Cart data fetched successfully');
                    return {
                        ...pageDetails,
                        cart: cartData
                    };
                })
                .catch(error => {
                    console.warn('⚠️ HANDLE CART PAGE - Failed to fetch cart data:', error);
                    return pageDetails;
                });
        },

        // Handle checkout page detection and data fetching
        handleCheckoutPage: function(pageDetails) {
            console.log('💳 HANDLE CHECKOUT PAGE - Processing checkout page');
            pageDetails.title = 'checkout';

            return this.getCart()
                .then(cartData => {
                    console.log('✅ HANDLE CHECKOUT PAGE - Cart data fetched successfully');
                    return {
                        ...pageDetails,
                        cart: cartData
                    };
                })
                .catch(error => {
                    console.warn('⚠️ HANDLE CHECKOUT PAGE - Failed to fetch cart data:', error);
                    return pageDetails;
                });
        },

        // Handle shop/category page detection
        handleShopPage: function(pageDetails) {
            console.log('🏪 HANDLE SHOP PAGE - Processing shop/category page');
            pageDetails.title = 'shop';
            return pageDetails;
        },

        // Handle home page detection
        handleHomePage: function(pageDetails) {
            console.log('🏠 HANDLE HOME PAGE - Processing home page');
            pageDetails.title = 'home';
            return pageDetails;
        },

        // Handle account page detection
        handleAccountPage: function(pageDetails) {
            console.log('👤 HANDLE ACCOUNT PAGE - Processing account page');
            pageDetails.title = 'account';
            return pageDetails;
        },

        // Get page context for voice assistants and AI
        getPageContext: function() {
            console.log('🎯 GET PAGE CONTEXT - Starting context generation');

            return this.getCurrentPageDetails()
                .then(pageDetails => {
                    console.log('🎯 GET PAGE CONTEXT - Page details received, building context');

                    const context = {
                        page: pageDetails,
                        capabilities: []
                    };

                    // Add context-specific capabilities
                    switch (pageDetails.title) {
                        case 'product':
                            context.capabilities = [
                                'add_to_cart',
                                'check_stock',
                                'view_product_details',
                                'select_variations'
                            ];
                            if (pageDetails.product) {
                                context.product_name = pageDetails.product.name;
                                context.product_price = pageDetails.product.price;
                                context.in_stock = pageDetails.product.in_stock;
                            }
                            break;

                        case 'cart':
                            context.capabilities = [
                                'view_cart',
                                'update_quantities',
                                'remove_items',
                                'proceed_to_checkout',
                                'clear_cart'
                            ];
                            if (pageDetails.cart) {
                                context.cart_count = pageDetails.cart.cart_count;
                                context.cart_total = pageDetails.cart.cart_total;
                            }
                            break;

                        case 'checkout':
                            context.capabilities = [
                                'view_cart',
                                'select_addresses',
                                'choose_payment_method',
                                'place_order'
                            ];
                            if (pageDetails.cart) {
                                context.cart_count = pageDetails.cart.cart_count;
                                context.cart_total = pageDetails.cart.cart_total;
                            }
                            break;

                        case 'shop':
                            context.capabilities = [
                                'browse_products',
                                'search_products',
                                'filter_products',
                                'add_to_cart'
                            ];
                            break;

                        default:
                            context.capabilities = [
                                'navigate_to_shop',
                                'view_cart',
                                'search_products'
                            ];
                    }

                    console.log('✅ GET PAGE CONTEXT - Context generated successfully:', {
                        pageType: context.page.title,
                        capabilitiesCount: context.capabilities.length,
                        hasProductData: !!context.product_name,
                        hasCartData: !!context.cart_count
                    });

                    return context;
                });
        },

        // Make API request
        apiRequest: function(method, url, data = null) {
            // Use native fetch API for better compatibility
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                }
            };

            // Add nonce if available (WordPress REST API nonce)
            if (typeof pcm_settings !== 'undefined' && pcm_settings.nonce) {
                options.headers['X-WP-Nonce'] = pcm_settings.nonce;
            }

            if (method !== 'GET' && data) {
                options.body = JSON.stringify(data);
            }

            return fetch(url, options)
                .then(response => {
                    // Always expect HTTP 200, check success flag in response body
                    if (!response.ok) {
                        return response.json().then(err => Promise.reject(err));
                    }
                    return response.json();
                })
                .then(responseData => {
                    // Check if the API returned success: false
                    if (responseData.hasOwnProperty('success') && !responseData.success) {
                        console.log('🎤 API returned error:', responseData);
                        throw new Error(responseData.message || responseData.error || 'API request failed');
                    }
                    return responseData;
                })
                .catch(error => {
                    console.error('🎤 API request failed:', error);
                    throw error;
                });
        },

    };

    // Make PrimalComCart globally available
    window.PrimalComCart = PrimalComCart;

    // Auto-initialize if DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🛒 PrimalComCart - DOM loaded, initializing...');
            // Auto-refresh cart on page load
            PrimalComCart.refreshCart();
        });
    } else {
        console.log('🛒 PrimalComCart - DOM already ready, initializing...');
        // DOM is already ready
        PrimalComCart.refreshCart();
    }

})();

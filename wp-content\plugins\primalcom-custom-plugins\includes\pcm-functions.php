<?php
/**
 * Helper Functions for PrimalCom Cart Manager
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Get user addresses by type (returns clean arrays with is_default flag)
 */
function pcm_get_user_addresses($user_id, $type = 'both') {
    if (!$user_id) {
        return array();
    }

    $addresses = array();

    if ($type === 'billing' || $type === 'both') {
        $billing_addresses = array();

        // Get default WooCommerce billing address
        $default_billing = pcm_get_default_woocommerce_address($user_id, 'billing');
        if (!empty($default_billing)) {
            $billing_addresses[] = $default_billing;
        }

        // Get additional addresses from PCM format
        $pcm_billing = get_user_meta($user_id, 'pcm_billing_addresses', true) ?: array();
        foreach ($pcm_billing as $address) {
            $address['is_default'] = false;
            $billing_addresses[] = $address;
        }

        // Get additional addresses from CMA format (Custom Multiple Addresses plugin)
        $cma_billing = get_user_meta($user_id, 'cma_billing_addresses', true) ?: array();
        foreach ($cma_billing as $address) {
            $address['is_default'] = false;
            $billing_addresses[] = $address;
        }

        $addresses['billing'] = $billing_addresses;
    }

    if ($type === 'shipping' || $type === 'both') {
        $shipping_addresses = array();

        // Get default WooCommerce shipping address
        $default_shipping = pcm_get_default_woocommerce_address($user_id, 'shipping');
        if (!empty($default_shipping)) {
            $shipping_addresses[] = $default_shipping;
        }

        // Get additional addresses from PCM format
        $pcm_shipping = get_user_meta($user_id, 'pcm_shipping_addresses', true) ?: array();
        foreach ($pcm_shipping as $address) {
            $address['is_default'] = false;
            $shipping_addresses[] = $address;
        }

        // Get additional addresses from CMA format (Custom Multiple Addresses plugin)
        $cma_shipping = get_user_meta($user_id, 'cma_shipping_addresses', true) ?: array();
        foreach ($cma_shipping as $address) {
            $address['is_default'] = false;
            $shipping_addresses[] = $address;
        }

        $addresses['shipping'] = $shipping_addresses;
    }

    return $addresses;
}

/**
 * Get default WooCommerce address for a user
 */
function pcm_get_default_woocommerce_address($user_id, $type) {
    if (!$user_id || !in_array($type, array('billing', 'shipping'))) {
        return array();
    }

    $address_fields = array(
        'first_name',
        'last_name',
        'company',
        'address_1',
        'address_2',
        'city',
        'state',
        'postcode',
        'country',
        'phone'
    );

    // Add email for billing addresses
    if ($type === 'billing') {
        $address_fields[] = 'email';
    }

    $address = array();
    $has_data = false;

    foreach ($address_fields as $field) {
        $meta_key = $type . '_' . $field;
        $value = get_user_meta($user_id, $meta_key, true);

        if (!empty($value)) {
            $address[$field] = $value;
            $has_data = true;
        }
    }

    // Only return address if it has meaningful data
    if ($has_data && !empty($address['address_1'])) {
        $address['is_default'] = true;
        return $address;
    }

    return array();
}

/**
 * Save user address (saves to both PCM and CMA formats for compatibility)
 */
function pcm_save_user_address($user_id, $type, $address_data, $address_id = null) {
    if (!$user_id || !in_array($type, array('billing', 'shipping'))) {
        return false;
    }

    // Get existing addresses from PCM format
    $pcm_addresses = get_user_meta($user_id, 'pcm_' . $type . '_addresses', true) ?: array();

    // Also get from CMA format to maintain compatibility
    $cma_addresses = get_user_meta($user_id, 'cma_' . $type . '_addresses', true) ?: array();

    if ($address_id === null) {
        $address_id = 'addr_' . time() . '_' . wp_rand(1000, 9999);
    }

    // Sanitize address data
    $sanitized_data = pcm_sanitize_address_data($address_data);

    // Save to both formats for compatibility
    $pcm_addresses[$address_id] = $sanitized_data;
    $cma_addresses[$address_id] = $sanitized_data;

    update_user_meta($user_id, 'pcm_' . $type . '_addresses', $pcm_addresses);
    update_user_meta($user_id, 'cma_' . $type . '_addresses', $cma_addresses);

    return $address_id;
}

/**
 * Delete user address (removes from both PCM and CMA formats)
 */
function pcm_delete_user_address($user_id, $type, $address_id) {
    if (!$user_id || !in_array($type, array('billing', 'shipping'))) {
        return false;
    }

    // Get addresses from both formats
    $pcm_addresses = get_user_meta($user_id, 'pcm_' . $type . '_addresses', true) ?: array();
    $cma_addresses = get_user_meta($user_id, 'cma_' . $type . '_addresses', true) ?: array();

    $deleted = false;

    // Remove from PCM format
    if (isset($pcm_addresses[$address_id])) {
        unset($pcm_addresses[$address_id]);
        update_user_meta($user_id, 'pcm_' . $type . '_addresses', $pcm_addresses);
        $deleted = true;
    }

    // Remove from CMA format
    if (isset($cma_addresses[$address_id])) {
        unset($cma_addresses[$address_id]);
        update_user_meta($user_id, 'cma_' . $type . '_addresses', $cma_addresses);
        $deleted = true;
    }

    return $deleted;
}

/**
 * Sanitize address data
 */
function pcm_sanitize_address_data($address_data) {
    $sanitized = array();
    
    $allowed_fields = array(
        'first_name',
        'last_name',
        'company',
        'address_1',
        'address_2',
        'city',
        'state',
        'postcode',
        'country',
        'phone',
        'email'
    );
    
    foreach ($allowed_fields as $field) {
        if (isset($address_data[$field])) {
            $sanitized[$field] = sanitize_text_field($address_data[$field]);
        }
    }
    
    return $sanitized;
}

/**
 * Format address for display
 */
function pcm_format_address($address) {
    $formatted = '';
    
    if (!empty($address['first_name']) || !empty($address['last_name'])) {
        $formatted .= trim($address['first_name'] . ' ' . $address['last_name']) . '<br>';
    }
    
    if (!empty($address['company'])) {
        $formatted .= $address['company'] . '<br>';
    }
    
    if (!empty($address['address_1'])) {
        $formatted .= $address['address_1'] . '<br>';
    }
    
    if (!empty($address['address_2'])) {
        $formatted .= $address['address_2'] . '<br>';
    }
    
    if (!empty($address['city'])) {
        $formatted .= $address['city'];
    }
    
    if (!empty($address['state'])) {
        $formatted .= ', ' . $address['state'];
    }
    
    if (!empty($address['postcode'])) {
        $formatted .= ' ' . $address['postcode'] . '<br>';
    }
    
    if (!empty($address['country'])) {
        $countries = WC()->countries->get_countries();
        $formatted .= isset($countries[$address['country']]) ? $countries[$address['country']] : $address['country'];
    }
    
    return $formatted;
}

/**
 * Validate address data
 */
function pcm_validate_address_data($address_data, $type = 'billing') {
    $errors = array();
    
    // Required fields
    $required_fields = array(
        'first_name' => __('First name is required', 'primalcom-cart-manager'),
        'last_name' => __('Last name is required', 'primalcom-cart-manager'),
        'address_1' => __('Street address is required', 'primalcom-cart-manager'),
        'city' => __('City is required', 'primalcom-cart-manager'),
        'postcode' => __('Postcode is required', 'primalcom-cart-manager'),
        'country' => __('Country is required', 'primalcom-cart-manager')
    );
    
    // Add email requirement for billing addresses
    if ($type === 'billing') {
        $required_fields['email'] = __('Email is required for billing address', 'primalcom-cart-manager');
    }
    
    foreach ($required_fields as $field => $message) {
        if (empty($address_data[$field])) {
            $errors[] = $message;
        }
    }
    
    // Validate email format
    if (!empty($address_data['email']) && !is_email($address_data['email'])) {
        $errors[] = __('Please enter a valid email address', 'primalcom-cart-manager');
    }
    
    // Validate country code
    if (!empty($address_data['country'])) {
        $countries = WC()->countries->get_countries();
        if (!isset($countries[$address_data['country']])) {
            $errors[] = __('Please select a valid country', 'primalcom-cart-manager');
        }
    }
    
    return $errors;
}

/**
 * Get cart session for guest users
 */
function pcm_get_guest_cart_session($session_key) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'pcm_cart_sessions';
    
    $session = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $table_name WHERE session_key = %s AND expires > NOW()",
        $session_key
    ));
    
    return $session ? json_decode($session->cart_data, true) : array();
}

/**
 * Save cart session for guest users
 */
function pcm_save_guest_cart_session($session_key, $cart_data) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'pcm_cart_sessions';
    $expires = date('Y-m-d H:i:s', strtotime('+24 hours'));
    
    $wpdb->replace(
        $table_name,
        array(
            'session_key' => $session_key,
            'cart_data' => wp_json_encode($cart_data),
            'expires' => $expires
        ),
        array('%s', '%s', '%s')
    );
}

/**
 * Clean expired cart sessions
 */
function pcm_cleanup_expired_sessions() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'pcm_cart_sessions';
    
    $wpdb->query("DELETE FROM $table_name WHERE expires < NOW()");
}

/**
 * Log PCM events
 */
function pcm_log($message, $data = null) {
    if (defined('WP_DEBUG') && WP_DEBUG) {
        $log_message = 'PCM: ' . $message;
        if ($data !== null) {
            $log_message .= ' - Data: ' . wp_json_encode($data);
        }
        error_log($log_message);
    }
}

/**
 * Check if product is in stock
 */
function pcm_check_product_stock($product_id, $quantity = 1, $variation_id = 0) {
    if ($variation_id > 0) {
        $product = wc_get_product($variation_id);
    } else {
        $product = wc_get_product($product_id);
    }
    
    if (!$product || !$product->exists()) {
        return false;
    }
    
    return $product->has_enough_stock($quantity);
}

/**
 * Get product price including variations
 */
function pcm_get_product_price($product_id, $variation_id = 0) {
    if ($variation_id > 0) {
        $product = wc_get_product($variation_id);
    } else {
        $product = wc_get_product($product_id);
    }
    
    if (!$product || !$product->exists()) {
        return 0;
    }
    
    return $product->get_price();
}

/**
 * Generate unique session key
 */
function pcm_generate_session_key() {
    return wp_generate_password(32, false);
}

/**
 * Get current user cart session key
 */
function pcm_get_cart_session_key() {
    if (is_user_logged_in()) {
        return 'user_' . get_current_user_id();
    }
    
    // For guest users, use WooCommerce session or create new one
    if (WC()->session) {
        return WC()->session->get_customer_id();
    }
    
    return pcm_generate_session_key();
}

/**
 * Schedule cleanup of expired sessions
 */
function pcm_schedule_cleanup() {
    if (!wp_next_scheduled('pcm_cleanup_expired_sessions')) {
        wp_schedule_event(time(), 'daily', 'pcm_cleanup_expired_sessions');
    }
}

// Hook for scheduled cleanup
add_action('pcm_cleanup_expired_sessions', 'pcm_cleanup_expired_sessions');

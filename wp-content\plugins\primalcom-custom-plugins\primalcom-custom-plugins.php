<?php
/**
 * Plugin Name: PrimalCom Custom Plugins
 * Plugin URI: https://localhost
 * Description: Custom plugins collection including advanced cart management system with REST API endpoints for WooCommerce
 * Version: 1.0.0
 * Author: Azirul A
 * Text Domain: primalcom-custom-plugins
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.3
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('PRIMALCOM_PLUGINS_VERSION', '1.0.0');
define('PRIMALCOM_PLUGINS_FILE', __FILE__);
define('PRIMALCOM_PLUGINS_DIR', plugin_dir_path(__FILE__));
define('PRIMALCOM_PLUGINS_URL', plugin_dir_url(__FILE__));

/**
 * Main PrimalCom Custom Plugins Class
 */
class PrimalCom_Custom_Plugins {

    /**
     * Single instance of the class
     */
    private static $instance = null;

    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }

    /**
     * Custom logging function for PCM
     */
    public function pcm_log($message) {
        $log_file = WP_CONTENT_DIR . '/pcm-debug.log';
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[{$timestamp}] {$message}" . PHP_EOL;
        file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);

        // Also log to WordPress debug log for backup
        error_log('PCM - ' . $message);
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        error_log('PCM - Initializing hooks');
        add_action('plugins_loaded', array($this, 'load_plugins'));
        add_action('init', array($this, 'init'));
        add_action('rest_api_init', array($this, 'register_rest_routes'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));

        // Add WooCommerce fragment support for default cart UI
        add_filter('woocommerce_add_to_cart_fragments', array($this, 'cart_fragments'));

        // Plugin activation/deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));

        // Declare WooCommerce HPOS compatibility
        add_action('before_woocommerce_init', array($this, 'declare_hpos_compatibility'));

        error_log('PCM - Hooks initialized');
    }

    /**
     * Initialize plugin
     */
    public function init() {
        error_log('PCM - Init method called');
        load_plugin_textdomain('primalcom-custom-plugins', false, dirname(plugin_basename(PRIMALCOM_PLUGINS_FILE)) . '/languages');

        // Add shortcode for testing
        add_shortcode('primalcom_cart_test', array($this, 'cart_test_shortcode'));
        error_log('PCM - Init method completed');
    }

    /**
     * Shortcode for testing cart functionality
     */
    public function cart_test_shortcode($atts) {
        ob_start();
        ?>
        <div id="primalcom-cart-test" style="padding: 20px; border: 1px solid #ddd; margin: 20px 0;">
            <h3>PrimalCom Cart Test</h3>
            <button onclick="testPrimalComCartShortcode()" style="padding: 10px 15px; margin: 5px; background: #0073aa; color: white; border: none; cursor: pointer;">Test PrimalComCart</button>
            <div id="primalcom-test-result" style="margin: 10px 0; padding: 10px; background: #f9f9f9; border-left: 4px solid #0073aa;"></div>

            <script>
                function testPrimalComCartShortcode() {
                    const element = document.getElementById('primalcom-test-result');

                    console.log('🧪 Testing PrimalComCart from shortcode...');
                    console.log('🧪 window.PrimalComCart:', window.PrimalComCart);
                    console.log('🧪 pcm_settings:', typeof pcm_settings !== 'undefined' ? pcm_settings : 'undefined');

                    if (typeof window.PrimalComCart !== 'undefined') {
                        element.style.borderLeftColor = '#46b450';
                        element.style.background = '#eafaea';
                        element.innerHTML = '<strong>✅ Success:</strong> PrimalComCart is defined!<br>' +
                            'Available methods: ' + Object.keys(window.PrimalComCart).join(', ') + '<br>' +
                            'pcm_settings: ' + (typeof pcm_settings !== 'undefined' ? 'defined' : 'undefined');
                    } else {
                        element.style.borderLeftColor = '#dc3232';
                        element.style.background = '#ffeaea';
                        element.innerHTML = '<strong>❌ Error:</strong> PrimalComCart is not defined!<br>' +
                            'pcm_settings: ' + (typeof pcm_settings !== 'undefined' ? 'defined' : 'undefined') + '<br>' +
                            'Check console for more details.';
                    }
                }

                // Auto-test after a short delay
                setTimeout(function() {
                    testPrimalComCartShortcode();
                }, 2000);
            </script>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Load individual plugins
     */
    public function load_plugins() {
        // Show notice if WooCommerce is not available
        if (!class_exists('WooCommerce')) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
        }

        // Include helper functions
        require_once PRIMALCOM_PLUGINS_DIR . 'includes/pcm-functions.php';
    }

    /**
     * Show WooCommerce missing notice
     */
    public function woocommerce_missing_notice() {
        echo '<div class="notice notice-error"><p><strong>PrimalCom Custom Plugins:</strong> WooCommerce plugin is required for the Cart Manager functionality.</p></div>';
    }

    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts() {
        if (is_admin()) {
            return;
        }

        error_log('PCM - Enqueuing scripts and styles');

        wp_enqueue_script(
            'primalcom-cart',
            PRIMALCOM_PLUGINS_URL . 'assets/js/primalcom-cart.js',
            array('jquery'),
            PRIMALCOM_PLUGINS_VERSION,
            true
        );

        // Localize script with proper nonce and API URL
        wp_localize_script('primalcom-cart', 'pcm_settings', array(
            'nonce' => wp_create_nonce('wp_rest'),
            'api_url' => rest_url('primalcom/v1/'),
            'messages' => array(
                'added_to_cart' => __('Product added to cart successfully!', 'primalcom-custom-plugins'),
                'updated_cart' => __('Cart updated successfully!', 'primalcom-custom-plugins'),
                'removed_from_cart' => __('Product removed from cart!', 'primalcom-custom-plugins'),
                'cart_cleared' => __('Cart cleared successfully!', 'primalcom-custom-plugins'),
                'error' => __('An error occurred. Please try again.', 'primalcom-custom-plugins')
            )
        ));

        error_log('PCM - Scripts enqueued successfully');
    }

    /**
     * Plugin activation
     */
    public function activate() {
        // Activation tasks
        error_log('PrimalCom Custom Plugins activated');

        // Flush rewrite rules to ensure REST API endpoints work
        flush_rewrite_rules();
    }

    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Deactivation tasks
        error_log('PrimalCom Custom Plugins deactivated');

        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Declare WooCommerce High-Performance Order Storage (HPOS) compatibility
     */
    public function declare_hpos_compatibility() {
        if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
            \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('custom_order_tables', __FILE__, true);
            error_log('PCM - Declared HPOS compatibility');
        }
    }

    /**
     * Register REST API routes
     */
    public function register_rest_routes() {
        error_log('PCM - Registering REST API routes');

        // Get cart
        register_rest_route('primalcom/v1', '/cart', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_cart'),
            'permission_callback' => '__return_true'
        ));

        // Add to cart
        register_rest_route('primalcom/v1', '/cart/add', array(
            'methods' => 'POST',
            'callback' => array($this, 'add_to_cart'),
            'permission_callback' => '__return_true',
            'args' => array(
                'product_id' => array('required' => true, 'type' => 'integer'),
                'quantity' => array('required' => false, 'type' => 'integer', 'default' => 1),
                'variation_id' => array('required' => false, 'type' => 'integer'),
                'variation' => array('required' => false, 'type' => 'object')
            )
        ));

        // Update cart
        register_rest_route('primalcom/v1', '/cart/update', array(
            'methods' => 'POST',
            'callback' => array($this, 'update_cart'),
            'permission_callback' => '__return_true',
            'args' => array(
                'cart_item_key' => array('required' => true, 'type' => 'string'),
                'quantity' => array('required' => false, 'type' => 'integer'),
                'variation_id' => array('required' => false, 'type' => 'integer'),
                'variation' => array('required' => false, 'type' => 'object')
            )
        ));

        // Remove from cart
        register_rest_route('primalcom/v1', '/cart/remove', array(
            'methods' => 'POST',
            'callback' => array($this, 'remove_from_cart'),
            'permission_callback' => '__return_true',
            'args' => array(
                'cart_item_key' => array('required' => true, 'type' => 'string')
            )
        ));

        // Clear cart
        register_rest_route('primalcom/v1', '/cart/clear', array(
            'methods' => 'POST',
            'callback' => array($this, 'clear_cart'),
            'permission_callback' => '__return_true'
        ));

        // Refresh cart
        register_rest_route('primalcom/v1', '/cart/refresh', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_cart'),
            'permission_callback' => '__return_true'
        ));

        // Get product details
        register_rest_route('primalcom/v1', '/product/(?P<id>\d+)', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_product_details'),
            'permission_callback' => '__return_true',
            'args' => array(
                'id' => array('required' => true, 'type' => 'integer')
            )
        ));

        // Check stock
        register_rest_route('primalcom/v1', '/stock/check', array(
            'methods' => 'POST',
            'callback' => array($this, 'check_stock'),
            'permission_callback' => '__return_true',
            'args' => array(
                'product_id' => array('required' => true, 'type' => 'integer'),
                'quantity' => array('required' => false, 'type' => 'integer', 'default' => 1),
                'variation_id' => array('required' => false, 'type' => 'integer'),
                'variation' => array('required' => false, 'type' => 'object')
            )
        ));

        // Bulk check stock
        register_rest_route('primalcom/v1', '/stock/check-bulk', array(
            'methods' => 'POST',
            'callback' => array($this, 'check_bulk_stock'),
            'permission_callback' => '__return_true',
            'args' => array(
                'products' => array('required' => true, 'type' => 'array')
            )
        ));

        error_log('PCM - REST API routes registered successfully');
    }

    /**
     * Check if user is logged in (permission callback for address endpoints)
     */
    public function check_user_logged_in() {
        return is_user_logged_in();
    }

    /**
     * Ensure WooCommerce cart is properly initialized
     */
    private function ensure_cart_initialized() {
        // Check if WooCommerce is available
        if (!class_exists('WooCommerce') || !function_exists('WC')) {
            error_log('PCM - WooCommerce not available');
            return false;
        }

        // Initialize WooCommerce if not already done
        if (!WC()->cart) {
            // Initialize WooCommerce session and cart
            if (!WC()->session) {
                WC()->session = new WC_Session_Handler();
                WC()->session->init();
            }

            if (!WC()->customer) {
                WC()->customer = new WC_Customer();
            }

            if (!WC()->cart) {
                WC()->cart = new WC_Cart();
            }
        }

        return WC()->cart !== null;
    }

    public function get_cart(WP_REST_Request $request) {
        try {
            // Ensure WooCommerce and cart are initialized
            if (!$this->ensure_cart_initialized()) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'WooCommerce cart not available'
                ), 200);
            }

            $cart_data = $this->get_cart_data();
            $cart_data['success'] = true;
            return new WP_REST_Response($cart_data, 200);
        } catch (Exception $e) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => $e->getMessage()
            ), 200);
        }
    }

    public function add_to_cart(WP_REST_Request $request) {
        $product_id = $request->get_param('product_id');
        $quantity = $request->get_param('quantity') ?: 1;
        $variation_id = $request->get_param('variation_id') ?: 0;
        $variation = $request->get_param('variation') ?: array();

        try {
            // Ensure WooCommerce and cart are initialized
            if (!$this->ensure_cart_initialized()) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'WooCommerce cart not available'
                ), 200);
            }

            $product = wc_get_product($product_id);
            if (!$product) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'Product not found'
                ), 200);
            }

            $cart_item_key = WC()->cart->add_to_cart($product_id, $quantity, $variation_id, $variation);

            if ($cart_item_key) {
                $cart_data = $this->get_cart_data();
                $cart_data['success'] = true;
                $cart_data['message'] = 'Product added to cart successfully';
                $cart_data['cart_item_key'] = $cart_item_key;
                return new WP_REST_Response($cart_data, 200);
            } else {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'Failed to add product to cart'
                ), 200);
            }
        } catch (Exception $e) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => $e->getMessage()
            ), 200);
        }
    }

    public function update_cart(WP_REST_Request $request) {
        $cart_item_key = $request->get_param('cart_item_key');
        $quantity = $request->get_param('quantity');
        $variation_id = $request->get_param('variation_id');
        $variation = $request->get_param('variation');

        $this->pcm_log('CART UPDATE - Received request with cart_item_key: ' . $cart_item_key);
        $this->pcm_log('CART UPDATE - Quantity: ' . $quantity);
        $this->pcm_log('CART UPDATE - Variation ID: ' . $variation_id);
        $this->pcm_log('CART UPDATE - Variation: ' . print_r($variation, true));

        // Ensure WooCommerce and cart are initialized
        if (!$this->ensure_cart_initialized()) {
            $this->pcm_log('CART UPDATE - WooCommerce cart not available');
            return new WP_REST_Response(array(
                'success' => false,
                'error' => 'WooCommerce cart not available'
            ), 200);
        }

        // Log all current cart item keys for debugging
        $cart_contents = WC()->cart->get_cart();
        $this->pcm_log('CART UPDATE - Current cart contains ' . count($cart_contents) . ' items');
        foreach ($cart_contents as $key => $item) {
            $this->pcm_log('CART UPDATE - Available cart item key: ' . $key . ' (Product ID: ' . $item['product_id'] . ')');
        }

        $cart_item = WC()->cart->get_cart_item($cart_item_key);
        if (!$cart_item) {
            $this->pcm_log('CART UPDATE - Cart item not found for key: ' . $cart_item_key);
            return new WP_REST_Response(array(
                'success' => false,
                'error' => 'Cart item not found'
            ), 200);
        }

        $this->pcm_log('CART UPDATE - Found cart item: ' . print_r($cart_item, true));

        try {
            $updated = false;
            $message = '';

            if ($variation_id || $variation) {
                // Update variation - remove old and add new
                $product_id = $cart_item['product_id'];
                $old_quantity = $quantity !== null ? $quantity : $cart_item['quantity'];

                WC()->cart->remove_cart_item($cart_item_key);

                $variation_data = array();
                if ($variation && is_array($variation)) {
                    $variation_data = $variation;
                }

                $new_cart_item_key = WC()->cart->add_to_cart(
                    $product_id,
                    $old_quantity,
                    $variation_id ? $variation_id : 0,
                    $variation_data
                );

                if ($new_cart_item_key) {
                    $updated = true;
                    $message = 'Cart item variation updated successfully';
                }
            } else if ($quantity !== null) {
                $updated = WC()->cart->set_quantity($cart_item_key, $quantity);
                $message = $quantity > 0 ? 'Cart updated successfully' : 'Item removed from cart';
            }

            if ($updated) {
                $cart_data = $this->get_cart_data();
                $cart_data['success'] = true;
                $cart_data['message'] = $message;
                return new WP_REST_Response($cart_data, 200);
            } else {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'Failed to update cart'
                ), 200);
            }
        } catch (Exception $e) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => $e->getMessage()
            ), 200);
        }
    }

    public function remove_from_cart(WP_REST_Request $request) {
        $cart_item_key = $request->get_param('cart_item_key');

        $this->pcm_log('CART REMOVE - Received request with cart_item_key: ' . $cart_item_key);

        try {
            // Ensure WooCommerce and cart are initialized
            if (!$this->ensure_cart_initialized()) {
                $this->pcm_log('CART REMOVE - WooCommerce cart not available');
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'WooCommerce cart not available'
                ), 200);
            }

            // Log all current cart item keys for debugging
            $cart_contents = WC()->cart->get_cart();
            $this->pcm_log('CART REMOVE - Current cart contains ' . count($cart_contents) . ' items');
            foreach ($cart_contents as $key => $item) {
                $this->pcm_log('CART REMOVE - Available cart item key: ' . $key . ' (Product ID: ' . $item['product_id'] . ')');
            }

            // Check if the cart item exists before trying to remove it
            $cart_item = WC()->cart->get_cart_item($cart_item_key);
            if (!$cart_item) {
                $this->pcm_log('CART REMOVE - Cart item not found for key: ' . $cart_item_key);
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'Cart item not found'
                ), 200);
            }

            $this->pcm_log('CART REMOVE - Found cart item, attempting removal: ' . print_r($cart_item, true));

            $removed = WC()->cart->remove_cart_item($cart_item_key);

            if ($removed) {
                $this->pcm_log('CART REMOVE - Successfully removed cart item');
                $cart_data = $this->get_cart_data();
                $cart_data['success'] = true;
                $cart_data['message'] = 'Product removed from cart';
                return new WP_REST_Response($cart_data, 200);
            } else {
                $this->pcm_log('CART REMOVE - Failed to remove cart item');
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'Failed to remove product from cart'
                ), 200);
            }
        } catch (Exception $e) {
            $this->pcm_log('CART REMOVE - Exception: ' . $e->getMessage());
            return new WP_REST_Response(array(
                'success' => false,
                'error' => $e->getMessage()
            ), 200);
        }
    }

    public function clear_cart(WP_REST_Request $request) {
        try {
            // Ensure WooCommerce and cart are initialized
            if (!$this->ensure_cart_initialized()) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'WooCommerce cart not available'
                ), 200);
            }

            WC()->cart->empty_cart();

            $cart_data = $this->get_cart_data();
            $cart_data['success'] = true;
            $cart_data['message'] = 'Cart cleared successfully';
            return new WP_REST_Response($cart_data, 200);
        } catch (Exception $e) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => $e->getMessage()
            ), 200);
        }
    }

    public function get_product_details(WP_REST_Request $request) {
        $product_id = $request->get_param('id');

        try {
            $product = wc_get_product($product_id);
            if (!$product) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'Product not found'
                ), 200);
            }

            $product_data = array(
                'id' => $product->get_id(),
                'name' => $product->get_name(),
                'type' => $product->get_type(),
                'price' => $product->get_price(),
                'price_html' => $product->get_price_html(),
                'in_stock' => $product->is_in_stock(),
                'purchasable' => $product->is_purchasable(),
                'variations' => array(),
                'variation_attributes' => array()
            );

            // If it's a variable product, get variations
            if ($product->is_type('variable')) {
                $variations = $product->get_available_variations();
                $product_data['variations'] = array();

                foreach ($variations as $variation_data) {
                    $variation = wc_get_product($variation_data['variation_id']);
                    if ($variation) {
                        $product_data['variations'][] = array(
                            'variation_id' => $variation->get_id(),
                            'price' => $variation->get_price(),
                            'is_in_stock' => $variation->is_in_stock(),
                            'purchasable' => $variation->is_purchasable(),
                            'attributes' => $variation_data['attributes']
                        );
                    }
                }

                // Get variation attributes
                $product_data['variation_attributes'] = $product->get_variation_attributes();
            }

            return new WP_REST_Response($product_data, 200);
        } catch (Exception $e) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => $e->getMessage()
            ), 200);
        }
    }

    private function get_cart_data() {
        if (!WC()->cart) {
            return array(
                'cart_items' => array(),
                'cart_count' => 0,
                'cart_total' => '$0.00',
                'cart_subtotal' => '$0.00'
            );
        }

        $cart_items = array();
        $cart_contents = WC()->cart->get_cart();

        $this->pcm_log('CART DATA - Building cart data for ' . count($cart_contents) . ' items');

        foreach ($cart_contents as $cart_item_key => $cart_item) {
            $product = $cart_item['data'];
            $product_id = $cart_item['product_id'];
            $variation_id = $cart_item['variation_id'];

            $this->pcm_log('CART DATA - Item key: ' . $cart_item_key . ', Product ID: ' . $product_id . ', Variation ID: ' . $variation_id);

            // Use the product name as WooCommerce formats it (includes variation details)
            $product_name = $product->get_name();

            $cart_items[] = array(
                'cart_item_key' => $cart_item_key,
                'product_id' => $product_id,
                'variation_id' => $variation_id,
                'quantity' => $cart_item['quantity'],
                'name' => $product_name,
                'price' => floatval($product->get_price()),
                'total' => floatval($cart_item['line_total']),
                'image' => wp_get_attachment_image_src(get_post_thumbnail_id($product_id), 'thumbnail')[0] ?? '',
                'variation' => $cart_item['variation'] ?? array()
            );
        }

        $cart_data = array(
            'cart_items' => $cart_items,
            'cart_count' => WC()->cart->get_cart_contents_count(),
            'cart_total' => floatval(WC()->cart->get_total()),
            'cart_subtotal' => floatval(WC()->cart->get_subtotal())
        );

        $this->pcm_log('CART DATA - Final cart data: ' . print_r($cart_data, true));

        return $cart_data;
    }

    /**
     * WooCommerce cart fragments for default UI updates
     */
    public function cart_fragments($fragments) {
        // Let WooCommerce handle the default cart fragments
        // This ensures the default WooCommerce cart UI updates properly
        return $fragments;
    }











    /**
     * Select address with smart matching (Voice Integration)
     */
    public function select_address(WP_REST_Request $request) {
        try {
            $user_id = get_current_user_id();
            if (!$user_id) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'User not logged in'
                ), 401);
            }

            $type = $request->get_param('type'); // 'billing' or 'shipping'
            $query = $request->get_param('query'); // search query for matching

            if (!in_array($type, array('billing', 'shipping'))) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'Invalid address type. Must be billing or shipping.'
                ), 400);
            }

            // Get all addresses
            $addresses = pcm_get_user_addresses($user_id, $type);
            $target_addresses = $addresses[$type] ?? array();

            if (empty($target_addresses)) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'No ' . $type . ' addresses found'
                ), 404);
            }

            $selected_address = null;
            $match_reason = '';

            // Smart matching logic
            if (empty($query) || strtolower($query) === 'default') {
                // No query or "default" - find default address
                foreach ($target_addresses as $address) {
                    if (isset($address['is_default']) && $address['is_default']) {
                        $selected_address = $address;
                        $match_reason = 'default address';
                        break;
                    }
                }
                // If no default found, use first address
                if (!$selected_address) {
                    $selected_address = $target_addresses[0];
                    $match_reason = 'first available address';
                }
            } else {
                // Smart matching by query
                $query_lower = strtolower($query);
                $best_match = null;
                $best_score = 0;

                foreach ($target_addresses as $index => $address) {
                    $score = 0;
                    $address_text = strtolower($this->format_address_summary($address));

                    // Exact matches get highest score
                    if (strpos($address_text, $query_lower) !== false) {
                        $score += 100;
                    }

                    // Check individual fields
                    $fields_to_check = ['first_name', 'last_name', 'city', 'state', 'address_1', 'company'];
                    foreach ($fields_to_check as $field) {
                        if (isset($address[$field]) && strpos(strtolower($address[$field]), $query_lower) !== false) {
                            $score += 50;
                        }
                    }

                    // Check for numeric index (first, second, etc.)
                    if (preg_match('/(\d+)/', $query, $matches)) {
                        $requested_index = intval($matches[1]) - 1; // Convert to 0-based
                        if ($index === $requested_index) {
                            $score += 200;
                        }
                    }

                    // Check for ordinal words
                    $ordinals = ['first' => 0, 'second' => 1, 'third' => 2, 'fourth' => 3, 'fifth' => 4];
                    foreach ($ordinals as $ordinal => $ordinal_index) {
                        if (strpos($query_lower, $ordinal) !== false && $index === $ordinal_index) {
                            $score += 200;
                        }
                    }

                    if ($score > $best_score) {
                        $best_score = $score;
                        $best_match = $address;
                        $match_reason = "matched '{$query}' with score {$score}";
                    }
                }

                $selected_address = $best_match;
            }

            if (!$selected_address) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => "No address found matching '{$query}'"
                ), 404);
            }

            // Store selected address in session for checkout
            if (!session_id()) {
                session_start();
            }
            $_SESSION['pcm_selected_' . $type . '_address'] = $selected_address;

            return new WP_REST_Response(array(
                'success' => true,
                'message' => ucfirst($type) . ' address selected successfully',
                'selected_address' => $selected_address,
                'address_summary' => $this->format_address_summary($selected_address),
                'match_reason' => $match_reason
            ), 200);

        } catch (Exception $e) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => $e->getMessage()
            ), 500);
        }
    }



    /**
     * Format address for voice summary
     */
    private function format_address_summary($address) {
        $parts = array();

        if (!empty($address['first_name']) || !empty($address['last_name'])) {
            $parts[] = trim(($address['first_name'] ?? '') . ' ' . ($address['last_name'] ?? ''));
        }

        if (!empty($address['address_1'])) {
            $parts[] = $address['address_1'];
        }

        if (!empty($address['city'])) {
            $parts[] = $address['city'];
        }

        if (!empty($address['state'])) {
            $parts[] = $address['state'];
        }

        return implode(', ', array_filter($parts));
    }

    /**
     * Check stock for a single product
     */
    public function check_stock(WP_REST_Request $request) {
        try {
            $product_id = $request->get_param('product_id');
            $quantity = $request->get_param('quantity') ?: 1;
            $variation_id = $request->get_param('variation_id') ?: 0;
            $variation = $request->get_param('variation') ?: array();

            $this->pcm_log("Stock check requested for product ID: $product_id, quantity: $quantity");

            // Get the product
            if ($variation_id) {
                $product = wc_get_product($variation_id);
                $parent_product = wc_get_product($product_id);
            } else {
                $product = wc_get_product($product_id);
                $parent_product = null;
            }

            if (!$product) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'Product not found',
                    'product_id' => $product_id,
                    'variation_id' => $variation_id
                ), 404);
            }

            // Check if product manages stock
            $manages_stock = $product->managing_stock();
            $stock_quantity = $product->get_stock_quantity();
            $stock_status = $product->get_stock_status();
            $backorders_allowed = $product->backorders_allowed();

            // Determine availability
            $is_in_stock = $product->is_in_stock();
            $has_enough_stock = true;
            $max_quantity = null;

            if ($manages_stock) {
                if ($stock_quantity !== null) {
                    $has_enough_stock = $stock_quantity >= $quantity;
                    $max_quantity = $stock_quantity;

                    // If backorders are allowed, we can still add to cart even if out of stock
                    if ($backorders_allowed && $stock_quantity < $quantity) {
                        $has_enough_stock = true; // Allow backorders
                    }
                }
            }

            // Check if product is purchasable
            $is_purchasable = $product->is_purchasable();

            // Get stock message
            $stock_message = $this->get_stock_message($product, $quantity, $has_enough_stock);

            $response_data = array(
                'success' => true,
                'product_id' => $product_id,
                'variation_id' => $variation_id,
                'requested_quantity' => $quantity,
                'stock_data' => array(
                    'is_in_stock' => $is_in_stock,
                    'has_enough_stock' => $has_enough_stock,
                    'is_purchasable' => $is_purchasable,
                    'manages_stock' => $manages_stock,
                    'stock_quantity' => $stock_quantity,
                    'stock_status' => $stock_status,
                    'backorders_allowed' => $backorders_allowed,
                    'max_quantity' => $max_quantity,
                    'stock_message' => $stock_message
                ),
                'product_info' => array(
                    'name' => $product->get_name(),
                    'price' => $product->get_price(),
                    'regular_price' => $product->get_regular_price(),
                    'sale_price' => $product->get_sale_price(),
                    'type' => $product->get_type()
                )
            );

            // Add parent product info for variations
            if ($parent_product) {
                $response_data['parent_product_info'] = array(
                    'name' => $parent_product->get_name(),
                    'id' => $parent_product->get_id()
                );
            }

            $this->pcm_log("Stock check completed for product ID: $product_id - In stock: " . ($is_in_stock ? 'Yes' : 'No') . ", Enough stock: " . ($has_enough_stock ? 'Yes' : 'No'));

            return new WP_REST_Response($response_data, 200);

        } catch (Exception $e) {
            $this->pcm_log("Stock check error: " . $e->getMessage());
            return new WP_REST_Response(array(
                'success' => false,
                'error' => $e->getMessage(),
                'product_id' => $product_id ?? null
            ), 500);
        }
    }

    /**
     * Check stock for multiple products (bulk check)
     */
    public function check_bulk_stock(WP_REST_Request $request) {
        try {
            $products = $request->get_param('products');

            if (!is_array($products) || empty($products)) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => 'Products array is required and cannot be empty'
                ), 400);
            }

            $this->pcm_log("Bulk stock check requested for " . count($products) . " products");

            $results = array();
            $overall_success = true;

            foreach ($products as $index => $product_data) {
                // Validate product data structure
                if (!isset($product_data['product_id'])) {
                    $results[] = array(
                        'success' => false,
                        'error' => 'Missing product_id',
                        'index' => $index
                    );
                    $overall_success = false;
                    continue;
                }

                $product_id = $product_data['product_id'];
                $quantity = $product_data['quantity'] ?? 1;
                $variation_id = $product_data['variation_id'] ?? 0;

                // Create a mock request for individual stock check
                $mock_request = new WP_REST_Request();
                $mock_request->set_param('product_id', $product_id);
                $mock_request->set_param('quantity', $quantity);
                $mock_request->set_param('variation_id', $variation_id);

                // Use the single stock check method
                $stock_response = $this->check_stock($mock_request);
                $stock_data = $stock_response->get_data();

                // Add index for reference
                $stock_data['index'] = $index;
                $results[] = $stock_data;

                // Track overall success
                if (!$stock_data['success'] || !$stock_data['stock_data']['has_enough_stock']) {
                    $overall_success = false;
                }
            }

            $response_data = array(
                'success' => true,
                'overall_stock_available' => $overall_success,
                'total_products_checked' => count($products),
                'results' => $results,
                'summary' => array(
                    'in_stock_count' => 0,
                    'out_of_stock_count' => 0,
                    'insufficient_stock_count' => 0,
                    'error_count' => 0
                )
            );

            // Calculate summary
            foreach ($results as $result) {
                if (!$result['success']) {
                    $response_data['summary']['error_count']++;
                } elseif (!$result['stock_data']['is_in_stock']) {
                    $response_data['summary']['out_of_stock_count']++;
                } elseif (!$result['stock_data']['has_enough_stock']) {
                    $response_data['summary']['insufficient_stock_count']++;
                } else {
                    $response_data['summary']['in_stock_count']++;
                }
            }

            $this->pcm_log("Bulk stock check completed - Total: " . count($products) . ", In stock: " . $response_data['summary']['in_stock_count'] . ", Issues: " . ($response_data['summary']['out_of_stock_count'] + $response_data['summary']['insufficient_stock_count'] + $response_data['summary']['error_count']));

            return new WP_REST_Response($response_data, 200);

        } catch (Exception $e) {
            $this->pcm_log("Bulk stock check error: " . $e->getMessage());
            return new WP_REST_Response(array(
                'success' => false,
                'error' => $e->getMessage()
            ), 500);
        }
    }

    /**
     * Get appropriate stock message for a product
     */
    private function get_stock_message($product, $requested_quantity, $has_enough_stock) {
        if (!$product->is_in_stock()) {
            return 'Out of stock';
        }

        if (!$product->managing_stock()) {
            return 'In stock';
        }

        $stock_quantity = $product->get_stock_quantity();

        if ($stock_quantity === null) {
            return 'In stock';
        }

        if (!$has_enough_stock) {
            if ($product->backorders_allowed()) {
                return "Only {$stock_quantity} in stock (backorders allowed)";
            } else {
                return "Only {$stock_quantity} in stock (not enough for requested quantity of {$requested_quantity})";
            }
        }

        // Show stock quantity if low stock threshold is set
        $low_stock_threshold = get_option('woocommerce_notify_low_stock_amount', 2);
        if ($stock_quantity <= $low_stock_threshold) {
            return "Only {$stock_quantity} left in stock";
        }

        // For higher quantities, just show "In stock"
        if ($stock_quantity > 10) {
            return 'In stock';
        }

        return "{$stock_quantity} in stock";
    }
}

/**
 * Initialize the plugin
 */
function primalcom_custom_plugins_init() {
    $instance = PrimalCom_Custom_Plugins::get_instance();
    // Test the PCM debug log
    $instance->pcm_log('Plugin initialization completed - PCM debug log is working!');
    return $instance;
}

/**
 * Helper function to get main plugin instance
 */
function PCM() {
    return PrimalCom_Custom_Plugins::get_instance();
}

// Initialize plugin immediately
primalcom_custom_plugins_init();

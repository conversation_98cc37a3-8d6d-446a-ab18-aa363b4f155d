
<?php

/**
 * SupportBoard Reports Admin Page
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Include the table creation functions
require_once(dirname(__FILE__) . '/include/create_usages_table.php');

// Add Reports menu to admin
function sb_add_reports_menu() {
    add_submenu_page(
        'sb-admin',
        'Reports',
        'Reports',
        'manage_options',
        'sb-reports',
        'sb_reports_page'
    );
}
add_action('admin_menu', 'sb_add_reports_menu');

// Reports page content
function sb_reports_page() {
    // Handle CSV export
    if (isset($_GET['export']) && $_GET['export'] === 'csv') {
        sb_export_usage_csv();
        return;
    }
    
    // Get current page
    $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
    $per_page = 20;
    $offset = ($current_page - 1) * $per_page;
    
    // Get filters
    $filters = array();
    if (!empty($_GET['date_from'])) {
        $filters['date_from'] = sanitize_text_field($_GET['date_from']);
    }
    if (!empty($_GET['date_to'])) {
        $filters['date_to'] = sanitize_text_field($_GET['date_to']);
    }
    if (!empty($_GET['provider'])) {
        $filters['provider'] = sanitize_text_field($_GET['provider']);
    }
    
    // Get data
    $usage_records = sb_get_usage_records($filters, $per_page, $offset);
    $total_records = sb_get_usage_records_count($filters);
    $total_pages = ceil($total_records / $per_page);
    
    ?>
    <div class="wrap">
        <h1>OpenAI Usage Reports</h1>
        
        <!-- Filters -->
        <form method="get" action="">
            <input type="hidden" name="page" value="sb-reports">
            <table class="form-table">
                <tr>
                    <th scope="row">Date From</th>
                    <td><input type="date" name="date_from" value="<?php echo esc_attr($filters['date_from'] ?? ''); ?>"></td>
                </tr>
                <tr>
                    <th scope="row">Date To</th>
                    <td><input type="date" name="date_to" value="<?php echo esc_attr($filters['date_to'] ?? ''); ?>"></td>
                </tr>
                <tr>
                    <th scope="row">Provider</th>
                    <td>
                        <select name="provider">
                            <option value="">All Providers</option>
                            <option value="openai" <?php selected($filters['provider'] ?? '', 'openai'); ?>>OpenAI</option>
                        </select>
                    </td>
                </tr>
            </table>
            <p class="submit">
                <input type="submit" name="filter" class="button-primary" value="Filter">
                <a href="<?php echo esc_url(add_query_arg(array_merge($_GET, array('export' => 'csv')))); ?>" class="button">Export CSV</a>
                <a href="<?php echo esc_url(admin_url('admin.php?page=sb-reports')); ?>" class="button">Clear Filters</a>
            </p>
        </form>
        
        <!-- Results Table -->
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th scope="col">ID</th>
                    <th scope="col">Provider</th>
                    <th scope="col">Prompt Tokens</th>
                    <th scope="col">Completion Tokens</th>
                    <th scope="col">Cached Input Tokens</th>
                    <th scope="col">Total Tokens</th>
                    <th scope="col">Creation Time</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($usage_records)) : ?>
                    <tr>
                        <td colspan="7">No usage records found.</td>
                    </tr>
                <?php else : ?>
                    <?php foreach ($usage_records as $record) : ?>
                        <tr>
                            <td><?php echo esc_html($record->id); ?></td>
                            <td><?php echo esc_html($record->provider); ?></td>
                            <td><?php echo esc_html(number_format($record->prompt_tokens)); ?></td>
                            <td><?php echo esc_html(number_format($record->completion_tokens)); ?></td>
                            <td><?php echo esc_html(number_format($record->cached_input_tokens)); ?></td>
                            <td><?php echo esc_html(number_format($record->prompt_tokens + $record->completion_tokens + $record->cached_input_tokens)); ?></td>
                            <td><?php echo esc_html(date('Y-m-d H:i:s', strtotime($record->creation_time))); ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1) : ?>
            <div class="tablenav">
                <div class="tablenav-pages">
                    <span class="displaying-num"><?php echo number_format($total_records); ?> items</span>
                    <?php
                    $page_links = paginate_links(array(
                        'base' => add_query_arg('paged', '%#%'),
                        'format' => '',
                        'prev_text' => '&laquo;',
                        'next_text' => '&raquo;',
                        'total' => $total_pages,
                        'current' => $current_page
                    ));
                    if ($page_links) {
                        echo '<span class="pagination-links">' . $page_links . '</span>';
                    }
                    ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Summary Statistics -->
        <?php
        global $wpdb;
        $table_name = $wpdb->prefix . 'sb_usages';
        
        $where_conditions = array();
        $values = array();
        
        if (!empty($filters['date_from'])) {
            $where_conditions[] = "creation_time >= %s";
            $values[] = $filters['date_from'] . ' 00:00:00';
        }
        
        if (!empty($filters['date_to'])) {
            $where_conditions[] = "creation_time <= %s";
            $values[] = $filters['date_to'] . ' 23:59:59';
        }
        
        if (!empty($filters['provider'])) {
            $where_conditions[] = "provider = %s";
            $values[] = $filters['provider'];
        }
        
        $where_clause = '';
        if (!empty($where_conditions)) {
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
        }
        
        $sql = "SELECT 
                    SUM(prompt_tokens) as total_prompt_tokens,
                    SUM(completion_tokens) as total_completion_tokens,
                    SUM(cached_input_tokens) as total_cached_tokens,
                    COUNT(*) as total_requests
                FROM $table_name $where_clause";
        
        if (!empty($values)) {
            $prepared_sql = $wpdb->prepare($sql, $values);
        } else {
            $prepared_sql = $sql;
        }
        
        $summary = $wpdb->get_row($prepared_sql);
        ?>
        
        <div class="postbox" style="margin-top: 20px;">
            <h3 class="hndle">Summary Statistics</h3>
            <div class="inside">
                <table class="form-table">
                    <tr>
                        <th>Total Requests:</th>
                        <td><?php echo number_format($summary->total_requests ?? 0); ?></td>
                    </tr>
                    <tr>
                        <th>Total Prompt Tokens:</th>
                        <td><?php echo number_format($summary->total_prompt_tokens ?? 0); ?></td>
                    </tr>
                    <tr>
                        <th>Total Completion Tokens:</th>
                        <td><?php echo number_format($summary->total_completion_tokens ?? 0); ?></td>
                    </tr>
                    <tr>
                        <th>Total Cached Input Tokens:</th>
                        <td><?php echo number_format($summary->total_cached_tokens ?? 0); ?></td>
                    </tr>
                    <tr>
                        <th>Total Tokens:</th>
                        <td><?php echo number_format(($summary->total_prompt_tokens ?? 0) + ($summary->total_completion_tokens ?? 0) + ($summary->total_cached_tokens ?? 0)); ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <style>
    .form-table th {
        width: 150px;
    }
    .wp-list-table th, .wp-list-table td {
        text-align: center;
    }
    .wp-list-table th:first-child, .wp-list-table td:first-child {
        text-align: left;
    }
    </style>
    <?php
}

// CSV Export function
function sb_export_usage_csv() {
    // Get filters
    $filters = array();
    if (!empty($_GET['date_from'])) {
        $filters['date_from'] = sanitize_text_field($_GET['date_from']);
    }
    if (!empty($_GET['date_to'])) {
        $filters['date_to'] = sanitize_text_field($_GET['date_to']);
    }
    if (!empty($_GET['provider'])) {
        $filters['provider'] = sanitize_text_field($_GET['provider']);
    }
    
    // Get all records (no pagination for export)
    $usage_records = sb_get_usage_records($filters, 999999, 0);
    
    // Set headers for CSV download
    $filename = 'openai-usage-' . date('Y-m-d') . '.csv';
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // Create file pointer
    $output = fopen('php://output', 'w');
    
    // Add CSV headers
    fputcsv($output, array(
        'ID',
        'Provider',
        'Prompt Tokens',
        'Completion Tokens',
        'Cached Input Tokens',
        'Total Tokens',
        'Creation Time'
    ));
    
    // Add data rows
    foreach ($usage_records as $record) {
        fputcsv($output, array(
            $record->id,
            $record->provider,
            $record->prompt_tokens,
            $record->completion_tokens,
            $record->cached_input_tokens,
            $record->prompt_tokens + $record->completion_tokens + $record->cached_input_tokens,
            $record->creation_time
        ));
    }
    
    fclose($output);
    exit;
}

?>

<?php

/*
 * ==========================================================
 * COMPONENTS.PHP
 * ==========================================================
 *
 * Library of static html components for the Artificial Intelligence area. This file must not be executed directly. © 2017-2025 board.support. All rights reserved.
 *
 */

function sb_dialogflow_chatbot_area() { 
    // Get absolute URLs to the JS and CSS files
    // --- ADJUSTED BLOCK START ---
    $r2r_index_css_url = SB_URL . '/css/R2R/r2r-index.css?ver=' . SB_VERSION;
    $r2r_index_js_url = SB_URL . '/js/R2R/r2r-index.js?ver=' . SB_VERSION;

    $js_modal = SB_URL . '/js/modalHelper.js?ver=' . SB_VERSION; // Assuming modalHelper.js is still at root js folder
    // --- ADJUSTED BLOCK END ---

    // Include the required CSS file
    // --- ADJUSTED BLOCK START ---
    echo '<link rel="stylesheet" href="' . $r2r_index_css_url . '" type="text/css">';
    // --- ADJUSTED BLOCK END ---

    // Get the active user token for authentication
    $active_user = sb_get_active_user();
    $token = '';

    if ($active_user && isset($active_user['token'])) {
        $token = $active_user['token'];
    }

    $collection_id = sb_db_escape(sb_get_external_setting('r2r_default_collection_id'));

    echo '<script>
        var SB_AJAX_URL = "' . SB_URL . '/include/ajax.php";
        var SB_NONCE = "' . $token . '";
        var r2rCollectionIdFromPHP = "' . $collection_id . '";
    </script>';

    // ⚠️ Load modalHelper first (if it's not an ES Module, it should load before ES Modules)
    echo '<script src="' . $js_modal . '" defer></script>';

    // --- ADJUSTED BLOCK START ---
    // Load the main R2R JavaScript index file
    echo '<script src="' . $r2r_index_js_url . '" type="module" defer></script>';
    // --- ADJUSTED BLOCK END ---

    
?>
    <div class="sb-area-chatbot">
        <div class="sb-top-bar">
            <div>
                <h2>
                    <?php sb_e('Chatbot') ?>
                </h2>
                <div class="sb-menu-wide sb-menu-chatbot">
                    <div>
                        <?php sb_e('Chatbot') ?>
                    </div>
                    <ul>
                        <li data-type="training" class="sb-active">
                            <?php sb_e('Training') ?>
                        </li>
                        <li data-type="flows">
                            <?php sb_e('Flows') ?>
                        </li>
                        <li data-type="playground">
                            <?php sb_e('Playground') ?>
                        </li>
                        <li data-type="settings">
                            <?php sb_e('Settings') ?>
                        </li>
                        <?php sb_docs_link('#open-ai') ?>
                    </ul>
                </div>
            </div>
            <div>
                <a id="sb-train-chatbot" class="sb-btn sb-icon">
                    <i class="sb-icon-automation"></i>
                    <?php sb_e('Train chatbot') ?>
                </a>
            </div>
        </div>
        <div data-id="training" class="sb-tab sb-inner-tab sb-active">
            <div class="sb-nav">
                <ul>
                    <li data-value="files" class="sb-active">
                        <?php sb_e('Files') ?>
                    </li>
                    <li data-value="website">
                        <?php sb_e('Website') ?>
                    </li>
                    <li data-value="qea">
                        <?php sb_e('Q&A') ?>
                    </li>
                    <?php
                    if (sb_get_multi_setting('open-ai', 'open-ai-user-train-conversations')) {
                        echo '<li data-value="conversations">' . sb_('Conversations') . '</li>';
                    }
                    ?>
                    <li data-value="info">
                        <?php sb_e('Information') ?>
                    </li>
                </ul>
            </div>
            <div class="sb-content sb-scroll-area">
                <div class="sb-active">
                    <div style="border: 2px solid #1DA1F2; border-radius: 8px; margin-bottom: 20px; padding: 15px;">
                        <div class="sb-settings-box sb-settings-group sb-padding">
                            <h3 class="sb-title" style="color: #1DA1F2; padding-bottom: 10px; border-bottom: 1px solid #eee;">
                                <i class="sb-icon-upload"></i> <?php sb_e('📤 Upload New Files') ?>
                            </h3>

                            <table id="sb-table-chatbot-files" class="sb-table sb-mt sb-loading">
                                <tbody></tbody>
                            </table>

                            <div class="sb-flex sb-gap sb-mt">
                                <div id="sb-chatbot-add-files" class="sb-btn sb-icon sb-btn-white">
                                    <i class="sb-icon-plus"></i>
                                    <?php sb_e('Add new files') ?>
                                </div>
                                <div id="sb-chatbot-delete-files" class="sb-btn-icon sb-btn-red">
                                    <i class="sb-icon-delete"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="border: 2px solid #1DA1F2; border-radius: 8px; margin-top: 40px; padding: 15px;">
                        <div class="sb-settings-box sb-settings-group sb-padding">
                            <div style="display: flex; align-items: flex-end; justify-content: space-between; padding-bottom: 10px; border-bottom: 1px solid #eee; margin-bottom: 15px; padding-top: 20px;">
                                <h3 class="sb-title" style="color: #1DA1F2; margin: 0;">
                                    <i class="sb-icon-folder"></i> <?php sb_e('📂 List of Uploaded Files') ?>
                                </h3>
                            </div>

                            <div id="r2r-files-search-status"></div>

                            <div class="r2r-files-search-container" style="display: flex; align-items: center; justify-content: space-between; position: relative;">
                                <div id="sb-r2r-delete-files"
                                    class="sb-btn sb-btn-red sb-disabled"
                                    title="<?php sb_e('Delete selected files') ?>"
                                    style="
                                        width: 16px;
                                        height: 36px;
                                        background-color: #dc3545;
                                        border-radius: 8px;
                                        display: inline-flex;
                                        align-items: center;
                                        justify-content: center;
                                        margin-bottom: 15px;
                                        position: absolute;
                                        left: 0;
                                        visibility: visible;
                                    ">
                                    <svg fill="#ffffff" width="20" height="20" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512.004 512.004">
                                        <path d="M490.668,149.337c0-27.992-17.963-51.762-42.99-60.455c1.437-8.533-2.416-17.439-10.35-22.02l-110.848-64
                                            c-10.204-5.892-23.252-2.395-29.143,7.81l-13.25,22.953l-4.311-17.347c-2.841-11.433-14.412-18.399-25.845-15.56L67.607,46.99
                                            c-11.435,2.84-18.403,14.412-15.563,25.847l4.77,19.204c-21.03,10.486-35.479,32.193-35.479,57.296
                                            c0,28.775,18.976,53.102,45.1,61.162l35.208,246.558c4.513,31.528,31.518,54.947,63.359,54.947h0.004h181.99h0.004
                                            c31.841,0,58.846-23.419,63.358-54.94l6.956-48.711c0-0.001,0-0.002,0-0.003l28.27-197.856
                                            C471.701,202.429,490.668,178.106,490.668,149.337z M323.624,50.48l60.372,34.857h-80.494L323.624,50.48z M252.974,85.337
                                            H99.112l-0.517-2.08l144.919-35.989L252.974,85.337z M82.103,128.274c0.601,0.011,1.205,0.001,1.814-0.04
                                            c1.514-0.136,1.731-0.155,2.28-0.23h180.359h13.702h134.095h12.316c11.791,0,21.333,9.542,21.333,21.333
                                            s-9.542,21.333-21.333,21.333H85.344h-0.009c-11.791,0-21.333-9.542-21.333-21.333
                                            C64.002,138.647,71.852,129.832,82.103,128.274z M377.694,384.004H134.311L109.94,213.337h292.139L377.694,384.004z
                                            M165.007,469.337c-10.611,0-19.618-7.811-21.122-18.319l-3.477-24.348h231.19l-3.48,24.353
                                            c-1.503,10.502-10.51,18.313-21.121,18.313H165.007z"/>
                                        <path d="M337.654,362.455c11.663,1.668,22.471-6.435,24.138-18.099l12.203-85.333c1.668-11.663-6.435-22.471-18.099-24.138
                                            c-11.663-1.668-22.471,6.435-24.138,18.099l-12.203,85.333C317.887,349.98,325.99,360.788,337.654,362.455z"/>
                                        <path d="M138.004,259.024l12.203,85.333c1.668,11.663,12.475,19.766,24.138,18.099c11.663-1.668,19.766-12.475,18.099-24.138
                                            l-12.203-85.333c-1.668-11.663-12.475-19.766-24.138-18.099C144.439,236.553,136.337,247.36,138.004,259.024z"/>
                                        <path d="M256.002,362.67c11.782,0,21.333-9.551,21.333-21.333v-85.333c0-11.782-9.551-21.333-21.333-21.333
                                            c-11.782,0-21.333,9.551-21.333,21.333v85.333C234.668,353.119,244.22,362.67,256.002,362.67z"/>
                                    </svg>
                                </div>

                                <div class="r2r-files-search-container" style="display: flex; align-items: center; max-width: 300px; margin-left: auto;">
                                    <div class="r2r-files-search-input-wrapper">
                                        <input type="text" id="r2r-files-search-input"
                                            placeholder="<?php sb_e('Search files...') ?>">
                                        <button id="r2r-files-clear-search" type="button">&times;</button>
                                    </div>

                                    <button id="r2r-files-search-button" type="button">
                                        <i class="sb-icon-search"></i>
                                    </button>
                                </div>
                            </div>

                            <div id="r2r-file-cards-container" class="r2r-files-card-view-hidden" style="min-height: 600px;">
                                <div id="r2r-cards-inner-container"></div>
                                <div id="r2r-cards-load-more" class="r2r-files-loading" style="display: none;">
                                    Loading more documents...
                                </div>
                            </div>

                            <div id="r2r-file-list-view" style="min-height: 500px;">
                                <table id="r2r-files-table-uploaded" class="sb-table">
                                    <tbody>
                                        <tr>
                                            <td colspan="8" class="r2r-files-loading">Loading files...</td>
                                        </tr>
                                    </tbody>
                                </table>

                                <div style="
                                    margin: 20px auto 0 auto;
                                    padding: 6px 10px;
                                    max-width: fit-content;
                                    background-color: #fff3cd;
                                    border: 1px solid #ffeeba;
                                    border-radius: 5px;
                                    color: #856404;
                                    text-align: center;
                                    font-size: 13px;
                                ">
                                    ⚠️ Uploaded files may take a few seconds to appear. Please refresh the page if they don’t show up.
                                </div>

                                <div id="r2r-files-pagination"></div>
                            </div>

                        </div>
                    </div>
                </div>
                <div>
                    <table id="sb-table-chatbot-website" class="sb-table sb-loading">
                        <tbody></tbody>
                    </table>
                    <hr />
                    <div id="sb-repeater-chatbot-website" data-type="repeater" class="sb-setting sb-type-repeater">
                        <div class="input">
                            <div class="sb-repeater">
                                <div class="repeater-item">
                                    <div>
                                        <label>
                                            <?php sb_e('URL') ?>
                                        </label>
                                        <input data-id="open-ai-sources-url" type="url" />
                                    </div>
                                    <div>
                                        <label>
                                            <?php sb_e('Extract URLs') ?>
                                        </label>
                                        <input data-id="open-ai-sources-extract-url" type="checkbox" />
                                    </div>
                                    <i class="sb-icon-close"></i>
                                </div>
                            </div>
                            <div class="sb-btn sb-repeater-add sb-btn-white sb-icon">
                                <i class="sb-icon-plus"></i>
                                <?php sb_e('Add new item') ?>
                            </div>
                            <div id="sb-chatbot-delete-website" class="sb-btn-icon sb-btn-red">
                                <i class="sb-icon-delete"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <div id="sb-chatbot-qea" data-type="repeater" class="sb-setting sb-type-repeater">
                        <div class="input">
                            <div class="sb-repeater">
                                <div class="repeater-item">
                                    <div>
                                        <label>
                                            <?php sb_e('Question') ?>
                                        </label>
                                        <div>
                                            <div data-id="open-ai-faq-questions" class="sb-repeater">
                                                <div class="repeater-item">
                                                    <input data-id="question" placeholder="<?php sb_e('Add question') ?>" type="text" />
                                                    <i class="sb-icon-close"></i>
                                                </div>
                                            </div>
                                            <div class="sb-btn sb-btn-white sb-repeater-add sb-icon">
                                                <i class="sb-icon-plus"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="sb-qea-repeater-answer">
                                        <label>
                                            <?php sb_e('Answer') ?>
                                            <i class="sb-btn-open-ai sb-icon-openai sb-btn-icon" data-sb-tooltip="<?php sb_e('Rewrite') ?>" data-sb-tooltip-init="true"></i>
                                        </label>
                                        <textarea data-id="open-ai-faq-answer"></textarea>
                                    </div>
                                    <div>
                                        <label>
                                            <?php sb_e('Function calling') ?>
                                        </label>
                                        <div class="sb-enlarger sb-enlarger-function-calling">
                                            <div>
                                                <label>
                                                    <?php sb_e('URL') ?>
                                                </label>
                                                <input data-id="open-ai-faq-function-calling-url" type="text" />
                                            </div>
                                            <div>
                                                <label>
                                                    <?php sb_e('Method') ?>
                                                </label>
                                                <select data-id="open-ai-faq-function-calling-method">
                                                    <option>POST</option>
                                                    <option value="GET">GET</option>
                                                    <option value="PUT">PUT</option>
                                                    <option value="PATH">PATH</option>
                                                    <option value="DELETE">DELETE</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label>
                                                    <?php sb_e('Headers') ?>
                                                </label>
                                                <input data-id="open-ai-faq-function-calling-headers" type="text" />
                                            </div>
                                            <div>
                                                <label>
                                                    <?php sb_e('Properties') ?>
                                                </label>
                                                <div>
                                                    <div data-id="open-ai-faq-function-calling-properties" class="sb-repeater">
                                                        <div class="repeater-item">
                                                            <div>
                                                                <input data-id="name" type="text" placeholder="<?php sb_e('Name') ?>" />
                                                            </div>
                                                            <div>
                                                                <input data-id="description" type="text" placeholder="<?php sb_e('Description') ?>" />
                                                            </div>
                                                            <div>
                                                                <input data-id="allowed" type="text" placeholder="<?php sb_e('Allowed values separated by commas') ?>" />
                                                            </div>
                                                            <i class="sb-icon-close"></i>
                                                        </div>
                                                    </div>
                                                    <div class="sb-btn sb-btn-white sb-repeater-add sb-icon">
                                                        <i class="sb-icon-plus"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <label>
                                            <?php sb_e('Set data and actions') ?>
                                        </label>
                                        <div class="sb-enlarger">
                                            <div class="sb-repeater" data-id="open-ai-faq-set-data">
                                                <div class="repeater-item">
                                                    <div>
                                                        <div class="sb-setting">
                                                            <select data-id="id">
                                                                <?php
                                                                $code = '';
                                                                $fields = array_merge([['name' => 'Name', 'id' => 'full_name'], ['name' => 'Email', 'id' => 'email'], ['name' => 'Password', 'id' => 'password']], sb_users_get_fields(), [['name' => 'Assign tags', 'id' => 'tags'], ['name' => 'Assign a department', 'id' => 'department'], ['name' => 'Assign an agent', 'id' => 'agent'], ['name' => 'Go to URL', 'id' => 'redirect'], ['name' => 'Show an article', 'id' => 'open_article'], ['name' => 'Download transcript', 'id' => 'transcript'], ['name' => 'Email transcript', 'id' => 'transcript_email'], ['name' => 'Send email to user', 'id' => 'send_email'], ['name' => 'Send email to agents', 'id' => 'send_email_agents'], ['name' => 'Archive the conversation', 'id' => 'archive_conversation'], ['name' => 'Human takeover', 'id' => 'human_takeover']]);
                                                                for ($i = 0; $i < count($fields); $i++) {
                                                                    $code .= '<option value="' . $fields[$i]['id'] . '">' . $fields[$i]['name'] . '</option>';
                                                                }
                                                                echo $code;
                                                                ?>
                                                            </select>
                                                        </div>
                                                        <div class="sb-setting">
                                                            <input data-id="value" type="text" placeholder="<?php sb_e('Enter the value') ?>" />
                                                        </div>
                                                    </div>
                                                    <i class="sb-icon-close"></i>
                                                </div>
                                            </div>
                                            <div class="sb-btn sb-btn-white sb-repeater-add sb-icon">
                                                <i class="sb-icon-plus"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <i class="sb-icon-close"></i>
                                </div>
                            </div>
                            <div class="sb-btn sb-btn-white sb-repeater-add sb-icon">
                                <i class="sb-icon-plus"></i>
                                <?php sb_e('Add new item') ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php if (sb_get_multi_setting('open-ai', 'open-ai-user-train-conversations')) { ?>
                    <div>
                        <div id="sb-chatbot-conversations">
                            <div data-type="repeater" class="sb-setting sb-type-repeater">
                                <div class="input">
                                    <div class="sb-repeater"></div>
                                </div>
                            </div>
                            <hr />
                            <div id="sb-chatbot-delete-all-training-conversations" class="sb-btn sb-btn-white">Delete all training data</div>
                        </div>
                    </div>
                <?php } ?>
                <div id="sb-chatbot-info" class="sb-active"></div>
            </div>
        </div>
        <div data-id="flows" class="sb-tab sb-inner-tab sb-loading">
            <div class="sb-nav sb-nav-only sb-scroll-area">
                <ul id="sb-flows-nav"></ul>
                <div id="sb-flow-add" class="sb-btn sb-icon sb-btn-white">
                    <i class="sb-icon-plus"></i>
                    <?php sb_e('Add new flow') ?>
                </div>
            </div>
            <div class="sb-content sb-scroll-area"></div>
            <i class="sb-flow-scroll sb-btn sb-btn-white sb-icon-arrow-left"></i>
            <i class="sb-flow-scroll sb-btn sb-btn-white sb-icon-arrow-right"></i>
        </div>
        <div data-id="playground">
            <div class="sb-flex">
                <div class="sb-playground">
                    <div class="sb-scroll-area sb-list"></div>
                    <div class="sb-no-results">
                        <?php sb_e('Send a message') ?>
                    </div>
                    <div class="sb-playground-editor">
                        <div class="sb-setting">
                            <textarea placeholder="<?php sb_e('Write a message...') ?>"></textarea>
                        </div>
                        <div class="sb-flex">
                            <div class="sb-flex">
                                <div data-value="user" class="sb-btn sb-btn-white sb-icon">
                                    <i class="sb-icon-reload"></i>
                                    <?php sb_e('User') ?>
                                </div>
                                <i data-value="clear" class="sb-icon-close sb-btn-icon sb-btn-red"></i>
                            </div>
                            <div class="sb-flex">
                                <div data-value="add" class="sb-btn sb-btn-white">
                                    <?php sb_e('Add') ?>
                                </div>
                                <div data-value="send" class="sb-btn sb-btn-white sb-icon" data-sb-tooltip="<?php sb_e('Send message') ?>">
                                    <i class="sb-icon-send"></i>
                                    <?php sb_e('Send') ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sb-playground-info"></div>
            </div>
        </div>
    </div>
<?php } ?>
<?php
function sb_dialogflow_intent_box() {
    $is_dialogflow = sb_chatbot_active(true, false);
    ?>
    <div class="sb-lightbox sb-dialogflow-intent-box<?php echo $is_dialogflow ? '' : ' sb-dialogflow-disabled' ?>">
        <div class="sb-info"></div>
        <div class="sb-top-bar">
            <div>
                <?php sb_e('Chatbot Training') ?><a href="<?php echo sb_is_cloud() ? sb_defined('SB_CLOUD_DOCS', '') : 'https://board.support/docs' ?>#chatbot-training-window" target="_blank">
                    <i class="sb-icon-help"></i>
                </a>
            </div>
            <div>
                <a class="sb-send sb-btn sb-icon">
                    <i class="sb-icon-check"></i>
                    <?php sb_e('Train chatbot') ?>
                </a>
                <a class="sb-close sb-btn-icon sb-btn-red">
                    <i class="sb-icon-close"></i>
                </a>
            </div>
        </div>
        <div class="sb-main sb-scroll-area">
            <div class="sb-title sb-intent-add">
                <?php echo sb_('Question') . '<i data-value="add" data-sb-tooltip="' . sb_('Add question') . '" class="sb-btn-icon sb-icon-plus"></i><i data-value="previous" class="sb-btn-icon sb-icon-arrow-up"></i><i data-value="next" class="sb-btn-icon sb-icon-arrow-down"></i>' ?>
            </div>
            <div class="sb-setting sb-type-text sb-first">
                <input type="text" />
            </div>
            <div class="sb-title sb-bot-response">
                <?php
                sb_e('Answer');
                if (sb_get_multi_setting('open-ai', 'open-ai-rewrite')) {
                    echo '<i class="sb-btn-open-ai sb-btn-icon sb-icon-openai" data-sb-tooltip="' . sb_('Rewrite') . '"></i>';
                }
                ?>
            </div>
            <div class="sb-setting sb-type-textarea sb-bot-response">
                <textarea></textarea>
            </div>
            <div class="sb-title">
                <?php sb_e('Language') ?>
            </div>
            <?php
            echo sb_dialogflow_languages_list();
            if ($is_dialogflow) {
                echo '<div class="sb-title sb-title-search">' . sb_('Intent') . '<div class="sb-search-btn"><i class="sb-icon sb-icon-search"></i><input type="text" autocomplete="false" placeholder="' . sb_('Search for Intents...') . '" /></div><i id="sb-intent-preview" data-sb-tooltip="' . sb_('Preview') . '" class="sb-icon-help"></i></div><div class="sb-setting sb-type-select"><select id="sb-intents-select"></select></div>';
                if (sb_chatbot_active(false, true)) {
                    echo '<div class="sb-title">' . sb_('Services to update') . '</div><div class="sb-setting sb-type-select"><select id="sb-train-chatbots"><option value="">' . sb_('All') . '</option><option value="open-ai">OpenAI</option><option value="dialogflow">Dialogflow</option></select></div>';
                }
            } else {
                echo '<div class="sb-title sb-title-search">' . sb_('Q&A') . '<div class="sb-search-btn"><i class="sb-icon sb-icon-search"></i><input type="text" autocomplete="false" placeholder="' . sb_('Search for Q&A...') . '" /></div><i id="sb-qea-preview" data-sb-tooltip="' . sb_('Preview') . '" class="sb-icon-help"></i></div><div class="sb-setting sb-type-select"><select id="sb-qea-select"></select></div>';
            }
            ?>
        </div>
    </div>
<?php } ?>


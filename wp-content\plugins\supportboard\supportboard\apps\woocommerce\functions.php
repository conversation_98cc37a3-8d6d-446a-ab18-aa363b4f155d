<?php

/*
 * ==========================================================
 * WOOCOMMERCE APP
 * ==========================================================
 *
 * WooCommerce app. � 2017-2025 board.support. All rights reserved.
 *
 */

define('SB_WOOCOMMERCE', '1.1.6');

/*
 * -----------------------------------------------------------
 * PANEL DATA
 * -----------------------------------------------------------
 *
 * Return the user details for the conversations panel
 *
 */

function sb_woocommerce_get_conversation_details($user_id) {
    $response = ['products' => 0, 'total' => 0, 'orders_count' => 0, 'currency_symbol' => sb_get_setting('wc-currency-symbol', ''), 'cart' => 0, 'orders' => []];
    $session_key = sb_woocommerce_get_session_key($user_id);
    if ($session_key === false) {
        return $response;
    }
    $session = sb_woocommerce_get_session($session_key);
    $total = 0;
    $orders = sb_woocommerce_get_user_orders($user_id);
    for ($i = 0; $i < count($orders); $i++) {
        $total += floatval($orders[$i]['total']);
    }
    $response['total'] = round($total, 2);
    $response['orders'] = $orders;
    $response['orders_count'] = count($orders);
    if ($session && sb_isset($session, 'cart')) {
        $products_cart = [];
        foreach ($session['cart'] as $value) {
            $product = sb_woocommerce_get_product($value['product_id']);
            $product['price'] = $value['line_subtotal'];
            $product['quantity'] = $value['quantity'];
            array_push($products_cart, $product);
        }
        $response['cart'] = $products_cart;
    }
    return $response;
}

/*
 * -----------------------------------------------------------
 * USERS
 * -----------------------------------------------------------
 *
 * 1. Send a chat message if returning visitor
 * 2. Return the session user
 *
 */

function sb_woocommerce_returning_visitor() {
    $user = sb_get_active_user();
    if ($user) {
        $message = sb_get_multi_setting('wc-returning-visitor', 'wc-returning-visitor-message');
        $conversation_id = sb_get_last_conversation_id_or_create($user['id'], 3);
        if (sb_is_error($conversation_id)) {
            return $conversation_id;
        }
        return sb_send_message(sb_get_bot_id(), $conversation_id, sb_woocommerce_merge_fields($message, ['coupon-discount' => sb_get_multi_setting('wc-returning-visitor', 'wc-returning-visitor-coupon-discount'), 'coupon-expiration' => sb_get_multi_setting('wc-returning-visitor', 'wc-returning-visitor-coupon-expiration') . ' seconds', 'user-id' => $user['id']]), [], -1, '{ "event": "open-chat" }');
    }
    return false;
}

function sb_woocommerce_get_customer($session_key = false) {
    $session = sb_woocommerce_get_session($session_key);
    if ($session) {
        $session = $session['customer'];
        $query = '';
        if (sb_isset($session, 'id') && $session['id'] != '0') {
            $query = 'customer_id = "' . $session['id'] . '"';
        } else if (sb_isset($session, 'email')) {
            $query = 'email = "' . $session['email'] . '"';
        }
        return $query ? sb_db_get('SELECT * FROM ' . SB_WP_PREFIX . 'wc_customer_lookup WHERE ' . $query . ' LIMIT 1') : false;
    }
    return false;
}

/*
 * ----------------------------------------------------------
 * CART, ORDERS, AND CHECKOUT
 * ----------------------------------------------------------
 *
 * 1. Recover abandoned carts
 * 2. Return the orders of the user
 * 3. Return the last order of the user
 * 4. Return the order details
 *
 */

function sb_woocommerce_abandoned_carts($cart_item = false) {
    $notifications = [sb_get_multi_setting('wc-abandoned-cart', 'wc-abandoned-cart-1'), intval(sb_get_multi_setting('wc-abandoned-cart', 'wc-abandoned-cart-2'))];
    if (!empty($notifications[0])) {
        $carts = sb_db_get('SELECT * FROM ' . SB_WP_PREFIX . 'woocommerce_sessions', []);
        if ($cart_item)
            array_push($carts, $cart_item);
        if (is_array($carts) && count($carts)) {
            $now = time();
            $index = empty($notifications[1]) ? 1 : 2;
            $notifications = [($notifications[0] == 'now' ? 0 : (intval($notifications[0]) * 3600)), (empty($notifications[1]) ? 0 : ($notifications[1] * 3600))];
            $emails = sb_get_external_setting('wc-emails');
            $admin_notification = sb_get_multi_setting('wc-abandoned-cart', 'wc-abandoned-cart-notify-admin');
            $admin_notification_code = '';
            $users = [];

            // History
            $history = sb_get_external_setting('wc-abandoned-carts');
            $history_updated = [];
            $history_users = [];
            $notification_sent = false;
            if (!$history || $history == false) {
                $history = [];
            }
            for ($i = 0; $i < count($carts); $i++) {
                $user = sb_db_get('SELECT A.* FROM sb_users A, sb_users_data B WHERE B.slug = "woocommerce_session_key" AND B.value = "' . $carts[$i]['session_key'] . '" AND B.user_id = A.id');
                if (empty($user['email']) && isset($carts[$i]['customer'])) {
                    $user['email'] = sb_isset(unserialize($carts[$i]['customer']), 'email', '');
                }
                array_push($users, $user);
                if (!empty($user))
                    array_push($history_updated, sb_isset($user, 'id', sb_isset($user, 'email')));
            }
            sb_save_external_setting('wc-abandoned-carts', $history_updated);

            // Process abandoned carts
            for ($i = 0; $i < count($carts); $i++) {
                $user = $users[$i];
                if (empty($user) || in_array($user['email'], $history) || in_array($user['id'], $history))
                    continue;
                $creation_time = intval($carts[$i]['session_expiry']) - 259200;
                for ($j = 0; $j < $index; $j++) {
                    if ($now > ($creation_time + $notifications[$j])) {
                        $cart = unserialize($carts[$i]['session_value']);
                        $cart_products = isset($cart['cart']) ? unserialize($cart['cart']) : [];
                        if (count($cart_products)) {
                            $customer_email = sb_isset($user, 'email');
                            $user_name = sb_get_user_name($user);
                            $email_subject = false;
                            $email_content = false;
                            $chat_message = sb_get_multi_setting('wc-abandoned-cart', 'wc-abandoned-cart-message-' . ($j + 1));
                            $discount = sb_get_multi_setting('wc-abandoned-cart', 'wc-abandoned-cart-coupon-discount');
                            $parameters = [];
                            $notification_sent = true;
                            $products = [];
                            $parameters['user-name'] = $user_name;

                            if (count($emails) && !empty($emails['wc-email-1'][0]['wc-email-1-subject'][0]) && !empty($emails['wc-email-1'][0]['wc-email-1-content'][0])) {
                                $email_subject = $emails['wc-email-' . ($j + 1)][0]['wc-email-' . ($j + 1) . '-subject'][0];
                                $email_content = $emails['wc-email-' . ($j + 1)][0]['wc-email-' . ($j + 1) . '-content'][0];
                            }

                            // Coupon
                            if (strpos($email_content, '{coupon}') !== false || strpos($chat_message, '{coupon}') !== false) {
                                $coupon = sb_woocommerce_coupon($discount, sb_get_multi_setting('wc-abandoned-cart', 'wc-abandoned-cart-coupon-expiration') . ' days');
                                $parameters['coupon'] = is_array($coupon) ? $coupon[0] : $coupon;
                                $parameters['coupon-discount'] = $discount;
                            }

                            // Cart products
                            foreach ($cart_products as $value) {
                                $product = sb_woocommerce_get_product($value['product_id']);
                                $product['price'] = $value['line_subtotal'];
                                $product['quantity'] = $value['quantity'];
                                array_push($products, $product);
                            }
                            $parameters['products'] = $products;
                            if ($admin_notification) {
                                $admin_notification_code .= '<p style="display:block;line-height:25px;font-size:16px;color:#222;font-weight:600;font-size:12px;letter-spacing:.3px;margin:15px 0;">' . $user_name . ' | <a href="#" style="text-decoration:none;border:none;color:#666;">' . $customer_email . '</a></p>' . sb_woocommerce_merge_fields_html('list', $parameters);
                            }

                            // Send email
                            if (!empty($customer_email)) {
                                sb_email_send($customer_email, sb_woocommerce_merge_fields($email_subject, $parameters), sb_woocommerce_merge_fields($email_content, $parameters));
                                array_push($history_users, $customer_email);
                            }

                            // Send chat message
                            if ($chat_message != '' && sb_isset($user, 'id')) {
                                sb_send_message(sb_get_bot_id(), sb_get_last_conversation_id_or_create($user['id'], 3), sb_woocommerce_merge_fields($chat_message, $parameters), [], 1, '{ "event": "open-chat" }');
                                array_push($history_users, $user['id']);
                            }
                        }
                    }
                }
            }

            // Send admin notification
            if ($notification_sent && isset($emails['wc-email-admin'])) {
                $email_admin = $emails['wc-email-admin'][0]['wc-email-admin-email'][0];
                if ($admin_notification && $email_admin != '') {
                    $email_subject = $emails['wc-email-admin'][0]['wc-email-admin-subject'][0];
                    $email_content = str_replace('{carts}', $admin_notification_code, $emails['wc-email-admin'][0]['wc-email-admin-content'][0]);
                    sb_email_send($email_admin, $email_subject, $email_content);
                }
            }
        }
    }
}

function sb_woocommerce_get_user_orders($user_id) {
    return sb_db_get('SELECT A.id, A.post_status `status`, A.post_date_gmt `date`, C.meta_value `total` FROM ' . SB_WP_PREFIX . 'posts A, ' . SB_WP_PREFIX . 'postmeta B, ' . SB_WP_PREFIX . 'postmeta C WHERE A.id = B.post_id AND A.post_type = "shop_order" AND B.post_id = C.post_id AND C.meta_key = "_order_total" AND B.meta_key = "sb-user" AND B.meta_value = ' . $user_id . ' ORDER BY A.post_date DESC', false);
}

function sb_woocommerce_get_order($order_id) {
    $order = sb_db_get('SELECT A.id, A.post_status `status`, A.post_date_gmt `date`, B.meta_value `total` FROM ' . SB_WP_PREFIX . 'posts A, ' . SB_WP_PREFIX . 'postmeta B WHERE A.id = ' . $order_id . ' AND A.post_type = "shop_order" AND B.post_id = A.id AND B.meta_key = "_order_total"');
    if (!empty($order) && !sb_is_error($order)) {

        // Products
        $order['products'] = [];
        $products = sb_db_get('SELECT A.order_item_name, B.meta_key, B.meta_value FROM ' . SB_WP_PREFIX . 'woocommerce_order_items A, ' . SB_WP_PREFIX . 'woocommerce_order_itemmeta B WHERE A.order_id = ' . $order_id . ' AND A.order_item_id = B.order_item_id AND (B.meta_key = "_product_id" OR B.meta_key = "_qty" OR B.meta_key = "_line_subtotal") ORDER BY order_item_name', false);
        $count = count($products);
        $checks = [];
        for ($i = 0; $i < $count; $i++) {
            $name = $products[$i]['order_item_name'];
            if (!in_array($name, $checks)) {
                $product = ['name' => $name];
                for ($j = 0; $j < $count; $j++) {
                    if ($name == $products[$j]['order_item_name']) {
                        $key;
                        switch ($products[$j]['meta_key']) {
                            case '_product_id':
                                $key = 'id';
                                break;
                            case '_qty':
                                $key = 'quantity';
                                break;
                            case '_line_subtotal':
                                $key = 'price';
                                break;
                        }
                        $product[$key] = $products[$j]['meta_value'];
                    }
                }
                array_push($checks, $name);
                array_push($order['products'], $product);
            }
        }

        // Order details
        for ($i = 0; $i < 2; $i++) {
            $key = $i == 0 ? 'billing' : 'shipping';
            $order_details = sb_db_get('SELECT meta_key, meta_value FROM ' . SB_WP_PREFIX . 'postmeta WHERE post_id = ' . $order['id'] . ' AND (meta_key = "_' . $key . '_address_1" OR meta_key = "_' . $key . '_address_2" OR meta_key = "_' . $key . '_city" OR meta_key = "_' . $key . '_state" OR meta_key = "_' . $key . '_postcode" OR meta_key = "_' . $key . '_country" OR meta_key = "_' . $key . '_first_name" OR meta_key = "_' . $key . '_last_name")', false);
            $order_details_2 = ['_' . $key . '_address_1' => '', '_' . $key . '_address_2' => '', '_' . $key . '_city' => '', '_' . $key . '_state' => '', '_' . $key . '_postcode' => '', '_' . $key . '_country' => '', '_' . $key . '_first_name' => '', '_' . $key . '_last_name' => ''];
            for ($j = 0; $j < count($order_details); $j++) {
                $order_details_2[$order_details[$j]['meta_key']] = $order_details[$j]['meta_value'];
            }
            $order[$key . '_address'] = empty($order_details_2['_' . $key . '_first_name']) ? '' : trim(str_replace('\n\n', '\n', $order_details_2['_' . $key . '_first_name'] . ' ' . $order_details_2['_' . $key . '_last_name'] . '\n' . $order_details_2['_' . $key . '_address_1'] . '\n' . $order_details_2['_' . $key . '_address_2'] . '\n' . $order_details_2['_' . $key . '_postcode'] . ' ' . $order_details_2['_' . $key . '_city'] . ' ' . $order_details_2['_' . $key . '_state'] . ' ' . $order_details_2['_' . $key . '_country']));
        }
        $order['currency_symbol'] = sb_get_setting('wc-currency-symbol');
    }
    return $order;
}

function sb_woocommerce_get_last_order($user_id) {
    $orders = sb_woocommerce_get_user_orders($user_id);
    if (!empty($orders) && count($orders)) {
        return sb_woocommerce_get_order($orders[0]['id']);
    }
    return false;
}

/*
 * ----------------------------------------------------------
 * PRODUCTS
 * ----------------------------------------------------------
 *
 * 1. Return a product
 * 2. Return the products of a category or tag
 * 3. Search products
 * 4. Get and assign additional details to the product
 * 5. Return the product tags and categories
 * 6. Return the product attributes
 * 7. Return the product ID by the name
 * 8. Return the product images
 * 9. Return the taxonomies(tags, categories, attribute terms) of a product
 * 10. Return the parent attribute name of a term
 * 11. Check if a product is in of stock
 * 12. Out of stock notification
 * 13. Fix WooCommerce product images
 *
 */

function sb_woocommerce_get_product($product_id) {
    if (!is_numeric($product_id)) {
        $product_id = sb_woocommerce_get_product_id_by_name($product_id);
    }
    $product = sb_db_get('SELECT p.id, p.post_title name, p.post_excerpt description, m.meta_value price FROM ' . SB_WP_PREFIX . 'posts p, ' . SB_WP_PREFIX . 'postmeta m WHERE p.id = ' . $product_id . ' AND m.meta_key = "_price" AND m.post_id = p.id AND p.post_type = "product"');
    if (!sb_is_error($product) && !empty($product)) {
        $image = sb_db_get('SELECT guid FROM ' . SB_WP_PREFIX . 'posts, ' . SB_WP_PREFIX . 'postmeta WHERE post_id = ' . $product_id . ' AND id = meta_value AND meta_key = "_thumbnail_id"');
        $rating = sb_db_get('SELECT meta_value FROM ' . SB_WP_PREFIX . 'postmeta WHERE post_id = ' . $product_id . ' AND meta_key = "_wc_rating_count" AND meta_value <> "" AND meta_value <> "a:0:{}"');
        $product['image'] = isset($image['guid']) ? sb_woocommerce_fix_image($image['guid']) : SB_URL . '/media/thumb.png';
        $product['rating'] = isset($rating['meta_value']) ? unserialize($rating['meta_value']) : '';
        $product['url'] = sb_wp_site_url() . '?p=' . $product['id'];
        $product['taxonomies'] = sb_woocommerce_get_product_taxonomies($product_id);
    }
    return $product;
}

function sb_woocommerce_get_products($filters = [], $pagination = false, $language = '') {
    $taxonomy = '';
    $date = '';
    $discounted = '';
    $price = '';
    $attribute = '';

    // Categories and tags
    if (sb_isset($filters, 'taxonomy')) {
        $taxonomy = $filters['taxonomy'];
        if (!is_numeric($taxonomy) && $taxonomy != 'Uncategorized') {
            $taxonomy = sb_db_get('SELECT term_id FROM ' . SB_WP_PREFIX . 'terms WHERE name = "' . $taxonomy . '" || slug = "' . $taxonomy . '" LIMIT 1');
            if (isset($taxonomy['term_id'])) {
                $taxonomy = $taxonomy['term_id'];
            } else {
                return [];
            }
        }
        $taxonomy = ' AND p.id IN (SELECT object_id FROM ' . SB_WP_PREFIX . 'term_relationships WHERE term_taxonomy_id = "' . $taxonomy . '")';
    }

    // Attribute
    if (sb_isset($filters, 'attribute')) {
        $attribute_value = sb_db_escape($filters['attribute']);
        // Fixed: Proper WooCommerce attribute search using correct taxonomy structure
        $attribute = ' AND p.ID IN (
            SELECT tr.object_id
            FROM ' . SB_WP_PREFIX . 'terms t
            JOIN ' . SB_WP_PREFIX . 'term_taxonomy tt ON t.term_id = tt.term_id
            JOIN ' . SB_WP_PREFIX . 'term_relationships tr ON tt.term_taxonomy_id = tr.term_taxonomy_id
            WHERE tt.taxonomy LIKE "pa_%"
            AND (t.name = "' . $attribute_value . '" OR t.slug = "' . $attribute_value . '")
        )';
    }

    // Date
    if (sb_isset($filters, 'date')) {
        if (is_array($filters['date'])) {
            $date = ' AND post_date > "' . gmdate('Y-m-d H:i:s', strtotime($filters['date']['startDate'])) . '" AND post_date < "' . gmdate('Y-m-d H:i:s', strtotime($filters['date']['endDate'])) . '"';
        } else {
            $date = ' AND post_date > "' . gmdate('Y-m-d', strtotime($filters['date'])) . '" AND post_date < "' . gmdate('Y-m-d', strtotime($filters['date']) + 86400) . '"';
        }
    }

    // Price
    if (sb_isset($filters, 'max-price')) {
        $price = ' AND m.meta_value <= ' . $filters['max-price'];
    }
    if (sb_isset($filters, 'min-price')) {
        $price = ' AND m.meta_value >= ' . $filters['min-price'];
    }

    // Discounted
    if (sb_isset($filters, 'discounted')) {
        $discounted = ' AND (p.id IN (SELECT post_id FROM ' . SB_WP_PREFIX . 'postmeta WHERE meta_key = "_sale_price") OR p.id IN (SELECT post_parent FROM ' . SB_WP_PREFIX . 'posts, ' . SB_WP_PREFIX . 'postmeta WHERE post_type = "product_variation" AND meta_key = "_sale_price" AND id = post_id))';
    }

    // Get the products
    $query = ['SELECT p.id, p.post_title name, p.post_excerpt description, p.post_date_gmt `date`, m.meta_value price FROM ' . SB_WP_PREFIX . 'posts p, ' . SB_WP_PREFIX . 'postmeta m', ' WHERE p.post_type = "product" AND m.meta_key = "_price" AND m.post_id = p.id AND p.post_status = "publish"' . $taxonomy . $date . $discounted . $price . $attribute, (sb_isset_num($pagination) || $pagination === 0 ? ' LIMIT ' . (intval($pagination) * 100) . ',100' : '')];
    $language = sb_wp_language_get_data('product', 'post-types', $language);
    $language_link = false;
    $language_link_type = false;
    if ($language) {
        $active_language = $language['language'];
        switch ($language['settings']['plugin']) {
            case 'wpml':
                $products = sb_db_get($query[0] . $query[1] . ' AND p.id IN (SELECT element_id FROM ' . SB_WP_PREFIX . 'icl_translations WHERE language_code = "' . $active_language . '") GROUP BY p.id' . $query[2], false);
                break;
            case 'polylang':
                $products = sb_db_get($query[0] . ', ' . SB_WP_PREFIX . 'terms t, ' . SB_WP_PREFIX . 'term_relationships r' . $query[1] . ' AND t.term_id = r.term_taxonomy_id AND r.object_id = p.id AND t.slug = "' . $active_language . '" GROUP BY p.id' . $query[2], false);
                break;
        }
        if ($active_language != $language['settings']['default']) {
            $language_link = $active_language;
            $language_link_type = $language['settings']['link-type'];
        }
    } else {
        $products = sb_db_get($query[0] . $query[1] . ' GROUP BY p.id' . $query[2], false);
    }
    return sb_woocommerce_finalize_products($products, $language_link, $language_link_type);
}

function sb_woocommerce_search_products($search) {
    $search = sb_db_escape(mb_strtolower($search));

    // First try exact match (case insensitive)
    $products = sb_db_get('SELECT p.id, p.post_title name, p.post_excerpt description, m.meta_value price FROM ' . SB_WP_PREFIX . 'posts p, ' . SB_WP_PREFIX . 'postmeta m WHERE LOWER(p.post_title) = "' . $search . '" AND m.meta_key = "_price" AND m.post_id = p.id AND p.post_status = "publish" AND p.post_parent = "" GROUP BY p.id', false);

    // If no exact match, try partial match
    if (empty($products)) {
        $products = sb_db_get('SELECT p.id, p.post_title name, p.post_excerpt description, m.meta_value price FROM ' . SB_WP_PREFIX . 'posts p, ' . SB_WP_PREFIX . 'postmeta m WHERE (LOWER(p.post_title) LIKE "%' . $search . '%" || m.meta_value LIKE "%' . $search . '%" || p.post_excerpt LIKE "%' . $search . '%") AND m.meta_key = "_price" AND m.post_id = p.id AND p.post_status = "publish" AND p.post_parent = "" GROUP BY p.id', false);
    }

    return sb_woocommerce_finalize_products($products);
}

function sb_woocommerce_finalize_products($products, $language = false, $language_type = false) {
    $count = count($products);
    if ($count) {
        $ids = '';
        $site_url = sb_wp_site_url();
        $currency_symbol = sb_get_setting('wc-currency-symbol');
        for ($i = 0; $i < $count; $i++) {
            $ids .= $products[$i]['id'] . ',';
            $products[$i]['title'] = $products[$i]['name'];
            $products[$i]['image'] = '';
            $products[$i]['rating'] = '';
            $products[$i]['price'] .= ' ' . $currency_symbol;
            $products[$i]['url'] = $site_url . '?p=' . $products[$i]['id'];
            if ($language) {
                $products[$i]['url'] = $language_type == 1 ? str_replace('?', $language . '/?', $products[$i]['url']) : $products[$i]['url'] . '&lang=' . $language;
            }
        }
        $ids = substr($ids, 0, -1);
        $images = sb_db_get('SELECT post_id, guid FROM ' . SB_WP_PREFIX . 'posts, ' . SB_WP_PREFIX . 'postmeta WHERE post_id IN (' . $ids . ') AND id = meta_value AND meta_key = "_thumbnail_id"', false);
        $ratings = sb_db_get('SELECT post_id, meta_value FROM ' . SB_WP_PREFIX . 'postmeta WHERE post_id IN (' . $ids . ') AND meta_key = "_wc_rating_count" AND meta_value <> "" AND meta_value <> "a:0:{}"', false);
        for ($i = 0; $i < count($images); $i++) {
            for ($j = 0; $j < $count; $j++) {
                if ($products[$j]['id'] == $images[$i]['post_id']) {
                    $products[$j]['image'] = sb_woocommerce_fix_image($images[$i]['guid']);
                    break;
                }
            }
        }
        for ($i = 0; $i < count($ratings); $i++) {
            for ($j = 0; $j < $count; $j++) {
                if ($products[$j]['id'] == $ratings[$i]['post_id']) {
                    $products[$j]['rating'] = unserialize($ratings[$i]['meta_value']);
                    break;
                }
            }
        }

        // Add stock information to all products
        for ($i = 0; $i < $count; $i++) {
            $product_id = $products[$i]['id'];
            $in_stock = sb_woocommerce_is_in_stock($product_id);
            $products[$i]['in_stock'] = $in_stock;
            $products[$i]['stock_status'] = $in_stock ? 'In Stock' : 'Out of Stock';

            // Check if product has variations
            $has_variations = sb_db_get('SELECT COUNT(*) as count FROM ' . SB_WP_PREFIX . 'posts WHERE post_parent = ' . $product_id . ' AND post_type = "product_variation" AND post_status = "publish"');
            $products[$i]['has_variations'] = $has_variations && $has_variations['count'] > 0;
        }
    }
    return $products;
}

function sb_woocommerce_get_taxonomies($type, $language = '') {
    $is_category = $type == 'category';
    $query = 'SELECT a.term_id id, a.name, a.slug FROM ' . SB_WP_PREFIX . 'terms a, ' . SB_WP_PREFIX . 'term_taxonomy b WHERE a.term_id = b.term_id AND b.taxonomy = ' . ($is_category ? '"product_cat" AND a.slug <> "uncategorized"' : '"product_tag"');
    $language = sb_wp_language_get_data($is_category ? 'product_cat' : 'product_tag', 'taxonomies', $language);
    if ($language) {
        $active_language = $language['language'];
        switch ($language['settings']['plugin']) {
            case 'wpml':
                if ($is_category) {
                    return sb_db_get($query . ' AND a.term_id IN (SELECT element_id FROM ' . SB_WP_PREFIX . 'icl_translations WHERE element_type = "tax_product_cat" AND language_code = "' . $active_language . '")', false);
                } else {
                    return sb_db_get($query . ' AND a.term_id IN (SELECT element_id FROM ' . SB_WP_PREFIX . 'icl_translations WHERE element_type = "tax_product_tag" AND language_code = "' . $active_language . '")', false);
                }
            case 'polylang':
                $translations = sb_db_get('SELECT description FROM ' . SB_WP_PREFIX . 'term_taxonomy WHERE taxonomy = "term_translations"', false);
                $ids = '';
                for ($i = 0; $i < count($translations); $i++) {
                    $translation = unserialize($translations[$i]['description']);
                    if (isset($translation[$active_language])) {
                        $ids .= $translation[$active_language] . ',';
                    }
                }
                return $ids ? sb_db_get($query . ' AND a.term_id IN (' . substr($ids, 0, -1) . ')', false) : [];
        }
    } else {
        return sb_db_get($query, false);
    }
    return false;
}

function sb_woocommerce_get_attributes($type = false, $language = '') {
    $response = [];
    $attributes = sb_db_get('SELECT attribute_id id, attribute_label name, attribute_name slug FROM ' . SB_WP_PREFIX . 'woocommerce_attribute_taxonomies', false);
    $language = sb_wp_language_get_data('pa_' . $attributes[0]['slug'], 'taxonomies', $language);
    $count = count($attributes);
    if ($count) {
        $attribute_terms = [];
        $attribute_slugs = '';
        $attribute_names = '';

        // Attributes
        for ($i = 0; $i < $count; $i++) {
            $slug = $attributes[$i]['slug'];
            $attributes[$i]['slug'] = 'pa_' . $attributes[$i]['slug'];
            $attribute_slugs .= '"' . $attributes[$i]['slug'] . '",';
            $response[$slug] = $attributes[$i];
            $response[$slug]['terms'] = [];
        }
        $attribute_slugs = substr($attribute_slugs, 0, -1);

        // Attribute terms
        $query = 'SELECT a.*, b.* FROM ' . SB_WP_PREFIX . 'terms a, ' . SB_WP_PREFIX . 'term_taxonomy b WHERE b.taxonomy IN (' . $attribute_slugs . ') AND b.term_taxonomy_id = a.term_id';
        if ($language) {
            $active_language = $language['language'];
            switch ($language['settings']['plugin']) {
                case 'wpml':

                    // Attributes translations
                    $attribute_names = '';
                    for ($i = 0; $i < $count; $i++) {
                        $attribute_names .= '"' . $attributes[$i]['name'] . '","Product ' . $attributes[$i]['name'] . '",';
                    }
                    $language_strings = sb_db_get('SELECT A.value, B.value original FROM ' . SB_WP_PREFIX . 'icl_string_translations A, ' . SB_WP_PREFIX . 'icl_strings B WHERE B.value IN (' . substr($attribute_names, 0, -1) . ') AND A.string_id = B.id AND A.`language` = "' . $active_language . '"', false);
                    foreach ($response as $key => $value) {
                        for ($i = 0; $i < count($language_strings); $i++) {
                            if ($value['name'] == $language_strings[$i]['original']) {
                                $response[$key]['name'] = $language_strings[$i]['value'];
                                break;
                            } else if (('Product ' . $value['name']) == $language_strings[$i]['original']) {
                                $response[$key]['name_plural'] = $language_strings[$i]['value'];
                            }
                        }
                    }

                    // Attribute terms translations
                    $attribute_terms = sb_db_get($query . ' AND a.term_id IN (SELECT element_id FROM ' . SB_WP_PREFIX . 'icl_translations WHERE element_type IN (' . str_replace('pa_', 'tax_pa_', $attribute_slugs) . ') AND language_code = "' . $active_language . '")', false);
                    break;
                case 'polylang':

                    // Attributes translations
                    $mo_id = sb_isset($language['settings']['extra']['mo'], $active_language);
                    if ($mo_id) {
                        $language_strings = sb_db_get('SELECT meta_value FROM ' . SB_WP_PREFIX . 'postmeta WHERE post_id = "' . $mo_id . '" AND meta_key = "_pll_strings_translations"');
                        if (!empty($language_strings)) {
                            $language_strings = unserialize($language_strings['meta_value']);
                            for ($i = 0; $i < count($language_strings); $i++) {
                                foreach ($response as $key => $value) {
                                    if ($value['name'] == $language_strings[$i][0]) {
                                        $response[$key]['name'] = $language_strings[$i][1];
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    // Attribute terms translations
                    $translations = sb_db_get('SELECT description FROM ' . SB_WP_PREFIX . 'term_taxonomy WHERE taxonomy = "term_translations"', false);
                    $ids = '';
                    for ($i = 0; $i < count($translations); $i++) {
                        $translation = unserialize($translations[$i]['description']);
                        if (isset($translation[$active_language])) {
                            $ids .= $translation[$active_language] . ',';
                        }
                    }
                    $attribute_terms = $ids ? sb_db_get($query . ' AND a.term_id IN (' . substr($ids, 0, -1) . ')', false) : [];
                    break;
            }
        } else {
            $attribute_terms = sb_db_get($query, false);
        }
        for ($i = 0; $i < count($attribute_terms); $i++) {
            $response[substr($attribute_terms[$i]['taxonomy'], 3)]['terms'][$attribute_terms[$i]['slug']] = $attribute_terms[$i]['name'];
        }
    }
    switch ($type) {
        case 'terms':
            $names = [];
            foreach ($response as $value) {
                foreach ($value['terms'] as $term) {
                    array_push($names, ['name' => $term]);
                }
            }
            return $names;
        case 'attributes':
            $names = [];
            foreach ($response as $value) {
                array_push($names, ['id' => $value['id'], 'name' => $value['name'], 'slug' => $value['slug']]);
            }
            return $names;
        default:
            return $response;
    }
}

function sb_woocommerce_get_product_id_by_name($name) {
    $product_id = sb_db_get('SELECT id FROM ' . SB_WP_PREFIX . 'posts WHERE post_title LIKE "%' . sb_db_escape(strtolower($name)) . '%" LIMIT 1');
    if (sb_isset($product_id, 'id')) {
        return $product_id['id'];
    }
    return false;
}

function sb_woocommerce_get_product_images($product_id) {
    if (!is_numeric($product_id)) {
        $product_id = sb_woocommerce_get_product_id_by_name($product_id);
    }
    $images = sb_db_get('SELECT guid FROM ' . SB_WP_PREFIX . 'posts, ' . SB_WP_PREFIX . 'postmeta WHERE (post_id = ' . $product_id . ' AND meta_key = "_product_image_gallery" AND FIND_IN_SET(id, meta_value) > 0) OR (id = meta_value AND post_id = ' . $product_id . ' AND meta_key = "_thumbnail_id")', false);
    for ($i = 0; $i < count($images); $i++) {
        $images[$i] = sb_woocommerce_fix_image($images[$i]['guid']);
    }
    return $images;
}

function sb_woocommerce_get_product_taxonomies($product_id) {
    if (!is_numeric($product_id)) {
        $product_id = sb_woocommerce_get_product_id_by_name($product_id);
    }
    $response = [];
    $attributes = sb_woocommerce_get_attributes('attributes');
    $attributes_terms = sb_db_get('SELECT A.*, C.taxonomy FROM ' . SB_WP_PREFIX . 'terms A, ' . SB_WP_PREFIX . 'term_relationships B, ' . SB_WP_PREFIX . 'term_taxonomy C WHERE B.object_id = ' . $product_id . ' AND B.term_taxonomy_id = A.term_id AND C.term_id = A.term_id', false);
    for ($i = 0; $i < count($attributes); $i++) {
        $attributes[$i] = $attributes[$i]['slug'];
    }
    array_push($attributes, 'product_tag', 'product_cat');
    for ($i = 0; $i < count($attributes_terms); $i++) {
        if (in_array($attributes_terms[$i]['taxonomy'], $attributes)) {
            array_push($response, $attributes_terms[$i]);
        }
    }
    return $response;
}

function sb_woocommerce_get_attribute_by_term($term_name) {
    $attributes = sb_woocommerce_get_attributes();
    $term_name = strtolower($term_name);
    foreach ($attributes as $value) {
        foreach ($value['terms'] as $term) {
            if (strtolower($term) == $term_name) {
                return ['id' => $value['id'], 'name' => $value['name'], 'slug' => $value['slug']];
            }
        }
    }
    return false;
}

function sb_woocommerce_get_attribute_by_name($name) {
    $attributes = sb_woocommerce_get_attributes();
    $name = strtolower($name);
    foreach ($attributes as $value) {
        if (strtolower($value['name']) == $name) {
            return ['id' => $value['id'], 'name' => $value['name'], 'slug' => $value['slug']];
        }
    }
    return false;
}

function sb_woocommerce_is_in_stock($product_id) {
    // First check if this is a simple product (no variations)
    $has_variations = sb_db_get('SELECT COUNT(*) as count FROM ' . SB_WP_PREFIX . 'posts WHERE post_parent = ' . $product_id . ' AND post_type = "product_variation" AND post_status = "publish"');

    if (!$has_variations || $has_variations['count'] == 0) {
        // Simple product - check parent product stock
        return empty(sb_db_get('SELECT * FROM ' . SB_WP_PREFIX . 'postmeta WHERE post_id = ' . $product_id . ' AND meta_key = "_stock_status" AND meta_value = "outofstock" LIMIT 1'));
    } else {
        // Variable product - check if ANY variation is in stock
        $in_stock_variations = sb_db_get('
            SELECT COUNT(*) as count
            FROM ' . SB_WP_PREFIX . 'posts v
            JOIN ' . SB_WP_PREFIX . 'postmeta stock_meta ON stock_meta.post_id = v.ID AND stock_meta.meta_key = "_stock_status"
            WHERE v.post_parent = ' . $product_id . '
            AND v.post_type = "product_variation"
            AND v.post_status = "publish"
            AND stock_meta.meta_value != "outofstock"
        ');

        return $in_stock_variations && $in_stock_variations['count'] > 0;
    }
}

function sb_woocommerce_fix_image($url) {
    if (substr_count($url, '.jpg') > 1) {
        $url = substr($url, 0, strpos($url, '.jpg') + 4);
    }
    if (substr_count($url, '.png') > 1) {
        $url = substr($url, 0, strpos($url, '.png') + 4);
    }
    return $url;
}

/*
 * -----------------------------------------------------------
 * COUPON
 * -----------------------------------------------------------
 *
 * 1. Create a coupon and return the code
 * 2. Delete expired coupons
 * 3. Check if the user has active coupons
 *
 */

function sb_woocommerce_coupon($discount, $expiration, $product_id = '', $user_id = '') {
    $coupon_code = substr(md5(uniqid(rand(), true)), 0, 15);
    $post_id = sb_wp_post($coupon_code, '', 'shop_coupon');
    if (!sb_is_error($post_id) && is_numeric($post_id)) {
        $response = sb_db_query('INSERT INTO ' . SB_WP_PREFIX . 'postmeta (post_id, meta_key, meta_value) VALUES (' . $post_id . ', "discount_type", "percent"), (' . $post_id . ', "coupon_amount", "' . $discount . '"), (' . $post_id . ', "individual_use", "yes"), (' . $post_id . ', "usage_limit", "1"), (' . $post_id . ', "usage_limit_per_user", "0"), (' . $post_id . ', "limit_usage_to_x_items", "0"), (' . $post_id . ', "usage_count", "0"), (' . $post_id . ', "date_expires", "' . strtotime('+' . $expiration) . '"), (' . $post_id . ', "free_shipping", "no"), (' . $post_id . ', "exclude_sale_items", "no"), (' . $post_id . ', "product_ids", "' . $product_id . '")' . ($user_id ? ', (' . $post_id . ', "user_id", "' . $user_id . '")' : ''));
        if (sb_is_error($response))
            return $response;
        return [$coupon_code, $discount];
    }
    return $post_id;
}

function sb_woocommerce_coupon_check($user_id) {
    return intval(sb_isset(sb_db_get('SELECT COUNT(*) count FROM ' . SB_WP_PREFIX . 'postmeta WHERE meta_key = "coupon_amount" AND post_id = (SELECT post_id FROM ' . SB_WP_PREFIX . 'postmeta WHERE meta_value = "' . $user_id . '" AND meta_key = "user_id")'), 'count')) > 0;
}

function sb_woocommerce_coupon_delete_expired() {
    $expired_coupons_ids = sb_db_get('SELECT p.id FROM ' . SB_WP_PREFIX . 'posts p, ' . SB_WP_PREFIX . 'postmeta m WHERE p.post_type = "shop_coupon" AND p.ID = m.post_id AND m.meta_key = "date_expires" AND m.meta_value < ' . strtotime('now'), false);
    if (!sb_is_error($expired_coupons_ids) && is_array($expired_coupons_ids)) {
        $expired_coupons_ids_string = '';
        for ($i = 0; $i < count($expired_coupons_ids); $i++) {
            $expired_coupons_ids_string .= $expired_coupons_ids[$i]['id'] . ',';
        }
        $expired_coupons_ids_string = substr($expired_coupons_ids_string, 0, -1);
        if ($expired_coupons_ids_string) {
            sb_db_query('DELETE FROM ' . SB_WP_PREFIX . 'posts WHERE id IN (' . $expired_coupons_ids_string . ')');
            sb_db_query('DELETE FROM ' . SB_WP_PREFIX . 'postmeta WHERE post_id IN (' . $expired_coupons_ids_string . ')');
        }
    }
    return true;
}

function sb_woocommerce_waiting_list($product_id, $conversation_id = false, $user_id = false, $action = 'request', $token = -1) {
    $settings = sb_get_setting('wc-waiting-list');
    $user = $user_id === false ? sb_get_active_user() : sb_get_user($user_id);
    if (!$user) {
        return sb_error('user-not-found', 'sb_woocommerce_waiting_list');
    }
    if (sb_isset($settings, 'wc-waiting-list-active')) {
        $product = sb_woocommerce_get_product($product_id);
        if (empty($conversation_id)) {
            $conversation_id = sb_get_last_conversation_id_or_create(sb_isset($user, 'id'), 3);
        }
        switch ($action) {
            case 'request':
                if (!empty($product)) {
                    $message = sb_(sb_woocommerce_merge_fields($settings['wc-waiting-list-message'], ['products' => [$product]]));
                    $rich_message = sb_isset($settings, 'wc-waiting-list-button-text') ? '[chips id="sb-waiting-list" message="' . sb_rich_value($message, false, false) . '" options="' . sb_($settings['wc-waiting-list-button-text']) . ',' . sb_($settings['wc-waiting-list-button-text-cancel']) . '"]' : '';
                    return sb_send_message(sb_get_bot_id(), $conversation_id, $rich_message ? $rich_message : $message, [], 1, '{ "event": "open-chat" }');
                }
                return sb_error('product-not-found', 'sb_woocommerce_waiting_list');
            case 'submit':
                $message = '';
                if ($user['email']) {
                    $waiting_list = sb_get_external_setting('wc-waitig-list');
                    $waiting_list_product = sb_isset($waiting_list, $product_id, []);
                    if (!in_array($user['email'], $waiting_list_product)) {
                        array_push($waiting_list_product, $user['email']);
                        $waiting_list[$product_id] = $waiting_list_product;
                        sb_save_external_setting('wc-waitig-list', $waiting_list);
                    }
                    $message = sb_woocommerce_merge_fields(sb_($settings['wc-waiting-list-message-success']), ['products' => [$product]]);
                } else {
                    $follow = sb_get_setting('follow-message');
                    $message = '[email id="sb-waiting-list-email" message="' . sb_woocommerce_merge_fields(sb_rich_value($settings['wc-waiting-list-message-email']), ['products' => [$product]]) . '" placeholder="' . sb_rich_value($follow['follow-placeholder'], false) . '" name="' . $follow['follow-name'] . '" last-name="' . sb_isset($follow, 'follow-last-name') . '"]';
                }
                return sb_send_message(sb_get_bot_id(), $conversation_id, $message);
            case 'send':
                $waiting_list = sb_get_external_setting('wc-waitig-list');
                foreach ($waiting_list as $key => $value) {
                    if ($key == $product_id) {
                        if (sb_woocommerce_is_in_stock($product_id)) {
                            $emails = sb_isset(sb_get_external_setting('wc-emails'), 'wc-waiting-list-email', []);
                            $subject = sb_woocommerce_merge_fields($emails[0]['wc-waiting-list-email-subject'][0], ['products' => [$product]]);
                            $content = sb_woocommerce_merge_fields($emails[0]['wc-waiting-list-email-content'][0], ['products' => [$product]]);
                            for ($i = 0; $i < count($value); $i++) {
                                sb_email_send($value[$i], $subject, $content);
                            }
                            unset($waiting_list[$key]);
                            return sb_save_external_setting('wc-waitig-list', $waiting_list);
                        }
                        break;
                    }
                }
                break;
        }
    }
    return false;
}

/*
 * -----------------------------------------------------------
 * WOOCOMMERCE CRON JOBS
 * -----------------------------------------------------------
 *
 * Execute the WooCommerce cron jobs
 *
 */

function sb_woocommerce_cron_jobs($cron_functions = []) {
    $cron_jobs = ['product-removed', 'follow-up'];
    $emails = sb_get_external_setting('wc-emails');
    $now = strtotime('now');
    for ($i = 0; $i < count($cron_jobs); $i++) {
        $job = $cron_jobs[$i];
        $scheduled = sb_get_external_setting('cron-wc-' . $job);
        $scheduled_updated = [];
        if (!empty($scheduled)) {
            $email = sb_isset($emails, 'wc-' . $job . '-email', [false])[0];
            $email_subject = sb_isset($email, 'wc-' . $job . '-email-subject', [''])[0];
            $email_content = sb_isset($email, 'wc-' . $job . '-email-content', [''])[0];
            if (!empty($email_subject) && !empty($email_content) && !empty($email)) {
                foreach ($scheduled as $key => $value) {
                    $scheduled_updated[$key] = $value;
                    if ($now > $value[1]) {
                        $user = sb_get_user($key);
                        if ($user['email'] != '') {
                            $products = [];
                            $parameters = ['coupon-discount' => $email['wc-' . $job . '-email-coupon-discount'][0], 'coupon-expiration' => $email['wc-' . $job . '-email-coupon-expiration'][0] . ' days', 'user-name' => sb_get_user_name($user), 'user-id' => $user['id']];
                            for ($i = 0; $i < count($value[0]); $i++) {
                                array_push($products, sb_woocommerce_get_product($value[0][$i]));
                            }
                            $parameters['products'] = $products;
                            sb_email_send($user['email'], sb_woocommerce_merge_fields($email_subject, $parameters), sb_woocommerce_merge_fields($email_content, $parameters));
                        }
                        unset($scheduled_updated[$key]);
                    }
                }
                sb_save_external_setting('cron-wc-' . $job, $scheduled_updated);
            }
        }
    }
    sb_woocommerce_abandoned_carts();
    sb_woocommerce_coupon_delete_expired();
}

/*
 * ----------------------------------------------------------
 * MERGE FIELDS
 * ----------------------------------------------------------
 *
 * Replace the merge fields with the real values
 *
 */

function sb_woocommerce_merge_fields($message, $parameters = [], $language = '') {
    if (strpos($message, '{') === false) {
        return $message;
    }
    $extra = [0, '', ''];
    $products = sb_isset($parameters, 'products', []);
    $products_count = count($products);
    $replace = '';
    $marge_fields = ['purchase_button', 'original_price', 'product_names', 'html_products_list', 'product_price', 'product_rating', 'product_link', 'product_images', 'html_product_card', 'product_card', 'product_name', 'product_description', 'product_link', 'product_price', 'product_image', 'user_name', 'coupon', 'product_card', 'products_slider', 'shop_link', 'cart_link', 'cart', 'payment_methods', 'shipping_locations', 'order_status', 'order_details'];

    // Set merge fields
    if (isset($parameters['coupon-discount'])) {
        array_push($marge_fields, 'discount_price');
    }
    if (isset($parameters['woocommerce-products'])) {
        $products = [sb_woocommerce_get_product($parameters['woocommerce-products'])];
        $products_count = 1;
    }
    if (isset($parameters['woocommerce-categories'])) {
        array_push($marge_fields, 'category_link');
    }
    if (isset($parameters['woocommerce-tags'])) {
        array_push($marge_fields, 'tag_link');
    }

    // Set extra data
    for ($i = 0; $i < $products_count; $i++) {
        $extra[0] += sb_isset($products[$i], 'price');
        $extra[1] .= sb_isset($products[$i], 'name') . ', ';
        $extra[2] .= sb_isset($products[$i], 'id') . ',';
    }

    // Process merge fields
    for ($i = 0; $i < count($marge_fields); $i++) {
        if (strpos($message, '{' . $marge_fields[$i]) !== false) {
            $merge_field = '{' . $marge_fields[$i] . '}';
            $shortcode = sb_get_shortcode($message, $marge_fields[$i], true);
            $product = isset($shortcode['id']) ? sb_woocommerce_get_product($shortcode['id']) : ($products_count ? $products[0] : '');
            switch ($marge_fields[$i]) {
                case 'coupon':
                    if (isset($parameters['coupon'])) {
                        $replace = $parameters['coupon'];
                    } else {
                        $is_shortcode = !sb_isset($parameters, 'coupon-discount');
                        $discount = $is_shortcode ? $shortcode['discount'] : $parameters['coupon-discount'];
                        $expiration = $is_shortcode ? $shortcode['expiration'] : $parameters['coupon-expiration'];
                        $product_ids = $is_shortcode ? sb_isset($shortcode, 'product-ids', '') : sb_isset($parameters, 'coupon-product-ids', '');
                        $merge_field = $is_shortcode ? $shortcode['shortcode'] : $merge_field;
                        $coupon = sb_woocommerce_coupon($discount, $expiration, $product_ids, sb_isset($parameters, 'user-id', ''));
                        $replace = $coupon[0];
                        $parameters['coupon-discount'] = $coupon[1];
                    }
                    break;
                case 'discount_price':
                    $replace = round($extra[0] * ((100 - intval($parameters['coupon-discount'])) / 100), 2);
                    break;
                case 'original_price':
                    $replace = round($extra[0], 2);
                    break;
                case 'product_names':
                    $replace = mb_substr($extra[1], 0, -2);
                    break;
                case 'product_name':
                    // previous code 
                    // $replace = $product['name'];
                    $replace = !empty($product['name']) ? $product['name'] : '';
                    break;
                case 'product_image':
                    $replace = $product['image'];
                    break;
                case 'product_description':
                    $replace = $product['description'];
                    break;
                case 'product_card':
                    $link_type = sb_isset($shortcode, 'link-type', 'link');
                    $merge_field = $shortcode['shortcode'];
                    if (!empty($product)) {
                        $catalog_id = sb_get_multi_setting('whatsapp-catalog', 'whatsapp-catalog-id');
                        $replace = '[card ' . ($catalog_id ? 'whatsapp-catalog-id="' . $catalog_id . '"' : '') . ' product-id="' . $product['id'] . '" image="' . $product['image'] . '" header="' . sb_rich_value($product['name']) . '"  description="' . str_replace([PHP_EOL, "\r", "\n"], ' ', sb_rich_value(strlen($product['description']) > 130 ? mb_substr($product['description'], 0, 130) : $product['description'])) . '..." link-text="' . sb_rich_value(sb_isset($shortcode, 'link-text', 'More details'), false) . '" extra="' . sb_get_setting('wc-currency-symbol') . $product['price'] . '" link="' . ($link_type == 'link' ? $product['url'] : '#') . '" settings="link-type:' . $link_type . ',id:' . $product['id'] . '"]';
                    }
                    break;
                case 'product_price':
                    if ($product) {
                        $currency_symbol = sb_get_setting('wc-currency-symbol');
                        $prices = sb_db_get('SELECT * FROM ' . SB_WP_PREFIX . 'postmeta WHERE (meta_key = "_price" OR meta_key = "_regular_price") AND post_id IN (SELECT id FROM ' . SB_WP_PREFIX . 'posts WHERE post_parent = ' . $product['id'] . ') ORDER BY post_id', false);
                        $min_price = floatval($product['price']);
                        $max_price = 0;
                        $count = count($prices);
                        for ($j = 0; $j < $count; $j++) {
                            $price = floatval($prices[$j]['meta_value']);
                            if ($min_price > $price) {
                                $min_price = $price;
                            } else {
                                $price = $j < ($count - 1) && $prices[$j + 1]['post_id'] == $prices[$j]['post_id'] && $prices[$j + 1]['meta_key'] == '_price' ? floatval($prices[$j + 1]['meta_value']) : $price;
                                if ($max_price < $price) {
                                    $max_price = $price;
                                }
                            }
                        }
                        $replace = $currency_symbol . ($min_price != $max_price && $max_price != 0 ? $min_price . ' - ' . $currency_symbol . $max_price : $min_price);
                    }
                    break;
                case 'product_rating':
                    if (!$product['rating']) {
                        $message = str_replace('{R}', $product['name'], sb_('{R} has no ratings yet.'));
                    } else {
                        $replace = sb_woocommerce_rating($product['rating']);
                    }
                    break;
                case 'product_link':
                    $replace = $product['url'];
                    break;
                case 'purchase_button':
                    $replace = '[woocommerce_button name="' . sb_(sb_isset($parameters, 'button-text', isset($parameters['checkout']) ? 'Purchase now' : 'Add to cart')) . '" ids="' . ($products_count ? substr($extra[2], 0, -1) : $parameters['id']) . '" coupon="' . sb_isset($parameters, 'coupon', '') . '" checkout="' . sb_isset($parameters, 'checkout') . '"]';
                    break;
                case 'shop_link':
                    $replace = sb_woocommerce_get_url('shop', '', $language);
                    break;
                case 'cart_link':
                    $replace = sb_woocommerce_get_url('cart', '', $language);
                    break;
                case 'cart':
                    $session_key = sb_woocommerce_get_session_key(sb_isset(sb_get_active_user(), 'id', -1));
                    $session = sb_woocommerce_get_session($session_key);
                    $products = [];
                    $extra_values = [];
                    if ($session) {
                        foreach ($session['cart'] as $value) {
                            array_push($products, sb_woocommerce_get_product($value['product_id']));
                            array_push($extra_values, [$value['quantity'], $value['line_total']]);
                        }
                    }
                    if (count($products)) {
                        $currency_symbol = sb_get_setting('wc-currency-symbol');
                        $replace = '[list-image values="';
                        for ($y = 0; $y < count($products); $y++) {
                            $replace .= $products[$y]['image'] . ':' . $extra_values[$y][0] . ' X ' . str_replace(',', '', sb_rich_value($products[$y]['name'], false, false)) . 'Total Price: ' . $currency_symbol . $extra_values[$y][1] . ',';
                        }
                        $replace = substr($replace, 0, -1) . '"]';
                    } else {
                        $message = sb_('The cart is empty.');
                    }
                    break;
                case 'order_status':
                case 'order_details':
                    $order = sb_woocommerce_get_last_order(sb_isset(sb_get_active_user(), 'id'));
                    if (!empty($order)) {
                        $order_status = sb_(str_replace('wc-', '', sb_isset($order, 'status', 'processing')));
                        if ($marge_fields[$i] == 'order_status') {
                            $replace = $order_status;
                        } else {
                            $date = strtotime($order['date']);
                            $replace = '[list values="' . sb_('Order ID') . ':' . $order['id'] . ',' . sb_('Date') . ':' . date('l', $date) . ' ' . date('d', $date) . ' ' . date('F', $date) . ',' . sb_('Total') . ':' . sb_get_setting('wc-currency-symbol') . $order['total'] . ',' . sb_('Status') . ':' . ucfirst($order_status) . (sb_isset($order, 'shipping_address') ? ',' . sb_('Shipping address') . ':' . sb_rich_value($order['shipping_address'], false, false) : '') . (sb_isset($order, 'billing_address') ? ',' . sb_('Billing address') . ':' . sb_rich_value($order['billing_address'], false, false) : '') . '"]';
                        }
                    } else {
                        $message = sb_('You haven\'t placed an order yet.');
                    }
                    break;
                case 'products_slider':
                    $shortcode = sb_get_shortcode($message, 'products_slider', true);
                    $merge_field = $shortcode['shortcode'];
                    $products = [];
                    $filters = [];
                    if (sb_isset($shortcode, 'ids')) {
                        $product_ids = explode(',', $shortcode['ids']);
                        $count = count($product_ids);
                        if ($count > 15)
                            $count = 15;
                        for ($y = 0; $y < $count; $y++) {
                            $product = sb_woocommerce_get_product($product_ids[$y]);
                            if (!empty($product)) {
                                array_push($products, sb_woocommerce_get_product($product_ids[$y]));
                            }
                        }
                    } else {
                        if (sb_isset($parameters, 'woocommerce-tags') || sb_isset($shortcode, 'tag')) {
                            $filters = ['taxonomy' => sb_isset($parameters, 'woocommerce-tags', sb_isset($shortcode, 'tag'))];
                        } else if (sb_isset($parameters, 'woocommerce-categories') || sb_isset($shortcode, 'category')) {
                            $filters = ['taxonomy' => sb_isset($parameters, 'woocommerce-categories', sb_isset($shortcode, 'category'))];
                        }
                        if (!empty($filters['taxonomy']) && is_array($filters['taxonomy'])) {
                            $filters['taxonomy'] = $filters['taxonomy'][0];
                        }
                        if (sb_isset($parameters, 'sys-date-time')) {
                            $filters['date'] = $parameters['sys-date-time'];
                        }
                        if (sb_isset($shortcode, 'discounted')) {
                            $filters['discounted'] = true;
                        }
                        if (sb_isset($shortcode, 'min-price')) {
                            $filters['min-price'] = sb_isset($parameters, 'min-price', sb_isset($shortcode, 'min-price'));
                        }
                        if (sb_isset($parameters, 'sys-unit-currency')) {
                            $filters['max-price'] = $parameters['sys-unit-currency']['amount'];
                        }
                        if (sb_isset($parameters, 'woocommerce-attribute-terms') || sb_isset($shortcode, 'attribute')) {
                            $filters['attribute'] = sb_isset($parameters, 'woocommerce-attribute-terms', sb_isset($shortcode, 'attribute'));
                        }
                        $products = sb_woocommerce_get_products($filters, 0, $language);
                        if (sb_isset($shortcode, 'rating')) {
                            $products_update = [];
                            $rating = intval($shortcode['rating']) - 0.5;
                            for ($y = 0; $y < count($products); $y++) {
                                if (sb_woocommerce_rating($products[$y]['rating']) > $rating) {
                                    array_push($products_update, $products[$y]);
                                }
                            }
                            $products = $products_update;
                        }
                    }
                    $count = count($products);
                    if ($count) {
                        $count = $count > 15 ? 15 : $count;
                        $link_type = sb_isset($shortcode, 'link-type', 'link');
                        $link_text = sb_rich_value(sb_isset($shortcode, 'link-text', 'More details'), false, true);
                        $currency_symbol = sb_get_setting('wc-currency-symbol');
                        $catalog_id = sb_get_multi_setting('whatsapp-catalog', 'whatsapp-catalog-id');
                        $replace = '[slider';
                        $ids = '';
                        for ($y = 0; $y < count($products); $y++) {
                            $index = $y + 1;
                            $description = $products[$y]['description'];
                            $description = str_replace([PHP_EOL, "\r", "\n"], ' ', sb_rich_value(strlen($description) > 130 ? substr($description, 0, 130) . ' ...' : $description));
                            $replace .= ' image-' . $index . '="' . $products[$y]['image'] . '" header-' . $index . '="' . sb_rich_value($products[$y]['name']) . '"  description-' . $index . '="' . $description . '" link-' . $index . '="' . ($link_type == 'link' ? $products[$y]['url'] : '#') . '" link-text-' . $index . '="' . $link_text . '" extra-' . $index . '="' . $currency_symbol . $products[$y]['price'] . '"';
                            $ids .= $products[$y]['id'] . '|';
                        }
                        $ids = substr($ids, 0, -1);
                        $replace .= (isset($filters) ? ' filters="' . sb_rich_value(implode(' ', $filters)) . '"' : '') . ($catalog_id ? ' whatsapp-catalog-id="' . $catalog_id . '"' : '') . ' product-id="' . $ids . '" settings="link-type:' . $link_type . ',id:' . $ids . '"]';
                    } else {
                        $message = sb_('No results found.');
                    }
                    break;
                case 'payment_methods':
                    $replace = implode(', ', sb_woocommerce_payment_methods());
                    break;
                case 'product_images':
                    if ($product) {
                        $images = sb_woocommerce_get_product_images($product['id']);
                        $count = count($images);
                        if ($count) {
                            $replace = '[slider-images images="';
                            for ($y = 0; $y < count($images); $y++) {
                                $replace .= $images[$y] . ',';
                            }
                            $replace = substr($replace, 0, -1) . '"]';
                        } else {
                            $message = sb_('No results found.');
                        }
                    } else {
                        $message = sb_('No results found.');
                    }
                    break;
                case 'shipping_locations':
                    $replace = sb_woocommerce_shipping_locations()[0];
                    break;
                case 'shipping_location_check':
                    $location = $parameters['sys-geo-country-code'];
                    $replace = (sb_woocommerce_shipping_locations($location['alpha-2']) ? 'Yes, we ship in' : 'No, we don\'t ship in') . ' ' . $location['name'];
                    break;
                case 'html_product_card':
                    $replace = sb_woocommerce_merge_fields_html('product_card', $parameters);
                    break;
                case 'html_products_list':
                    $replace = sb_woocommerce_merge_fields_html('list', $parameters);
                    break;
            }
            $message = str_replace(sb_isset($shortcode, 'shortcode', $merge_field), $replace, $message);
        }
    }
    return $message;
}

function sb_woocommerce_merge_fields_html($name, $parameters = []) {
    $code = '';
    $html = ['table' => '<table cellspacing="0" border="0" cellpadding="0" bgcolor="transparent" style="border:none;border-collapse:separate;border-spacing:0;margin:0;table-layout:fixed">', 'td' => '<td valign="middle" width="50" align="left" style="border:none;padding:0;vertical-align:middle">', 'td2' => 'style="border:none;font-family:Helvetica,Arial,sans-serif;padding:0;vertical-align:middle"', 'a' => ' style="outline:none;text-decoration:none;border:none"', 'a2' => 'style="display:block;color:#222;outline:none;text-decoration:none;border:none;font-weight:600;font-size:12px;letter-spacing:.3px"', 'img' => 'style="border-radius:3px;margin:5px 15px 5px 0;outline:none;text-decoration:none;border:none"', 'text-gray' => 'style="display:block;line-height:25px;color:#666;font-size:14px;"', 'button' => 'style="outline:none;text-decoration:none;border:none;border-radius:3px;background:#404040;color:#FFF;line-height:20px;height:20px;white-space:nowrap;font-size:13px;margin-top:15px;padding:5px 10px;display:inline-block;font-weight:500;"'];
    switch ($name) {
        case 'list':
            $discount = sb_isset($parameters, 'coupon-discount');
            $products = sb_isset($parameters, 'products', sb_isset($parameters, 'items', []));
            if ($products) {
                $currency_symbol = sb_get_setting('wc-currency-symbol');
                $code = $html['table'];
                for ($i = 0; $i < count($products); $i++) {
                    $code .= '<tr>' . $html['td'] . '<a href="' . $products[$i]['url'] . '" ' . $html['a'] . '><img width="50" height="50" src="' . $products[$i]['image'] . '" ' . $html['img'] . '></a></td><td valign="middle" align="left" ' . $html['td2'] . '><a href="' . $products[$i]['url'] . '" ' . $html['a2'] . '>' . $products[$i]['name'] . (isset($products[$i]['quantity']) ? (' X ' . $products[$i]['quantity']) : '') . '</a><span ' . $html['text-gray'] . '>' . ($discount > 0 ? '<span style="text-decoration:line-through;opacity:.8">' : '') . $currency_symbol . round($products[$i]['price'], 2) . ($discount > 0 ? '</span> ' . $currency_symbol . round($products[$i]['price'] * ((100 - $discount) / 100), 2) : '') . '</span></td></tr>';
                }
                $code .= '</table>';
            }
            break;
        case 'product_card':
            $products = sb_isset($parameters, 'products');
            $currency_symbol = sb_get_setting('wc-currency-symbol');
            $description = $products[0]['description'];
            if (strlen($description) > 200) {
                $description = mb_substr($description, 0, 200) . ' ...';
            }
            if ($products) {
                $code = $html['table'];
                $code .= '<tr>' . $html['td'] . '<a href="' . $products[0]['url'] . '" ' . $html['a'] . '><img width="150" height="150" src="' . $products[0]['image'] . '" ' . $html['img'] . '></a></td><td valign="middle" align="left" ' . $html['td2'] . '><a href="' . $products[0]['url'] . '" ' . $html['a2'] . '><span style="font-size:15px">' . $products[0]['name'] . '</span></a><span ' . $html['text-gray'] . '>' . $currency_symbol . $products[0]['price'] . '</span><div style="line-height:20px;color:#666;font-size:12px;margin-top:15px;max-width: 500px;">' . $products[0]['description'] . '</div><a ' . $html['button'] . ' href="' . $products[0]['url'] . '">' . sb_('More details') . '</a></td></tr>';
                $code .= '</table>';
            }
            break;
    }
    return $code;
}

/*
 * ----------------------------------------------------------
 * OPEN AI
 * ----------------------------------------------------------
 *
 * 1. Function calling
 *
 */

function sb_woocommerce_open_ai_function() {
    return [
        [
            'type' => 'function',
            'function' => ['name' => 'sb-woocommerce-checkout-redirect', 'description' => 'Step 3 or last step (checkout process): The user completed all the neccessary steps espically billing and shipping address only then this function is called to redirect the user to the checkout page. For example: "All addresses are confirmed" ', 'parameters' => ['type' => 'object', 'properties' => json_decode('{}'), 'required' => []]]
        ],
        [
            'type' => 'function',
            'function' => ['name' => 'sb-woocommerce-shipping-address', 'description' => 'Step 1 (checkout process): This function is use when the user want to checkout. This will start the address collection flow. For example: "I want to checkout", "Proceed to checkout", "checkout", "i want to checkout now" and this handle shipping address collection and management. For example: "Ship to 123 Main Street", "Use existing shipping address", "New shipping address: 456 Oak Ave".', 'parameters' => ['type' => 'object', 'properties' =>
            [
                'address' => [
                    'type' => 'object',
                    'description' => 'Retrieve only include this field if the user provides a new shipping address from the last two message, if the shipping address contain pipe separator "|" do replace it with a comma. For example, if the user says "Ship to 123 Main Street, NY", then fill in this field. Sample attributes: "address_1", "address_2" or "address", "city", "state", "country", "postal_code".',
                    'properties' => json_decode('{}')
                ],
                'option' => [
                    'type' => 'integer',
                    'description' => 'Shipping address option. Option=1 for initial request, Option=2 for new address, Option=3 for using existing/saved address.',
                ]
            ], 'required' => ['option']]]
        ],
        [
            'type' => 'function',
            'function' => [ 'name' => 'sb-woocommerce-billing-address', 'description' => 'Step 2 (checkout process): Handle billing address for checkout process. For example: select billing address from my account"", "select billing address" ,"different billing address", "same for billing address", "Use the same address for billing", "Use a different address for billing", "Bill to 123 Main Street, NY".', 'parameters' => [ 'type' => 'object', 'properties' => [
                'use_same' => [
                    'type' => 'boolean',
                    'description' => 'Set to true if the user wants to use the same address as shipping for billing, false if the user wants to provide a different billing address.'
                ],
                'billing_address' => [
                    'type' => 'object',
                    'description' => 'Retrieve only include this field if the user or agent provides a new billing address in the current message (not from memory or earlier messages), if the billing address contain pipe separator "|" do replace it with a comma. For example, if the user says "Bill to 123 Main Street, NY", then fill in this field. If the user does NOT mention an address in this message, leave this field empty. Sample attributes when address provided: "address_1", "address_2" or if short just "address" then include "city.',
                    'properties' => json_decode('{}')
                ],
                'shipping_data' => [
                    'type' => 'object',
                    'description' => 'Retrieve the shipping data from the previous function call.',
                    'properties' => json_decode('{}')
                ]
            ], 'required' => ['use_same']]]
        ],
        [
            'type' => 'function',
            'function' => ['name' => 'sb-woocommerce-cart-batch-update', 'description' => 'REQUIRED: Use this function when user mentions TWO OR MORE products in the same or in a last user message only, IMPORTANT! if not in single user message or last user message do not call this function. Examples: "Add 2 Nike shoes and 3 Adidas shirts", "Add urban for 2 unit and item b for 3 unit", "Add product1, product2, and product3" , "change product A to 2 qty and add product B for 5 qty". DO NOT use sb-woocommerce-cart-update for multiple products operation in a single last text or last messages.', 'parameters' => ['type' => 'object', 'properties' =>
            [
                'items' => [
                    'type' => 'array',
                    'description' => 'Array of items to add/remove from cart',
                    'items' => [
                        'type' => 'object',
                        'properties' => [
                            'product_name' => [
                                'type' => 'string',
                                'description' => 'The name of the product'
                            ],
                            'action' => [
                                'type' => 'string',
                                'description' => 'The action to perform',
                                'enum' => ['Add', 'Remove', 'Update']
                            ],
                            'quantity' => [
                                'type' => 'integer',
                                'description' => 'The quantity of items'
                            ],
                            'attributes' => [
                                'type' => 'object',
                                'description' => 'Product attributes (size, color, etc.)',
                                'properties' => json_decode('{}')
                            ]
                        ],
                        'required' => ['product_name', 'action', 'quantity']
                    ]
                ]
            ], 'required' => ['items']]]
        ],
        ['type' => 'function', 'function' => ['name' => 'sb-woocommerce-payment', 'description' => 'Retrieve information about accepted payment methods, Only call this function when the user ask about payment method. For example: "Do you accept PayPal?".', 'parameters' => ['type' => 'object', 'properties' => json_decode('{}'), 'required' => []]]],
        ['type' => 'function', 'function' => ['name' => 'sb-woocommerce-shipment', 'description' => 'Retrieve information about the shipment locations. For example: "Do you ship in Australia?".', 'parameters' => ['type' => 'object', 'properties' => json_decode('{}'), 'required' => []]]],
        ['type' => 'function', 'function' => ['name' => 'sb-woocommerce-cart', 'description' => 'Retrieve information about the user current cart. For example: "Display the items in my cart". IMPORTANT: Do NOT use this function immediately after sb-woocommerce-cart-batch-update - the batch function already provides confirmation of success.', 'parameters' => ['type' => 'object', 'properties' => json_decode('{}'), 'required' => []]]],
        ['type' => 'function', 'function' => ['name' => 'sb-woocommerce-order', 'description' => 'Retrieve information about a previous order. For example: "What is the shipping address of my order?", "What is my last order?".', 'parameters' => ['type' => 'object', 'properties' => json_decode('{}'), 'required' => []]]],
        [
            'type' => 'function',
            'function' => ['name' => 'sb-woocommerce-cart-update', 'description' => 'REQUIRED: Use this function when user mention Add or remove ONE SINGLE item from the cart. Use ONLY when user mentions ONE product. Examples: "Add Nike shoes", "Add 3 Nike shoes", "Remove nike".', 'parameters' => ['type' => 'object', 'properties' =>
            [
                'product_name' => [
                    'type' => 'string',
                    'description' => 'The name of the product the user is asking about, if the product name contain attribute like color and size, please only take the non attribute name.'
                ],
                'action' => [
                    'type' => 'string',
                    'description' => 'The action, It can be adding an item, or removing it, or updating its quantity.',
                    'enum' => ['Add', 'Remove', 'Update']
                ],
                'attributes' => [
                    'type' => 'object',
                    'description' => 'Some product have attributes, The attributes of the product (size, color, material, etc.). This is a dynamic object where keys are attributes names and values are the selected options. For example: {"size": "M", "color": "black"}.',
                    'properties' => json_decode('{}')
                ],
                'quantity' => [
                    'type' => 'integer',
                    'description' => 'The quantity of items to add or remove.'
                ]
            ], 'required' => ['product_name', 'action', 'quantity']]]
        ],
        [
            'type' => 'function',
            'function' => ['name' => 'sb-woocommerce-single', 'description' => 'Retrieve a specific information about a specific product in our store. For example: "What is the price of the PlayStation?", "Do you have the Nike Air Force in XL size?", "Is the urban chic in stock?", "Does timeless classic have stock?".', 'parameters' => ['type' => 'object', 'properties' =>
            [
                'product_name' => [
                    'type' => 'string',
                    'description' => 'The name of the product the user is asking about.'
                ],
                'information' => [
                    'type' => 'string',
                    'description' => 'The product information the user is asking about.',
                    'enum' => array_merge(array_column(sb_woocommerce_get_attributes('attributes'), 'name'), ['Price', 'Photos', 'Rating', 'Stock', 'Availability'])
                ]
            ], 'required' => ['product_name', 'information']]]
        ],
        [
            'type' => 'function',
            'function' => ['name' => 'sb-woocommerce', 'description' => 'Search for products in our store that meet the user\'s criteria. For example: "Do you sell monitors for less than 100 USD?", "I want to see some red t-shirt".', 'parameters' => ['type' => 'object', 'properties' =>
            [
                'product_name' => [
                    'type' => 'string',
                    'description' => 'The name of the product the user is asking about.'
                ],
                'category' => [
                    'type' => 'string',
                    'description' => 'The category of the products.',
                    'enum' => array_column(sb_woocommerce_get_taxonomies('category'), 'name')
                ],
                'tag' => [
                    'type' => 'string',
                    'description' => 'The tags of the products.',
                    'enum' => array_column(sb_woocommerce_get_taxonomies('tag'), 'name')
                ],
                'attribute' => [
                    'type' => 'string',
                    'description' => 'A specific product attribute.',
                    'enum' => array_column(sb_woocommerce_get_attributes('terms'), 'name')
                ],
                'term' => [
                    'type' => 'string',
                    'description' => 'A specific product term.',
                    'enum' => array_column(sb_woocommerce_get_taxonomies('terms'), 'name')
                ],
                'max-price' => [
                    'type' => 'string',
                    'description' => 'A max price.'
                ],
                'min-price' => [
                    'type' => 'string',
                    'description' => 'A minimum price.'
                ],
                'discounted' => [
                    'type' => 'boolean',
                    'description' => 'The user is asking specifically for discounted products.'
                ]
            ], 'required' => []]]
        ],
        [
            'type' => 'function',
            'function' => [ 'name' => 'sb-woocommerce-reset', 'description' => 'Clear or reset all items in the shopping cart. Use when user wants to empty their cart completely. Examples: "Clear my cart", "Reset cart", "Empty my cart", "Remove all items".', 'parameters' => [ 'type' => 'object', 'properties' => [
                'isReset' => [
                    'type' => 'boolean',
                    'description' => 'Set to true to confirm cart reset, false to ask for confirmation first.'
                ]
            ], 'required' => ['isReset']]]
        ],
        [
            'type' => 'function',
            'function' => ['name' => 'sb-woocommerce-current-page', 'description' => 'Retrieve information about the current product or item page. For example: "What is this product?" ', 'parameters' => ['type' => 'object', 'properties' => json_decode('{}'), 'required' => []]]
        ],
        [
            'type' => 'function',
            'function' => ['name' => 'sb-woocommerce-add-current-product', 'description' => 'Add the current page product to cart. For example: "Add this product to cart" or "Add to cart" when on a product page.', 'parameters' => ['type' => 'object', 'properties' =>
            [
                'product_name' => [
                    'type' => 'string',
                    'description' => 'The name of the product the user is asking about, if the product name contain attribute like color and size, please only take the non attribute name.'
                ],
                'action' => [
                    'type' => 'string',
                    'description' => 'The action, It can be adding an item, or removing it, or updating its quantity.',
                    'enum' => ['Add', 'Remove', 'Update']
                ],
                'attributes' => [
                    'type' => 'object',
                    'description' => 'Some product have attributes, The attributes of the product (size, color, material, etc.). This is a dynamic object where keys are attributes names and values are the selected options. For example: {"size": "M", "color": "black"}.',
                    'properties' => json_decode('{}')
                ],
                'quantity' => [
                    'type' => 'integer',
                    'description' => 'The quantity of items to add or remove.'
                ]
            ], 'required' => ['product_name', 'action']]]
        ]
    ];
}

function sb_woocommerce_open_ai_function_calling($function_name, $id, $arguments, $query_tools) {
    // Add logging for sb-woocommerce-single function calls
    // if ($function_name == 'sb-woocommerce-single') {
    //     custom_log("FUNCTION AI CALLING ".$function_name, $arguments);
    // }

    $tag = sb_isset($arguments, 'tag');
    $term = sb_isset($arguments, 'term');
    if ($query_tools) {
        for ($i = 0; $i < count($query_tools); $i++) {
            $query_tools_function = $query_tools[$i]['function'];
            if ($query_tools_function['name'] == $function_name) {
                $properties = $query_tools_function['parameters']['properties'];
                $enum_tag = sb_isset(sb_isset($properties, 'tag'), 'enum');
                $enum_terms = sb_isset(sb_isset($properties, 'term'), 'enum');
                if ($tag && in_array($tag, $enum_terms)) {
                    $term = $tag;
                    $tag = false;
                }
                if ($term && in_array($term, $enum_tag) && !in_array($term, $enum_terms)) {
                    $tag = $term;
                    $term = false;
                }
                break;
            }
        }
    }
    $single_product_information = sb_isset($arguments, 'information');
    $response = sb_woocommerce_open_ai_message(sb_isset($arguments, 'product_name'), sb_isset($arguments, 'category'), $tag, $term, sb_isset($arguments, 'attribute'), sb_isset($arguments, 'max-price'), sb_isset($arguments, 'min-price'), $single_product_information, sb_isset($arguments, 'discounted'));
    return $response ? ($function_name == 'sb-woocommerce' || $single_product_information == 'Photos' ? ['sb-shortcode', $id, $response] : [$function_name, $id, $response]) : [$function_name, $id, []];
}

function sb_woocommerce_open_ai_function_calling_2($function_name, $id, $arguments) {
    custom_log("FUNCTION AI CALLING ".$function_name, $arguments);
    $is_shortcode = true;
    switch ($function_name) {
        case 'sb-woocommerce-order':
            $response = sb_woocommerce_merge_fields('{order_details}');
            break;
        case 'sb-woocommerce-cart':
            $response = sb_woocommerce_merge_fields('{cart}');
            break;
        case 'sb-woocommerce-shipment':
            $response = sb_woocommerce_shipping_locations()[1];
            $is_shortcode = false;
            break;
        case 'sb-woocommerce-payment':
            $response = sb_woocommerce_payment_methods();
            $is_shortcode = false;
            break;
        case 'sb-woocommerce-cart-update':
            $product_id = sb_woocommerce_get_product_id_by_name($arguments['product_name']);
            $action = ucfirst($arguments['action']);
            $quantity = isset($arguments['quantity']) && is_numeric($arguments['quantity']) ? intval($arguments['quantity']) : 1;

            // Extract all variation parameters
            $attributes = [];
            if (isset($arguments['attributes']) && is_array($arguments['attributes'])) {
                $attributes = $arguments['attributes'];
            } else {
                foreach ($arguments as $key => $value) {
                    if (!in_array($key, ['product_name', 'action', 'quantity']) && !empty($value)) {
                        $attributes[$key] = $value;
                    }
                }
            }

            $message = false;
            if ($product_id) {
                $custom_detail = custom_get_product($product_id);
                if ($action == 'Add') {
                    if (!sb_woocommerce_is_in_stock($product_id)) {
                        $message = 'Sorry, the product is out of stock.';
                    }
                    if ($custom_detail['has_variations']) {
                        $variation_attr = $custom_detail['variation_attributes'];
                        if (empty($attributes)) {
                            $message = 'Sorry, the '.$custom_detail['name'].' has variations. Do not show the list variation available, just show what type of option missing';
                        } else {
                            $variation_valid = true;
                            $missing_attrs = [];
                            $mismatch_attrs = [];

                            foreach ($variation_attr as $attr_key => $attr_values) {
                                $attr_name = str_replace('pa_', '', $attr_key);
                                $attr_found = false;

                                foreach ($attributes as $var_key => $var_value) {
                                    if (strtolower($var_key) === strtolower($attr_name)) {
                                        $attr_found = true;
                                        $valid_values = array_map('strtolower', $attr_values);
                                        if (!in_array(strtolower($var_value), $valid_values)) {
                                            $mismatch_attrs[$attr_name] = $attr_values;
                                            $variation_valid = false;
                                        }
                                        break;
                                    }
                                }

                                if (!$attr_found) {
                                    $missing_attrs[] = $attr_name;
                                }
                            }

                            if (!empty($missing_attrs)) {
                                $message = 'Sorry, Missing required options: ' . implode(', ', $missing_attrs) . '. Please specify these attributes.';
                                $variation_valid = false;
                            } elseif (!empty($mismatch_attrs)) {
                                $message = 'Sorry, Incorrect options. Available options: ' . json_encode($mismatch_attrs) . '.';
                                $variation_valid = false;
                            }
                        }
                    }
                } else if ($action == 'Remove') {
                    $is_in_cart = false;
                    $session_key = sb_woocommerce_get_session_key(sb_isset(sb_get_active_user(), 'id', -1));
                    if ($session_key !== false) {
                        $session = sb_woocommerce_get_session($session_key);
                        if ($session && sb_isset($session, 'cart')) {
                            foreach ($session['cart'] as $value) {
                                if (sb_isset($value, 'product_id') == $product_id) {
                                    $is_in_cart = true;
                                    break;
                                }
                            }
                        }
                    }
                    if (!$is_in_cart) {
                        $message = 'The product is not in the cart.';
                    }
                } else if ($action == 'Update') {
                    if ($custom_detail['has_variations']) {
                        $variation_attr = $custom_detail['variation_attributes'];
                        $keys = array_map(function($key) {
                            if (is_string($key) && strpos($key, 'pa_') === 0) {
                                return substr($key, 3);
                            }
                            return $key;
                        }, array_keys($variation_attr));
                        if (empty($attributes) || !empty(array_diff($keys, array_keys($attributes)))) {
                            $message = 'Sorry, the product variation not provided in details. the user need product variation details like '.json_encode($keys).' to proceed update and prevent affecting other similar product.';
                        }
                    }
                    $is_in_cart = false;
                    $session_key = sb_woocommerce_get_session_key(sb_isset(sb_get_active_user(), 'id', -1));
                    if ($session_key !== false) {
                        $session = sb_woocommerce_get_session($session_key);
                        if ($session && sb_isset($session, 'cart')) {
                            foreach ($session['cart'] as $value) {
                                if (sb_isset($value, 'product_id') == $product_id) {
                                    $is_in_cart = true;
                                    break;
                                }
                            }
                        }
                    }
                    if (!$is_in_cart) {
                        $message = 'The product is not in the cart.';
                    }
                }
            } else {
                $message = 'The product was not found.';
            }
            $cart_action = '';
            if ($action == 'Add') $cart_action = 'cart-add';
            else if ($action == 'Remove') $cart_action = 'cart-remove';
            else if ($action == 'Update') $cart_action = 'cart-update';

            return [
                'payload',
                $id,
                $message ? $message : $arguments['product_name'] . ' has been processed.',
                $message ? [] : ['event' => 'woocommerce-update-cart', 'action' => $cart_action, 'id' => $product_id, 'quantity' => $quantity, 'attributes' => $attributes]
            ];

        case 'sb-woocommerce-checkout-redirect':
            return [
                'payload',
                $id,
                'Great! Let\'s start the checkout process.',
                ['event' => 'woocommerce-checkout']
            ];

        case 'sb-woocommerce-shipping-address':
            $message = false;
            $option = isset($arguments['option']) ? $arguments['option'] : 1;
            $found = false;
            $isExist = false;
            $wp_user = false;
            $useAddress = $option;
            $user = sb_get_active_user();
            $final = [];
            $address = isset($arguments['address']) ? $arguments['address'] : [];
            $addressSlugs = ['address_1', 'address_2', 'address', 'city', 'state', 'country', 'postal_code'];
            $addressDetails = [];
            $user_details = sb_get_user($user['id'], true);
            $extra_response = '';
            custom_log("The Addreses are: ", $address);

            if ($user_details['user_type'] === 'user') {
                // Extract address details from user profile
                foreach ($user_details['details'] as $detail) {
                    if (!empty($detail['slug']) && in_array($detail['slug'], $addressSlugs)) {
                        $addressDetails[$detail['slug']] = $detail['value'];
                        $found = true;
                    }
                    if ($detail['slug'] === 'wp-id') {
                        $wp_user = $detail['value'];
                    }
                }
                // Handle WooCommerce user with multiple addresses
                if ($wp_user) {
                    [$is_shortcode, $response, $isExist] = sb_wp_handle_address_chips('shipping_addresses', $user['id'], $wp_user);
                    if (empty($address)) {
                        // If chips were returned, use sb-shortcode return
                        if ($is_shortcode && $response) {
                            custom_log("Returning sb-shortcode for shipping address chips: ", $response);
                            return [
                                'sb-shortcode',
                                $id,
                                $response
                            ];
                        }
                        // If no multiple addresses found, fall through to standard logic
                    } elseif($isExist && !empty($address)) {
                        custom_log("inside not empty shipping address", null);
                        // User provided new address
                        setcookie("sb_temp_address", json_encode($address), time() + 3600);
                        $final = ['shipping' => $address];
                    }
                }

                // Handle standard user logic (non-WooCommerce or no multiple addresses)
                if ($useAddress === 1) {
                    if ($found) {
                        $message = 'I found your saved shipping address: ' . json_encode($addressDetails) . '. Would you like to use this address (respond "Use existing shipping address") or provide a new one (respond "New shipping address:" followed by the details)?';
                    } elseif (!$found && empty($address)) {
                        $message = 'No saved shipping address found. Please provide your shipping address with the prefix "shipping address:" followed by the details. Make sure prompt the prefix "shipping address:", for user to understand.';
                    } elseif (!$found && !empty($address)) {
                        $useAddress = 2;
                        $found = true;
                    }
                }

                // Process address based on user choice
                if ($found && !empty($address) && $useAddress === 2) {
                    foreach ($addressSlugs as $slug) {
                        if (!empty($arguments['address'][$slug])) {
                            $addressDetails[$slug] = $arguments['address'][$slug];
                        }
                    }
                    $final = ['shipping' => $addressDetails];
                    setcookie("sb_temp_address", json_encode($final), time() + 3600);
                } elseif ($found && empty($address) && $useAddress === 3) {
                    $final = ['shipping' => $addressDetails];
                    setcookie("sb_temp_address", json_encode($final), time() + 3600);
                } elseif ($found && empty($address) && $useAddress === 2) {
                    $message = 'Please provide your shipping address with the prefix "shipping address:" followed by the address details.  Make sure prompt the prefix "shipping address:", for user to understand.';
                } elseif (!$found && empty($address) && $useAddress === 2) {
                    $message = 'No saved shipping address found. Please provide your shipping address with the prefix "shipping address:" followed by the address details.  Make sure prompt the prefix "shipping address:", for user to understand.';
                }
            } else {
                // Handle visitor (non-registered user)
                if ($useAddress !== 2) {
                    if (!$found && empty($address)) {
                        $message = 'Please provide your shipping address with the prefix "shipping address:" followed by the address details.  Make sure prompt the prefix "shipping address:", for user to understand.';
                    } elseif (!$found && !empty($address)) {
                        $useAddress = 2;
                    }
                }

                // Set cookie for visitor with address data
                if (!$found && !empty($address) && $useAddress === 2) {
                    foreach ($addressSlugs as $slug) {
                        if (!empty($arguments['address'][$slug])) {
                            $addressDetails[$slug] = $arguments['address'][$slug];
                        }
                    }
                    $final = ['shipping' => $addressDetails];
                    setcookie("sb_temp_address", json_encode($final), time() + 3600);
                }
            }

            if($isExist) {
                $extra_response = 'or select the billing addresses from my account';
            }

            return [
                'payload',
                $id,
                $message ? $message : 'Shipping address confirmed: ' . json_encode($final['shipping']) . '. Next step, call function sb-woocommerce-billing-address. prompt user whether to use the same address for billing or new address ' . $extra_response . '. Never ask for payment options.',
                $message ? [] : ['event' => 'woocommerce-shipping-prompt', $final]
            ];

        case 'sb-woocommerce-billing-address':
            $use_same_address = isset($arguments['use_same']) ? $arguments['use_same'] : false;
            $billing_address = isset($arguments['billing_address']) ? $arguments['billing_address'] : [];
            $message = false;
            $wp_user = false;
            $isExist = false;
            $user = sb_get_active_user();
            $user_details = sb_get_user($user['id'], true);
            
            // Get shipping data from database or arguments
            $shipping_data = isset($arguments['shipping_data']) ? $arguments['shipping_data'] : [];
            if (empty($shipping_data)) {
                if (isset($_COOKIE['sb_temp_address'])) {
                    $temp_shipping = urldecode($_COOKIE['sb_temp_address']);
                    if ($temp_shipping) {
                        $shipping_data = json_decode($temp_shipping, true);
                    }
                }
            }

            // Check if user has WordPress ID for multiple address support
            foreach ($user_details['details'] as $detail) {
                if ($detail['slug'] === 'wp-id') {
                    $wp_user = $detail['value'];
                    break;
                }
            }

            $billing_final = [];

            if ($use_same_address) {
                // Use shipping address as billing address
                $billing_final = !empty($shipping_data) ? $shipping_data : $billing_address;
            } else {
                // Check if user has multiple billing addresses
                if ($wp_user) {
                    [$is_shortcode, $response, $isExist] = sb_wp_handle_address_chips('billing_addresses', $user['id'], $wp_user);
                    if (empty($billing_address)) {

                        // If chips were returned, use sb-shortcode return
                        if ($is_shortcode && $response) {
                            custom_log("Returning sb-shortcode for billing address chips: ", $response);
                            return [
                                'sb-shortcode',
                                $id,
                                $response
                            ];
                        }
                        // If no multiple addresses found, fall through to standard logic
                    } elseif($isExist && !empty($billing_address)) {
                        custom_log("inside not empty billing_addresses address", null);
                        // User provided new address
                        setcookie("sb_temp_address", json_encode($billing_address), time() + 3600);
                        $final = ['billing' => $billing_address];
                    }
                }

                // Handle different billing address (fallback or manual entry)
                $billing_addressSlugs = ['address_1', 'address_2', 'address', 'city', 'state', 'country', 'postal_code'];
                $billing_details = [];
                $found = false;

                // Check existing billing address
                foreach ($user_details['details'] as $detail) {
                    if (in_array($detail['slug'], $billing_addressSlugs)) {
                        $billing_details[$detail['slug']] = $detail['value'];
                        $found = true;
                    }
                }

                if (!empty($billing_address)) {
                    // Process new billing address
                    foreach ($billing_addressSlugs as $slug) {
                        if (!empty($billing_address[$slug])) {
                            $billing_details[$slug] = $billing_address[$slug];
                        }
                    }
                    $billing_final = ['billing' => $billing_details];
                } elseif ($found) {
                    $billing_final = ['billing' => $billing_details];
                } else {
                    $message = 'Please provide your billing address with the prefix "billing address:" followed by the address details. Make sure prompt the prefix "billing address:", for user to understand.';
                }
            }

            return [
                'payload',
                $id,
                $message ? $message : 'Billing address confirmed: '. json_encode($billing_final['billing']) .'. All addresses are confirmed. Do not ask for payment options, only ask if user wish to proceed with the current setup.',
                $message ? [] : ['event' => 'woocommerce-billing-prompt', $billing_final]
            ];

        // ... rest of the cases remain the same
        case 'sb-woocommerce-cart-batch-update':
            // custom_log("Last message in conversation: " , $data);
            // Check for existing contact form submission in this conversation
            // $existing_submission = sb_check_contact_form_submission($conversation_id);
        
            $items = isset($arguments['items']) ? $arguments['items'] : [];
            $results = [];
            $errors = [];
            $events = [];

            foreach ($items as $index => $item) {
                $product_name = isset($item['product_name']) ? $item['product_name'] : '';
                $action = isset($item['action']) ? ucfirst($item['action']) : 'Add';
                $quantity = isset($item['quantity']) && is_numeric($item['quantity']) ? intval($item['quantity']) : 1;
                $attributes = isset($item['attributes']) ? $item['attributes'] : [];

                if (empty($product_name)) {
                    $errors[] = 'Product name is required.';
                    continue;
                }

                $product_id = sb_woocommerce_get_product_id_by_name($product_name);

                if ($product_id) {
                    $custom_detail = custom_get_product($product_id);
                    $message = false;

                    if ($action == 'Add') {
                        if (!sb_woocommerce_is_in_stock($product_id)) {
                            $errors[] = $product_name . ' is out of stock.';
                            custom_log("Stock check failed for product $product_id", []);
                            continue;
                        }

                        if (!empty($custom_detail['has_variations']) && !empty($attributes)) {
                            $variation_valid = true;
                            $missing_attrs = [];

                            foreach ($custom_detail['variation_attributes'] as $attr_key => $attr_values) {
                                $attr_name = str_replace('pa_', '', $attr_key);
                                $attr_found = false;

                                foreach ($attributes as $var_key => $var_value) {
                                    if (strtolower($var_key) === strtolower($attr_name)) {
                                        $attr_found = true;
                                        $valid_values = array_map('strtolower', $attr_values);
                                        if (!in_array(strtolower($var_value), $valid_values)) {
                                            $errors[] = $product_name . ': Invalid ' . $attr_name . ' selection. Available: ' . implode(', ', $attr_values);
                                            $variation_valid = false;
                                        }
                                        break;
                                    }
                                }

                                if (!$attr_found) {
                                    $missing_attrs[] = $attr_name;
                                }
                            }

                            if (!empty($missing_attrs)) {
                                $errors[] = $product_name . ': Please specify ' . implode(', ', $missing_attrs);
                                $variation_valid = false;
                            }

                            if (!$variation_valid) {
                                continue;
                            }
                        }
                    }

                    if (!$message) {
                        $cart_action = '';
                        if ($action == 'Add') $cart_action = 'cart-add';
                        else if ($action == 'Remove') $cart_action = 'cart-remove';
                        else if ($action == 'Update') $cart_action = 'cart-update';

                        $results[] = $product_name . ' (' . $quantity . ' qty)';
                        $events[] = [
                            'event' => 'woocommerce-update-cart',
                            'action' => $cart_action,
                            'id' => $product_id,
                            'quantity' => $quantity,
                            'attributes' => $attributes,
                            'product_name' => $product_name,
                            'event_index' => count($events),
                            'original_index' => $index
                        ];
                    } else {
                        $errors[] = $product_name . ': ' . $message;
                    }
                } else {
                    $errors[] = $product_name . ' was not found.';
                }
            }

            $response_message = '';
            if (!empty($results)) {
                $response_message .= 'processing: ' . implode(', ', $results) . '.';
            }
            if (!empty($errors)) {
                $response_message .= (!empty($results) ? ' ' : '') . 'Errors: ' . implode(', ', $errors) . '.';
            }
            if (empty($results) && empty($errors)) {
                $response_message = 'No items were processed. Please check your product names and try again.';
            }

            return [
                'payload',
                $id,
                $response_message,
                ['event' => 'woocommerce-batch-update-cart', 'events' => $events]
            ];

        case 'sb-woocommerce-reset':
            $message = false;
            $isReset = isset($arguments['isReset']) ? $arguments['isReset'] : false;

            if(!$isReset) {
                $message = 'Are you sure you want to clear all items from your cart? This action cannot be undone.';
            }

            return [
                'payload',
                $id,
                $message ? $message : 'Proceeding to clear all item in cart.',
                $message ? [] : ['event' => 'woocommerce-reset']
            ];

        case 'sb-woocommerce-current-page':
            $user = sb_get_active_user();
            $current_url = sb_current_url($user['id']);
            $product_info = sb_woocommerce_detect_current_product($current_url);

            if ($product_info) {
                $response = sb_woocommerce_format_current_product_info($product_info);
                $is_shortcode = true;
            } else {
                $response = 'I cannot detect a product on the current page. Please make sure you are on a product page and try again.';
                $is_shortcode = false;
            }
            break;

        case 'sb-woocommerce-add-current-product':
            $user = sb_get_active_user();
            $current_url = sb_current_url($user['id']);
            $quantity = isset($arguments['quantity']) && is_numeric($arguments['quantity']) ? intval($arguments['quantity']) : 1;
            $product_info = sb_woocommerce_detect_current_product($current_url);

            if ($product_info && isset($product_info['name'])) {
                $product_id = sb_woocommerce_get_product_id_by_name($product_info['name']);
                $message = false;

                if ($product_id) {
                    if (!sb_woocommerce_is_in_stock($product_id)) {
                        $message = 'Sorry, this product is currently out of stock.';
                    }
                } else {
                    $message = 'Product not found in our system.';
                }

                return [
                    'payload',
                    $id,
                    $message ? $message : $product_info['name'] . ' processing into the cart. Do not comfirm addition, the next action will confirm.',
                    $message ? [] : ['event' => 'woocommerce-update-cart', 'action' => 'cart-add', 'id' => $product_id, 'quantity' => $quantity, 'attributes' => []]
                ];
            } else {
                $response = 'I cannot detect a product on the current page. Please make sure you are on a product page and try again.';
                $is_shortcode = false;
            }
            break;
    }
    
    return [$is_shortcode ? 'sb-shortcode' : $function_name, $id, $response];
}

function sb_woocommerce_open_ai_message($title = false, $category = false, $tag = false, $term = false, $attribute = false, $max_price = false, $min_price = false, $single_product_information = false, $discounted = false) {
    $products = $title && !$category && !$tag && !$attribute && !$discounted ? sb_woocommerce_search_products($title) : sb_woocommerce_get_products(['title' => $title, 'attribute' => $attribute, 'taxonomy' => $tag ? $tag : ($category ? $category : $term), 'max-price' => $max_price, 'min-price' => $min_price, 'discounted' => $discounted]);

    if (!empty($products)) {
        if ($single_product_information) {
            // Improved product selection logic for single product information requests
            $product = null;

            if ($title) {
                // First, try to find exact match (case insensitive)
                $title_lower = strtolower($title);
                foreach ($products as $p) {
                    if (strtolower($p['name']) === $title_lower) {
                        $product = sb_woocommerce_get_product($p['id']);
                        break;
                    }
                }

                // If no exact match, find the best partial match (most relevant)
                if (!$product) {
                    $best_match = null;
                    $best_score = 0;

                    foreach ($products as $p) {
                        $name_lower = strtolower($p['name']);
                        // Calculate relevance score based on how well the search term matches
                        $score = 0;

                        // Higher score for products that start with the search term
                        if (strpos($name_lower, $title_lower) === 0) {
                            $score += 100;
                        }

                        // Score based on how much of the product name matches the search term
                        $words_in_search = explode(' ', $title_lower);
                        $words_in_name = explode(' ', $name_lower);
                        $matching_words = 0;

                        foreach ($words_in_search as $search_word) {
                            foreach ($words_in_name as $name_word) {
                                if (strpos($name_word, $search_word) !== false) {
                                    $matching_words++;
                                    break;
                                }
                            }
                        }

                        $score += ($matching_words / count($words_in_search)) * 50;

                        // Prefer shorter names (more specific matches)
                        $score += (100 - strlen($name_lower)) / 10;

                        if ($score > $best_score) {
                            $best_score = $score;
                            $best_match = $p;
                        }
                    }

                    if ($best_match) {
                        $product = sb_woocommerce_get_product($best_match['id']);
                    }
                }
            }

            // Fallback to original logic if no product found
            if (!$product) {
                $ids = array_column($products, 'id');
                sort($ids);
                $product = sb_woocommerce_get_product($ids[0]);
            }
            if ($single_product_information == 'Photos') {
                $detail = sb_woocommerce_get_product_images($product['id']);
            } elseif ($single_product_information == 'Stock' || $single_product_information == 'Availability') {
                // Handle stock/availability queries - return JSON data
                $product_id = $product['id'];

                // Check if product has variations
                $safe_product_id = sb_db_escape($product_id, true);
                $has_variations = sb_db_get('SELECT COUNT(*) as count FROM ' . SB_WP_PREFIX . 'posts WHERE post_parent = ' . $safe_product_id . ' AND post_type = "product_variation" AND post_status = "publish"');
                $variation_count = $has_variations ? $has_variations['count'] : 0;

                if ($variation_count > 0) {
                    // Variable product - get variation details with stock using your suggested query structure
                    $variations_data = sb_db_get('
                        SELECT
                            p.ID AS product_id,
                            p.post_title AS product_name,
                            v.ID AS variation_id,
                            v.post_title AS variation_name,
                            stock_meta.meta_value AS stock_quantity,
                            size_meta.meta_value AS size,
                            color_meta.meta_value AS color,
                            status_meta.meta_value AS stock_status
                        FROM ' . SB_WP_PREFIX . 'posts p
                        JOIN ' . SB_WP_PREFIX . 'posts v ON v.post_parent = p.ID AND v.post_type = "product_variation"
                        LEFT JOIN ' . SB_WP_PREFIX . 'postmeta stock_meta ON stock_meta.post_id = v.ID AND stock_meta.meta_key = "_stock"
                        LEFT JOIN ' . SB_WP_PREFIX . 'postmeta size_meta ON size_meta.post_id = v.ID AND size_meta.meta_key = "attribute_pa_size"
                        LEFT JOIN ' . SB_WP_PREFIX . 'postmeta color_meta ON color_meta.post_id = v.ID AND color_meta.meta_key = "attribute_pa_color"
                        LEFT JOIN ' . SB_WP_PREFIX . 'postmeta status_meta ON status_meta.post_id = v.ID AND status_meta.meta_key = "_stock_status"
                        WHERE p.ID = ' . $safe_product_id . ' AND v.post_status = "publish"
                    ', false);

                    $detail = [
                        'product_name' => $product['name'],
                        'product_id' => $product_id,
                        'has_variations' => true,
                        'variation_count' => $variation_count,
                        'variations' => []
                    ];

                    foreach ($variations_data as $var) {
                        $stock_qty = intval($var['stock_quantity'] ?: 0);
                        $is_in_stock = ($var['stock_status'] !== 'outofstock') && ($stock_qty > 0);

                        $detail['variations'][] = [
                            'variation_id' => $var['variation_id'],
                            'variation_name' => $var['variation_name'],
                            'size' => $var['size'] ?: '',
                            'color' => $var['color'] ?: '',
                            'stock_quantity' => $stock_qty,
                            'stock_status' => $var['stock_status'] ?: 'instock',
                            'in_stock' => $is_in_stock
                        ];
                    }

                } else {
                    // Simple product
                    $in_stock = sb_woocommerce_is_in_stock($product_id);
                    $detail = [
                        'product_name' => $product['name'],
                        'product_id' => $product_id,
                        'in_stock' => $in_stock,
                        'stock_status' => $in_stock ? 'In Stock' : 'Out of Stock',
                        'has_variations' => false
                    ];
                }

                // Convert to JSON string for better AI processing
                $detail = json_encode($detail, JSON_PRETTY_PRINT);
            } else {
                $detail = sb_isset($product, strtolower($single_product_information));
                if ($detail && $single_product_information == 'Rating') {
                    $detail_ = [];
                    foreach ($detail as $key => $value) {
                        array_push($detail_, $value . ' users rated it ' . $key . ' out of 5');
                    }
                    $detail = $detail_;
                }
            }
            return $detail ? $detail : $product;
        }
        return $products;
    }
    return false;
}

function sb_woocommerce_open_ai_check_function_name($function_name, $index = 1) {
    $functions = $index == 1 ? ['sb-woocommerce', 'sb-woocommerce-single'] : ['sb-woocommerce-order', 'sb-woocommerce-cart', 'sb-woocommerce-shipment', 'sb-woocommerce-payment', 'sb-woocommerce-cart-update', 'sb-woocommerce-cart-batch-update', 'sb-woocommerce-checkout-redirect', 'sb-woocommerce-shipping-address', 'sb-woocommerce-billing-address' , 'sb-woocommerce-reset', 'sb-woocommerce-current-page', 'sb-woocommerce-add-current-product'];
    return in_array($function_name, $functions);
}

/*
 * ----------------------------------------------------------
 * MORE FUNCTIONS
 * ----------------------------------------------------------
 *
 * 1. Popup of the admin area products list
 * 2. Return the average rating of a product
 * 3. Return the url of category, tag, shop
 * 4. Return the user saved by WooCommerce
 * 5. Return the session object containing cart and customer details
 * 6. Return the session key of the user cookie
 * 7. Save the session key of the user cookie
 * 8. Return the active payment methods
 * 9. Return the shipment countries
 * 10. Assign orders to another Support Board user
 *
 */

function sb_woocommerce_products_popup() {
    echo '<div class="sb-popup sb-woocommerce-products"><div class="sb-header"><div class="sb-select"><p data-value="">' . sb_('All') . '</p><ul class="sb-scroll-area"></ul></div><div class="sb-search-btn"><i class="sb-icon sb-icon-search"></i><input type="text" placeholder="' . sb_('Search ...') . '" /></div></div><div class="sb-woocommerce-products-list sb-list-thumbs sb-scroll-area"><ul class="sb-loading"></ul></div><i class="sb-icon-close sb-popup-close"></i></div>';
}

function sb_woocommerce_rating($ratings) {
    $total = 0;
    $count = 0;
    if (empty($ratings))
        return false;
    foreach ($ratings as $key => $value) {
        $count += intval($value);
        $total += intval($key) * intval($value);
    }
    return round($total / $count, 2);
}

function sb_woocommerce_get_url($type, $name = '', $language = '') {
    $site_url = sb_wp_site_url();
    $url_slug = '';
    $url_parameter = '';
    $multilingual_plugin = '';
    $language_settings = [];
    if (is_array($name))
        $name = $name[0];
    if ($language != '') {
        $multilingual_plugin = sb_get_setting('wp-multilingual-plugin');
        if ($multilingual_plugin != '') {
            $language_settings = sb_wp_language_settings();
            if (in_array($language, $language_settings['languages']) && $language != $language_settings['default']) {
                $url_slug = $language_settings['link-type'] == 1 ? '/' . $language : '';
                $url_parameter = $language_settings['link-type'] == 1 ? '' : '?lang=' . $language;
            }
        }
    }
    switch ($type) {
        case 'tag':
        case 'category':
            $link = sb_db_get('SELECT slug FROM ' . SB_WP_PREFIX . 'terms WHERE name = "' . sb_db_escape($name) . '" LIMIT 1');
            if (sb_isset($link, 'slug')) {
                return $site_url . $url_slug . ($type == 'category' ? '/product-category/' : '/product-tag/') . $link['slug'] . $url_parameter;
            }
            break;
        case 'cart':
        case 'shop':
            $url = sb_get_multi_setting('wc-urls', 'wc-urls-' . $type);
            if ($url)
                return $url;
            $id = sb_db_get('SELECT option_value FROM ' . SB_WP_PREFIX . 'options WHERE option_name = "' . ($type == 'shop' ? 'woocommerce_shop_page_id' : 'woocommerce_cart_page_id') . '"');
            if (sb_isset($id, 'option_value')) {
                $id = $id['option_value'];
                if ($language && $language != sb_isset($language_settings, 'default')) {
                    $id = sb_wp_language_get_page_id($id, $language, $multilingual_plugin);
                }
                return $site_url . $url_slug . ($url_parameter ? $url_parameter . '&p=' : '?p=') . $id;
            }
            break;
        case 'checkout':
            $url = sb_get_multi_setting('wc-urls', 'wc-urls-checkout');
            return $url ? $url : wc_get_checkout_url();
    }
    return '';
}

function sb_woocommerce_get_session($session_key = false) {
    if (empty($session_key)) {
        $session_key = sb_woocommerce_get_session_key();
    }
    $session = sb_db_get('SELECT session_value FROM ' . SB_WP_PREFIX . 'woocommerce_sessions WHERE session_key = "' . $session_key . '"');
    if (sb_isset($session, 'session_value')) {
        $session = unserialize($session['session_value']);
        $session['cart'] = isset($session['cart']) ? unserialize($session['cart']) : [];
        $session['customer'] = isset($session['customer']) ? unserialize($session['customer']) : [];
        $session['cart_totals'] = isset($session['cart_totals']) ? unserialize($session['cart_totals']) : [];
        return $session;
    }
    return false;
}

function sb_woocommerce_get_session_key($user_id = false) {
    if ($user_id !== false) {
        return sb_isset(sb_db_get('SELECT value FROM sb_users_data WHERE slug = "woocommerce_session_key" AND user_id = ' . $user_id), 'value');
    } else if (!empty($_COOKIE)) {
        foreach ($_COOKIE as $key => $value) {
            if (strpos($key, 'wp_woocommerce_session_') !== false) {
                return explode('||', urldecode($value))[0];
            }
        }
    }
    return false;
}

function sb_woocommerce_save_session_key($session_key = false) {
    if ($session_key === false) {
        $session_key = sb_woocommerce_get_session_key();
    }
    if (!empty($session_key)) {
        $active_user = sb_get_active_user();
        if ($active_user && !sb_is_agent($active_user) && is_string($session_key) && $session_key != sb_woocommerce_get_session_key($active_user['id'])) {
            return sb_update_user_value($active_user['id'], 'woocommerce_session_key', $session_key, 'Woo Session Key');
        }
    }
    return false;
}

function sb_woocommerce_payment_methods() {
    $active_methods = [];
    $all_methods = sb_db_get('SELECT option_value FROM ' . SB_WP_PREFIX . 'options WHERE option_name = "woocommerce_gateway_order"');
    if (sb_isset($all_methods, 'option_value')) {
        $all_methods = unserialize($all_methods['option_value']);
        $query = '';
        foreach ($all_methods as $key => $value) {
            $query .= 'option_name = "woocommerce_' . $key . '_settings" OR ';
        }
        $methods = sb_db_get('SELECT option_name, option_value FROM ' . SB_WP_PREFIX . 'options WHERE ' . substr($query, 0, -4), false);
        for ($i = 0; $i < count($methods); $i++) {
            $method = unserialize($methods[$i]['option_value']);
            if (sb_isset($method, 'enabled') == 'yes') {
                $name = sb_isset($method, 'title');
                if (!$name) {
                    $name = str_replace(['woocommerce_', '_settings'], '', $methods[$i]['option_name']);
                    switch ($name) {
                        case 'bacs':
                            $name = 'Direct bank transfer';
                            break;
                        case 'cod':
                            $name = 'Cash on delivery';
                            break;
                        default:
                            $name = strtoupper($name);
                    }
                }
                array_push($active_methods, sb_($name));
            }
        }
    }
    return $active_methods;
}

function sb_woocommerce_shipping_locations($country_code = false) {
    $settings = sb_db_get('SELECT option_name, option_value FROM ' . SB_WP_PREFIX . 'options WHERE (option_name = "woocommerce_allowed_countries" OR option_name = "woocommerce_all_except_countries" OR option_name = "woocommerce_specific_allowed_countries" OR option_name = "woocommerce_ship_to_countries" OR option_name = "woocommerce_specific_ship_to_countries") AND option_value <> "" AND option_value <> "a:0:{}"', false);
    $sell_rule = '';
    $ship_rule = '';
    $countries_allowed = [];
    $countries_ship_allowed = [];
    $countries_excluded = [];
    for ($i = 0; $i < count($settings); $i++) {
        $name = $settings[$i]['option_name'];
        $value = $settings[$i]['option_value'];
        switch ($name) {
            case 'woocommerce_allowed_countries':
                $sell_rule = $value;
                break;
            case 'woocommerce_ship_to_countries':
                $ship_rule = $value;
                break;
            case 'woocommerce_specific_allowed_countries':
                $countries_allowed = unserialize($value);
                break;
            case 'woocommerce_specific_ship_to_countries':
                $countries_ship_allowed = unserialize($value);
                break;
            case 'woocommerce_all_except_countries':
                $countries_excluded = unserialize($value);
                break;
        }
    }
    if ($ship_rule == 'all' || ($sell_rule == 'all' && !$ship_rule)) {
        return $country_code ? true : [sb_('all countries'), 'all', true];
    } else {
        $countries = $ship_rule == 'specific' ? $countries_ship_allowed : ($sell_rule == 'all_except' ? $countries_excluded : $countries_allowed);
        $countries_string = '';
        $exclude = $sell_rule == 'all_except' && $ship_rule != 'specific';
        $country_code = strtoupper($country_code);
        if ($country_code) {
            $included = false;
            for ($i = 0; $i < count($countries); $i++) {
                if ($countries[$i] == $country_code) {
                    $included = true;
                    break;
                }
            }
            return (!$exclude && $included) || ($exclude && !$included);
        }
        $country_names = sb_get_json_resource('json/country_codes.json');
        for ($i = 0; $i < count($countries); $i++) {
            $countries[$i] = [sb_(sb_isset($country_names, $countries[$i], $countries[$i])), $countries[$i]];
            $countries_string .= sb_($countries[$i][0]) . ', ';
        }
        return [($exclude ? sb_('all countries except') . ' ' : '') . substr($countries_string, 0, -2), $countries, $exclude];
    }
}

function sb_woocommerce_assign_orders($user_id, $old_user_id) {
    return sb_db_query('UPDATE ' . SB_WP_PREFIX . 'postmeta SET meta_value = ' . sb_db_escape($user_id) . ' WHERE meta_key = "sb-user" AND meta_value = ' . sb_db_escape($old_user_id));
}

/*
 * ----------------------------------------------------------
 * ACTIONS
 * ----------------------------------------------------------
 *
 * WordPress actions and hooks.
 *
 */

function sb_woocommerce_actions() {
    add_action('woocommerce_order_status_changed', 'sb_woocommerce_order_status_completed', 10, 1);
    add_action('woocommerce_order_status_processing', 'sb_woocommerce_order_status_completed', 10, 1);
    add_action('woocommerce_order_status_pending', 'sb_woocommerce_order_status_completed', 10, 1);
    add_action('woocommerce_order_status_completed', 'sb_woocommerce_order_status_completed', 10, 1);
    add_action('woocommerce_new_order', 'sb_woocommerce_order_status_completed', 10, 1);
    add_action('woocommerce_add_to_cart_validation', 'sb_woocommerce_on_cart_add', 99, 1);
    add_action('woocommerce_add_cart_item', 'sb_woocommerce_save_session_key_action', 99, 1);
    add_action('woocommerce_update_product', 'sb_woocommerce_product_updated', 10, 1);
    add_action('wp_enqueue_scripts', 'register_autofill_checkout_script');
    add_action('delete_user', 'delete_sb_user');
    if (sb_get_multi_setting('wc-product-removed', 'wc-product-removed-active')) {
        add_action('woocommerce_remove_cart_item', 'sb_woocommerce_product_removed', 10, 2);
    }
}

function sb_woocommerce_order_status_completed($order_id) {
    $active_user_id = false;
    $active_user = sb_get_active_user();
    $is_follow_up = sb_get_multi_setting('wc-follow-up', 'wc-follow-up-active');
    $is_social_share = sb_get_multi_setting('wc-share', 'wc-share-active');
    $is_update_user = !$active_user || $active_user['user_type'] != 'user';
    $email_delay = sb_get_multi_setting('wc-follow-up', 'wc-follow-up-email-delay');
    $order = $is_update_user || $is_follow_up ? wc_get_order($order_id) : false;
    if (get_post_meta($order_id, 'sb-user')) {
        return;
    }

    // Link the order to the Support Board user
    if ($active_user) {
        $active_user_id = $active_user['id'];
        add_post_meta($order_id, 'sb-user', $active_user_id);
    }

    // Update user details
    if ($is_update_user) {
        $user_details = [['first_name' => '', 'last_name' => '', 'email' => ''], ['address' => '', 'phone' => '', 'company' => '', 'address_1' => '', 'address_2' => '', 'city' => '', 'state' => '', 'postcode' => '', 'country' => '']];
        for ($i = 0; $i < 2; $i++) {
            foreach ($user_details[$i] as $key => $value) {
                $user_details[$i][$key] = $order->data['billing'][$key] ? $order->data['billing'][$key] : $order->data['shipping'][$key];
            }
        }
        $user_details[1]['address'] .= $user_details[1]['address_1'] . ($user_details[1]['address_2'] != '' ? ', ' . $user_details[1]['address_2'] : '');
        unset($user_details[1]['address_1']);
        unset($user_details[1]['address_2']);
        foreach ($user_details[1] as $key => $value) {
            $user_details[1][$key] = [$value, ucfirst($key)];
        }
        if ($user_details[0]['first_name']) {
            if ($active_user) {
                sb_update_user($active_user_id, $user_details[0], $user_details[1]);
            } else {
                sb_add_user_and_login($user_details[0], $user_details[1]);
            }
        }
    }

    // Follow-up, social share, and email
    if ($order && ($is_follow_up || !empty($email_delay) || $is_social_share)) {
        $active_user = sb_get_active_user();
        $products = [];
        $product_ids = [];
        foreach ($order->get_items() as $value) {
            $data = $value->get_data();
            array_push($products, ['name' => $data['name']]);
            array_push($product_ids, $data['product_id']);
        }
        if ($is_follow_up) {
            $message = sb_get_multi_setting('wc-follow-up', 'wc-follow-up-message');
            if ($active_user && $message != '' && (strpos($message, '{coupon}') === false || !sb_woocommerce_coupon_check($active_user_id))) {
                sb_send_message(sb_get_bot_id(), sb_get_last_conversation_id_or_create($active_user_id, 3), sb_woocommerce_merge_fields($message, ['products' => $products, 'coupon-discount' => sb_get_multi_setting('wc-follow-up', 'wc-follow-up-coupon-discount'), 'coupon-expiration' => sb_get_multi_setting('wc-follow-up', 'wc-follow-up-coupon-expiration') . ' seconds', 'user-id' => $active_user_id]), [], 1, '{ "event": "open-chat" }');
            }
        }
        if ($is_social_share) {
            $title = sb_get_multi_setting('wc-share', 'wc-share-title');
            $message = sb_get_multi_setting('wc-share', 'wc-share-message');
            if ($title && $message) {
                $message = '[share title="' . sb_rich_value($title) . '" message="' . sb_rich_value($message) . '" link="' . get_permalink($product_ids[0]) . '" channels="fb,tw,li,pi,wa"]';
                sb_send_message(sb_get_bot_id(), sb_get_last_conversation_id_or_create($active_user_id, 3), sb_woocommerce_merge_fields($message, ['products' => [$products[0]]]), [], 1, '{ "event": "open-chat" }');
            }
        }
        if (!empty($email_delay)) {
            sb_cron_jobs_add('wc-follow-up', $product_ids, $email_delay . ' hours');
        }
    }
    return $order_id;
}

function sb_woocommerce_product_removed($key, $cart) {
    $removed_products = [];
    $removed_product_ids = [];
    $active_user = sb_get_active_user();
    $message = sb_get_multi_setting('wc-product-removed', 'wc-product-removed-message');
    $email_delay = sb_get_multi_setting('wc-product-removed', 'wc-product-removed-email-delay');
    foreach ($cart->removed_cart_contents as $product) {
        array_push($removed_product_ids, $product['product_id']);
        array_push($removed_products, sb_woocommerce_get_product($product['product_id']));
    }
    if ($active_user && $message != '' && (strpos($message, '{coupon}') === false || !sb_woocommerce_coupon_check($active_user['id']))) {
        sb_send_message(sb_get_bot_id(), sb_get_last_conversation_id_or_create($active_user['id'], 3), sb_woocommerce_merge_fields($message, ['products' => $removed_products, 'coupon-discount' => sb_get_multi_setting('wc-product-removed', 'wc-product-removed-coupon-discount'), 'coupon-expiration' => sb_get_multi_setting('wc-product-removed', 'wc-product-removed-coupon-expiration') . ' seconds', 'coupon-product-ids' => json_encode($removed_product_ids), 'user-id' => $active_user['id']]), [], 1, '{ "event": "open-chat" }');
    }
    if (!empty($email_delay)) {
        sb_cron_jobs_add('wc-product-removed', $removed_product_ids, $email_delay . ' hours');
    }
    return [$key, $cart];
}

function sb_woocommerce_product_updated($post_id) {
    $post = get_post($post_id);
    if ($post->post_type == 'product') {
        if (sb_get_multi_setting('wc-waiting-list', 'wc-waiting-list-active')) {
            sb_woocommerce_waiting_list($post_id, false, false, 'send');
        }
    }
    return $post_id;
}

function sb_woocommerce_save_session_key_action($cart_item) {
    $session_key = WC()->session->get_customer_id();
    sb_woocommerce_save_session_key($session_key);
    if (sb_get_multi_setting('wc-abandoned-cart', 'wc-abandoned-cart-1') == 'now' && sb_get_active_user()) {
        $cart = $cart_item['data']->get_data();
        $cart = [['product_id' => $cart_item['product_id'], 'line_subtotal' => $cart['price'], 'quantity' => $cart_item['quantity']]];
        $cart_item_serialized = ['session_value' => serialize(['cart' => serialize($cart)]), 'session_expiry' => time() - 1, 'session_id' => false, 'session_key' => sb_get_user_extra(sb_get_active_user()['id'], 'woocommerce_session_key')];
        sb_woocommerce_abandoned_carts($cart_item_serialized);
    }
    return $cart_item;
}

function sb_woocommerce_on_cart_add($parameters) {

    // Follow-up
    if (sb_get_setting('wc-follow-up-cart')) {
        $active_user = sb_get_active_user();
        if ($active_user && empty($active_user['email'])) {
            $settings = sb_get_setting('follow-message');
            sb_send_message(sb_get_bot_id(), sb_get_last_conversation_id_or_create($active_user['id'], 3), ' [email title="' . sb_rich_value($settings['title']) . '" message="' . sb_rich_value($settings['message']) . '" placeholder="' . sb_rich_value($settings['placeholder']) . '" name="' . $settings['name'] . '" last-name="' . $settings['last-name'] . '" success="' . sb_rich_value($settings['success']) . '"]');
        }
    }

    sb_woocommerce_save_session_key();
    return $parameters;
}

/*
 * ----------------------------------------------------------
 * WORDPRESS ENVIRONMENT FUNCTIONS
 * ----------------------------------------------------------
 *
 * Functions that require WordPress to work.
 *
 */

function sb_woocommerce_inline() {
    $code = '';
    $page_id = get_the_ID();
    if (sb_get_multi_setting('wc-waiting-list', 'wc-waiting-list-active') && is_product() && !sb_woocommerce_is_in_stock($page_id)) {
        $code = 'var SB_WP_WAITING_LIST = true;';
    }
    return $code;
}

function sb_woocommerce_update_cart($product_id, $type, $quantity = 1, $attributes = []) {
    $user = sb_get_active_user();
    $conversation_id = sb_get_last_conversation_id_or_create($user['id'], 3);

    if (sb_is_error($conversation_id)) {
        return $conversation_id;
    }
    if (!is_numeric($product_id)) {
        $product_id = sb_woocommerce_get_product_id_by_name($product_id);
    }
    if (empty($product_id)) {
        return false;
    }
    $custom_detail = custom_get_product($product_id);
    if ($type == 'cart-add') {
        if (!empty($custom_detail['has_variations'])) {
            $product_attributes = $custom_detail['variation_attributes'];

            $needs_variation = false;
            $missing_attributes = [];
            $selected_attributes = [];

            foreach ($product_attributes as $attr_key => $attr_values) {
                $attr_name = str_replace('pa_', '', $attr_key);
                $attr_value = null;

                // Enhanced attribute matching - handle mixed pa_ and non-pa_ attributes
                foreach ($attributes as $var_key => $var_value) {
                    $normalized_var_key = custom_woo_check_attributes($var_key);
                    $normalized_attr_name = custom_woo_check_attributes($attr_name);
                    $normalized_attr_key = custom_woo_check_attributes($attr_key);

                    // Try exact match with attribute name (without pa_)
                    if ($normalized_var_key === $normalized_attr_name) {
                        $attr_value = $var_value;
                        break;
                    }

                    // Try matching with the full attribute key (including pa_)
                    if ($normalized_var_key === $normalized_attr_key) {
                        $attr_value = $var_value;
                        break;
                    }

                    // Try matching the attribute key without pa_ prefix
                    $attr_key_no_pa = str_replace('pa_', '', $attr_key);
                    if ($normalized_var_key === custom_woo_check_attributes($attr_key_no_pa)) {
                        $attr_value = $var_value;
                        break;
                    }

                    // Try partial matching for common attribute names
                    if (strpos($normalized_attr_name, $normalized_var_key) !== false ||
                        strpos($normalized_var_key, $normalized_attr_name) !== false) {
                        $attr_value = $var_value;
                        break;
                    }
                }

                if (empty($attr_value)) {
                    $needs_variation = true;
                    $missing_attributes[] = $attr_name;
                } else {
                    $valid_values = array_map('strtolower', $attr_values);
                    if (!in_array(strtolower($attr_value), $valid_values)) {
                        $message = 'Invalid ' . $attr_name . ' selection. Available options: ' . implode(', ', $attr_values). '.';
                        return sb_send_message(sb_get_bot_id(), $conversation_id, $message);
                    }
                    $selected_attributes['attribute_' . $attr_key] = $attr_value;
                }
            }

            if ($needs_variation) {
                $message = 'Please select ' . implode(' and ', $missing_attributes) . ' before adding to cart.';
                return sb_send_message(sb_get_bot_id(), $conversation_id, $message);
            }
            $variation_id = custom_woocommerce_get_variation_id_by_attributes($selected_attributes, $custom_detail["variations"]);
            if (!$variation_id) {
                // If no variation found, try to find a variation that matches available attributes
                // This handles cases where some attributes are "Any" (empty)
                $fallback_variation_id = custom_find_best_matching_variation($selected_attributes, $custom_detail["variations"]);
                if ($fallback_variation_id) {
                    $variation_id = $fallback_variation_id;
                } else {
                    $message = 'The selected combination is not available.';
                    return sb_send_message(sb_get_bot_id(), $conversation_id, $message);
                }
            }
        }

        $is_in_stock = custom_woocommerce_is_in_stock($product_id, isset($variation_id) ? $variation_id : 0, $quantity);
        if (!$is_in_stock) {
            sb_woocommerce_waiting_list($product_id);
            return 'out-of-stock';
        }
        if (isset($variation_id) && $variation_id > 0) {
            return WC()->cart->add_to_cart($product_id, $quantity, $variation_id, $selected_attributes);
        } else {
            return WC()->cart->add_to_cart($product_id, $quantity);
        }
    } else if ($type == 'cart-remove') {
        foreach (WC()->cart->get_cart() as $key => $item) {
            if ($item['product_id'] == $product_id) {
                return WC()->cart->remove_cart_item($key);
            }
        }
    } else if ($type == 'cart-update') {
        // For updating cart, we need to find the correct cart item key
        // If the product has variations, we need to make sure we're updating the correct variation
        // Replace the existing variation handling code block with this simplified version
        // Improved code that preserves existing attributes when updating
        if ((!empty($custom_detail['has_variations']) && !empty($attributes)) || (!empty($custom_detail['has_variations']) && empty($attributes))) {
            $target_cart_key = null;
            $current_attributes = [];

            if (empty($attributes)) {
                $message = 'Please specify which variation you want to update (e.g., size, color).';
                return sb_send_message(sb_get_bot_id(), $conversation_id, $message);
            }

            // Find any variation of this product in the cart to update
            foreach (WC()->cart->get_cart() as $key => $item) {
                if ($item['product_id'] == $product_id) {
                    if (isset($item['variation']) && !empty($item['variation'])) {
                        $target_cart_key = $key;
                        $current_attributes = $item['variation'];
                        break;
                    }
                }
            }

            if ($target_cart_key === null) {
                $message = 'No ' . $custom_detail['name'] . ' found in cart to update.';
                return sb_send_message(sb_get_bot_id(), $conversation_id, $message);
            }

            // Update current attributes with new values
            $merged_attributes = $current_attributes;

            foreach ($custom_detail['variation_attributes'] as $attr_key => $attr_values) {
                $attr_name = str_replace('pa_', '', $attr_key);

                foreach ($attributes as $var_key => $var_value) {
                    $normalized_var_key = custom_woo_check_attributes($var_key);
                    $normalized_attr_name = custom_woo_check_attributes($attr_name);

                    if ($normalized_var_key === $normalized_attr_name && !empty($var_value)) {
                        $valid_values = array_map('strtolower', $attr_values);
                        if (in_array(strtolower($var_value), $valid_values)) {
                            $merged_attributes['attribute_' . $attr_key] = $var_value;
                        } else {
                            $message = 'Invalid ' . $attr_name . ' selection. Available options: ' . implode(', ', $attr_values) . '.';
                            return sb_send_message(sb_get_bot_id(), $conversation_id, $message);
                        }
                        break;
                    }
                }
            }

            // Get variation ID for merged attributes
            $variation_id = custom_woocommerce_get_variation_id_by_attributes($merged_attributes, $custom_detail['variations']);
            if (!$variation_id) {
                // Try to find a fallback variation that matches available attributes
                $fallback_variation_id = custom_find_best_matching_variation($merged_attributes, $custom_detail["variations"]);
                if ($fallback_variation_id) {
                    $variation_id = $fallback_variation_id;
                } else {
                    $message = 'The selected variation combination is not available.';
                    return sb_send_message(sb_get_bot_id(), $conversation_id, $message);
                }
            }

            // Check if new variation is in stock
            $is_in_stock = custom_woocommerce_is_in_stock($product_id, $variation_id, $quantity);
            if (!$is_in_stock) {
                sb_woocommerce_waiting_list($product_id);
                return 'out-of-stock';
            }

            // Check if we're changing to a variation that already exists in cart
            $duplicate_key = null;
            $current_variation_id = WC()->cart->get_cart_item($target_cart_key)['variation_id'];
            if ($variation_id != $current_variation_id) {
                foreach (WC()->cart->get_cart() as $key => $item) {
                    if ($key !== $target_cart_key &&
                        $item['product_id'] == $product_id &&
                        isset($item['variation_id']) &&
                        $item['variation_id'] == $variation_id) {
                        $duplicate_key = $key;
                        break;
                    }
                }
            }

            // Get the current quantity before removing the item
            $current_qty = WC()->cart->get_cart_item($target_cart_key)['quantity'];
            // If quantity wasn't specified, use the current quantity
            if ($quantity <= 0) {
                $quantity = $current_qty;
            }

            // Remove the target item
            WC()->cart->remove_cart_item($target_cart_key);

            // If we're changing to a variation that already exists, just increase its quantity
            if ($duplicate_key) {
                $duplicate_qty = WC()->cart->get_cart_item($duplicate_key)['quantity'];
                WC()->cart->set_quantity($duplicate_key, $duplicate_qty + $quantity);
            } else {
                // Otherwise add as new item
                WC()->cart->add_to_cart($product_id, $quantity, $variation_id, $merged_attributes);
            }

            // Build success message
            $product_name = $custom_detail['name'];
            $attr_description = [];
            foreach ($merged_attributes as $key => $value) {
                $clean_key = str_replace(['attribute_pa_', 'attribute_'], '', $key);
                $attr_description[] = $clean_key . ' ' . $value;
            }
            $variation_text = !empty($attr_description) ? ' in ' . implode(' and ', $attr_description) : '';
            $message = "The {$product_name} has been updated to {$quantity} units{$variation_text}. Would you like to review your cart or add anything else?";
            return sb_send_message(sb_get_bot_id(), $conversation_id, $message);
        } else {
            $is_in_stock = custom_woocommerce_is_in_stock($product_id, 0, $quantity);
            if (!$is_in_stock) {
                sb_woocommerce_waiting_list($product_id);
                return 'out-of-stock';
            } else {
                foreach (WC()->cart->get_cart() as $key => $item) {
                    if ($item['product_id'] == $product_id) {
                        return WC()->cart->set_quantity($key, $quantity);
                    }
                }
            }
        }
    }
    return false;
}

function sb_woocommerce_apply_coupon($coupon) {
    global $woocommerce;
    return $woocommerce->cart->add_discount($coupon);
}

function sb_woocommerce_purchase_button($product_ids, $coupon = false, $checkout = true) {
    for ($i = 0; $i < count($product_ids); $i++) {
        if (sb_woocommerce_update_cart($product_ids[$i], 'cart-add') == 'out-of-stock')
            return 'out-of-stock';
    }
    if (!empty($coupon)) {
        sb_woocommerce_apply_coupon($coupon);
    }
    return $checkout == 'true' ? wc_get_checkout_url() : true;
}


/*
 * ----------------------------------------------------------
 * CUSTOM CODE
 * ----------------------------------------------------------
 *
 * 2025
 *
 */

function custom_log($label, $data) {
    file_put_contents('custom_log.txt', "=== $label (". date("Y-m-d H:i:s") . ") ===\n" . print_r($data, true) . "\n\n", FILE_APPEND);
}

function register_autofill_checkout_script() {
    if (!function_exists('is_checkout')) {
        return;
    }
    if (is_checkout()) {
        add_action('wp_footer', 'inject_autofill_inline_script', 25);
    }
}
function inject_autofill_inline_script() {
    ?>
    <!-- <script type="text/javascript" src="https://your-server.com/path/to/woocommerce-autofill.js"></script> -->
    <script type="text/javascript">
    // Initialize the autofill when the external script loads
    (function($) {
        'use strict';
       
        $(document).ready(function() {
            if (typeof initWooCommerceAutofill === 'function') {
                initWooCommerceAutofill();
            }
        });
       
        // REMOVED: The updated_checkout event that was causing the reload issue
        // $(document.body).on('updated_checkout', function() {
        //     setTimeout(function() {
        //         wooCommerceAutofillCheckout();
        //     }, 300);
        // });
       
    })(jQuery);
    </script>
    <?php
}

function custom_woo_check_attributes($attr) {
    if(!is_numeric($attr)) return strtolower($attr);
    else return $attr;
}

function custom_woocommerce_reset_cart() {
    try {
        WC()->cart->empty_cart();

        return json_encode([
            'success' => true,
            'message' => 'Cart cleared successfully'
        ]);

    } catch (Exception $e) {
        return json_encode([
            'success' => false,
            'message' => 'Error clearing cart: ' . $e->getMessage()
        ]);
    }
}

function custom_get_product($id) {
    $product_id = sb_db_escape($id, True);
    $POSTTYPE = sb_db_escape("product");
    $METAKEYPRICE = sb_db_escape("_price");
    $POSTTYPEVARIATION = sb_db_escape("product_variation");
    $METAKEYVARIATION = sb_db_escape("_product_attributes");
    $POSTSTATUS = sb_db_escape("publish");
    $METAKEYSTOCK = sb_db_escape("_stock_status");
    $METAKEYATTR = sb_db_escape("attribute_");

    $product = sb_db_get('SELECT p.id, p.post_title name, p.post_excerpt description, m.meta_value price FROM ' . SB_WP_PREFIX . 'posts p, ' . SB_WP_PREFIX . 'postmeta m WHERE p.id = ' . $product_id . ' AND m.meta_key = "'.$METAKEYPRICE.'" AND m.post_id = p.id AND p.post_type = "'.$POSTTYPE.'"');
    if (!sb_is_error($product) && !empty($product)) {
        // Check if product has variations
        $has_variations = sb_db_get('SELECT COUNT(*) as count FROM ' . SB_WP_PREFIX . 'posts WHERE post_parent = ' . $product_id . ' AND post_type = "'.$POSTTYPEVARIATION.'"');
        $product['has_variations'] = ($has_variations && $has_variations['count'] > 0);

        // If product has variations, get variation attributes
        if ($product['has_variations']) {
            // Get product attributes
            $attributes_meta = sb_db_get('SELECT meta_value FROM ' . SB_WP_PREFIX . 'postmeta WHERE post_id = ' . $product_id . ' AND meta_key = "'.$METAKEYVARIATION.'"');
            $variation_attributes = [];

            if (isset($attributes_meta['meta_value'])) {
                $product_attributes = unserialize($attributes_meta['meta_value']);

                // Process each attribute
                foreach ($product_attributes as $key => $attribute) {
                    if (isset($attribute['is_variation']) && $attribute['is_variation']) {
                        $attribute_name = $attribute['name'];
                        $values = [];

                        if (substr($key, 0, 3) == 'pa_') {
                            // Get taxonomy terms
                            $terms = sb_db_get('SELECT t.name, t.slug FROM ' . SB_WP_PREFIX . 'terms t
                                JOIN ' . SB_WP_PREFIX . 'term_taxonomy tt ON t.term_id = tt.term_id
                                JOIN ' . SB_WP_PREFIX . 'term_relationships tr ON tt.term_taxonomy_id = tr.term_taxonomy_id
                                WHERE tr.object_id = ' . $product_id . ' AND tt.taxonomy = "' . $key . '"', false);

                            foreach ($terms as $term) {
                                $values[] = $term['name'];
                            }
                        } else {
                            // Custom product attribute
                            $values = explode('|', $attribute['value']);
                            $values = array_map('trim', $values);
                        }

                        $variation_attributes[$attribute_name] = $values;
                    }
                }
            }

            // Get available variations
            $variations = sb_db_get('SELECT ID FROM ' . SB_WP_PREFIX . 'posts WHERE post_parent = ' . $product_id . ' AND post_type = "'.$POSTTYPEVARIATION.'" AND post_status = "'.$POSTSTATUS.'"', false);
            $variation_data = [];

            foreach ($variations as $variation) {
                $variation_id = $variation['ID'];
                $variation_info = [
                    'variation_id' => $variation_id,
                    'attributes' => []
                ];

                // Get price
                $price = sb_db_get('SELECT meta_value FROM ' . SB_WP_PREFIX . 'postmeta WHERE post_id = ' . $variation_id . ' AND meta_key = "'.$METAKEYPRICE.'"');
                if (isset($price['meta_value'])) {
                    $variation_info['price'] = $price['meta_value'];
                }

                // Get stock status
                $stock = sb_db_get('SELECT meta_value FROM ' . SB_WP_PREFIX . 'postmeta WHERE post_id = ' . $variation_id . ' AND meta_key = "'.$METAKEYSTOCK.'"');
                if (isset($stock['meta_value'])) {
                    $variation_info['stock_status'] = $stock['meta_value'];
                }

                // Get variation attributes
                $like_pattern = $METAKEYATTR . '%';
                $var_attributes = sb_db_get('SELECT meta_key, meta_value FROM ' . SB_WP_PREFIX . 'postmeta WHERE post_id = ' . $variation_id . ' AND meta_key LIKE "'.$like_pattern.'"', false);
                foreach ($var_attributes as $attr) {
                    $attribute_name = str_replace('attribute_', '', $attr['meta_key']);
                    $attribute_value = $attr['meta_value'];

                    // For taxonomy attributes, get the display name
                    if (substr($attribute_name, 0, 3) == 'pa_') {
                        $term = sb_db_get('SELECT name FROM ' . SB_WP_PREFIX . 'terms WHERE slug = "' . $attribute_value . '"');
                        if (isset($term['name'])) {
                            $attribute_value = $term['name'];
                        }

                        // Get attribute label
                        $tax = sb_db_get('SELECT attribute_label FROM ' . SB_WP_PREFIX . 'woocommerce_attribute_taxonomies WHERE attribute_name = "' . substr($attribute_name, 3) . '"');
                        if (isset($tax['attribute_label'])) {
                            $attribute_name = $tax['attribute_label'];
                        }
                    }
                    $variation_info['attributes'][$attribute_name] = $attribute_value;
                }

                $variation_data[] = $variation_info;
            }
            $product['variation_attributes'] = $variation_attributes;
            $product['variations'] = $variation_data;
        }
    }
    return $product;
}

function custom_woocommerce_get_variation_id_by_attributes($attributes, $variations) {
    if (empty($attributes)) {
        return false;
    }

    if (empty($variations)) {
        return false;
    }

    // For each variation, check if it matches the requested attributes
    foreach ($variations as $variation) {
        $variation_id = $variation['variation_id'];
        $matches = true;

        // Check each attribute to see if it matches
        foreach ($attributes as $attribute_name => $attribute_value) {
            // Get the attribute value for this variation
            $stored_value = sb_db_get('SELECT meta_value FROM ' . SB_WP_PREFIX . 'postmeta
                                     WHERE post_id = ' . $variation_id . '
                                     AND meta_key = "' . $attribute_name . '"
                                     LIMIT 1');
            // Now we check very carefully
            if ($stored_value !== false && isset($stored_value['meta_value'])) {
                $variation_attr_value = strtolower(trim($stored_value['meta_value']));
                $requested_attr_value = strtolower(trim($attribute_value));

                if ($variation_attr_value === '') {
                    // Empty attribute means "Any" — OK, continue
                    continue;
                } elseif ($variation_attr_value !== $requested_attr_value) {
                    // Attribute exists and doesn't match
                    $matches = false;
                    break;
                }
            } else {
                // If no meta_key exists at all, treat as "Any" (continue)
                continue;
            }
        }
        // If all attributes match, return this variation ID
        if ($matches) {
            return $variation_id;
        }
    }

    // No matching variation found
    return false;
}

function custom_find_best_matching_variation($selected_attributes, $variations) {
    $best_match_id = null;
    $best_match_score = 0;

    foreach ($variations as $variation) {
        $variation_id = $variation['variation_id'];
        $variation_attributes = $variation['attributes'];
        $match_score = 0;

        // Count how many attributes match
        foreach ($selected_attributes as $attr_key => $attr_value) {
            $attr_name = str_replace('attribute_', '', $attr_key);
            $attr_name = str_replace('pa_', '', $attr_name);

            // Check if this variation has a matching attribute
            foreach ($variation_attributes as $var_attr_name => $var_attr_value) {
                if (strtolower($attr_name) === strtolower($var_attr_name)) {
                    if (empty($var_attr_value) || strtolower($var_attr_value) === strtolower($attr_value)) {
                        $match_score++;
                    }
                    break;
                }
            }
        }

        // If this variation matches more attributes than previous best
        if ($match_score > $best_match_score) {
            $best_match_score = $match_score;
            $best_match_id = $variation_id;
        }
    }

    // Only return a match if we matched at least some attributes
    return ($best_match_score > 0) ? $best_match_id : null;
}



function custom_woocommerce_is_in_stock($id, $variation_id = 0, $quantity = 0) {
    $product_id = sb_db_escape($id, True);
    $in_stock = 0;
    if (!empty($variation_id)) {
        $current_product = wc_get_product($variation_id);
        $in_stock = $current_product->is_in_stock();
        $stock = $current_product->get_stock_quantity();
        if ($quantity > $stock) {
            $in_stock = 0;
        }
        return $in_stock;
    } else {
        $current_product = wc_get_product($product_id);
        $in_stock = $current_product->is_in_stock();
        $stock = $current_product->get_stock_quantity();
        if ($quantity > $stock) {
            $in_stock = 0;
        }
        return $in_stock;
    }
}


/*
 * ----------------------------------------------------------
 * CURRENT PAGE PRODUCT DETECTION
 * ----------------------------------------------------------
 *
 * Functions for detecting and retrieving product information from current page
 *
 */

function sb_woocommerce_detect_current_product($current_url) {
    // Method 1: WordPress context (if available and we're in WordPress environment)
    if (function_exists('is_product') && function_exists('get_the_ID')) {
        if (is_product()) {
            $product_id = get_the_ID();
            if ($product_id) {
                return sb_woocommerce_get_product($product_id);
            }
        }
    }

    // Method 2: URL pattern matching
    if ($current_url) {
        $product_id = sb_woocommerce_extract_product_id_from_url($current_url);
        if ($product_id) {
            return sb_woocommerce_get_product($product_id);
        }
    }

    return false;
}

function sb_woocommerce_extract_product_id_from_url($url) {
    if (!$url) return false;

    // Pattern 1: /product/product-slug/ (most common WooCommerce URL structure)
    if (preg_match('/\/product\/([^\/\?]+)\/?/', $url, $matches)) {
        $slug = $matches[1];
        // Get product ID by slug
        $product = sb_db_get('SELECT ID FROM ' . SB_WP_PREFIX . 'posts WHERE post_name = "' . sb_db_escape($slug) . '" AND post_type = "product" AND post_status = "publish" LIMIT 1');
        if ($product && isset($product['ID'])) {
            return $product['ID'];
        }
    }

    // Pattern 2: ?p=123 (direct post ID)
    if (preg_match('/[?&]p=(\d+)/', $url, $matches)) {
        $product_id = $matches[1];
        // Verify it's actually a product
        $product = sb_db_get('SELECT ID FROM ' . SB_WP_PREFIX . 'posts WHERE ID = ' . sb_db_escape($product_id, true) . ' AND post_type = "product" AND post_status = "publish" LIMIT 1');
        if ($product && isset($product['ID'])) {
            return $product['ID'];
        }
    }

    // Pattern 3: ?product_id=123
    if (preg_match('/[?&]product_id=(\d+)/', $url, $matches)) {
        return $matches[1];
    }

    return false;
}

function sb_woocommerce_format_current_product_info($product) {
    if (!$product || !isset($product['name'])) {
        return 'Product information not available.';
    }

    $info = [];
    $info[] = '**Product Name:** ' . $product['name'];

    if (isset($product['price']) && $product['price']) {
        $currency_symbol = sb_get_setting('wc-currency-symbol', '$');
        $info[] = '**Price:** ' . $currency_symbol . $product['price'];
    }

    if (isset($product['description']) && $product['description']) {
        $info[] = '**Description:** ' . strip_tags($product['description']);
    }

    if (isset($product['id'])) {
        // Check stock status
        $in_stock = sb_woocommerce_is_in_stock($product['id']);
        $info[] = '**Stock Status:** ' . ($in_stock ? 'In Stock' : 'Out of Stock');
    }

    $info[] = "\nWould you like to add this product to your cart? Just say 'add this product to cart' or 'add to cart'.";

    return implode("\n", $info);
}

/*
 * ----------------------------------------------------------
 * MULTIPLE ADDRESSES INTEGRATION
 * ----------------------------------------------------------
 *
 * Functions to retrieve users with multiple addresses from various plugins
 * and provide dummy data when needed for SupportBoard integration.
 *
 */

/**
 * Safe unserialize function 
 *
 * @param mixed $data Data to unserialize
 * @return mixed Unserialized data or original data if not serialized
 */
function sb_woocommerce_safe_unserialize($data) {
    if (!is_string($data)) {
        return $data;
    }

    // Check if it looks like serialized data (starts with a: for array or O: for object)
    if (substr($data, 0, 2) === 'a:' || substr($data, 0, 2) === 'O:' || substr($data, 0, 2) === 's:') {
        $unserialized = @unserialize($data);
        if ($unserialized !== false) {
            return $unserialized;
        }
    }

    return $data;
}

/**
 * Retrieve users with multiple addresses from Custom Multiple Addresses plugin
 *
 * This function checks for users who have multiple addresses stored by:
 * - Custom Multiple Addresses plugin (cma_billing_addresses, cma_shipping_addresses)
 *
 * If a specific user_id is provided, returns only that user's addresses.
 *
 * @param string $address_type Optional. Filter by address type: 'billing', 'shipping', or 'all' (default)
 * @param int $limit Optional. Maximum number of users to return (default: 10)
 * @param int $user_id Optional. Get addresses for specific user ID only
 * @return array Array of users with their multiple addresses, or single user data if user_id provided
 */
function sb_woocommerce_get_users_with_multiple_addresses($address_type = 'all', $limit = 10, $user_id = null) {
    // If specific user ID is requested
    if ($user_id !== null) {
        return sb_woocommerce_get_single_user_addresses($user_id, $address_type);
    }

    $users_with_addresses = array();

    // Check Custom Multiple Addresses plugin
    $cma_users = sb_woocommerce_get_cma_users_with_addresses($address_type);
    if (!empty($cma_users)) {
        $users_with_addresses = $cma_users;
    }
    // Apply limit
    if ($limit > 0 && count($users_with_addresses) > $limit) {
        $users_with_addresses = array_slice($users_with_addresses, 0, $limit);
    }

    return $users_with_addresses;
}

/**
 * Get users with multiple addresses from Custom Multiple Addresses plugin
 * Enhanced with default address detection and SQL injection protection
 *
 * @param string $address_type Address type filter
 * @param int $user_id Optional. Get addresses for specific user ID only
 * @return array Array of users with addresses including default information
 */
function sb_woocommerce_get_cma_users_with_addresses($address_type = 'all', $user_id = null) {
    $users = array();
    $meta_keys = array();

    // Sanitize address type input
    $address_type = sb_db_escape($address_type);

    // Build meta keys array for addresses
    if ($address_type === 'all' || $address_type === 'billing') {
        $meta_keys[] = 'cma_billing_addresses';
    }
    if ($address_type === 'all' || $address_type === 'shipping') {
        $meta_keys[] = 'cma_shipping_addresses';
    }

    // Always include default address meta keys
    $meta_keys[] = 'cma_default_billing_address';
    $meta_keys[] = 'cma_default_shipping_address';

    if (empty($meta_keys)) {
        return $users;
    }

    // Build query with optional user_id filter
    $meta_keys_escaped = array_map('sb_db_escape', $meta_keys);
    $meta_keys_string = '"' . implode('","', $meta_keys_escaped) . '"';
    $user_filter = '';

    if ($user_id !== null) {
        $user_filter = ' AND um.user_id = ' . intval($user_id);
    }

    $query = "
        SELECT DISTINCT um.user_id, um.meta_key, um.meta_value, u.user_login, u.user_email, u.display_name
        FROM " . SB_WP_PREFIX . "usermeta um
        INNER JOIN " . SB_WP_PREFIX . "users u ON um.user_id = u.ID
        WHERE um.meta_key IN ({$meta_keys_string})
        AND um.meta_value != ''
        AND um.meta_value != 'a:0:{}'
        {$user_filter}
    ";

    $results = sb_db_get($query, false);

    foreach ($results as $result) {
        $user_id = intval($result['user_id']); // Sanitize user ID

        if (!isset($users[$user_id])) {
            $users[$user_id] = array(
                'user_id' => $user_id,
                'user_login' => sb_db_escape($result['user_login']),
                'user_email' => sb_db_escape($result['user_email']),
                'display_name' => sb_db_escape($result['display_name']),
                'plugin' => 'Custom Multiple Addresses',
                'billing_addresses' => array(),
                'shipping_addresses' => array(),
                'default_billing_address' => '',
                'default_shipping_address' => ''
            );
        }

        // Handle address arrays
        if ($result['meta_key'] === 'cma_billing_addresses' || $result['meta_key'] === 'cma_shipping_addresses') {
            $addresses = sb_woocommerce_safe_unserialize($result['meta_value']);

            if (!is_array($addresses) || empty($addresses)) {
                continue;
            }

            if ($result['meta_key'] === 'cma_billing_addresses') {
                $users[$user_id]['billing_addresses'] = $addresses;
            } elseif ($result['meta_key'] === 'cma_shipping_addresses') {
                $users[$user_id]['shipping_addresses'] = $addresses;
            }
        }

        // Handle default address meta keys
        if ($result['meta_key'] === 'cma_default_billing_address') {
            $users[$user_id]['default_billing_address'] = sb_db_escape($result['meta_value']);
        } elseif ($result['meta_key'] === 'cma_default_shipping_address') {
            $users[$user_id]['default_shipping_address'] = sb_db_escape($result['meta_value']);
        }
    }

    // REMOVED: Duplicate processing that was overriding correct is_default values
    // The addresses already have correct is_default values from the serialized data
    // No need to process them again here

    return array_values($users);
}

/**
 * Get addresses for a single user with fallback to default WooCommerce addresses
 *
 * @param int $user_id User ID to get addresses for
 * @param string $address_type Address type filter ('billing', 'shipping', 'all')
 * @return array User data with addresses, or empty array if user not found
 */
function sb_woocommerce_get_single_user_addresses($user_id, $address_type = 'all') {
    // Sanitize user ID
    $user_id = intval($user_id);
    if ($user_id <= 0) {
        return array();
    }

    // First, try to get multiple addresses from Custom Multiple Addresses plugin
    $cma_users = sb_woocommerce_get_cma_users_with_addresses($address_type, $user_id);

    if (!empty($cma_users) && isset($cma_users[0])) {
        $user_data = $cma_users[0];

        // Check if we have any default addresses in the additional address list
        $has_default_billing = false;
        $has_default_shipping = false;

        if (!empty($user_data['billing_addresses'])) {
            foreach ($user_data['billing_addresses'] as $address) {
                if (!empty($address['is_default'])) {
                    $has_default_billing = true;
                    break;
                }
            }
        }

        if (!empty($user_data['shipping_addresses'])) {
            foreach ($user_data['shipping_addresses'] as $address) {
                if (!empty($address['is_default'])) {
                    $has_default_shipping = true;
                    break;
                }
            }
        }

        // If no default addresses found in additional addresses, merge with WooCommerce defaults
        if ((!$has_default_billing && ($address_type === 'all' || $address_type === 'billing')) ||
            (!$has_default_shipping && ($address_type === 'all' || $address_type === 'shipping'))) {

            $wc_defaults = sb_woocommerce_get_default_woocommerce_addresses($user_id, $address_type);
            if (!empty($wc_defaults)) {
                // Merge WooCommerce default addresses with CMA addresses
                if (!$has_default_billing && !empty($wc_defaults['billing_addresses'])) {
                    $user_data['billing_addresses'] = array_merge(
                        $wc_defaults['billing_addresses'],
                        $user_data['billing_addresses']
                    );
                }

                if (!$has_default_shipping && !empty($wc_defaults['shipping_addresses'])) {
                    $user_data['shipping_addresses'] = array_merge(
                        $wc_defaults['shipping_addresses'],
                        $user_data['shipping_addresses']
                    );
                }
            }
        }

        return $user_data; // Return the enhanced user data
    }

    // If no multiple addresses found, try to get default WooCommerce addresses
    $user_data = sb_woocommerce_get_default_woocommerce_addresses($user_id, $address_type);

    if (!empty($user_data)) {
        return $user_data;
    }

    // Return empty array if no addresses found
    return array();
}

/**
 * Get default WooCommerce billing and shipping addresses for a user
 *
 * @param int $user_id User ID
 * @param string $address_type Address type filter ('billing', 'shipping', 'all')
 * @return array User data with default addresses, or empty array if none found
 */
function sb_woocommerce_get_default_woocommerce_addresses($user_id, $address_type = 'all') {
    $user_id = intval($user_id);
    if ($user_id <= 0) {
        return array();
    }

    // Get user info
    $user_info = sb_db_get("
        SELECT ID, user_login, user_email, display_name
        FROM " . SB_WP_PREFIX . "users
        WHERE ID = " . $user_id . "
    ");

    if (!$user_info) {
        return array();
    }

    // Build meta keys for WooCommerce default addresses
    $meta_keys = array();
    $address_fields = array('first_name', 'last_name', 'company', 'address_1', 'address_2', 'city', 'state', 'postcode', 'country', 'phone', 'email');

    if ($address_type === 'all' || $address_type === 'billing') {
        foreach ($address_fields as $field) {
            $meta_keys[] = 'billing_' . $field;
        }
    }

    if ($address_type === 'all' || $address_type === 'shipping') {
        foreach ($address_fields as $field) {
            $meta_keys[] = 'shipping_' . $field;
        }
    }

    if (empty($meta_keys)) {
        return array();
    }

    // Get user meta for addresses
    $meta_keys_escaped = array_map('sb_db_escape', $meta_keys);
    $meta_keys_string = '"' . implode('","', $meta_keys_escaped) . '"';

    $address_meta = sb_db_get("
        SELECT meta_key, meta_value
        FROM " . SB_WP_PREFIX . "usermeta
        WHERE user_id = " . $user_id . "
        AND meta_key IN ({$meta_keys_string})
        AND meta_value != ''
    ", false);

    if (empty($address_meta)) {
        return array();
    }

    // Build user data structure
    $user_data = array(
        'user_id' => $user_id,
        'user_login' => sb_db_escape($user_info['user_login']),
        'user_email' => sb_db_escape($user_info['user_email']),
        'display_name' => sb_db_escape($user_info['display_name']),
        'plugin' => 'WooCommerce Default',
        'billing_addresses' => array(),
        'shipping_addresses' => array(),
        'default_billing_address' => 'default',
        'default_shipping_address' => 'default'
    );

    // Process address meta into structured format
    $billing_address = array();
    $shipping_address = array();

    foreach ($address_meta as $meta) {
        $key = $meta['meta_key'];
        $value = sb_db_escape($meta['meta_value']);

        if (strpos($key, 'billing_') === 0) {
            $field = str_replace('billing_', '', $key);
            $billing_address[$field] = $value;
        } elseif (strpos($key, 'shipping_') === 0) {
            $field = str_replace('shipping_', '', $key);
            $shipping_address[$field] = $value;
        }
    }

    // Add addresses to user data if they have content
    if (!empty($billing_address) && ($address_type === 'all' || $address_type === 'billing')) {
        $billing_address['is_default'] = true;
        $user_data['billing_addresses']['default'] = $billing_address;
    }

    if (!empty($shipping_address) && ($address_type === 'all' || $address_type === 'shipping')) {
        $shipping_address['is_default'] = true;
        $user_data['shipping_addresses']['default'] = $shipping_address;
    }

    // Return user data only if at least one address was found
    if (!empty($user_data['billing_addresses']) || !empty($user_data['shipping_addresses'])) {
        return $user_data;
    }

    return array();
}

function delete_sb_user($wp_user_id) {
    $user = sb_wp_get_user($wp_user_id);
    if ($user) {
        $sb_user = sb_search_users($user['email']);
        $response = sb_delete_user($sb_user[0]['id']);
    }
}

/*
 * -----------------------------------------------------------
 * CHIPS DISPLAY HANDLERS
 * -----------------------------------------------------------
 *
 * Handle the display of address selection chips for WordPress AJAX
 *
 */

function sb_wp_handle_address_chips($address_type, $sb_user_id, $wp_user_id) {
    $conversation_id = sb_get_last_conversation_id_or_create($sb_user_id, 1);
    $label = preg_replace('/_addresses$/', '', $address_type);
    $addresses = sb_woocommerce_get_users_with_multiple_addresses($label, 10, $wp_user_id);
    $isExist = false;

    if (!$addresses || !$conversation_id) {
        return false;
    }

    $address_options = [];
    foreach ($addresses[$address_type] as $address) {
        $isExist = true;
        // Filter out empty address fields
         // Filter out empty address fields and add commas
        $address_fields = [
            $address['address_1'] ?? '',
            $address['address_2'] ?? '',
            $address['city'] ?? '',
            $address['state'] ?? '',
            $address['postcode'] ?? ''
        ];
        
        $address_parts = [];
        foreach ($address_fields as $field) {
            if (!empty(trim($field))) {
                $address_parts[] = trim($field) . ',';
            }
        }
        
        // Remove the trailing comma from the last element
        if (!empty($address_parts)) {
            $address_parts[count($address_parts) - 1] = rtrim(end($address_parts), ',');
        }

        $formatted_address = implode(' ', $address_parts);
        // $default_marker = (!empty($address['is_default']) && $address['is_default']) ? "(DEFAULT) " : "";
        // $display_text = $default_marker . $formatted_address;

        // Clean display text for chips format

        // $clean_display_text = str_replace(' ,', ' | ', $formatted_address);
        $escaped_display_text = str_replace([',', ':', '"'], [' |', '\:', '\"'], $formatted_address);
        $address_options[] = $escaped_display_text;
    }

    $response = '';
    if ($label == "shipping") { 
        $response = 'success="Thank you for selecting your shipping address! call sb-woocommerce-billing-address function to collect billing address."]';
    } else {
        $response = 'success="Thank you for selecting your billing address! completed address collection."]';
    }
    $options_string = implode(',', $address_options);
    $rich_message = '[chips id="sb-'.$label.'-address-selection" message="Please select your '.$label.' address:" options="' . $options_string . '" '.$response.']';

    // resturn  true for sb shortcode, and the rich messages
    return [true, $rich_message, $isExist];
}
[{"type": "text", "id": "wc-currency-symbol", "title": "Currency symbol", "content": "Set the currency symbol used by your system."}, {"type": "multi-input", "id": "wc-returning-visitor", "title": "Returning visitor message", "content": "Send a message to users who visit the website again after at least 24 hours. You can use the following merge fields and more: {coupon}, {user_name}. See the docs for more details.", "help": "https://board.support/docs/#woocommerce-returning-visitor", "value": [{"type": "checkbox", "id": "wc-returning-visitor-active", "title": "Active"}, {"type": "number", "id": "wc-returning-visitor-coupon-expiration", "title": "Coupon expiration (seconds)"}, {"type": "number", "id": "wc-returning-visitor-coupon-discount", "title": "Coupon discount (%)"}, {"type": "textarea", "id": "wc-returning-visitor-message", "title": "Message"}]}, {"type": "multi-input", "id": "wc-share", "title": "Social share message", "content": "Send a message to the customers who complete a purchase asking to share the product they just bought. You can use the following merge fields and more: {product_name}, {user_name}.", "value": [{"type": "checkbox", "id": "wc-share-active", "title": "Active"}, {"type": "text", "id": "wc-share-title", "title": "Title"}, {"type": "textarea", "id": "wc-share-message", "title": "Message"}]}, {"type": "multi-input", "id": "wc-follow-up", "title": "Follow up message", "content": "Send a message to the customers who complete a purchase. You can use the following merge fields and more: {coupon}, {product_names}, {user_name}.", "help": "https://board.support/docs/#woocommerce", "value": [{"type": "checkbox", "id": "wc-follow-up-active", "title": "Active"}, {"type": "number", "id": "wc-follow-up-coupon-expiration", "title": "Coupon expiration (seconds)"}, {"type": "number", "id": "wc-follow-up-coupon-discount", "title": "Coupon discount (%)"}, {"type": "textarea", "id": "wc-follow-up-message", "title": "Message"}, {"type": "select", "id": "wc-follow-up-email-delay", "title": "Email notification delay (hours)", "value": [["", "Disabled"], [1, 1], [2, 2], [3, 3], [4, 4], [5, 5], [6, 6], [7, 7], [8, 8], [9, 9], [10, 10], [11, 11], [12, 12], [13, 13], [14, 14], [15, 15], [16, 16], [17, 17], [18, 18], [19, 19], [20, 20], [21, 21], [22, 22], [23, 23], [24, 24], [25, 25], [26, 26], [27, 27], [28, 28], [29, 29], [30, 30], [31, 31], [32, 32], [33, 33], [34, 34], [35, 35], [36, 36], [37, 37], [38, 38], [39, 39], [39, 39], [40, 40], [41, 41], [42, 42], [43, 43], [44, 44], [45, 45], [46, 46], [47, 47], [48, 48], [49, 49], [50, 50], [51, 51], [52, 52], [53, 53], [54, 54], [55, 55], [56, 56], [57, 57], [58, 58], [59, 59], [60, 60], [61, 61], [62, 62], [63, 63], [64, 64], [65, 65], [66, 66], [67, 67], [68, 68], [69, 69], [70, 70], [71, 71]]}]}, {"type": "multi-input", "setting": "wc-emails", "id": "wc-follow-up-email", "title": "Follow up - Email", "content": "Follow-up email template. You can use text, HTML, and the following merge fields and more: {coupon}, {product_names}, {user_name}.", "help": "https://board.support/docs/#woocommerce-merge-fields", "value": [{"type": "number", "id": "wc-follow-up-email-coupon-expiration", "title": "Coupon expiration (days)"}, {"type": "number", "id": "wc-follow-up-email-coupon-discount", "title": "Coupon discount (%)"}, {"type": "text", "id": "wc-follow-up-email-subject", "title": "Subject"}, {"type": "textarea", "id": "wc-follow-up-email-content", "title": "Content"}]}, {"type": "multi-input", "id": "wc-product-removed", "title": "Product removed notification", "content": "Send a message to the customer after a product has been removed from the cart. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.", "help": "https://board.support/docs/#woocommerce-removed-item", "value": [{"type": "checkbox", "id": "wc-product-removed-active", "title": "Active"}, {"type": "number", "id": "wc-product-removed-coupon-expiration", "title": "Coupon expiration (seconds)"}, {"type": "number", "id": "wc-product-removed-coupon-discount", "title": "Coupon discount (%)"}, {"type": "text", "id": "wc-product-removed-button-text", "title": "Purchase button text"}, {"type": "textarea", "id": "wc-product-removed-message", "title": "Message"}, {"type": "select", "id": "wc-product-removed-email-delay", "title": "Email notification delay (hours)", "value": [["", "Disabled"], [1, 1], [2, 2], [3, 3], [4, 4], [5, 5], [6, 6], [7, 7], [8, 8], [9, 9], [10, 10], [11, 11], [12, 12], [13, 13], [14, 14], [15, 15], [16, 16], [17, 17], [18, 18], [19, 19], [20, 20], [21, 21], [22, 22], [23, 23], [24, 24], [25, 25], [26, 26], [27, 27], [28, 28], [29, 29], [30, 30], [31, 31], [32, 32], [33, 33], [34, 34], [35, 35], [36, 36], [37, 37], [38, 38], [39, 39], [39, 39], [40, 40], [41, 41], [42, 42], [43, 43], [44, 44], [45, 45], [46, 46], [47, 47], [48, 48], [49, 49], [50, 50], [51, 51], [52, 52], [53, 53], [54, 54], [55, 55], [56, 56], [57, 57], [58, 58], [59, 59], [60, 60], [61, 61], [62, 62], [63, 63], [64, 64], [65, 65], [66, 66], [67, 67], [68, 68], [69, 69], [70, 70], [71, 71]]}]}, {"type": "multi-input", "setting": "wc-emails", "id": "wc-product-removed-email", "title": "Product removed notification - Email", "content": "Template of the email sent to the customer after a product has been removed from the cart. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.", "help": "https://board.support/docs/#woocommerce-merge-fields", "value": [{"type": "number", "id": "wc-product-removed-email-coupon-expiration", "title": "Coupon expiration (days)"}, {"type": "number", "id": "wc-product-removed-email-coupon-discount", "title": "Coupon discount (%)"}, {"type": "text", "id": "wc-product-removed-email-subject", "title": "Subject"}, {"type": "textarea", "id": "wc-product-removed-email-content", "title": "Content"}]}, {"type": "multi-input", "id": "wc-abandoned-cart", "title": "Abandoned cart notification", "content": "Automatically send cart reminders to customers with products in their carts. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.", "help": "https://board.support/docs/#woocommerce-merge-fields", "value": [{"type": "select", "id": "wc-abandoned-cart-1", "title": "First reminder delay (hours)", "value": [["", "Disabled"], ["now", "No delay"], [1, 1], [2, 2], [3, 3], [4, 4], [5, 5], [6, 6], [7, 7], [8, 8], [9, 9], [10, 10], [11, 11], [12, 12], [13, 13], [14, 14], [15, 15], [16, 16], [17, 17], [18, 18], [19, 19], [20, 20], [21, 21], [22, 22], [23, 23], [24, 24], [25, 25], [26, 26], [27, 27], [28, 28], [29, 29], [30, 30], [31, 31], [32, 32], [33, 33], [34, 34], [35, 35], [36, 36], [37, 37], [38, 38], [39, 39], [39, 39], [40, 40], [41, 41], [42, 42], [43, 43], [44, 44], [45, 45], [46, 46], [47, 47], [48, 48], [49, 49], [50, 50], [51, 51], [52, 52], [53, 53], [54, 54], [55, 55], [56, 56], [57, 57], [58, 58], [59, 59], [60, 60], [61, 61], [62, 62], [63, 63], [64, 64], [65, 65], [66, 66], [67, 67], [68, 68], [69, 69], [70, 70], [71, 71]]}, {"type": "select", "id": "wc-abandoned-cart-2", "title": "Second reminder delay (hours)", "value": [["", "Disabled"], [1, 1], [2, 2], [3, 3], [4, 4], [5, 5], [6, 6], [7, 7], [8, 8], [9, 9], [10, 10], [11, 11], [12, 12], [13, 13], [14, 14], [15, 15], [16, 16], [17, 17], [18, 18], [19, 19], [20, 20], [21, 21], [22, 22], [23, 23], [24, 24], [25, 25], [26, 26], [27, 27], [28, 28], [29, 29], [30, 30], [31, 31], [32, 32], [33, 33], [34, 34], [35, 35], [36, 36], [37, 37], [38, 38], [39, 39], [39, 39], [40, 40], [41, 41], [42, 42], [43, 43], [44, 44], [45, 45], [46, 46], [47, 47], [48, 48], [49, 49], [50, 50], [51, 51], [52, 52], [53, 53], [54, 54], [55, 55], [56, 56], [57, 57], [58, 58], [59, 59], [60, 60], [61, 61], [62, 62], [63, 63], [64, 64], [65, 65], [66, 66], [67, 67], [68, 68], [69, 69], [70, 70], [71, 71]]}, {"type": "number", "id": "wc-abandoned-cart-coupon-expiration", "title": "Coupon expiration (days)"}, {"type": "number", "id": "wc-abandoned-cart-coupon-discount", "title": "Coupon discount (%)"}, {"type": "textarea", "id": "wc-abandoned-cart-message-1", "title": "First chat message"}, {"type": "textarea", "id": "wc-abandoned-cart-message-2", "title": "Second chat message"}, {"type": "checkbox", "id": "wc-abandoned-cart-notify-admin", "title": "Admin notifications"}]}, {"type": "multi-input", "setting": "wc-emails", "id": "wc-email-1", "title": "Abandoned cart notification - First email", "content": "Template of the first notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.", "help": "https://board.support/docs/#woocommerce-merge-fields", "value": [{"type": "text", "id": "wc-email-1-subject", "title": "Subject"}, {"type": "textarea", "id": "wc-email-1-content", "title": "Content"}]}, {"type": "multi-input", "setting": "wc-emails", "id": "wc-email-2", "title": "Abandoned cart notification - Second email", "content": "Template of the second notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.", "help": "https://board.support/docs/#woocommerce-merge-fields", "value": [{"type": "text", "id": "wc-email-2-subject", "title": "Subject"}, {"type": "textarea", "id": "wc-email-2-content", "title": "Content"}]}, {"type": "multi-input", "setting": "wc-emails", "id": "wc-email-admin", "title": "Abandoned cart notification - Admin email", "content": "Template of the admin notification email. You can use text, HTML, and the following merge field and more: {carts}. Enter the email you want to send notifications to in the email address field.", "help": "https://board.support/docs/#woocommerce-merge-fields", "value": [{"type": "text", "id": "wc-email-admin-email", "title": "Email address"}, {"type": "text", "id": "wc-email-admin-subject", "title": "Subject"}, {"type": "textarea", "id": "wc-email-admin-content", "title": "Content"}]}, {"type": "multi-input", "id": "wc-waiting-list", "title": "Waiting list", "content": "Send a message to allow customers to be notified when they can purchase a product they are interested in, but that is currently out of stock. You can use the following merge fields: {user_name}, {product_name}.", "help": "https://board.support/docs/#woocommerce-waiting-list", "value": [{"type": "checkbox", "id": "wc-waiting-list-active", "title": "Active"}, {"type": "textarea", "id": "wc-waiting-list-message", "title": "Message"}, {"type": "textarea", "id": "wc-waiting-list-message-email", "title": "Email request message"}, {"type": "textarea", "id": "wc-waiting-list-message-success", "title": "Success message"}, {"type": "text", "id": "wc-waiting-list-button-text", "title": "Confirm button text"}, {"type": "text", "id": "wc-waiting-list-button-text-cancel", "title": "Cancel button text"}]}, {"type": "multi-input", "setting": "wc-emails", "id": "wc-waiting-list-email", "title": "Waiting list - Em<PERSON>", "content": "Template of the waiting list notification email. You can use text, HTML, and the following merge field and more: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}.", "value": [{"type": "text", "id": "wc-waiting-list-email-subject", "title": "Subject"}, {"type": "textarea", "id": "wc-waiting-list-email-content", "title": "Content"}]}, {"type": "checkbox", "id": "wc-follow-up-cart", "title": "<PERSON>t follow up message", "content": "Show the follow up message when a visitor add an item to the cart. The message is sent only if the user has not provided an email yet."}, {"type": "multi-input", "id": "wc-tickets-products", "title": "Ticket products selector", "content": "Allow users to select a product on ticket creation.", "value": [{"type": "checkbox", "id": "wc-tickets-products-active", "title": "Active"}, {"type": "text", "id": "wc-tickets-products-label", "title": "Label"}, {"type": "text", "id": "wc-tickets-products-exclude", "title": "Exclude products"}]}, {"type": "multi-input", "id": "wc-urls", "title": "WooCommerce URLs", "content": "Enter the URLs of your shop.", "value": [{"type": "text", "id": "wc-urls-cart", "title": "<PERSON><PERSON>"}, {"type": "text", "id": "wc-urls-checkout", "title": "Checkout"}, {"type": "text", "id": "wc-urls-shop", "title": "Shop"}]}]
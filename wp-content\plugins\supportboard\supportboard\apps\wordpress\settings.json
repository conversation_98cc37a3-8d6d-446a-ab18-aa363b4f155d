[{"type": "checkbox", "id": "wp-multisite", "title": "Multisite routing", "content": "Automatically create a department for each website and route the conversations of each website to the right department. This setting requires a WordPress Multisite installation."}, {"type": "multi-input", "id": "wp-visibility", "title": "Visibility", "content": "Choose where to display the chat. Enter the values separated by commas.", "help": "https://board.support/docs/#wp-settings", "value": [{"type": "select", "id": "wp-visibility-type", "title": "Type", "value": [["show", "Show"], ["hide", "<PERSON>de"]]}, {"type": "text", "id": "wp-visibility-ids", "title": "Page IDs", "content": ""}, {"type": "text", "id": "wp-visibility-post-types", "title": "Post Type slugs", "content": ""}]}, {"type": "select", "id": "wp-users-system", "title": "User system", "content": "Choose which user system the front-end chat will use to register and log in users.", "help": "https://board.support/docs/#wp-sync", "value": [["sb", "Support Board"], ["wp", "WordPress"]]}, {"type": "button", "id": "wp-sync", "title": "Synchronize users", "content": "Sync all WordPress users with Support Board. Only new users will be imported.", "button-text": "Start importing", "button-url": "#"}, {"type": "select", "id": "wp-multilingual-plugin", "title": "Multilingual plugin", "content": "Set the multilingual plugin you're using, or leave it disabled if your site uses only one language.", "value": [["", "Disabled"], ["wpml", "WPML"], ["polylang", "<PERSON><PERSON><PERSON>"]]}, {"type": "select", "id": "wp-language", "title": "Force language", "content": "Force the chat to ignore the language preferences, and to use always the same language.", "value": [["", "Disabled"], ["ar", "Arabic"], ["bg", "Bulgarian"], ["br", "Brasilian <PERSON>"], ["cs", "Czech"], ["da", "Danish"], ["de", "German"], ["el", "Greek"], ["es", "Spanish"], ["et", "Estonian"], ["fa", "Persian"], ["fi", "Finnish"], ["fr", "French"], ["he", "Hebrew"], ["hi", "Hindi"], ["hr", "Croatian"], ["hu", "Hungarian"], ["am", "Armenian"], ["id", "Indonesian"], ["it", "Italian"], ["ja", "Japanese"], ["ka", "Georgian"], ["ko", "Korean"], ["mk", "Macedonian"], ["mn", "Mongolian"], ["my", "Burmese"], ["nl", "Dutch"], ["no", "Norwegian"], ["pl", "Polish"], ["pt", "Portuguese"], ["ro", "Romanian"], ["ru", "Russian"], ["sk", "Slovak"], ["sl", "Slovenian"], ["sq", "Albanian"], ["sr", "Serbian"], ["su", "Sundanese"], ["sv", "Swedish"], ["th", "Thai"], ["tr", "Turkish"], ["uk", "Ukrainian"], ["vi", "Vietnamese"], ["zh", "Chinese"], ["zt", "Traditional Chinese"]]}, {"type": "text", "id": "wp-roles", "title": "User roles", "content": "Add comma separated WordPress user roles. The Support Board administration area will be available for new roles, in addition to the default one: editor, administrator, author."}, {"type": "checkbox", "id": "wp-force-logout", "title": "Force log out", "content": "Force the loggout of Support Board agents if they are not logged in WordPress."}, {"type": "checkbox", "id": "wp-registration", "title": "WordPress registration", "content": "Create a WordPress user upon registration."}, {"type": "checkbox", "id": "wp-manual", "title": "Manual initialization", "content": "Disable auto-initialization of the chat widget. When this setting is active you must initialize the chat widget with a custom JavaScript API code written by you. If the chat doesn't appear and this setting is enabled, disable it."}]
<?php

/*
 * ==========================================================
 * WORDPRESS AI FUNCTIONS
 * ==========================================================
 *
 * WordPress OpenAI function calling implementation. © 2017-2025 board.support. All rights reserved.
 *
 */

// Define WordPress constant for function calling
if (!defined('SB_WORDPRESS_AI')) {
    define('SB_WORDPRESS_AI', true);
}

/*
 * ----------------------------------------------------------
 * WORDPRESS OPEN AI FUNCTION DEFINITIONS
 * ----------------------------------------------------------
 *
 * 1. Function definitions for OpenAI
 * 2. Function calling handler
 * 3. Function name checker
 *
 */

function sb_wordpress_open_ai_function() {
    return [
        [
            'type' => 'function',
            'function' => [
                'name' => 'sb-wordpress-contact-info',
                'description' => 'Show contact form when user specifically asks how to contact or reach the company. Trigger phrases: "how can i contact you", "how can i reach you", "how do i contact", "how do i reach", "contact information", "get in touch with you", "reach out to you". DO NOT call this function if a contact form has already been submitted successfully or if the user just received a "Thank you" message about their submission.',
                'parameters' => [
                    'type' => 'object',
                    'properties' => [
                        'department' => [
                            'type' => 'string',
                            'description' => 'The name of the department the user wants to contact. Example: "Sales", "Technical"'
                        ],
                    ],
                    'required' => []
                ]
            ]
        ]
    ];
}

function sb_wordpress_open_ai_function_calling($function_name, $id, $arguments) {
    custom_log("WORDPRESS AI FUNCTION CALLING: ".$function_name, $arguments);
    switch ($function_name) {
        case 'sb-wordpress-contact-info':
            // Extract department parameter, default to 'sales'
            $department = isset($arguments['department']) ? $arguments['department'] : 'sales';

            // Check if contact form was already submitted in this conversation
            $user = sb_get_active_user();
            if (!$user) {
                // custom_log("No active user found for contact form", null);
                // Show form anyway as fallback
                $rich_message = '[contact-form id="contact-form" name="true" email="true" phone="true" content="true" settings="department:' . $department . '" title="Contact Us" message="Please fill out the form below and we\'ll get back to you soon!" success="✅ Submitted!"]';
                return [
                    'sb-shortcode',
                    $id,
                    $rich_message
                ];
            }

            $conversation_id = sb_get_last_conversation_id_or_create($user['id'], 1);
            $data = sb_get_last_message($conversation_id,  false, sb_get_bot_id());
            // custom_log("Last message in conversation: " , $data);
            // Check for existing contact form submission in this conversation
            // $existing_submission = sb_check_contact_form_submission($conversation_id);

            $payload = json_decode($data['payload'], true);
            if (!isset($payload['rich-messages']) && !isset($payload['rich-messages']['contact-form']) || isset($data['message']) && strpos($data['message'], 'contact-form') !== false) {
                custom_log("Inside rich Last message in conversation: " , $data);
                $rich_message = 'Do not change this format! [contact-form id="contact-form" name="true" email="true" phone="true" content="true" settings="department:' . $department . '" title="Contact Us" message="Please fill out the form below and we\'ll get back to you soon!" success="✅ Submitted successfully!"]';
                return [
                    'sb-shortcode',
                    $id,
                    $rich_message
                ];
            }
            // custom_log("Contact form already submitted in conversation: " . $conversation_id, null);
            return [
                'payload',
                $id,
                'The User already send the form. response that We have received your form submission, our team will get back to you soon.',
                []
            ];

            // custom_log("Showing contact form for conversation: " . $conversation_id, null);
           
            break;

        default:
            return [
                'payload',
                $id,
                'Sorry, I could not process your request. Please try again.',
                []
            ];
    }
}

function sb_wordpress_open_ai_check_function_name($function_name) {
    $functions = [
        'sb-wordpress-contact-info',
        // Add more WordPress function names here as needed
    ];
    return in_array($function_name, $functions);
}



/*
 * -----------------------------------------------------------
 * CONTACT FORM STATE MANAGEMENT
 * -----------------------------------------------------------
 *
 * Functions to track contact form submissions and prevent re-showing
 *
 */

// function sb_check_contact_form_submission($conversation_id) {
//     // Handle invalid conversation ID or errors
//     if (!$conversation_id || is_object($conversation_id)) {
//         return false;
//     }

//     // Check if there's a contact form submission marker in this conversation
//     $query = 'SELECT COUNT(*) as count FROM sb_messages WHERE conversation_id = ' . sb_db_escape($conversation_id, true) . ' AND payload LIKE "%contact-form-submitted%"';
//     $result = sb_db_get($query);
//     return $result && isset($result['count']) && $result['count'] > 0;
// }

// function sb_mark_contact_form_submitted($conversation_id, $user_id) {
//     // Add a marker message to indicate contact form was submitted
//     $payload = ['event' => 'contact-form-submitted', 'timestamp' => time()];
//     $query = 'INSERT INTO sb_messages(user_id, message, creation_time, status_code, payload, conversation_id) VALUES (' .
//              sb_db_escape($user_id, true) . ', "", "' . sb_gmt_now() . '", 0, "' .
//              sb_db_json_escape($payload) . '", ' . sb_db_escape($conversation_id, true) . ')';
//     return sb_db_query($query);
// }

function sb_contact_form_submit($data) {
    // Extract form data from rich message payload
    $payload = sb_isset($data, 'payload', []);
    $rich_messages = sb_isset($payload, 'rich-messages', []);
    $user = sb_get_active_user();
    // Find the contact form data
    $form_data = null;
    foreach ($rich_messages as $message) {
        if (sb_isset($message, 'type') === 'contact-form') {
            $form_data = sb_isset($message, 'result', []);
            break;
        }
    }

    if (!$form_data) {
        return ['error' => 'No contact form data found'];
    }

    // Extract individual fields (now simple strings, not arrays)
    $name = sb_isset($form_data, 'name', '');
    $email = sb_isset($form_data, 'email', '');
    $phone = sb_isset($form_data, 'phone', '');
    $content = sb_isset($form_data, 'content', '');

    // Extract department from form data (passed from JavaScript via data-settings)
    $department = sb_isset($form_data, 'department', 'sales');

    // Get conversation_id for the "View Conversation" link
    $conversation_id = null;
    if ($user) {
        $conversation_id = sb_get_last_conversation_id_or_create($user['id'], 1);
    }

    // Validate required fields
    if (empty($name) || empty($email) || empty($content)) {
        return ['error' => 'Please fill in all required fields'];
    }

    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return ['error' => 'Please enter a valid email address'];
    }

    // Process the contact form directly
    // Prepare email content
    $subject = 'New Contact Form Submission from ' . $user['first_name'] . ' ' . $user['last_name'];
    $html_message = createContactFormEmailHTML($name, $email, $phone, $content, $department, $conversation_id);
    $available_departments = sb_get_departments();
    $department_id = findDepartmentIndex($available_departments, $department);
    $agents_ids = sb_get_agents_ids();
    $agents_details = [];  

    foreach ($agents_ids as $agent_id) {
        $agents_details[] = sb_get_user($agent_id);  
    }

    $selected_agents = getUsersByDepartment($agents_details, $department_id);

    $send_status = true;
    $failed_emails = 0;

    foreach($selected_agents as $agent) {
        // commenout to disable the send email, to fix the form first
        $current_status = sb_email_send($agent['email'], $subject, $html_message);
        if (!$current_status) {
            $send_status = false;
            $failed_emails++;
            // Continue sending to other agents even if one fails
        }
    }

    if ($send_status) {
        // Mark contact form as submitted in this conversation
        // $conversation_id = sb_get_last_conversation_id_or_create($user['id'], 1);
        // sb_mark_contact_form_submitted($conversation_id, $user['id']);

        // // Send a user message to indicate form submission (similar to chips pattern)
        // $submission_message = "I have submitted my contact information: Name: $name, Email: $email" .
        //                     ($phone ? ", Phone: $phone" : "") . ", Message: " . substr($content, 0, 100) .
        //                     (strlen($content) > 100 ? "..." : "");
        // sb_send_message(sb_get_bot_id(), $conversation_id, "We have recieved your request");

        // custom_log("Contact form marked as submitted for conversation: " . $conversation_id, null);

        return ['success' => true, 'message' => '✅ Form submitted successfully! We\'ll get back to you soon.'];
    } else {
        return ['error' => 'Failed to send emails: ' . $failed_emails];
    }
}


function getUsersByDepartment($users, $departmentIndex) {
    $departmentUsers = [];
    
    foreach ($users as $user) {
        if ($user['department'] == $departmentIndex) {
            $departmentUsers[] = $user;
        }
    }
    
    return $departmentUsers;
}

function findDepartmentIndex($departments, $message) {
    // Extract department-related keywords from the message
    $searchTerms = extractDepartmentKeywords($message);
    
    // If no keywords found, default to Sales (index 0)
    if (empty($searchTerms)) {
        return 0; // Assuming Sales is at index 0
    }
    
    $bestMatch = null;
    $highestSimilarity = 0;
    $threshold = 0.6; // Minimum similarity threshold (60%)
    
    foreach ($searchTerms as $searchTerm) {
        foreach ($departments as $index => $department) {
            $departmentName = strtolower(trim($department['name']));
            $searchName = strtolower(trim($searchTerm));
            
            // Exact match check first
            if ($departmentName === $searchName) {
                return $index;
            }
            
            // Check if search term is contained in department name
            if (strpos($departmentName, $searchName) !== false || strpos($searchName, $departmentName) !== false) {
                return $index;
            }
            
            // Calculate similarity using multiple methods
            $similarity = calculateSimilarity($departmentName, $searchName);
            
            if ($similarity > $highestSimilarity && $similarity >= $threshold) {
                $highestSimilarity = $similarity;
                $bestMatch = $index;
            }
        }
    }
    
    // Return best match if found, otherwise default to Sales (index 0)
    return $bestMatch !== null ? $bestMatch : 0;
}

function extractDepartmentKeywords($message) {
    // Parse JSON if it's a string
    if (is_string($message)) {
        $messageData = json_decode($message, true);
        if (json_last_error() === JSON_ERROR_NONE && isset($messageData['message'])) {
            $messageText = $messageData['message'];
        } else {
            $messageText = $message;
        }
    } else if (is_array($message) && isset($message['message'])) {
        $messageText = $message['message'];
    } else {
        $messageText = (string)$message;
    }
    
    // Common department-related keywords and their variations
    $departmentKeywords = [
        'sales' => ['sale', 'sales', 'selling', 'sell', 'purchase', 'buy'],
        'technical' => ['tech', 'technical', 'support', 'help', 'assistance', 'technology', 'developer'],
        'marketing' => ['marketing', 'market', 'promo', 'promotion', 'advertising'],
        'customer' => ['customer', 'client', 'service', 'cs'],
        'billing' => ['billing', 'bill', 'payment', 'invoice', 'account'],
        'hr' => ['hr', 'human', 'resources', 'recruitment', 'hiring']
    ];
    
    $messageText = strtolower($messageText);
    $foundKeywords = [];
    
    // Extract words from message (remove punctuation)
    $words = preg_split('/[^a-zA-Z0-9]+/', $messageText);
    $words = array_filter($words, function($word) {
        return strlen($word) > 2; // Only consider words longer than 2 characters
    });
    
    // Check each word against department keywords
    foreach ($words as $word) {
        foreach ($departmentKeywords as $dept => $variations) {
            foreach ($variations as $variation) {
                if (calculateSimilarity($word, $variation) >= 0.7) {
                    $foundKeywords[] = $dept;
                    break 2; // Break out of both loops
                }
            }
        }
    }
    
    // If no specific keywords found, try to extract any word that might be a department
    if (empty($foundKeywords)) {
        $foundKeywords = array_filter($words, function($word) {
            return strlen($word) >= 3; // Consider words 3+ characters as potential department names
        });
    }
    
    return array_unique($foundKeywords);
}

function calculateSimilarity($str1, $str2) {
    // Use multiple similarity algorithms and take the highest score
    
    // 1. Levenshtein distance similarity
    $maxLen = max(strlen($str1), strlen($str2));
    if ($maxLen == 0) return 1.0;
    $levenshteinSim = 1 - (levenshtein($str1, $str2) / $maxLen);
    
    // 2. Similar text similarity
    similar_text($str1, $str2, $similarTextSim);
    $similarTextSim = $similarTextSim / 100;
    
    // 3. Jaro-Winkler similarity (approximation)
    $jaroSim = calculateJaroSimilarity($str1, $str2);
    
    // 4. Substring similarity
    $substringSim = calculateSubstringSimilarity($str1, $str2);
    
    // Return the highest similarity score
    return max($levenshteinSim, $similarTextSim, $jaroSim, $substringSim);
}

function calculateJaroSimilarity($str1, $str2) {
    $len1 = strlen($str1);
    $len2 = strlen($str2);
    
    if ($len1 == 0 && $len2 == 0) return 1.0;
    if ($len1 == 0 || $len2 == 0) return 0.0;
    
    $matchWindow = max($len1, $len2) / 2 - 1;
    if ($matchWindow < 0) $matchWindow = 0;
    
    $str1Matches = array_fill(0, $len1, false);
    $str2Matches = array_fill(0, $len2, false);
    
    $matches = 0;
    for ($i = 0; $i < $len1; $i++) {
        $start = max(0, $i - $matchWindow);
        $end = min($i + $matchWindow + 1, $len2);
        
        for ($j = $start; $j < $end; $j++) {
            if ($str2Matches[$j] || $str1[$i] != $str2[$j]) continue;
            $str1Matches[$i] = $str2Matches[$j] = true;
            $matches++;
            break;
        }
    }
    
    if ($matches == 0) return 0.0;
    
    $transpositions = 0;
    $k = 0;
    for ($i = 0; $i < $len1; $i++) {
        if (!$str1Matches[$i]) continue;
        while (!$str2Matches[$k]) $k++;
        if ($str1[$i] != $str2[$k]) $transpositions++;
        $k++;
    }
    
    return ($matches / $len1 + $matches / $len2 + ($matches - $transpositions / 2) / $matches) / 3;
}

function calculateSubstringSimilarity($str1, $str2) {
    $shorter = strlen($str1) < strlen($str2) ? $str1 : $str2;
    $longer = strlen($str1) >= strlen($str2) ? $str1 : $str2;
    
    if (strlen($shorter) == 0) return 0.0;
    
    // Check if shorter string is contained in longer string
    if (strpos($longer, $shorter) !== false) {
        return strlen($shorter) / strlen($longer);
    }
    
    // Find longest common substring
    $longestCommon = 0;
    for ($i = 0; $i < strlen($shorter); $i++) {
        for ($j = $i + 1; $j <= strlen($shorter); $j++) {
            $substring = substr($shorter, $i, $j - $i);
            if (strpos($longer, $substring) !== false) {
                $longestCommon = max($longestCommon, strlen($substring));
            }
        }
    }
    
    return $longestCommon / strlen($shorter);
}

function createContactFormEmailHTML($name, $email, $phone, $content, $department = 'sales', $conversation_id = null) {
    date_default_timezone_set('Asia/Kuala_Lumpur');
    $submissionDate = date('l, F j, Y \a\t g:i A');

    $html = '
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Contact Form Submission</title>
        <style>
            body {
                margin: 0;
                padding: 0;
                font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
                background-color: #f5f5f5;
                color: #333;
            }
            .email-wrapper {
                max-width: 600px;
                margin: 40px auto;
                background-color: #ffffff;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                overflow: hidden;
            }
            .email-header {
                text-align: center;
                padding: 30px 20px 20px;
                background-color: #ffffff;
            }
            .email-header h1 {
                margin: 0;
                font-size: 24px;
                font-weight: 600;
                color: #333;
            }
            .intro-section {
                background: linear-gradient(135deg, #6b7c5c, #8a9b7a);
                color: #fff;
                padding: 25px;
                margin: 0 20px 20px;
                border-radius: 8px;
            }
            .intro-section p {
                margin: 0 0 10px 0;
                line-height: 1.5;
                font-size: 14px;
            }
            .intro-section .greeting {
                font-weight: bold;
                margin-bottom: 15px;
            }
            .contact-details {
                padding: 0 20px;
                margin-bottom: 20px;
            }
            .info-table {
                width: 100%;
                border-collapse: collapse;
                background-color: #ffffff;
            }
            .info-table tr {
                border-bottom: 1px solid #e0e0e0;
            }
            .info-table tr:last-child {
                border-bottom: none;
            }
            .info-table td {
                padding: 12px 0;
                vertical-align: top;
            }
            .info-table .label {
                font-weight: 500;
                width: 150px;
                color: #666;
                font-size: 14px;
            }
            .info-table .value {
                color: #333;
                font-size: 14px;
            }
            .info-table .value a {
                color: #333;
                text-decoration: none;
                font-weight: 500;
            }
            .info-table .value a:hover {
                text-decoration: underline;
            }
            .message-section {
                background: linear-gradient(135deg, #6b7c5c, #8a9b7a);
                color: #fff;
                padding: 25px;
                margin: 20px 20px;
                border-radius: 8px;
            }
            .message-section h3 {
                margin: 0 0 15px 0;
                font-size: 16px;
                font-weight: 600;
            }
            .message-box {
                background-color: rgba(255,255,255,0.1);
                padding: 15px;
                border-radius: 6px;
                white-space: pre-wrap;
                font-size: 14px;
                line-height: 1.5;
                border-left: 3px solid rgba(255,255,255,0.3);
            }
            .contact-info {
                background: linear-gradient(135deg, #6b7c5c, #8a9b7a);
                color: #fff;
                padding: 25px;
                margin: 0 20px 20px;
                border-radius: 8px;
            }
            .contact-info h3 {
                margin: 0 0 10px 0;
                font-size: 16px;
                font-weight: 600;
            }
            .contact-info p {
                margin: 0 0 5px 0;
                font-size: 14px;
                line-height: 1.4;
            }
            .email-footer {
                text-align: center;
                padding: 20px;
                font-size: 12px;
                color: #999;
                background-color: #f8f9fa;
            }
            .action-button {
                text-align: center;
                margin: 20px 0;
            }
            .action-button a {
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                padding: 12px 25px;
                text-decoration: none;
                border-radius: 6px;
                font-weight: 600;
                font-size: 14px;
                display: inline-block;
            }
            .action-button a:hover {
                background: linear-gradient(135deg, #218838, #1da88a);
            }
        </style>
    </head>
    <body>
        <div class="email-wrapper">
            <div class="email-header">
                <h1>Contact Form Submission</h1>
            </div>
            
            <div class="intro-section">
                <p class="greeting">Dear Team,</p>
                <p>Please find below a summary of your recent contact form submission. For a detailed breakdown of the inquiry, please refer to the attached message details.</p>
            </div>

            <div class="contact-details">
                <table class="info-table">
                    <tr>
                        <td class="label">Contact Name</td>
                        <td class="value">' . htmlspecialchars($name) . '</td>
                    </tr>
                    <tr>
                        <td class="label">Email Address</td>
                        <td class="value"><a href="mailto:' . htmlspecialchars($email) . '">' . htmlspecialchars($email) . '</a></td>
                    </tr>';

    if (!empty($phone)) {
        $html .= '
                    <tr>
                        <td class="label">Phone Number</td>
                        <td class="value"><a href="tel:' . htmlspecialchars($phone) . '">' . htmlspecialchars($phone) . '</a></td>
                    </tr>';
    }

    $html .= '
                    <tr>
                        <td class="label">Submission Date</td>
                        <td class="value">' . $submissionDate . '</td>
                    </tr>
                </table>
            </div>

            <div class="message-section">
                <h3>Message Content</h3>
                <div class="message-box">' . nl2br(htmlspecialchars($content)) . '</div>
            </div>

            <div class="contact-info">
                <h3>If you have any questions or require further clarification, please do not hesitate to contact us.</h3>
                <p>Thank you for your business. Sincerely, The Team</p>
            </div>

            <div class="action-button">
                <a href="mailto:' . htmlspecialchars($email) . '">Reply to Customer</a>';

    // Add "View Conversation" button if conversation_id is provided
    if ($conversation_id) {
        $conversation_link = (defined('SB_CLOUD') && SB_CLOUD ? CLOUD_URL : SB_URL . '/admin.php') . '?conversation=' . $conversation_id;
        $html .= '
                <a href="' . $conversation_link . '" style="background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin-left: 10px; display: inline-block;">View Conversation</a>';
    }

    $html .= '
            </div>

            <div class="email-footer">
                '.$conversation_link.'
                This is an automated email from your website system.<br>
                Please do not reply directly to this message.
            </div>
        </div>
    </body>
    </html>';

    return $html;
}
?>

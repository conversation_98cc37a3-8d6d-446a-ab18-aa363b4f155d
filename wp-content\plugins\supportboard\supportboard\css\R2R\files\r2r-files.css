/*
 * ==========================================================
 * R2R FILES STYLING
 * ==========================================================
 *
 * CSS for the R2R Files module.
 * All IDs and classes are prefixed with 'r2r-files-' for module-specific isolation.
 *
 */

/* --- R2R FILES MODULE: SHARED COMPONENTS --- */

/* Search bar container and elements */
.r2r-files-search-container {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.r2r-files-search-input-wrapper {
  position: relative;
  flex-grow: 1;
}

/* Search input - ensure text is always black for better readability */
#r2r-files-search-input {
  width: 100%;
  padding: 8px 30px 8px 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  box-sizing: border-box;
  font-size: 14px;
  color: #022f74 !important; /* Force black text color */
  -webkit-text-fill-color: #022f74 !important; /* For WebKit browsers */
}

/* Ensure placeholder text has sufficient contrast */
#r2r-files-search-input::placeholder {
  color: #666666 !important;
  -webkit-text-fill-color: #666666 !important;
}

/* Focus state */
#r2r-files-search-input:focus {
  border-color: #1da1f2;
  box-shadow: 0 0 0 2px rgba(29, 161, 242, 0.3);
  outline: none;
}

#r2r-files-clear-search {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #999;
  font-size: 16px;
  cursor: pointer;
  line-height: 1;
}

#r2r-files-clear-search:hover {
  color: #d32f2f;
}

#r2r-files-search-button {
  padding: 6px 12px;
  background-color: #1da1f2;
  border: 1px solid #1da1f2;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  color: white;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

#r2r-files-search-button:hover {
  background-color: #0c8ad6;
  border-color: #0c8ad6;
}

/* Status message for search (visible for both views depending on context) */
#r2r-files-search-status {
  margin-bottom: 10px;
  padding: 8px 12px;
  background-color: #e3f2fd;
  border-left: 4px solid #1da1f2;
  border-radius: 0 4px 4px 0;
  font-size: 14px;
  display: none;
  color: #356dc3 !important; /* Dark blue text for better readability */
  font-weight: 500; /* Slightly bolder text */
}

/* Delete button (always present, but disabled in search view via JS) */
#sb-r2r-delete-files.sb-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Pagination - shared between list and search views */
/* #r2r-files-pagination {
  margin-top: 15px;
  text-align: center;
}

.r2r-files-pagination-controls {
  display: inline-flex;
  gap: 5px;
  align-items: center;
}

.r2r-files-pagination-btn {
  padding: 5px 10px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.r2r-files-pagination-btn:hover {
  background-color: #e0e0e0;
}

.r2r-files-pagination-btn.r2r-files-current-page {
  background-color: #1da1f2;
  color: white;
  border-color: #1da1f2;
}

.r2r-files-pagination-btn.r2r-files-disabled {
  opacity: 0.5;
  cursor: not-allowed;
} */

/* Common display states for elements like table cells */
.r2r-files-loading {
  /* Used for table loading rows */
  text-align: center;
  padding: 20px;
  color: #666;
}

.r2r-files-error {
  text-align: center;
  padding: 20px;
  color: #d32f2f;
  background-color: #ffebee;
}

.r2r-files-empty {
  /* Used for "no files found" message */
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
}

/* Table checkbox */
.r2r-files-checkbox {
  cursor: pointer;
}

/* Table links (e.g., source file link) */
#r2r-files-table-uploaded td a {
  color: #1da1f2;
  text-decoration: none;
  max-width: 250px;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

#r2r-files-table-uploaded td a:hover {
  text-decoration: underline;
}

/* Responsive adjustments for shared components */
@media screen and (max-width: 480px) {
  .r2r-files-search-container {
    flex-direction: column;
    align-items: stretch;
  }

  #r2r-files-search-button {
    width: 100%;
  }
}

/* --- R2R FILES MODULE: LIST VIEW COMPONENTS --- */

#r2r-files-table-uploaded {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 15px;
}

#r2r-files-table-uploaded th,
#r2r-files-table-uploaded td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

#r2r-files-table-uploaded th {
  background-color: #f9f9f9;
  font-weight: bold;
}

#r2r-files-table-uploaded tr:last-child td {
  border-bottom: none;
}

#r2r-files-table-uploaded tr:hover {
  background-color: #f5f5f5;
}

/* Pagination */
#r2r-files-pagination {
  margin-top: 15px;
  text-align: center;
}

.r2r-files-pagination-controls {
  display: inline-flex;
  gap: 5px;
  align-items: center;
}

.r2r-files-pagination-btn {
  padding: 5px 10px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.r2r-files-pagination-btn:hover {
  background-color: #e0e0e0;
}

.r2r-files-pagination-btn.r2r-files-current-page {
  background-color: #1da1f2;
  color: white;
  border-color: #1da1f2;
}

.r2r-files-pagination-btn.r2r-files-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Common display states for elements like table cells */
.r2r-files-loading {
  /* Used for table loading rows */
  text-align: center;
  padding: 20px;
  color: #666;
}

.r2r-files-error {
  text-align: center;
  padding: 20px;
  color: #d32f2f;
  background-color: #ffebee;
}

.r2r-files-empty {
  /* Used for "no files found" message */
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
}

/* Table checkbox */
.r2r-files-checkbox {
  cursor: pointer;
}

/* Table links (e.g., source file link) */
#r2r-files-table-uploaded td a {
  color: #1da1f2;
  text-decoration: none;
  max-width: 250px;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

#r2r-files-table-uploaded td a:hover {
  text-decoration: underline;
}

/* Responsive adjustments for list view */
@media screen and (max-width: 768px) {
  #r2r-files-table-uploaded th:nth-child(3),
  #r2r-files-table-uploaded td:nth-child(3),
  #r2r-files-table-uploaded th:nth-child(5),
  #r2r-files-table-uploaded td:nth-child(5) {
    display: none;
  }
}

@media screen and (max-width: 480px) {
  #r2r-files-table-uploaded th:nth-child(4),
  #r2r-files-table-uploaded td:nth-child(4) {
    display: none;
  }
}

/* --- R2R FILES MODULE: SEARCH VIEW COMPONENTS --- */

/* Utility classes for toggling search card view visibility */
.r2r-files-card-view-hidden {
  display: none;
}

.r2r-files-card-view-active {
  display: block;
  height: 600px; /* Set a fixed height */
  max-height: 600px; /* Ensure it doesn't grow beyond this */
  overflow-y: auto; /* Enable vertical scrolling */
  border: 1px solid #eee; /* Visual indicator */
  padding: 10px;
  position: relative;
}

/* Make sure the inner container doesn't collapse */
#r2r-cards-inner-container {
  min-height: 50px;
}

/* Base card container */
.r2r-files-card-set {
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  background: white;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.r2r-files-card-set:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

/* Card header */
.r2r-files-header-card {
  background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
  color: white;
  padding: 25px;
  position: relative;
  overflow: hidden;
}

.r2r-files-header-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
  pointer-events: none;
}

.r2r-files-header-content {
  position: relative;
  z-index: 2;
}

.r2r-files-document-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 12px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  color: white;
}

.r2r-files-document-meta {
  display: flex;
  gap: 20px;
  opacity: 0.9;
  font-size: 0.95rem;
}

.r2r-files-document-type {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.r2r-files-document-score {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.r2r-files-document-source-badge {
  background: rgba(255, 255, 255, 0.15);
  padding: 4px 12px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.r2r-files-document-source-badge a {
  color: white;
  text-decoration: none;
}

.r2r-files-document-source-badge a:hover {
  text-decoration: underline;
}

.r2r-files-source-icon:before {
  content: "\e904";
  font-family: "Support Board Icons";
  font-size: 14px;
  color: white;
}

.r2r-files-document-words {
  background: rgba(255, 255, 255, 0.15);
  padding: 4px 12px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

/* Collapse/Expand button */
.r2r-files-collapse-btn {
  position: absolute;
  top: 50%;
  right: 25px;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  z-index: 3;
}

.r2r-files-collapse-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-50%) scale(1.05);
}

.r2r-files-collapse-btn.r2r-files-collapsed {
  background: rgba(46, 204, 113, 0.3);
  border-color: rgba(46, 204, 113, 0.5);
}

/* Card body (collapsible content) */
.r2r-files-body-card {
  background: white;
  padding: 30px;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.r2r-files-body-card.r2r-files-collapsed {
  max-height: 0;
  padding: 0 30px;
  opacity: 0;
}

.r2r-files-body-card.r2r-files-expanded {
  max-height: 1000px; /* Sufficiently large to reveal content */
  opacity: 1;
}

.r2r-files-content-text {
  line-height: 1.7;
  color: #444;
  font-size: 1rem;
}

.r2r-files-content-text p {
  margin-bottom: 15px;
}

/* End of results message styling */
.r2r-files-end-results {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px 20px;
  margin: 20px 0;
  background: linear-gradient(to right, #f5f7fa, #e4e8f0);
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  text-align: center;
  opacity: 0; /* Start invisible for fade-in effect */
  transition: opacity 0.6s ease-out;
}

.r2r-files-end-results.r2r-files-fade-in {
  opacity: 1; /* Fade in when class is added */
}

.r2r-files-end-results-icon {
  background-color: #4caf50;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-right: 15px;
  box-shadow: 0 3px 6px rgba(76, 175, 80, 0.3);
}

.r2r-files-end-results-text {
  color: #555;
}

.r2r-files-end-results-text p {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 5px 0;
}

.r2r-files-end-results-text small {
  font-size: 14px;
  opacity: 0.7;
}

/* Fade-in animation for cards (keep this for general use) */
.r2r-files-fade-in {
  animation: r2r-files-fadeIn 0.6s ease-out;
}

@keyframes r2r-files-fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments for search cards */
@media (max-width: 768px) {
  .r2r-files-header-card {
    padding: 20px;
  }

  .r2r-files-document-title {
    font-size: 1.4rem;
    margin-bottom: 10px;
  }

  .r2r-files-document-meta {
    flex-direction: column;
    gap: 8px;
  }

  .r2r-files-collapse-btn {
    position: static;
    transform: none;
    margin-top: 15px;
    align-self: flex-start;
  }

  .r2r-files-collapse-btn:hover {
    transform: scale(1.05);
  }
}

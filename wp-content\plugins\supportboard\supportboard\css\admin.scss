/*
* 
* ==========================================================
* ADMIN.SCSS
* ==========================================================
*
* Main style file of the administration page. Written in SCSS. 
*
*/

@import "shared.scss";

@font-face {
    font-family: "Support Board Font";
    src: url("../media/fonts/bold.woff2") format("woff2"), url("../media/fonts/bold.woff") format("woff");
    font-weight: 600;
    font-style: normal;
}

@keyframes sb-fade-out {
    0% {
        opacity: 1;
    }

    90% {
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}

@keyframes sb-typing {
    0% {
        width: 0;
    }

    100% {
        width: 15px;
    }
}

@keyframes sb-fade-in-out {
    0% {
        opacity: 0;
    }

    20% {
        opacity: 1;
    }

    80% {
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}

.sb-fade-out {
    opacity: 0;
    animation: sb-fade-out .5s;
}

/* 
 *
 * -----------------------------------------------------------
 * GLOBAL
 * -----------------------------------------------------------
 *
*/

body, html {
    margin: 0;
    padding: 0;
    min-height: 100%;
    background: rgb(245, 247, 250);
}

.sb-admin, .sb-admin input, .sb-admin textarea, .sb-admin select, .sb-title, .daterangepicker, .ct__content {
    font-family: "Support Board Font", "Helvetica Neue", "Apple Color Emoji", Helvetica, Arial, sans-serif;
    color: $color-black;
}

.sb-admin, .sb-admin * {
    box-sizing: content-box;
}

.sb-admin ul {
    padding: 0;
    margin: 0;
    list-style: none;
}

.sb-admin li {
    margin: 0;
}

.sb-title {
    font-size: 17px;
    font-weight: 600;

    &.sb-title-search {
        position: relative;

        .sb-search-btn {
            position: absolute;
            right: 0;
            top: -12px;
            width: 30px;
        }
    }
}

.cp-color-picker {
    z-index: 9;
}

.sb-tooltip > div, .sb-admin > .sb-header > .sb-admin-nav > div > a > span, .sb-admin > .sb-header > .sb-admin-nav-right .sb-account > div,
.sb-lightbox, .sb-color-palette ul, .sb-flows-blocks-nav-box .sb-close {
    background: $white;
    border-radius: 4px;
    padding: 10px 0;
    box-shadow: $box-shadow;
    z-index: 99995;
    list-style: none;
}

hr {
    background: #e6e6e6;
    height: 1px;
    border: none;
    margin: 30px 0;
}

.sb-admin-nav-right [data-value="status"], td.sb-online, td.sb-offline {
    position: relative;

    &:before {
        content: "";
        width: 8px;
        height: 8px;
        position: absolute;
        border-radius: 50%;
        margin-top: -4px;
        top: 50%;
        right: 3px;
        background: rgb(19, 202, 126);
    }

    &:after {
        content: "";
        width: 14px;
        height: 14px;
        position: absolute;
        border-radius: 50%;
        margin-top: -7px;
        top: 50%;
        right: 0;
        background: rgba(19, 202, 126, .2);
    }

    &.sb-offline:after {
        display: none !important;
    }
}

td.sb-offline:after {
    display: none !important;
}

.sb-offline:before {
    background: #d6d6d6 !important;
}

.sb-flex {
    display: flex;
    align-items: center;
    align-content: center;

    .sb-btn + .sb-btn, .sb-btn + .sb-btn-icon {
        margin-left: 15px;
    }
}

.sb-menu-wide ul, .sb-tab > .sb-nav > ul {
    display: flex;

    li {
        font-weight: 600;
        color: $color-black;
        font-size: 15px;
        line-height: 38px;
        margin: 0 30px 0 0;
        transition: $transition;
        letter-spacing: 0.3px;
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        user-select: none;

        span {
            font-weight: 400;
            opacity: 0.7;
            font-size: 12px;
            display: inline-block;
            letter-spacing: 1px;
            transform: translateY(-2px);
        }
    }

    li.sb-active, li:hover {
        color: $color-blue;
    }

    li.sb-active {
        border-bottom: 2px solid $color-blue;
        transition: none;
    }

    li.sb-tab-nav-title {
        font-size: 16px;
        font-weight: 600;
        cursor: default;
        color: $color-black;
    }

    li + li.sb-tab-nav-title {
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px solid #e1e1e1;
    }
}

.sb-menu-wide {
    > div {
        display: none;
    }
}

.sb-menu li {
    ul {
        position: absolute;
        left: 100%;
        margin-top: -36px;
        display: none;

        li {
            color: $color-black;
        }
    }

    &:hover ul {
        display: block;
        animation: sb-fade-animation 0.4s;
    }
}

.sb-tab {
    display: flex;

    > div {

        > div, .sb-nav > div {
            display: none;

            &.sb-active, &.sb-menu-wide {
                display: block;
            }
        }

        &.sb-nav-only + div > div {
            display: block;

            &.sb-tab {
                display: flex;
            }
        }
    }

    > .sb-nav {
        min-width: 200px;
        border-right: 1px solid $border-color;
        padding: 15px 20px 20px 20px;

        > ul {
            display: block;
            max-width: 200px;

            li {
                margin-right: 0;

                &.sb-active {
                    border-bottom: none;
                }
            }
        }
    }

    > .sb-nav.sb-scroll-area {
        border-right: none;
    }

    .sb-content {
        width: 100%;
        padding: 20px 15px 30px 30px;

        > .sb-active {
            animation: sb-fade-animation 0.4s;
        }

        > div:last-child {
            margin-bottom: 0;
        }
    }

    h2 {
        font-size: 15px;
        letter-spacing: .3px;
        line-height: 27px;
        margin: 2px 0 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    > .sb-nav > .sb-menu-wide {
        margin-bottom: 15px;
    }
}

#sb-table-chatbot-files, #sb-table-chatbot-website {
    &:empty, &:empty + hr {
        display: none;
    }
}

#sb-table-chatbot-files td:last-child i, #sb-table-chatbot-website td:last-child i, .sb-inner-tab .sb-nav > ul li i {
    width: 30px;
    height: 30px;
    line-height: 35px;
    font-size: 15px;
    text-align: center;
    color: $color-red;
    position: absolute;
    display: none;
    cursor: pointer;
    right: -5px;
    top: 4px;
}

#sb-table-chatbot-files tr:hover td:last-child i, #sb-table-chatbot-website tr:hover td:last-child i, .sb-inner-tab .sb-nav > ul li:hover i {
    display: block;
    animation: sb-fade-animation 0.5s;
}

#sb-table-chatbot-files td:last-child i.sb-loading, #sb-table-chatbot-website td:last-child i.sb-loading, .sb-inner-tab .sb-nav > ul li i.sb-loading {
    display: block;
}

.sb-inner-tab {
    .sb-nav {
        > ul {
            li {
                position: relative;
                padding-right: 25px;
                font-weight: 500;
                font-size: 13px;
                color: $color-gray;

                span {
                    position: absolute;
                    right: 32px;
                    top: 2px;
                    padding-left: 10px;
                    background: $white;
                    opacity: 1;
                }

                i {
                    background: $white;
                }
            }

            > .sb-no-results {
                opacity: 0.6;
                font-weight: 400;
                font-size: 13px;
                cursor: default;
                color: $color-black !important;
                cursor: default !important;
            }
        }

        .sb-btn {
            display: inline-block;
            margin-top: 15px;
        }
    }
}

.sb-menu-mobile {

    > ul {
        display: flex;

        li a {
            margin: 0 3px;
        }

        li:empty {
            display: none;
        }

        .sb-loading:before {
            line-height: 30px;
        }
    }

    > i, .sb-mobile {
        display: none;
    }

    [data-value="panel"]:not(.sb-active) {
        display: none;
    }
}

.sb-list-thumbs li {
    display: flex;
    cursor: pointer;
    transition: $transition;
    padding: 5px 15px;

    > div.sb-image {
        background-size: cover;
        width: 40px;
        min-width: 40px;
        height: 40px;
        margin-right: 15px;
        background-position: center center;
        border: 1px solid $border-color;
    }

    > div {
        min-width: 0;

        span {
            display: block;
            font-weight: 500;
            font-size: 13px;
            line-height: 20px;
            letter-spacing: .2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: left;
            transition: $transition;
        }

        span:last-child {
            color: $color-gray;
            font-weight: 400;
        }
    }

    &:hover {
        background-color: $background-gray;

        span:first-child {
            color: $color-blue;
        }
    }
}

.sb-table {
    margin: 0 0 20px 0;
    width: 100%;
    max-width: 100%;
    border-collapse: collapse;
    table-layout: fixed;

    th {
        white-space: nowrap;
        padding: 9px 15px;
        text-align: left;
        border-bottom: 1px solid $border-color;
        font-size: 15px;
        line-height: 20px;
        font-weight: 600;
        color: rgb(115, 115, 118);
    }

    td {
        white-space: nowrap;
        padding: 9px 15px;
        text-align: left;
        border-bottom: 1px solid $border-color;
        color: $color-black;
        font-size: 14px;
        transition: $transition;

        a {
            position: relative;
            text-decoration: none;
            color: $color-black;
        }

        .sb-profile span {
            font-size: 15px;
            padding-top: 2px;
        }

        &.sb-td-ut {
            text-transform: uppercase;
            font-size: 13px;
        }

        &.sb-td-language {
            text-transform: uppercase;
        }
    }

    tr:last-child td {
        border-bottom: none;
    }

    input[type="checkbox"] {
        background: $white;
        clear: none;
        cursor: pointer;
        display: inline-block;
        line-height: 0;
        outline: 0;
        padding: 0;
        margin: 8px 0;
        text-align: center;
        vertical-align: middle;
        width: 20px;
        height: 20px;
        border: 1px solid $border-color;
        border-radius: 3px;
        outline: none;
        box-shadow: none;
        -webkit-appearance: none;
    }

    input[type="checkbox"]:checked,
    input[type="checkbox"]:hover {
        border-color: $color-blue;
    }

    input[type="checkbox"]:checked:before {
        content: "\77";
        font-family: "Support Board Icons" !important;
        font-style: normal !important;
        font-weight: normal !important;
        font-variant: normal !important;
        text-transform: none !important;
        line-height: 22px;
        font-size: 10px;
        margin: 0;
        width: 100%;
        height: 100%;
        color: $color-blue;
    }

    tr:hover td {
        cursor: pointer;
        background-color: $background-gray;
    }

    .sb-no-results {
        position: absolute;
        white-space: nowrap;
        margin: 30px 15px;
    }

    tr + p {
        display: none;
    }

    &.sb-loading {
        text-indent: unset;
        height: 175px;

        tbody:empty, thead {
            display: none;
        }
    }
}

.sb-profile {
    position: relative;
    color: $color-black;
    line-height: 30px;
    padding-left: 45px;
    text-decoration: none;
    display: flex;
    align-items: center;

    img {
        position: absolute;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
    }

    > span {
        font-size: 16px;
        font-weight: 600;
        letter-spacing: 0.3px;
    }
}

.sb-profile-list > ul > li > span, .sb-panel-details .sb-title {
    font-weight: 500;
    padding-right: 10px;
    font-size: 11px;
    letter-spacing: .3px;
    color: #88969e;
    text-transform: uppercase;
    transition: $transition;

    span {
        color: $color-black;
    }
}

.sb-profile-list > ul > li:hover > span, .sb-panel-details .sb-split > div:hover > .sb-title {
    color: #2a3a43;
}

.sb-profile-list > ul > li, .sb-panel-details .sb-list-items > div, .sb-panel-details .sb-list-items > a {
    position: relative;
    font-size: 13px;
    line-height: 27px;
    letter-spacing: 0.3px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.sb-list-items:not(:empty) + .sb-btn {
    margin-top: 15px;
}

.sb-profile-list {
    padding: 0;

    > ul {
        margin-top: 15px;

        > li {
            padding-left: 30px;

            .sb-icon, > img {
                position: absolute;
                left: 0;
                top: 7px;
                font-size: 14px;
                color: $color-dark-blue;

                &:before {
                    width: 16px;
                    height: 16px;
                    opacity: 0.8;
                    position: absolute;
                }
            }

            > img {
                width: 15px;
                height: 15px;
            }

            > span {
                position: relative;
                top: 1px;

                &:empty {
                    display: none;
                }
            }

            label {
                font-weight: 400;
                margin: 0;
            }

            &:first-child span {
                text-transform: uppercase;
            }
        }
    }

    [data-id="wp-id"]:hover,
    [data-id="wp-id"]:hover label,
    [data-id="conversation-source"]:hover,
    [data-id="conversation-source"]:hover label,
    [data-id="location"]:hover,
    [data-id="location"]:hover label,
    [data-id="timezone"]:hover,
    [data-id="timezone"]:hover label,
    [data-id="current_url"]:hover,
    [data-id="current_url"]:hover label,
    [data-id="envato-purchase-code"]:hover,
    [data-id="envato-purchase-code"]:hover label {
        color: $color-blue;
        cursor: pointer;
    }

    [data-id="browser_language"] img, [data-id="language"] img {
        border-radius: 50%;
    }

    [data-id="country_code"] img {
        border-radius: 2px;
        height: 10px;
        margin-top: 3px !important;
    }

    [data-id="language"] label {
        text-transform: uppercase;
    }

    [data-id="rating"] {
        .sb-icon-like {
            color: $color-green;
        }

        .sb-icon-dislike {
            color: $color-red;
        }
    }
}

.sb-panel-details {
    border-top: 1px solid $border-color;
    padding-bottom: 15px;
    position: relative;
    overflow: hidden;

    h3 {
        justify-content: space-between;

        img {
            height: 11px;
            margin-right: 5px;
        }

        .sb-select {
            padding: 0 !important;

            p {
                opacity: .7;
            }

            p, li {
                font-weight: 400;
            }

            ul {
                right: -2px;
                left: auto;
            }

            &:hover p {
                opacity: 1;
            }
        }
    }

    > div {
        position: relative;

        > .sb-title {
            margin: 15px 15px 0 15px;

            > i {
                position: absolute;
                right: 12px;
                font-size: 14px;
                line-height: 20px;
                width: 20px;
                height: 20px;
                cursor: pointer;
                text-align: center;

                &:hover {
                    color: $color-blue;
                }
            }

            & + .sb-list-items {
                margin: 5px 0 -3px 0;
            }
        }

        .sb-list-items:last-child > p {
            margin-bottom: 5px;
        }
    }

    .sb-split {
        margin: 0 15px;
        display: flex;
        justify-content: space-between;

        > div {
            width: 50%;

            & + div {
                border-left: 1px solid $border-color;
                padding-left: 15px;
            }

            span {
                display: block;
                margin-top: 5px;
                font-size: 13px;
            }
        }
    }

    .sb-list-names {
        margin: -5px 0;

        > div {
            padding: 5px 15px;
            display: flex;
            justify-content: space-between;
            overflow: hidden;

            > span {
                white-space: nowrap;
                font-size: 13px;
                font-weight: 500;
            }

            > span:last-child {
                opacity: .6;
                font-weight: 400;
            }

            &.sb-expired > span:last-child {
                opacity: 1;
                color: $color-red;
            }
        }
    }

    .sb-list-links {
        > a {
            padding: 0 15px;
            cursor: pointer;
            display: block;
            text-decoration: none;
            color: $color-black;
            transition: $transition;

            > span span {
                display: inline-block;
            }

            > span:last-child {
                color: #7e8c98;
            }

            i {
                position: absolute;
                right: 0;
                width: 27px;
                height: 25px;
                line-height: 27px;
                text-align: left;
                font-size: 13px;
                opacity: .5;
                z-index: 2;

                &.sb-icon-file {
                    font-size: 16px;
                    line-height: 23px;

                    &:hover {
                        color: $color-blue;
                    }
                }

                &:hover {
                    opacity: 1;
                    color: $color-red;
                }

                &.sb-loading {
                    opacity: 1;
                    right: 6px;
                    top: 4px;

                    &:before {
                        width: 22px;
                        height: 22px;
                        line-height: 22px;
                        font-size: 13px;
                    }
                }
            }

            &:hover {
                color: $color-blue;
            }
        }

        > p {
            font-size: 13px;
            padding: 0 0 0 15px;
            margin: 0;
            opacity: .5;
        }
    }

    .sb-list-icon > a {
        padding-right: 40px;
    }

    .sb-accordion {

        > div {
            position: relative;
            overflow: hidden;


            > span {
                padding: 0 15px;
                cursor: pointer;
                white-space: nowrap;
                display: block;
                overflow: hidden;
                text-overflow: ellipsis;
                transition: $transition;

                > span:first-child {
                    opacity: .6;
                }

                > span + span {
                    margin-left: 15px;
                }

                > a {
                    position: absolute;
                    right: 15px;
                    line-height: 30px;
                    text-decoration: none;
                    font-weight: normal;
                    font-style: normal;
                    color: $color-black;
                    transition: $transition;

                    &:hover {
                        color: $color-blue;
                    }
                }

                &:hover {
                    color: $color-blue;
                }
            }

            > div {
                height: 0;
                overflow: hidden;

                > a {
                    display: block;
                    text-decoration: none;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    color: $color-black;
                    transition: $transition;

                    span:first-child {
                        opacity: .6;
                    }

                    span:last-child {
                        opacity: 1;
                    }

                    &:hover {
                        color: $color-blue;
                    }
                }
            }

            &.sb-active > div {
                padding: 15px;
                height: auto;
            }

            .sb-multiline {
                line-height: 20px;
                padding: 5px 0;
            }
        }
    }

    > i {
        position: absolute;
        right: 12px;
        top: 22px;
        font-size: 13px;
        cursor: pointer;
        opacity: .7;
        transition: $transition;

        &:hover {
            opacity: 1;
            color: $color-blue;
        }

        &.sb-loading {
            position: static;
            height: 60px;
            display: block;
            opacity: 1;
        }

        & + .sb-list-names {
            padding-top: 15px;
        }
    }

    > .sb-title {
        padding: 15px 15px 0 15px;
        display: block;

        .sb-btn-text {
            margin-left: 15px;
            float: right;
            text-transform: none;

            i {
                margin-right: 5px;
            }
        }
    }

    .sb-btn {
        background-color: $white;
        color: $color-gray;
        border: 1px solid #ccd2d5;
        box-shadow: 0 1px 1px rgba(0,0,0,0.12);
        padding: 0 10px;
        white-space: nowrap;
        border-radius: 4px;
        height: 33px;
        line-height: 33px;

        &:hover {
            color: $white;
            border-color: $color-blue;
            background-color: $color-blue;
        }

        &.sb-loading {
            width: 15px;
            background-color: $white !important;
            border: 1px solid $border-color !important;

            &:before {
                color: $color-blue;
                cursor: default;
            }
        }
    }

    > .sb-btn:first-child {
        margin: 15px 15px 0 15px;
    }

    &.sb-collapse > .sb-collapse-btn {
        margin-left: 15px;
    }

    &:empty {
        display: none;
    }
}

.sb-panel-attachments {
    overflow: visible;
}

.sb-info-card {
    position: fixed;
    bottom: 10px;
    right: 10px;
    left: 10px;
    border-radius: 4px;
    padding: 10px 30px;
    background: $color-green;
    color: $white;
    text-align: center;
    box-shadow: $box-shadow;
    cursor: pointer;
    display: none;
    z-index: 9999995;

    &.sb-active {
        display: block;
        animation: sb-fade-bottom-animation .5s;
    }

    h3 {
        margin: 0;
        padding: 0;
        font-size: 15px;
        line-height: 25px;
        white-space: nowrap;
        letter-spacing: 0.5px;
        color: $white;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    p {
        margin: 5px 0 5px 0;
        padding: 0;
        font-size: 12px;
        line-height: 20px;
        letter-spacing: 0.3px;

        &:empty {
            display: none;
        }
    }

    a {
        color: $white;
    }

    .sb-icon-link {
        text-decoration: none;
        font-size: 13px;
        margin-left: 5px;
        display: inline-block;
        transform: translateY(2px);
    }

    span {
        text-decoration: underline;

        &:hover {
            text-decoration: none;
        }
    }

    &.sb-info-card-error {
        background: $color-red;
    }

    &.sb-info-card-info {
        background: $color-gray;

        h3 {
            font-weight: 400;
            font-size: 13px;
        }
    }
}

.sb-panel-notes > div {
    > div {
        margin: 0 15px;
        background: #ffeab0;
        padding: 15px;
        border-radius: 4px;
        word-wrap: break-word;
        overflow-wrap: break-word;
        overflow: hidden;

        > span:first-child {
            display: block;
            font-weight: 500;
            position: relative;
            margin-bottom: 10px;

            i {
                position: absolute;
                right: -5px;
                font-size: 10px;
                cursor: pointer;
                opacity: 0;
                z-index: 9;
                text-align: center;
                width: 20px;
                height: 20px;
                line-height: 23px;
                margin-top: -5px;
                transition: all .5s;

                &:hover {
                    color: $color-red;
                }

                &.sb-edit-note {
                    right: 17px;
                    font-size: 16px;
                    line-height: 26px;
                    background: #ffeab0;

                    &:hover {
                        color: $color-blue;
                    }
                }
            }

            &.sb-noname-note {
                margin-bottom: 0;

                & + span {
                    padding-right: 35px;
                }
            }
        }

        > span:last-child {
            font-size: 13px;
            line-height: 21px;
            display: block;
            opacity: .9;
        }

        &:hover > span i {
            opacity: 1;
        }

        & + div {
            margin-top: 10px;
        }
    }

    &:empty {
        margin-bottom: -15px;
    }
}

.sb-note-text a {
    color: $color-black;
    text-decoration: underline;
}

.sb-panel-tags > div {
    > div {
    }

    &:empty {
        margin-bottom: -15px;
    }
}

.sb-tags-box .sb-main {
    float: left;
    width: calc(100% - 40px);
}

.sb-tags-cnt, .sb-panel-tags > div {
    margin: -5px;

    > span {
        padding: 5px 10px;
        margin: 5px;
        letter-spacing: .3px;
        font-size: 13px;
        line-height: 15px;
        border-radius: 4px;
        cursor: pointer;
        display: block;
        float: left;
        -webkit-user-select: none;
        user-select: none;
        transition: $transition;
    }
}

.sb-tags-cnt {
    > span {
        border: 1px dashed transparent;

        &:not(.sb-active):not(:hover) {
            background: none;
            color: $color-black;
            border-color: $color-gray;
        }
    }



    [data-color=""] {
        &.sb-active, &:hover {
            background-color: $color-blue;
            color: $white !important;
        }
    }
}

.sb-panel-tags > div {
    margin: 10px;

    span {
        cursor: default;
        letter-spacing: 0.3px;
        font-size: 12px;
    }

    [data-color=""] {
        background-color: $color-blue;
        color: #FFF;
    }
}

#sb-add-tag {
    margin: 5px;
    height: 25px;
    width: 25px;
    display: block;
    float: left;

    &:before {
        font-size: 15px;
        line-height: 25px;
    }
}

.sb-popup {
    bottom: 70px;

    .sb-scroll-area {
        height: calc(100% - 65px);

        > p, ul > p {
            padding: 0 30px 15px 15px;
        }

        ul.sb-loading {
            height: 100%;
            text-indent: unset;

            &:not(:empty) {
                height: auto;

                &:before {
                    top: auto;
                    bottom: 0;
                }
            }
        }
    }

    .sb-popup-close {
        position: fixed;
        right: -25px;
        top: -25px;
        cursor: pointer;
        display: none;
        transition: $transition;

        &:hover {
            color: $color-red;
        }
    }

    &.sb-popup-lightbox {
        left: 50% !important;
        top: 50%;
        transform: translateY(-50%) translateX(-50%);

        .sb-popup-close {
            display: block;
        }

        &:after {
            display: none;
        }
    }
}

.sb-popup.sb-replies .sb-replies-list ul {

    li {
        display: flex;
        white-space: nowrap;
        font-size: 13px;
        height: 30px;
        line-height: 33px;
        padding-left: 15px;
        padding-right: 15px;
        margin-right: 15px;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
        overflow: hidden;

        div {
            transition: $transition;
        }

        div:first-child {
            margin-right: 15px;
            font-weight: 500;
            letter-spacing: .2px;
            position: relative;
            padding-left: 15px;
            color: $color-gray;

            &:before {
                content: "#";
                position: absolute;
                left: 0;
                font-size: 12px;
                line-height: 37px;
                opacity: 0.5;
            }

            &:empty {
                display: none;
            }
        }

        div:last-child {
            text-overflow: ellipsis;
            overflow: hidden;
        }

        &:hover {
            cursor: pointer;

            > div {
                color: $color-blue;
                opacity: 1;
            }
        }
    }
}

.sb-tooltip {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9999995;
    font-family: "Support Board Font", "Helvetica Neue", "Apple Color Emoji", Helvetica, Arial, sans-serif;
    display: none;

    &:before {
        content: "";
        background: url(../media/triangle.svg) no-repeat center center;
        background-size: contain;
        position: absolute;
        width: 20px;
        height: 15px;
        top: -11px;
        left: 50%;
        transform: translateX(-50%) rotate(180deg);
        margin-left: 1px;
    }

    &.n:before {
        top: auto;
        bottom: -11px;
        transform: translateX(-50%);
    }

    > div {
        font-size: 13px;
        line-height: 20px;
        padding: 8px 12px;
        max-width: 250px;
        text-align: center;
        letter-spacing: 0.3px;
        white-space: nowrap;
        color: $color-gray;
    }
}

.sb-collapse {

    > div, > ul {
        overflow: hidden;
        position: relative;
    }

    > .sb-collapse-btn {
        margin: 15px 0 -5px 0;
        text-transform: uppercase;
        font-weight: 500;
        font-size: 11px;
        letter-spacing: .3px;
        padding-right: 20px;

        &:after {
            content: "\61";
            font-family: "Support Board Icons";
            position: absolute;
            right: 1px;
            font-size: 9px;
            font-style: normal;
            font-weight: normal;
            line-height: 16px;
            text-transform: none;
        }

        &.sb-active:after {
            transform: rotate(180deg);
        }
    }

    .sb-btn + .sb-collapse-btn {
        transform: translateY(10px);
    }
}

.sb-filter-btn {
    i {
        z-index: 0;
    }

    > div {
        display: none;
        position: absolute;
        right: 0;
        left: 0;
        top: 0;
        background: #FFF;
        z-index: 4;
        padding: 15px;
        height: 38px;
    }

    .sb-select {
        min-width: 0;

        &:last-child {
            margin-right: 30px;
        }
    }

    &.sb-active {
        position: static;

        i {
            z-index: 5;
            top: 25px;
            right: 15px;
        }

        > div {
            display: flex;
        }

        &:after {
            display: none;
        }
    }

    &[data-badge]:after {
        content: attr(data-badge);
        position: absolute;
        right: -17px;
        top: 5px;
        background: $color-red;
        color: $white;
        font-size: 10px;
        line-height: 15px;
        height: 15px;
        width: 15px;
        text-align: center;
        border-radius: 50%;
        font-weight: 500;
    }

    &[data-badge="0"]:after {
        display: none;
    }
}

.sb-filter-star {
    cursor: pointer;
    font-size: 20px;
    height: 20px;
    transition: $transition;

    &:not(:hover):not(.sb-active) {
        color: $color-black;
    }
}

.sb-admin {
    > .sb-header {
        position: fixed;
        background: $background-gray;
        border-right: 1px solid $border-color;
        width: 65px;
        top: 0;
        left: 0;
        bottom: 0;
        z-index: 9;

        > .sb-admin-nav {
            > img {
                height: 35px;
                margin: 18px 15px;
            }

            > div {
                padding: 5px 0;

                > a {
                    height: 50px;
                    display: block;
                    cursor: pointer;
                    position: relative;
                    transition: $transition;
                    color: $color-black;

                    &:before {
                        content: "";
                        position: absolute;
                        left: 20px;
                        font-size: 22px;
                        line-height: 50px;
                        opacity: 0.6;
                        background-size: contain;
                        background-repeat: no-repeat;
                        font-family: "Support Board Icons";
                        font-style: normal !important;
                        font-weight: normal !important;
                        font-variant: normal !important;
                        text-transform: none !important;
                    }

                    &:hover,
                    &.sb-active {
                        color: $color-blue;
                    }

                    &:hover span {
                        transition-delay: 1s;
                        visibility: visible;
                        opacity: 1;
                        left: 65px;
                    }
                }
            }
        }

        > .sb-admin-nav > div > a > span,
        > .sb-admin-nav-right .sb-account > div {
            margin: 8px 0px;
            font-size: 15px;
            line-height: 20px;
            font-weight: 600;
            padding: 8px 12px;
            text-align: center;
            letter-spacing: 0.5px;
            color: rgb(40, 40, 40);
            position: absolute;
            left: 75px;
            z-index: 9999995;
            opacity: 0;
            visibility: hidden;
            transition: $transition;

            &:before {
                content: "";
                background: url(../media/triangle.svg) no-repeat center center;
                background-size: contain;
                position: absolute;
                width: 20px;
                height: 15px;
                top: 10px;
                left: -13px;
                transform: rotate(90deg);
            }

            &:after {
                content: "";
                position: absolute;
                width: 10px;
                top: 10px;
                bottom: 10px;
                left: 0px;
                background-color: $white;
            }
        }

        > .sb-admin-nav-right {
            align-items: center;
            padding: 15px 0;
            width: 65px;
            bottom: 0;
            left: 0;
            position: fixed;
            text-align: center;

            > div a {
                font-size: 13px;
                line-height: 20px;
                cursor: pointer;
                text-decoration: none;
                display: block;
                transition: $transition;
                color: $color-gray;

                &:hover {
                    color: $color-blue;
                }

                &.sb-profile {
                    padding-left: 35px;

                    img {
                        width: 25px;
                        height: 25px;
                    }
                }

                > i {
                    font-size: 16px;
                    opacity: 0.8;

                    &:hover {
                        opacity: 1;
                    }
                }
            }

            .sb-account {
                padding: 5px 0;
                cursor: pointer;
                text-align: center;

                > img {
                    width: 35px;
                    height: 35px;
                    border-radius: 50%;
                    display: block;
                    margin: 0 auto;
                }

                > div {
                    position: absolute;
                    bottom: 30px;
                    text-align: left;
                    min-width: 200px;
                    padding: 0;
                    transition-delay: 0.5s;

                    &:before {
                        top: auto;
                        bottom: 45px;
                    }
                }

                .sb-profile {
                    padding: 20px 20px 20px 65px;
                    border-bottom: 1px solid rgb(215, 215, 215);
                    background: $white;
                    text-decoration: none;
                    z-index: 9;

                    img {
                        left: 20px;
                    }

                    span {
                        white-space: nowrap;
                    }
                }

                &:hover > div {
                    visibility: visible;
                    opacity: 1;
                    left: 65px;
                }

                .sb-menu {
                    position: relative;
                    box-shadow: none;
                    padding: 20px;
                    margin: 0;
                    z-index: 9;

                    li {
                        padding: 6px 0;
                        position: relative;

                        &.sb-active {
                            color: $color-black;
                        }

                        &:hover,
                        &.sb-active:hover {
                            background: none;
                            color: $color-blue;
                        }
                    }

                    a {
                        text-decoration: none;
                        color: $color-black;

                        &:hover {
                            color: $color-blue;
                        }
                    }
                }
            }

            > div > a + a,
            > div > div + a {
                margin-top: 10px;
            }
        }
    }

    #sb-conversations:before {
        content: "\64";
    }

    #sb-users:before {
        content: "\6e";
    }

    #sb-settings:before {
        content: "\6d";
    }

    #sb-automations:before {
        content: "\e915";
    }

    #sb-reports:before {
        content: "\59";
    }

    #sb-articles:before {
        content: "\e908";
    }

    #sb-chatbot:before {
        content: "\e90a";
    }
}

@media(min-width: 465px) and (max-height: 475px) {
    .sb-admin > .sb-header > .sb-admin-nav > div > a {
        height: 35px;

        &:before {
            line-height: 35px;
        }
    }
}

.sb-enlarger {
    &:not(.sb-active) {
        cursor: pointer;
        position: relative;

        > div {
            display: none !important;
        }

        &:before {
            content: "\4d";
            font-family: "Support Board Icons";
            position: absolute;
            left: 1px;
            font-size: 9px;
            font-style: normal;
            font-weight: normal;
            line-height: 16px;
            text-transform: none;
        }
    }
}

.sb-admin {
    height: 100%;
    width: calc(100% - 65px);
    padding-left: 65px;
    position: fixed;
    font-size: 14px;
    line-height: 17px;
    background: $background-gray;
    top: 0;
    z-index: 9;

    &.sb-pwa {
        border-top: 1px solid $border-color;
    }

    &.sb-admin-start {
        width: auto;
        padding: 30px;
        position: static;
    }

    > main {
        padding: 0;
        background: $white;
        height: 100%;
        overflow: hidden;

        > div:not(.sb-active) {
            display: none;
        }

        > div {
            height: 100%;

            &.sb-active {
                animation: sb-fade-animation 0.4s;
            }

            > .sb-top-bar {
                height: 40px;
                margin: 0;
                padding: 15px 20px;
                align-items: center;
                display: flex;
                justify-content: space-between;
                border-bottom: 1px solid $border-color;

                h2 {
                    margin: 0;
                    font-size: 22px;
                    line-height: 35px;
                    font-weight: 600;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                > div {
                    display: flex;
                    align-items: center;

                    h2 {
                        margin-right: 60px;
                    }

                    h3 + .sb-setting {
                        margin-left: 15px;
                    }

                    &:first-child {
                        overflow: hidden;
                        padding-right: 30px;

                        > ul li {
                            flex-shrink: 0;
                        }
                    }

                    &:last-child {
                        .sb-search-btn {
                            margin-right: 30px;
                        }

                        .sb-btn + .sb-btn, .sb-btn-icon + .sb-btn, .sb-btn + .sb-btn-icon {
                            margin-left: 15px;
                        }
                    }
                }
            }

            > .sb-tab {
                height: calc(100% - 70px);
            }
        }
    }
}

.sb-search-dropdown {
    min-width: 391px;
    position: relative;
    text-align: right;

    .sb-search-btn {
        margin-left: auto;

        input {
            border-bottom-right-radius: 0;
            border-bottom-left-radius: 0;
            box-shadow: 0 0px 5px rgba(39, 156, 255, 0.5);
        }
    }

    .sb-search-dropdown-items {
        position: absolute;
        right: 30px;
        left: -1px;
        top: 40px;
        padding: 15px;
        background: $white;
        border-radius: 4px;
        border-top-right-radius: 0;
        border-top-left-radius: 0;
        border: 1px solid $color-blue;
        box-shadow: 0 0px 5px rgba(39, 156, 255, 0.5);
        clip-path: inset(0px -10px -10px -10px);
        border-top: none;
        text-align: left;
        display: none;
        z-index: 4;

        div {
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            line-height: 25px;
            cursor: pointer;
            font-size: 13px;
            letter-spacing: 0.3px;
            transition: $transition;

            &:hover {
                color: $color-blue;
            }
        }

        &:empty {
            padding: 2px;
            margin-top: -2px;
        }
    }

    .sb-active + .sb-search-dropdown-items {
        display: block;
    }
}

/*
 *
 * -----------------------------------------------------------
 * CONVERSATIONS AREA
 * -----------------------------------------------------------
 *
*/

.sb-board {
    display: flex;
    justify-content: space-between;
    height: 100%;

    > div > .sb-top {
        border-bottom: 1px solid $border-color;
        padding: 15px 20px;
        height: 70px;
        min-height: 70px;
        box-sizing: border-box;

        .sb-flex {
            justify-content: flex-end;
        }
    }

    > .sb-admin-list {
        max-width: 400px;
        min-width: 400px;
        border-right: 1px solid $border-color;
        position: relative;


        > .sb-top {
            display: flex;
            align-items: center;
            justify-content: space-between;

            > .sb-select > p span {
                padding-left: 5px;
            }
        }

        .sb-scroll-bar {
            opacity: 0.2 !important;
        }

        > .sb-loading {
            position: absolute;
            bottom: 10px;
            left: 50%;
            z-index: 9;
        }

        > .sb-scroll-area {
            height: calc(100% - 90px);
            padding: 10px 0;

            li {
                position: relative;
                padding: 18px 20px 15px 20px;
                max-height: 85px;
                opacity: 0.8;
                cursor: pointer;
                margin: 0 10px;
                border-radius: 6px;
                z-index: 3;
                background: transparent;
                transition: $transition;

                > .sb-notification-counter {
                    position: absolute;
                    right: 19px;
                    bottom: 17px;
                    height: 21px;
                    width: 21px;
                    line-height: 21px;
                    font-size: 12px;
                    border-radius: 50%;
                    text-align: center;
                    background: $color-blue;
                    color: $white;
                    z-index: 9;
                    letter-spacing: 0.3px;
                    animation: sb-fade-animation .3s;
                }

                &:hover {
                    background-color: $background-gray !important;

                    &:after {
                        opacity: 0;
                    }
                }

                &.sb-active {
                    opacity: 1;
                    background-color: #f0f4f9 !important;

                    .sb-name {
                        color: rgb(0, 43, 73);
                    }

                    &:before {
                        background-color: $color-blue;
                    }

                    &:after {
                        opacity: 0;
                    }
                }

                &:before {
                    content: "";
                    position: absolute;
                    left: 0;
                    bottom: 10px;
                    top: 10px;
                    width: 2px;
                    background-color: transparent;
                    border-radius: 3px;
                    transition: $transition;
                    z-index: 2;
                }

                &:after {
                    content: "";
                    position: absolute;
                    left: 20px;
                    right: 20px;
                    bottom: -1px;
                    height: 1px;
                    background-color: $border-color;
                    transition: $transition;
                    z-index: 2;
                }

                &:last-child:after {
                    display: none;
                }

                .sb-profile {
                    align-items: flex-start;
                    padding-left: 58px;

                    img {
                        width: 45px;
                        height: 45px;
                        transform: none;
                        top: 0;
                    }

                    .sb-name {
                        height: 23px;
                        line-height: 23px;
                        padding-right: 10px;
                        font-size: 15px;
                        font-weight: 400;
                        white-space: nowrap;
                        letter-spacing: 0.3px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    .sb-time {
                        margin-left: auto;
                        font-size: 13px;
                        line-height: 13px;
                        font-weight: 400;
                        white-space: nowrap;
                        opacity: 0.8;
                    }
                }

                p {
                    font-size: 13px;
                    height: 22px;
                    line-height: 22px;
                    opacity: 0.8;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    letter-spacing: 0.3px;
                    padding-left: 58px;
                    margin: 0;
                    color: $color-black;
                }

                b {
                    font-weight: 400;
                }

                .sb-notification-counter + div + p {
                    margin-right: 30px;
                }

                &[data-conversation-status="2"] {
                    opacity: 1;
                    font-weight: 500;

                    &:not(.sb-active) {
                        background-color: transparent;
                    }

                    .sb-name {
                        font-size: 16px;
                        font-weight: 600;
                    }

                    .sb-time {
                        font-weight: 500;
                        color: $color-blue;
                    }
                }

                &[data-conversation-status="-1"] {
                    .sb-name {
                        font-weight: 600;
                    }
                }
            }

            > ul > p {
                margin: 20px;
            }

            > ul > li + p {
                display: none;
            }

            &.sb-loading {
                position: relative;
                left: auto;
                bottom: auto;
                z-index: 0;
                text-indent: initial;
                overflow: hidden;
                overflow-y: scroll;

                > ul {
                    opacity: 0;
                }
            }
        }

        &.sb-departments-show li.sb-active:before {
            background-color: $color-blue !important;
        }
    }

    .sb-conversation {
        width: 100%;
        min-width: 0;
        position: relative;
        display: flex;
        flex-direction: column;

        > .sb-top {
            width: auto;
            display: flex;
            align-items: center;
            flex-grow: 0;
            justify-content: flex-start;

            .sb-menu-mobile {
                position: absolute;
                right: 15px;
            }

            > .sb-btn-back {
                display: none;
            }

            > a {
                font-weight: 600;
                position: relative;
                padding: 0 15px 0 0;
                margin: 0;
                cursor: pointer;
                text-decoration: none;
                line-height: 40px;
                color: $color-black;
                font-size: 20px;
                transition: $transition;
                position: relative;
                white-space: nowrap;

                &:hover {
                    color: $color-blue;
                }
            }

            i {
                color: $color-gray;
                transition: color .5s
            }

            a:hover i {
                color: $color-blue;
            }
        }

        .sb-list {
            height: 100%;
            padding: 10px 0 5px 0;
            overflow-y: scroll;

            > div {
                max-width: calc(100% - 275px);

                .sb-menu {
                    width: 170px;
                    right: -183px;
                    top: 0;
                    display: none;
                    margin-bottom: 15px;
                    z-index: 9;

                    li {
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    &.sb-active {
                        display: block;
                    }

                    &:empty {
                        display: none;
                    }
                }

                .sb-menu-btn {
                    display: block;
                    height: 15px;
                    width: 30px;
                    position: absolute;
                    right: -24px;
                    text-align: right;
                    top: 11px;
                    color: $color-gray;
                    cursor: pointer;
                    transition: $transition;
                    opacity: 0;
                    z-index: 9;

                    &:hover {
                        color: $color-blue;
                    }
                }

                &[data-type="slider"], &[data-type="card"] {
                    max-width: 380px;
                }

                &:hover .sb-menu-btn {
                    opacity: 1;
                }

                &.sb-right {
                    .sb-menu-btn {
                        right: auto;
                        left: -24px;
                        text-align: left;
                    }

                    .sb-menu {
                        right: auto;
                        left: -181px;

                        &:empty {
                            display: none;
                        }
                    }
                }

                img {
                    max-width: 600px;
                }

                .sb-message img {
                    max-width: 100%;
                }

                .sb-image {
                    max-width: 500px;
                }

                &:last-child {
                    animation: none;
                }
            }

            .sb-highlight:after {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: #0000002b;
                border-radius: 6px;
                opacity: 0;
                animation: sb-fade-in-out 3s linear .5s;
            }

            .sb-rich-message:not(.sb-rich-list) {
                position: relative;
                z-index: 1;

                &:before {
                    content: "";
                    position: absolute;
                    bottom: -15px;
                    left: -15px;
                    right: -15px;
                    top: -15px;
                    z-index: 995;
                    transition: $transition;
                }

                &:hover > div {
                    opacity: .5;
                    transition: $transition;
                }

                .sb-card-img + div + .sb-card-extra {
                    left: 0;
                }

                .sb-slider .sb-card-img + div + .sb-card-extra {
                    left: 15px;
                    top: 15px;
                }

                .sb-input-btn > div, .sb-btn, .sb-select p {
                    transition: none;
                }

                input, textarea, select {
                    color: #E6F2FC;
                    background-color: #E6F2FC;
                }

                .sb-input > span.sb-active {
                    background: transparent !important;
                    top: 0;
                    left: 0;
                    bottom: 0;
                    padding: 0 10px;
                    border-radius: 5px;
                    font-size: 13px;
                    line-height: 44px;
                    color: $color-gray;
                }
            }

            .sb-delivery-failed {
                font-size: 14px;
                transform: translateY(2px);
                margin-right: 5px;
                display: inline-block;
                cursor: help;
                position: relative;
                z-index: 2;
            }

            iframe:not([height]) {
                height: 165px;
            }

            &.sb-touchmove {
                overflow: hidden;
            }
        }

        .sb-editor {
            flex-shrink: 0;
            margin: 1px 15px 15px 15px;
            border: 1px solid rgba(0, 0, 0, 0.2);
            border-radius: 4px;
            box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
            transition: box-shadow linear 40ms, border-color linear 0.2s;
            background-color: $white;

            .sb-textarea {
                border: none;
                padding: 0 !important;
            }

            .sb-bar {
                margin-top: 10px;
                position: relative;

                .sb-btn-attachment {
                    display: block !important;
                }

                .sb-btn {
                    background-color: $color-blue;
                    color: $white;
                    border-color: $color-blue;
                    font-size: 12px;
                    line-height: 27px;
                    height: 25px;
                    transition: $transition;

                    &:hover {
                        background-color: $color-dark-blue;
                        border-color: $color-dark-blue;
                    }
                }
            }

            .sb-attachments:not(:empty) {
                padding-top: 15px;
            }

            .sb-suggestions {
                display: flex;
                flex-wrap: wrap;
                margin: 10px -5px 0 -5px;

                &:empty {
                    display: none;
                }
            }

            .sb-suggestions span {
                display: block;
                max-width: calc(50% - 50px);
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                padding: 7px 15px;
                background: #eef1f6;
                border-radius: 15px;
                font-size: 13px;
                letter-spacing: 0.3px;
                margin: 5px;
                cursor: pointer;
                transition: background-color .5s, color .5s;

                &:hover {
                    color: $white;
                    background-color: $color-blue;
                }

                &.sb-suggestion-full {
                    max-width: 100%;
                    white-space: normal;
                }
            }

            &.sb-audio-message-active {
                .sb-textarea, .sb-bar-icons, .sb-suggestions, .sb-attachments {
                    display: none;
                }

                .sb-bar {
                    margin-top: 0;
                    justify-content: right;
                }
            }
        }

        &[data-conversation-status="4"] [data-value="delete"], [data-value="inbox"], [data-value="empty-trash"], &[data-conversation-status="3"] [data-value="archive"], [data-value="read"] {
            display: none;
        }

        &[data-conversation-status="3"] [data-value="inbox"], &[data-conversation-status="4"] [data-value="inbox"], &[data-conversation-status="4"] [data-value="empty-trash"], [data-value="read"].sb-active {
            display: block;
        }

        [data-value="archive"] {
            color: $color-blue;
            margin-right: 5px;

            i {
                color: $color-green !important;
                font-size: 20px;
            }

            &:hover {
                border-color: $color-green;
                background-color: #d8ece3;
            }
        }

        [data-value="delete"]:hover i {
            color: $color-red !important;
        }
    }

    .sb-user-details {
        min-width: 400px;
        width: 400px;
        border-left: 1px solid $border-color;
        display: flex;
        flex-direction: column;
        height: 100%;
        overflow: hidden;
        position: relative;
        transition: width .3s;

        .sb-top {
            line-height: 42px;
            font-size: 18px;
            font-weight: 600;
            display: flex;
        }

        h3 {
            margin: 15px;
            font-size: 15px;
            line-height: 25px;
            font-weight: 600;
        }

        > div > h3 {
            margin: 0;
            padding: 15px;
            border-top: 1px solid $border-color;
        }

        .sb-profile {
            margin-left: -10px;
            cursor: pointer;
            line-height: 17px;
            transition: opacity .3s;

            &:hover {
                opacity: .7;
            }
        }

        .sb-profile-list {
            padding: 15px;

            ul {
                margin-top: 0;
            }

            [data-id="telegram_bcid"], [data-id="token"], [data-id="woocommerce_session_key"], [data-id="perfex-id"], [data-id="whmcs-id"], [data-id="aecommerce-id"], [data-id="facebook-id"], [data-id="viber-id"], [data-id="zalo-id"], [data-id="telegram-id"], [data-id="wechat-id"], [data-id="martfury-session"], [data-id="line-id"], [data-id="opencart_id"], [data-id="shopify_id"] {
                display: none;
            }

            [data-id="cc"] {
                margin-left: 15px;
            }

            [data-id="cc"], [data-em] {
                transition: $transition;

                label {
                    cursor: pointer;
                }

                &:hover {
                    color: $color-blue;
                }
            }
        }

        .sb-user-details-close {
            position: absolute;
            right: 15px;
            top: 15px;

            i {
                line-height: 35px;

                &:before {
                    font-size: 11px;
                    line-height: 11px;
                }
            }
        }

        &:not(.sb-active) {
            width: 0;
            min-width: 0;
            overflow: hidden;
        }

        .sb-select {
            padding: 0 15px 15px 15px;
        }

        .sb-scroll-bar {
            opacity: 0.2 !important;
        }

        .sb-scroll-area {
            height: calc(100% - 70px);
        }

        .sb-user-conversations {
            margin: 0 0 15px 15px;

            li {
                border-left: 1px solid $border-color;

                > div {
                    padding-left: 45px;

                    .sb-message {
                        font-weight: 400;
                    }
                }

                &:hover, &.sb-active {
                    background-color: rgb(238, 241, 246);
                }

                &:first-child {
                    border-top: 1px solid $border-color;
                    border-top-left-radius: 6px;
                }

                &:last-child {
                    border-bottom-left-radius: 6px;
                }
            }

            img {
                top: 6px;
                width: 30px;
                height: 30px;
            }

            span {
                line-height: 21px;
            }

            .sb-message {
                height: 20px;
                line-height: 20px;
            }
        }

        .sb-inline {
            display: flex;
            align-content: center;
            align-items: center;
            padding: 15px;
            border-top: 1px solid $border-color;

            h3 {
                margin-right: 15px;
                line-height: 55px;
            }

            > * {
                margin: 0;
                padding: 0;
            }

            > .sb-select {
                width: 100%;
                height: 55px;

                > p {
                    font-weight: 400;
                    font-size: 13px !important;
                    line-height: 55px !important;
                    border-radius: 0;

                    &:after {
                        line-height: 55px;
                        right: 15px;
                    }
                }

                > ul {
                    width: 100%;
                }
            }

            &.sb-inline-departments, &.sb-inline-agents {
                padding: 0 0 0 15px;
            }
        }
    }

    .sb-no-conversation-message {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        justify-content: center;
        align-items: center;
        text-align: center;
        background: $white;
        padding: 15px;
        display: none;
        z-index: 9;

        > div {
            max-width: 650px;
        }

        label {
            font-weight: 600;
            font-size: 20px;
            line-height: 30px;
        }

        p {
            margin: 15px 0;
            font-size: 15px;
            line-height: 25px;
            color: $color-gray;
        }
    }

    .sb-labels {
        padding-left: 10px;
        display: flex;
        align-items: center;

        span {
            font-size: 14px;
            line-height: 30px;
            padding: 1px 10px 0 10px;
            border-radius: 3px;
            margin: 0 5px;
            display: block;
            font-weight: 600;
            white-space: nowrap;
            cursor: default;
            position: relative;
        }

        .sb-status-online {
            background: rgba(19, 202, 126, 0.21);
            color: rgb(0, 147, 65);
        }

        .sb-status-warning {
            background: $background-color-yellow;
            color: rgb(98, 87, 5);
        }

        .sb-status-typing {
            background-color: rgb(231, 242, 252);
            color: $color-blue;
            padding-right: 32px;

            &:after {
                content: "...";
                position: absolute;
                width: 15px;
                left: calc(100% - 27px);
                bottom: 0;
                font-weight: 600;
                letter-spacing: 1px;
                overflow: hidden;
                animation: sb-typing 1s infinite;
                text-indent: 0;
            }
        }
    }

    .sb-editor .sb-labels {
        position: absolute;
        left: -15px;
        top: -45px;
        z-index: 95;

        > span {
            animation: sb-fade-bottom-animation .5s;
        }
    }

    .sb-list {

        > div {
            &:not(.sb-right) {
                .sb-thumb {
                    .sb-tooltip {
                        top: 40px;
                        left: -10px;

                        &:before {
                            top: -11px;
                            left: 21px;
                        }
                    }

                    &:hover .sb-tooltip {
                        display: block;
                        opacity: 0;
                        animation: sb-fade-bottom-animation .5s;
                        animation-fill-mode: forwards;
                        animation-delay: 1s;
                    }
                }
            }
        }
    }

    &.sb-no-conversation .sb-no-conversation-message {
        display: flex;
    }

    .sb-tags-area {
        display: flex;
        align-items: center;
        padding-right: 10px;

        > i {
            line-height: 8px;
            font-weight: 400;
            background: none;
        }
    }
}

.sb-area-conversations > .sb-btn-collapse {
    display: none;
    position: fixed;
    left: 70px;
    top: 20px;
    transition: color 0.4s;
    cursor: pointer;
    width: 30px;
    height: 30px;
    line-height: 35px;
    font-size: 15px;
    text-align: center;
    z-index: 3;

    &.sb-left:not(.sb-active) {
        transform: rotate(180deg);
        left: 67px;
    }

    &.sb-right:not(.sb-active) {
        transform: rotate(180deg);
        right: 2px;
    }

    &.sb-right {
        left: auto;
        right: 10px;
    }
}

#sb-audio-clip {
    bottom: 0;
    right: auto;
    left: 0;
    top: 0;
    border-radius: 6px;
    background: $white;

    .sb-icon:before {
        line-height: 50px;
    }
}

.sb-notes-box .sb-setting textarea {
    min-height: 200px;
}

#note-ai-scraping {
    margin-right: 15px;
    max-width: 200px;

    select {
        min-width: auto;
    }
}

.sb-list .sb-select-phone > div > p {
    display: none;
}

.sb-cc-box .sb-setting .repeater-item + .repeater-item {
    border-top: none;
    padding-top: 0;
    margin-top: 10px;

    i {
        margin-top: -6px;
    }
}

.sb-shopify-orders {
    > div > span {
        display: flex !important;

        > span + span + span {
            margin-right: 35px;
            margin-left: auto !important;
        }
    }

    [data-status] {
        padding: 5px 8px;
        background: #ffeb78;
        color: #4f4700;
        font-size: 11px;
        line-height: 13px;
        border-radius: 4px;
    }

    [data-status="Fulfilled"] {
        color: #014b40;
        background: #affebf;
    }

    [data-product-id] {
        cursor: pointer;

        & + .sb-title {
            margin-top: 5px;
        }
    }
}

.sb-shopify-cart > a > span:first-child, .sb-shopify-orders [data-product-id] > span:first-child {
    padding: 3px 5px;
    background: #00000012;
    font-size: 11px;
    line-height: 13px;
    margin-right: 10px;
    border-radius: 4px;
}

.sb-shopify-cart > a > span:last-child {
    float: right;
}

.sb-editor .sb-bar-icons > .sb-btn-shopify:before {
    content: "\e900";
    font-size: 25px;
    left: 5px;
}


/*
 *
 * -----------------------------------------------------------
 * USERS AREA
 * -----------------------------------------------------------
 *
*/

.sb-area-users {
    height: 100%;

    .sb-scroll-area {
        overflow: hidden;
        overflow-y: scroll;
        margin: 15px 0 0 15px;
        padding-right: 15px;
        height: calc(100% - 85px);

        &:hover + .sb-scroll-bar {
            opacity: 0.1 !important;
        }
    }

    > .sb-loading-pagination {
        position: absolute;
        bottom: 10px;
        left: 50%;
    }

    .sb-filter-btn {
        > div {
            left: auto;
            right: 0;
            top: 56px;
            width: auto;
            padding: 15px;
            z-index: 5;

            .sb-select:last-child {
                margin-right: 0;
            }
        }

        &.sb-active {
            position: relative;

            i {
                top: 10px;
                right: 0;
            }
        }
    }

    .sb-filter-star {
        display: none;
    }

    .sb-top-bar > div:first-child {
        overflow: visible !important;
    }
}

.sb-table-users {
    .sb-profile {
        font-weight: 600;
        font-size: 15px;
        text-transform: capitalize;
    }

    th:first-child {
        max-width: 15px;
        width: 15px;
        border-bottom-color: $border-color !important;
    }

    th {
        cursor: pointer;
        transition: $transition;
        position: relative;

        &.sb-active,
        &:hover {
            border-bottom: 1px solid rgb(131, 131, 131);
            color: rgb(0, 0, 0);
        }

        &.sb-active:after {
            content: "\61";
            font-family: "Support Board Icons";
            font-style: normal;
            font-weight: normal;
            font-variant: normal;
            text-transform: none;
            line-height: 25px;
            font-size: 12px;
            position: absolute;
            right: 15px;
        }

        &.sb-order-asc:after {
            transform: rotate(180deg);
        }
    }

    td:not(:first-child), th:not(:first-child) {
        overflow: hidden;
        text-overflow: ellipsis;
    }

    &.sb-loading {
        tbody, thead {
            display: none;
        }
    }
}

.sb-profile-box {
    .sb-profile-list {
        padding-right: 30px;
    }

    .sb-user-conversations > p {
        padding: 10px 15px 8px 15px;
    }

    .sb-top-bar {

        .sb-profile {
            font-weight: 600;

            img {
                width: 45px;
                height: 45px;
            }

            span {
                margin-left: 20px;
                font-size: 20px;
                line-height: 45px;
                white-space: nowrap;
            }
        }

        > div > [data-value] {

            & + .sb-btn {
                margin-left: 15px;
            }

            &:not(.sb-active) {
                display: none;
            }
        }
    }

    .sb-user-conversations {
        max-width: 600px;
        border: 1px solid $border-color;
        border-radius: 4px;

        > li {
            padding: 15px;
            border-left: none;

            &:first-child {
                border-top: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }

            &:last-child {
                border-bottom: none;
                border-bottom-left-radius: 4px;
                border-bottom-right-radius: 4px;
            }
        }

        .sb-profile {
            padding: 0 0 5px 0;

            .sb-name {
                font-size: 14px;
            }

            img {
                display: none;
            }

            .sb-time {
                float: right;
            }
        }
    }

    .sb-agent-area {
        padding-top: 30px;

        .sb-title {
            margin-bottom: 30px;
        }

        .sb-rating-area {
            min-height: 30px;

            > div {
                display: flex;
                height: 35px;
                align-items: center;

                > div:first-child {
                    font-weight: 500;
                    padding-right: 10px;
                    font-size: 13px;
                    width: 80px;
                }

                > span {
                    height: 3px;
                    background-color: $color-green;
                    margin-right: 10px;

                    &[data-count="0"] {
                        display: none;
                    }
                }

                & + div > span {
                    background-color: $color-red;
                }

                > div:last-child {
                    font-size: 13px;
                }
            }

            .sb-rating-count {
                font-size: 12px;
                line-height: 40px;
                color: $color-gray;
                opacity: 0.7;
            }

            &:before {
                left: 10px;
            }
        }
    }

    &.sb-type-agent .sb-start-conversation, &.sb-type-admin .sb-start-conversation, .sb-agent-area, .sb-hide + .sb-user-conversations {
        display: none;
    }

    &.sb-type-agent .sb-agent-area, &.sb-type-admin .sb-agent-area {
        display: block;
    }
}

.sb-profile-edit-box {
    .sb-top-bar .sb-profile {
        font-weight: 600;
        padding-left: 65px;
        padding-right: 15px;

        img {
            width: 45px;
            height: 45px;
        }

        span {
            font-size: 20px;
            line-height: 45px;
            white-space: nowrap;
        }
    }

    .sb-main {
        justify-content: flex-start;

        > div {
            width: 50%;

            & + div {
                margin-left: 30px;
            }
        }
    }

    &.sb-user-new {
        .sb-top-bar .sb-profile {
            padding-left: 0;

            img {
                display: none;
            }
        }

        .sb-delete {
            display: none;
        }
    }

    .sb-delete {
        margin-top: 60px;
        font-size: 14px;
        display: none;
    }

    #user_type,
    #department {
        display: none;
    }

    &.sb-agent-admin.sb-type-admin #user_type,
    &.sb-agent-admin.sb-type-admin #department,
    &.sb-agent-admin.sb-type-agent #user_type,
    &.sb-agent-admin.sb-type-agent #department,
    &.sb-agent-admin.sb-user-new #user_type {
        display: flex;
    }

    &.sb-agent-admin .sb-delete {
        display: inline-block;
    }
}

.sb-direct-message-subject:not(.sb-active) {
    display: none;
}

/*
 *
 * -----------------------------------------------------------
 * SETTINGS AREA
 * -----------------------------------------------------------
 *
*/

.sb-area-settings {

    .sb-input {
        align-items: flex-start;
        margin-bottom: 30px;
    }



    > .sb-tab {

        > .sb-content {
            padding-left: 30px;

            > div {
                max-width: 1000px;

                > .sb-setting + .sb-setting {
                    margin-top: 15px;
                    padding-top: 15px;
                    border-top: 1px solid rgb(230, 230, 230);
                }
            }

            .sb-inner-tab {
                > .sb-nav {
                    border: none;
                    padding: 0;
                }

                > .sb-content {
                    margin-top: -37px;
                    padding: 0;
                }
            }
        }
    }
}

#registration-fields .input > div:nth-child(2n) {
    margin-top: -10px;
}

#user-additional-fields .repeater-item > div:nth-child(2) {
    display: none !important;
}

#messenger-path-btn, #messenger-key, #open-ai-assistant-id {
    &:not(.sb-active) {
        display: none !important
    }
}

#tags {
    [data-id="tag-name"] {
        min-width: 230px;
        margin-right: 50px;
    }

    .repeater-item div + div {
        position: absolute;
        bottom: 0;
        right: 0;
        margin: 0;

        label {
            display: none;
        }
    }
}

.sb-type-multi-input > .input > div:not(.multi-input-textarea), .sb-setting .sb-language-switcher-cnt, .sb-select-checkbox > div, #open-ai-prompt {
    display: flex;
    align-items: center;

    > label {
        margin: 0 15px 0 0;
    }
}

#open-ai-prompt {
    align-items: baseline;
}

.sb-type-multi-input > .input > div {
    input, select {
        min-width: 285px;
    }
}

@media (max-width: 1490px) {
    .sb-type-multi-input > .input > div:not(.multi-input-textarea), .sb-setting .sb-language-switcher-cnt, .sb-select-checkbox > div {
        display: block;
    }
}

.sb-type-select-checkbox > .input, .multi-input-select-checkbox {
    position: relative;
}

.sb-select-checkbox-input {
    opacity: 1;
    cursor: pointer;

    &.sb-active + div {
        display: block;
    }
}

.sb-select-checkbox {
    position: absolute;
    z-index: 2;
    background: $white;
    right: 0;
    left: 0;
    top: 40px;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid $border-color;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.12);
    display: none;

    > div {
        margin: 5px 0;
    }

    label {
        margin: 0 0 0 15px !important;
    }
}

.sb-select.sb-select-colors {
    > p {
        border-radius: 4px;
        transition: none;
        background: none !important;

        span {
            width: 10px;
            height: 10px;
            display: inline-block;
            border-radius: 50%;
            margin-left: 10px;
            opacity: 1;
        }

        &:after {
            right: 15px;
        }

        &:not(:hover):after {
            color: $color-gray;
        }

        &:not([data-value]), &[data-value=""], &[data-value="-1"] {
            background-color: $white
        }
    }

    > ul {
        padding: 0 !important;
    }

    &:not(.sb-disabled) > p:hover span {
        background-color: $color-blue;
    }

    &.sb-loading {
        > ul {
            display: none;
        }

        &:before {
            line-height: 30px;
        }
    }

    &:hover:before {
        color: $white;
    }
}

.sb-setting .sb-language-switcher-cnt {
    margin-top: 10px;
}

.sb-setting p, .sb-text-gray {
    font-size: 13px;
    line-height: 22px;
    letter-spacing: 0.3px;
    margin: 5px 0 0 0;
    color: rgb(120, 134, 146);
}

.sb-setting {
    display: flex;
    justify-content: flex-start;

    > .sb-setting-content {
        max-width: 420px;
        width: 420px;
        padding-right: 60px;
        flex-shrink: 0;
    }

    > .input {
        width: 100%;
    }

    h2 {
        display: inline-block;
        min-width: 150px;
        flex-grow: 1;
        font-weight: 600;
        font-size: 15px;
        letter-spacing: 0.3px;
        margin: 0;
        color: $color-gray;
    }

    .sb-icon-help {
        transform: translateY(1px);
        margin: 0 0 0 5px;
        color: #889aaa;
        text-decoration: none;
        display: inline-block;

        &:hover {
            color: $color-blue;
        }
    }

    input, select, textarea {
        border-radius: 4px;
        border: 1px solid $border-color;
        outline: none;
        font-size: 13px;
        line-height: 35px;
        height: 35px;
        padding: 0 10px;
        transition: $transition;
        width: 100%;
        min-width: 300px;
        box-sizing: border-box;
        color: rgb(36, 39, 42);
        background-color: $white;

        &:focus {
            border: 1px solid $color-blue;
            box-shadow: 0 0 5px rgba(39, 156, 255, 0.2);
            color: rgb(36, 39, 42);
            outline: none;
        }

        &.sb-error {
            border: 1px solid $color-red;
            box-shadow: 0 0 5px rgba(202, 52, 52, 0.25);
        }
    }

    select {
        padding: 0 5px;
    }

    textarea {
        box-sizing: content-box;
        width: calc(100% - 22px);
        min-width: 280px;
        line-height: 22px;
        padding: 6px 10px;
        min-height: 86px;
    }

    input[type="checkbox"] {
        background: $white;
        clear: none;
        cursor: pointer;
        display: inline-block;
        line-height: 0;
        height: 35px;
        min-height: 35px;
        outline: 0;
        padding: 0;
        margin: 0;
        text-align: center;
        vertical-align: middle;
        width: 35px;
        min-width: 35px !important;
        outline: none;
        box-shadow: none;
        -webkit-appearance: none;

        &:checked:before {
            content: "\77" !important;
            font-family: "Support Board Icons" !important;
            font-style: normal !important;
            font-weight: normal !important;
            font-variant: normal !important;
            text-transform: none !important;
            line-height: 35px;
            font-size: 15px;
            color: $color-blue;
            margin: 0;
            width: 100%;
            height: 100%;
        }
    }

    label {
        color: $color-gray;
        font-weight: 600;
        font-size: 14px;
        line-height: 23px;
        margin: 0 30px 15px 0;
        display: block;
        min-width: 220px;
        letter-spacing: 0.3px;

        &:empty {
            display: none !important;
        }
    }

    input[type="number"] {
        padding-right: 0;
    }

    .input > .sb-loading:not(.sb-btn):not(.image) {
        width: 30px;
        height: 30px;
    }

    .multi-input-upload-image[data-type="upload-image"] .image {
        min-width: 50px;
        width: 100%;
    }

    &.sb-type-multi-input .input > div {
        margin-bottom: 15px;
    }

    &.sb-type-input-button .input {
        display: flex;
        width: 100%;

        input {
            min-width: 0;
            flex-shrink: 10;
        }

        a {
            margin-left: 15px;
            min-width: 0;
        }
    }

    &.sb-type-upload-image, [data-type="upload-image"] {
        .image {
            border-radius: 4px;
            border: 1px solid $border-color;
            height: 150px;
            position: relative;
            cursor: pointer;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            transition: $transition;

            &:before {
                content: "\70";
                font-family: "Support Board Icons" !important;
                position: absolute;
                left: 50%;
                top: 50%;
                font-size: 9px;
                color: $color-gray;
                width: 30px;
                height: 30px;
                line-height: 30px;
                margin: -15px 0 0 -15px;
                text-align: center;
                transform: rotate(45deg);
            }

            &:hover {
                border: 1px solid $color-blue;
                box-shadow: 0 0 5px rgba(39, 156, 255, 0.2);

                &:before {
                    color: $color-blue;
                }
            }

            > i {
                position: absolute;
                z-index: 9;
                right: 10px;
                top: 10px;
                height: 21px;
                width: 21px;
                font-size: 7px;
                text-align: center;
                line-height: 23px;
                cursor: pointer;
                background: $color-gray;
                color: $white;
                border-radius: 50%;
                display: none;
            }

            &[data-value] {
                &:before {
                    display: none;
                }

                > i {
                    display: block;

                    &:hover {
                        background-color: $color-red;
                    }
                }
            }

            &[data-value=""] {
                > i {
                    display: none;
                }

                &:before {
                    display: block;
                }
            }
        }
    }

    .multi-input-upload-image .image {
        height: 100px;
        background-size: auto 64px;
    }

    &.sb-type-color {
        .input {
            position: relative;

            i {
                position: absolute;
                right: 12px;
                font-size: 10px;
                top: 11px;
                cursor: pointer;
                opacity: 0.5;
                z-index: 2;

                &:hover {
                    opacity: 1;
                }
            }

            &:after {
                content: "";
                position: absolute;
                background: $white;
                right: 1px;
                top: 1px;
                width: 32px;
                height: 33px;
                border-top-right-radius: 3px;
                border-bottom-right-radius: 3px;
                z-index: 0;
            }
        }
    }

    &.sb-type-double-select .input {
        > div {
            display: flex;
            align-items: center;
            margin: 0 0 8px 0;
        }

        label {
            margin: 0 30px 0 0;
            font-weight: 400;
            font-size: 13px;
            color: $color-black;
        }

        select {
            min-width: 100px;
        }
    }

    &.sb-type-repeater > .input {
        width: 100%;
    }

    .sb-repeater {
        margin-bottom: 15px;
        display: block;
    }

    .repeater-item {
        position: relative;

        .sb-repeater-add {
            padding: 0;
            margin-top: -5px;
            width: 30px;
        }

        & + .repeater-item {
            border-top: 1px solid rgb(226, 226, 226);
            padding-top: 20px;
            margin-top: 20px;
        }

        > div {
            display: flex;

            & + div {
                margin-top: 10px;
            }

            &:empty {
                display: none;
            }

            [readonly] {
                min-width: 0 !important;
                border: none;
                padding: 0;
                text-align: center;
                font-weight: 500;
                opacity: .5;
                width: 20px;

                &:hover {
                    opacity: 1;
                }
            }

            label {
                margin: 0 15px 0 0;

                & + div {
                    flex-grow: 1;
                }
            }
        }

        input, select {
            min-width: 285px;
        }

        textarea {
            min-width: 260px;
        }

        .sb-enlarger {
            &:before {
                font-size: 13px;
                top: 4px;
                transition: $transition;
            }

            &:hover:before {
                color: $color-blue;
            }
        }

        > i {
            position: absolute;
            right: -30px;
            top: 50%;
            margin-top: 4px;
            width: 14px;
            font-size: 9px;
            height: 14px;
            line-height: 16px;
            text-align: center;
            cursor: pointer;

            &:hover {
                color: $color-red;
            }
        }

        &:first-child > i {
            margin-top: -6px;
        }
    }

    .sb-repeater-add + .sb-btn-icon {
        transform: translate(11px, 13px);
        margin-top: -35px;

        &.sb-icon-clip:before {
            font-size: 16px;
        }
    }

    input + input {
        margin-top: 8px;
    }

    &.sb-type-select-images .input {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
        grid-gap: 27px;

        > div {
            width: 80px;
            height: 80px;
            background-size: contain;
            background-position: center center;
            background-repeat: no-repeat;
            border-radius: 4px;
            border: 1px solid $border-color;
            position: relative;
            cursor: pointer;
            opacity: .6;
            transition: all 0.4s;

            &.sb-icon-close {
                text-align: center;
                line-height: 82px;
                color: $border-color;
                border: none !important;
                box-shadow: none !important;
                font-size: 20px;
                opacity: 1;
            }

            &:hover, &.sb-active:not(.sb-icon-close) {
                color: $color-blue;
                border-color: $color-blue;
                box-shadow: 0 0 5px rgba(39, 156, 255, 0.2);
                opacity: 1;
            }
        }
    }

    [data-type="upload-file"] {
        width: 100%;
        overflow: hidden;

        input {
            min-width: 100px;
        }

        .sb-btn {
            margin-left: 5px;
            flex: 0 0 auto;
        }
    }

    #push-notifications-onesignal-app-id, #push-notifications-onesignal-api-key, #push-notifications-sw-path {
        &:not(.sb-active) {
            display: none !important;
        }
    }
}

.repeater-item {

    .sb-enlarger {
        label {
            min-width: 160px;
        }
    }

    .repeater-item {
        > div {

            input, select, textarea {
                &:last-child {
                    margin-bottom: -1px;
                    border-bottom-right-radius: 0;
                    border-bottom-left-radius: 0;
                }
            }


            & + div {
                margin-top: 0;

                input, select, textarea {
                    &:first-child {
                        border-top-right-radius: 0;
                        border-top-left-radius: 0;
                    }
                }
            }

            &:last-of-type {
                input, select, textarea {
                    border-bottom-right-radius: 4px;
                    border-bottom-left-radius: 4px;
                }
            }
        }

        input:focus, select:focus, textarea:focus {
            z-index: 2;
        }

        .sb-sub-repeater-close {
            right: 10px;
            background: $white;
            border-radius: 50%;
            color: $color-gray;
            z-index: 9;
        }

        & + .repeater-item {
            border-top: none;
            padding-top: 0;
            margin-top: 10px;

            > i {
                margin-top: -6px;
            }
        }
    }
}

.sb-translations, .sb-automations-area {
    .sb-setting {
        display: block;
        margin-bottom: 15px;
    }
}

.sb-type-select.sb-loading select {
    visibility: hidden;
    transition: none;
}

.sb-area-settings .sb-tab .sb-btn, .sb-btn-white, a.sb-btn-white, .sb-lightbox .sb-btn-white {
    background-color: $white;
    color: rgb(86, 96, 105);
    border: 1px solid rgb(204, 210, 213);
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.12);
    padding: 0 10px;
    white-space: nowrap;
    border-radius: 4px;
    height: 33px;
    line-height: 33px;

    &.sb-icon {
        padding-left: 30px;

        > i {
            left: 10px;
            font-size: 10px;
            line-height: 33px;

            &:before {
                line-height: 33px;
            }
        }
    }

    &:hover {
        color: $white;
        border-color: $color-blue;
        background-color: $color-blue;
    }

    &.sb-loading {
        width: 15px;
        background: $white;
        color: $color-blue;
        border-color: #ccd2d5;
        cursor: default;

        &:before, &:hover:before {
            color: rgb(38, 67, 92);
        }
    }
}

.sb-translations {
    width: 100%;

    h2 {
        font-size: 15px;
        line-height: 27px;
        margin: 2px 0 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }


    .sb-content {
        padding: 0;

        > div:first-child {
            display: flex;
            justify-content: space-between;
        }

        &.sb-loading:before {
            top: 15px;
        }
    }

    .sb-translations-list {
        padding-top: 30px;

        > div:not(.sb-active) {
            display: none;
        }
    }

    .sb-nav {
        padding: 0;
        border: none;

        li {
            padding-left: 30px !important;
            position: relative;

            img {
                position: absolute;
                left: 0;
                width: 17px;
                top: 13px;
                border-radius: 2px;
                box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.3);
            }
        }
    }

    .sb-new-translation {
        margin-bottom: 25px;
    }
}

.sb-automations-area {
    > .sb-tab > .sb-content {
        margin-top: -35px;
    }

    .sb-nav-only {
        margin-right: 30px;
    }
}

.sb-automation-values {
    [data-id="profile_image"], [data-id="icon"], [data-id="brand"] {
        width: 70px;
        height: 70px;
        max-height: 70px;
    }
}

[data-automation-type="more"] .sb-automation-values > h2 + div + h2, [data-automation-type="more"] .sb-automation-values > h2 + div + h2 + div {
    display: none;
}

.sb-conditions, .sb-flow-conditions {
    > div {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        grid-gap: 15px;
    }

    select, input {
        min-width: 50px;
    }
}

.sb-timetable {
    > div > div {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;

        span {
            padding: 0 10.5px;
            font-size: 12px;
        }

        & > div {
            font-size: 13px;
            line-height: 30px;
            height: 30px;
            min-width: 88px;
            padding: 0 0 0 7px;
            position: relative;
            border-radius: 4px;
            border: 1px solid $border-color;
            transition: $transition;

            &:after {
                content: "\61";
                font-family: "Support Board Icons";
                position: absolute;
                right: 8px;
                font-size: 9px;
                font-style: normal;
                font-weight: normal;
                line-height: 30px;
            }

            &:hover {
                cursor: pointer;
                border: 1px solid rgb(2, 139, 229);
                box-shadow: 0 0 5px rgba(39, 156, 255, 0.2);
            }
        }
    }

    > div + div {
        margin-top: 12px;
    }

    label {
        min-width: 95px;
        margin: 0 0 10px 0;
    }

    .sb-custom-select {
        position: absolute;
        top: 31px;
        left: -1px;
        width: 95px;
        margin: 0;
        background: $white;
        border-radius: 4px;
        border: 1px solid $border-color;
        z-index: 9;
        margin: 2px 0 0 0;
        font-size: 14px;
        line-height: 25px;
        height: 250px;
        overflow-y: scroll;

        span {
            display: block;
            cursor: pointer;
            padding: 5px;
            white-space: nowrap;
            min-height: 22px;

            &:hover {
                background-color: $color-blue;
                color: $white;
            }

            &:empty {
                position: relative;

                &:before {
                    content: "--";
                    position: absolute;
                    left: 5px;
                }
            }
        }
    }

    > .sb-custom-select {
        display: none;
    }
}

.sb-color-palette {
    background: none !important;

    span {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        cursor: pointer;
        display: block;
        transition: $transition;
        position: relative;

        &:hover {
            opacity: 0.7;
        }

        &.sb-active + ul {
            display: block;
        }
    }

    ul {
        padding: 5px 10px;
        position: absolute;
        margin: 15px 0 0 -10px;
        display: none;

        li {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            margin: 5px 0;
            transition: $transition;

            &:hover {
                opacity: 0.7;
            }

            &[data-value=""] {
                box-shadow: inset 0px 0px 1px rgb(145, 145, 145);
                margin-bottom: 6px;
            }
        }
    }
}

.sb-color-palette:not([data-value]) span,
.sb-color-palette[data-value=""] span {
    background-color: #f2f5f5;
}

[data-color="red"],
[data-value="red"],
[data-value="red"] > span {
    background-color: #e22424;
    color: $white;
}

[data-color="yellow"],
[data-value="yellow"],
[data-value="yellow"] > span {
    background-color: #f5a212;
    color: $white;
}

[data-color="green"],
[data-value="green"],
[data-value="green"] > span {
    background-color: #1fa839;
    color: $white;
}

[data-color="pink"],
[data-value="pink"],
[data-value="pink"] > span {
    background-color: #5b31f1;
    color: $white;
}

[data-color="gray"],
[data-value="gray"],
[data-value="gray"] > span {
    background-color: #282828;
    color: $white;
}

[data-color="blue"], [data-value="blue"], [data-value="blue"] > span {
    background-color: #008db5;
    color: $white;
}

[data-color-text="red"] {
    color: #e22424;
}

[data-color-text="yellow"] {
    color: #f5a212;
}

[data-color-text="green"] {
    color: #1fa839;
}

[data-color-text="pink"] {
    color: #5b31f1;
}

[data-color-text="gray"] {
    color: #282828;
}

[data-color-text="blue"] {
    color: #008db5;
}

.sb-select-colors {
    & > [data-value="red"] {
        color: #e22424;
    }

    & > [data-value="yellow"] {
        color: #f5a212;
    }

    & > [data-value="green"] {
        color: #1fa839;
    }

    & > [data-value="pink"] {
        color: #5b31f1;
    }

    & > [data-value="gray"] {
        color: #282828;
    }

    & > [data-value="blue"] {
        color: #008db5;
    }
}

.sb-board > .sb-admin-list .sb-scroll-area {
    li[data-color="red"]:before {
        background-color: #e22424;
    }

    li[data-color="yellow"]:before {
        background-color: #f5a212;
    }

    li[data-color="green"]:before {
        background-color: #1fa839;
    }

    li[data-color="pink"]:before {
        background-color: #5b31f1;
    }

    li[data-color="gray"]:before {
        background-color: #282828;
    }

    li[data-color="blue"]:before {
        background-color: #008db5;
    }
}

#departments {
    > .input {
        width: 100%;
    }

    .repeater-item {
        display: flex;
        border: none;
        padding-top: 0;

        > div {
            margin: 0;
        }

        > div:first-child {
            min-width: calc(100% - 128px);
        }

        input:not([readonly]) {
            min-width: 100px !important;
        }

        > div + div {
            padding-left: 15px;

            label {
                display: none;
            }
        }

        > i {
            top: 50%;
            margin-top: -7px;
        }

        label {
            margin-bottom: 0;
            line-height: 35px;
            min-width: 80px;
        }

        .image {
            width: 33px;
            height: 33px;
            border-radius: 50%;

            &:before {
                font-size: 7px;
                color: rgb(182, 182, 182);
                margin: -15px 0 0 -16px;
            }

            &:hover:before {
                color: $color-blue;
            }
        }
    }
}

#chat-sb-icons .input > div:not(.sb-icon-close) {
    background-color: $border-color;
    border-radius: 50%;
}

#chat-sb-icons .input > .sb-active:not(.sb-icon-close), #chat-sb-icons .input > div:not(.sb-icon-close):hover {
    background-color: $color-blue !important;
}

.sb-open-ai-faq-box {
    top: 30px !important;
    bottom: 30px;
    margin-top: 0 !important;
    max-height: none !important;

    textarea {
        min-height: 66px;
    }
}

/*
 *
 * -----------------------------------------------------------
 * REPORTS AREA
 * -----------------------------------------------------------
 *
*/

.sb-area-reports {
    > .sb-tab {
        > .sb-content {
            display: flex;
            padding-left: 0;

            > .sb-reports-chart {
                width: 80%;
                padding-left: 15px;
            }

            > .sb-reports-sidebar {
                flex: 1 1 auto;
                max-width: 280px;
                padding: 5px 0 0 15px;

                .sb-collapse {
                    padding-bottom: 30px;
                }
            }

            &.sb-no-results-active {
                overflow-y: hidden;
                justify-content: center;
                align-items: center;
            }

            > .sb-no-results {
                display: none;
            }
        }

        .sb-nav > ul li:not(.sb-tab-nav-title) {
            font-weight: 400;
            font-size: 14px;
            line-height: 30px;
        }
    }

    p {
        font-size: 13px;
        line-height: 22px;
        letter-spacing: 0.3px;
        margin: 5px 0 15px 0;
        color: rgb(120, 134, 146);
    }

    .sb-table th, .sb-table td {
        padding: 9px 0;
    }

    table td {
        font-size: 13px;
    }

    .sb-reports-text {
        margin-top: 15px;
    }

    .sb-report-export {
        margin-left: 5px;

        &:not(:hover) {
            border: 1px solid $border-color;
            opacity: 1;
        }
    }

    td {
        .sb-icon-check, .sb-icon-close, .sb-icon-like, .sb-icon-dislike {
            color: $color-green;
            font-size: 13px;
            transform: translateY(1px);
            display: inline-block;
        }

        .sb-icon-close, .sb-icon-dislike {
            color: $color-red;
        }

        .sb-icon-close {
            font-size: 11px;
        }

        div {
            margin: 5px 0 0 0;
        }

        img {
            border-radius: 3px;
            width: 20px;
            margin: 0 10px 0 0;
            transform: translateY(2px);

            &.sb-flag {
                height: 13px;
            }
        }

        &:first-child > div {
            margin: 0 15px 0 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: normal;
        }
    }
}

.chart-cnt {
    padding-bottom: 30px;
}

#sb-date-picker {
    text-align: center;
    font-weight: 500;
    min-width: 255px;
}

.sb-report-agents-ratings, .sb-report-articles-ratings {
    td .sb-icon-check, td .sb-icon-like {
        margin: 0 10px 0 0;
    }

    td .sb-icon-close, td .sb-icon-dislike {
        margin: 0 10px 0 15px;
    }
}

/*
 *
 * -----------------------------------------------------------
 * CHATBOT AREA
 * -----------------------------------------------------------
 *
*/
.sb-qea-repeater-answer {
    > label {
        position: relative;

        i {
            position: absolute;
            right: 0;
            top: 0;

            &:before {
                font-size: 17px;
                line-height: 35px;
            }

            &:not(.sb-active) {
                display: none;
            }
        }
    }
}

.sb-area-chatbot {
    .sb-repeater {
        margin-right: 30px;

        .sb-repeater {
            margin-right: 0;
        }
    }

    > [data-id]:not(.sb-active) {
        display: none;
    }
}

#sb-table-chatbot-files, #sb-table-chatbot-website {
    letter-spacing: .3px;

    td {
        &:first-child, &:last-child {
            max-width: 25px;
            width: 25px;
        }

        &:last-child {
            padding-right: 0;

            i {
                right: 5px;
                top: 12px;
            }
        }
    }

    td {
        position: relative;
        transition: background-color .4s;

        &:nth-child(3) {
            width: 100px;
            max-width: 100px;
        }
    }

    label {
        letter-spacing: 0.3px;
        font-size: 12px;
        line-height: 12px;
        color: $color-yellow;
        font-weight: 500;
        margin: 0 15px;
    }

    &:empty + div .sb-btn-red {
        display: none;
    }
}

.sb-enlarger-function-calling.sb-active > div, .sb-enlarger-rest-api.sb-active > div {
    display: flex;

    &:not(:last-child) {
        margin-bottom: 10px;
    }
}

[data-id="open-ai-faq-set-data"] {
    .sb-setting {
        width: 50%;

        & + .sb-setting {
            margin-left: 10px;
        }
    }
}

#sb-chatbot-info {
    min-height: calc(100% - 2px);

    p {
        line-height: 30px;
        font-size: 13px;
    }
}

[data-id="playground"] {
    height: 100%;
}

[data-id="playground"] > div {
    height: calc(100% - 70px);
}

[data-id="playground"] > div > div {
    height: calc(100% - 35px);
    padding: 15px 20px 20px 20px;
    position: relative;
}

.sb-playground {
    flex-grow: 1;

    .sb-scroll-area {
        height: calc(100% - 110px);
        letter-spacing: .3px;

        > div {
            margin: 0 15px 0 0;
            min-width: 70px;
            animation: none !important;

            > div:first-child {
                font-weight: 500;
                font-size: 13px;
                text-transform: uppercase;
                position: relative;
                margin-bottom: 5px;

                div {
                    font-weight: 400;
                    position: absolute;
                    right: 0;
                    top: 0;
                    opacity: 0;
                    transition: $transition;
                }

                i {
                    width: 17px;
                    height: 17px;

                    &:before {
                        font-size: 10px;
                        line-height: 18px;
                    }
                }
            }

            > div:last-child, > div a:not(.sb-btn) {
                color: $color-gray;
                font-size: 13px;
                line-height: 21px;
            }


            &:hover div div {
                opacity: 1;
            }

            & + div {
                margin-top: 15px;
            }
        }

        .sb-menu-btn, .sb-menu, .sb-time {
            display: none !important;
        }

        &:empty {
            height: 0;

            + .sb-no-results {
                display: block;
            }
        }

        .sb-rich-message {
            .sb-slider {
                margin: 0;
            }

            .sb-card .sb-card-img + div + .sb-card-extra {
                top: 45px;
                left: 25px;
            }
        }
    }

    .sb-no-results {
        display: none;
        min-height: 50px;
    }

    .sb-rich-cnt[data-type="chips"] .sb-text {
        padding: 0;
    }
}

#sb-playground-query-panel {
    max-width: 800px;

    pre {
        text-align: left;
        margin: 0;
        line-height: 20px;
        font-size: 15px;
    }
}

#sb-playground-embeddings-panel p {
    text-align: left;
    font-size: 13px;
    line-height: 20px;
    font-weight: 400;

    b {
        font-weight: 500;
    }

    a {
        margin: 0;
        color: $color-black;
    }

    span {
        display: block;

        & + span {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid $border-color;
        }
    }

    span span {
        line-height: 20px;
        margin-top: 10px;
    }
}

.sb-playground-info {
    min-width: 250px;
    border-left: 1px solid $border-color;
    line-height: 25px;
    font-size: 13px;
    letter-spacing: .3px;

    b {
        font-weight: 500;
    }

    .sb-btn-text {
        text-decoration: underline;
        margin-top: 15px;

        & + .sb-btn-text {
            margin-left: 15px;
        }
    }
}

.sb-playground-editor {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;

    > .sb-flex {
        justify-content: space-between;
        margin: 15px 0 0 0;

        [data-value=add]:not(:hover) {
            opacity: .6;
        }
    }

    textarea {
        min-height: 25px;
    }

    [data-value="clear"]:before {
        font-size: 10px;
    }
}

[data-id="flows"] {

    > .sb-content {
        overflow: hidden;
        overflow-x: scroll;
        position: relative;
        display: flex;
        padding-top: 0;
        padding-bottom: 0;

        > div {
            width: 200px;
            min-width: 200px;
            margin-right: 50px;
            display: flex !important;
            justify-content: center;
            flex-direction: column;
            align-items: center;
            position: relative;


            > div:not(.sb-flow-add-step) {
                overflow-y: scroll;
                -ms-overflow-style: none;
                scrollbar-width: none;
                padding: 20px 0;
                width: 100%;
            }

            &:first-child .sb-flow-add-block, &:last-child .sb-flow-add-step {
                display: none;
            }
        }
    }
}

.sb-flow-add-block {
    height: 40px;
    text-align: center;
    line-height: 45px;
    border-radius: 4px;
    cursor: pointer;
    border: 1px dashed $color-gray;
    position: relative;
    transition: $transition;

    &:hover {
        background: $color-blue;
        border-color: $color-blue;

        &:before {
            color: $white;
        }
    }
}

.sb-flow-add-step {
    position: absolute;
    right: -40px;
    width: 30px;
    height: 30px;
    top: 50%;
    margin-top: -15px;
    text-align: center;
    line-height: 35px;
    border-radius: 50%;
    cursor: default;
    transition: $transition;
}

.sb-flow-block {
    border-radius: 4px;
    border: 1px solid $border-color;
    transition: $transition;
    background: $white;
    cursor: pointer;
    letter-spacing: .3px;

    > div {
        padding: 10px;
        font-size: 13px;
        line-height: 23px;
        user-select: none;
        position: relative;

        &:first-child {
            transition: $transition;
            font-weight: 500;
            font-size: 14px;
            line-height: 20px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            user-select: none;
        }

        & + div {
            transition: $transition;
            border-top: 1px solid $border-color;

            &:empty {
                display: none;
            }
        }
    }

    & + div {
        margin-top: 10px;
    }

    &:hover {
        border-color: $color-blue;
    }

    &[data-type="video"] > div + div {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    &[data-type="set_data"] > div, &[data-type="action"] > div, &[data-type="rest_api"], &[data-type="condition"] > div {
        > div {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}

.sb-flow-connectors {
    padding: 0 !important;

    > div {
        padding: 5px 50px 5px 10px;
        position: relative;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 30px;
        transition: $transition;

        &:hover, &.sb-active {
            color: $color-blue;
        }

        & + div {
            border-top: 1px solid $border-color;
        }
    }

    &:empty {
        display: none;
    }
}

.sb-flow-connectors > div > span, .sb-flow-block-cnt-name {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 35px;
    font-size: 11px;
    letter-spacing: 1px;
    font-weight: 500;
    line-height: 42px;
    text-align: center;
    border-left: 1px solid $border-color;
}

#sb-flows-blocks-nav {
    position: fixed;

    &:not(.sb-active) {
        display: none;
    }
}

.sb-flow-block-cnt {
    background: $background-gray;
    padding: 10px;
    border-radius: 4px;
    position: relative;
    transition: $transition;

    & + .sb-flow-block-cnt {
        margin-top: 15px;
    }

    &.sb-active {
        background: #E6F2FC;

        .sb-flow-block-cnt-name {
            color: $color-blue;
            border-color: $color-blue;
        }
    }
}

.sb-flow-block-cnt-name {
    left: auto;
    right: 0;
    bottom: auto;
    background: $background-gray;
    border-radius: 4px;
    line-height: 25px;
    width: 30px;
    border: 1px solid $border-color;
    transition: $transition;
    z-index: 1;
}

.sb-flows-blocks-nav-box {
    background: none;
    box-shadow: none;
    width: 170px !important;

    .sb-top-bar {
        padding: 0 !important;
        border: none !important;
        margin: 0 0 10px 0 !important;

        .sb-close {
            background-color: $white;
            opacity: 1;
            padding: 0;
            z-index: 0;
        }
    }

    .sb-main {
        padding: 0 !important;
    }

    .sb-menu {
        width: 100%;
        position: relative;
    }
}

#sb-block-delete {
    margin-top: 60px;
    font-size: 15px;
    letter-spacing: .3px;

    &:hover {
        color: $color-red;
    }
}

.sb-flow-block-box {
    height: calc(100% - 50px) !important;

    .sb-setting + .sb-setting {
        margin-top: 30px;
    }

    .sb-setting .repeater-item + .repeater-item, .sb-flow-conditions > div + div {
        border-top: none;
        padding-top: 10px;
        margin-top: 0;

        > i {
            margin-top: -1px;
        }
    }

    .sb-title {
        font-size: 14px;
        font-weight: 500;
        letter-spacing: .3px;

        &.sb-hide + .sb-setting {
            display: none;
        }
    }

    .sb-flow-conditions {
        .sb-setting {
            margin-top: 0;
        }

        &:empty + div {
            margin-top: 0;
        }

        [value="custom_variable"], [value="repeat"], [value="browsing_time"], [value="scroll_position"] {
            display: none !important;
        }
    }

    .sb-main .sb-add-condition {
        margin: 10px 0 30px 0;
        width: auto;
    }
}

.sb-repeater-block-user-details {
    .repeater-item {
        > div {
            display: block;
        }

        & + .repeater-item {
            padding-top: 20px !important;
        }
    }

    .sb-setting {
        label {
            margin: 0 15px 0 0;
            min-width: 0;
            line-height: 25px;
            font-weight: 400;
            font-size: 13px;
        }

        & + .sb-setting {
            margin: 10px 0 0 0;
            align-items: center;
        }
    }

    input[type="checkbox"] {
        height: 25px;
        min-height: 25px;
        width: 25px;
        min-width: 25px !important;

        &:checked:before {
            line-height: 25px;
            font-size: 12px;
        }
    }
}

.sb-repeater-block-data, .sb-repeater-block-actions, .sb-repeater-block-rest-api {
    .sb-setting + .sb-setting {
        margin: 0 0 0 15px;
    }
}

.sb-repeater-block-actions input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    display: none;
}

.sb-flow-scroll {
    position: fixed;
    z-index: 1;
    bottom: 15px;
    right: 15px;
    width: 35px;
    line-height: 40px;
    height: 35px;
    padding: 0;
    border-radius: 50%;
    background: $white;
    text-align: center;
    cursor: pointer;
    transition: $transition;

    &:not(.sb-active) {
        display: none;
    }

    &:before {
        color: $color-gray;
    }

    &:hover {
        background-color: $color-blue;
        border-color: $color-blue;

        &:before {
            color: $white;
        }
    }

    &.sb-icon-arrow-left {
        right: 60px;
    }
}

.sb-flow-start-messages [data-id="message"] {
    min-height: 45px;
}

/*
 *
 * -----------------------------------------------------------
 * ARTICLES AREA
 * -----------------------------------------------------------
 *
*/

.sb-area-articles {

    > .sb-tab > .sb-content {

        .sb-loading:before {
            left: 20px;
            top: 30px;
        }

        .sb-article-content {
            padding-left: 40px;
            max-width: 1326px;
        }
    }

    .sb-add-category, .ul-categories {
        display: none !important;
    }

    #editorjs {
        margin-top: -5px;
    }

    &[data-type="categories"] {
        .sb-nav {

            .sb-add-category {
                display: inline-block !important;
            }

            .ul-categories {
                display: block !important;
            }

            .ul-articles, .sb-add-article, & + .sb-content-articles {
                display: none !important;
            }

            & + div + .sb-content-categories:not(.sb-hide) {
                display: block !important;
            }
        }

        .sb-view-article {
            display: none;
        }
    }

    .sb-content-categories {
        display: none !important;

        .sb-setting {
            display: block;
        }
    }

    #sb-article-id {
        display: block;
        margin-bottom: 0;

        span {
            font-weight: 400;
            padding-left: 15px;
        }
    }

    .sb-setting + h2, .sb-article-categories, #sb-article-id {
        margin-top: 30px;
    }
}

.sb-content-articles:not([data-id]) .sb-language-switcher {
    display: none;
}



.sb-article-categories .sb-setting > div {
    width: 100%;
}

.sb-view-article[href=""] {
    display: none;
}

#category-image {
    max-width: 100px;
    background-size: 60px;
}

.sb-menu-articles .sb-docs, .sb-menu-chatbot .sb-docs {
    text-decoration: none;
    color: $color-gray;
    line-height: 42px;
    opacity: .8;
}

/*
 *
 * -----------------------------------------------------------
 * MISCELLANEOUS
 * -----------------------------------------------------------
 *
*/

.sb-btn-app-disable {
    display: none !important;
}

@keyframes sb-lightbox-animation {
    0% {
        transform: translateY(-50px);
        opacity: 0;
    }

    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

[data-provider="twilio"] .sb-whatsapp-box-header, [data-provider="twilio"] .sb-whatsapp-box-header + div {
    display: none;
}

.sb-lightbox {
    position: fixed;
    width: calc(100% - 50px);
    height: auto;
    padding: 0;
    max-width: 700px;
    max-height: 700px;
    left: 50%;
    top: 50%;
    margin-left: 150px;
    margin-top: 150px;
    border-radius: 6px;
    display: none;
    transition: opacity 0.4s, transform 0.4s;

    &.sb-active {
        display: block;
        animation: sb-lightbox-animation 0.5s;
    }

    &.sb-dialogflow-intent-box, &.sb-updates-box {
        height: calc(100% - 100px);
    }

    .sb-top-bar {
        padding: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid $border-color;
        margin-bottom: 30px;

        > div {
            display: flex;
            align-items: center;

            &:first-child {
                font-weight: 600;
                font-size: 20px;
                line-height: 45px;
                white-space: nowrap;
                overflow: hidden;
                margin-right: 15px;

                a {
                    text-decoration: none;

                    i {
                        font-size: 13px;
                        color: #b1c2d1;
                        margin-left: 15px;

                        &:hover {
                            color: $color-blue;
                        }
                    }
                }
            }
        }
    }

    .sb-main {
        display: block;
        padding: 0 20px 30px 20px;
        justify-content: space-between;
        height: calc(100% - 145px);

        > div {
            height: auto;
            width: 100%;
        }

        > .sb-title + *, > div > .sb-title + *, p + .sb-title {
            margin-top: 27px;

            *:first-child {
                margin-top: 0;
            }
        }

        > .sb-setting + .sb-title, > div > .sb-setting + .sb-title {
            margin-top: 30px;
        }

        p {
            font-size: 14px;
            line-height: 24px;
            letter-spacing: 0.3px;
            margin: 0;
            color: $color-gray;

            a:not([class]) {
                color: $color-blue;
                text-decoration: none;
            }

            b {
                font-weight: 500;
            }
        }

        > .sb-bottom {
            padding-top: 30px;
            display: flex;
            align-items: center;
        }
    }

    .sb-info {
        padding: 20px;
        background: $color-red;
        color: $white;
        font-size: 14px;
        line-height: 23px;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
        letter-spacing: 0.3px;
        position: absolute;
        right: 0;
        left: 0;
        z-index: 5;
        height: 45px;
        font-weight: 600;
        cursor: pointer;
        align-items: center;
        display: none;

        a {
            color: #FFF;
            padding-left: 4px;
        }

        &.sb-active {
            display: flex;
            animation: sb-fade-animation 0.4s;
        }
    }

    .sb-repeater {
        width: calc(100% - 25px);
    }

    &.sb-lightbox-media {
        animation: none;
        background: #000;
    }
}

.sb-lightbox-overlay {
    z-index: 995 !important;
    background-color: rgba(255, 255, 255, 0.75) !important;

    &.sb-active {
        display: block;
    }
}

.sb-lightbox-media.sb-active + .sb-lightbox-overlay {
    background-color: rgba(0,0,0,0.6) !important;
}

.sb-profile-edit-box, .sb-profile-box {
    height: calc(100% - 50px);
    max-width: 1000px;

    .sb-main {
        display: flex;
        padding: 0 20px;

        > div {
            height: 100%;
            width: 50%;
        }
    }
}

.sb-direct-message-box, #sb-whatsapp-send-template-box {
    height: calc(100% - 50px);
    max-width: 1000px;

    .sb-bottom {
        div + a {
            margin-left: 15px;
        }

        > div {
            font-weight: 500;
            margin: 0 15px;
            color: $color-blue;

            &:empty {
                display: none;
            }
        }
    }
}

.sb-updates-box, .sb-requirements-box {
    .sb-input {
        justify-content: flex-start;

        > span {
            max-width: 200px;
            min-width: 200px;
        }

        > div {
            font-size: 13px;
            letter-spacing: 0.3px;
            background: rgba(255, 196, 88, 0.15);
            color: rgb(155, 116, 36);
            width: 100%;
            line-height: 20px;
            padding: 13px;
            border-radius: 4px;

            &.sb-green {
                background-color: rgba(26, 146, 96, 0.07);
                color: rgb(21, 116, 77);
            }
        }
    }
}

.sb-requirements-box {
    .sb-input > div {
        display: inline-block;
        padding: 5px 10px;
        width: auto;
    }

    .sb-main > p {
        margin-top: 30px;
    }
}

.sb-dialog-box {
    max-width: 500px;
    height: auto;
    min-height: 75px;
    text-align: center;
    padding: 30px;
    z-index: 999998;

    p {
        font-weight: 600;
        font-size: 16px;
        line-height: 30px;
        margin: 0 0 20px 0;
    }

    a {
        margin: 0 7px;
    }

    .sb-link-text {
        margin: 0;
        color: $color-black;
    }

    &[data-type="choice"] .sb-close,
    &[data-type="alert"] .sb-close,
    &[data-type="info"] .sb-cancel,
    &[data-type="info"] .sb-confirm,
    &[data-type="map"] .sb-cancel,
    &[data-type="map"] .sb-confirm {
        display: none;
    }

    &[data-type="alert"] {
        padding: 60px 30px;
    }

    iframe {
        width: 100%;
        height: 400px;
        border: none;
        margin: 0;
        display: block;
    }

    pre {
        font-family: monospace;
        margin: 10px 0 0 0;
        overflow-x: scroll;
        padding: 5px 5px 7px 5px;
        border-radius: 3px;
        border: 1px solid $border-color;
        font-weight: 400;
    }

    &#error pre {
        white-space: normal;
    }

    .sb-title {
        font-size: 21px;
        line-height: 50px;
        margin-top: -10px;
        color: $color-blue;

        &:empty {
            display: none;
        }
    }

    &[data-type="map"] {
        max-width: 1000px;
        padding: 0;

        p {
            margin: 0;
        }

        > div {
            position: absolute;
            z-index: 9;
            top: 10px;
            right: 0;
        }
    }
}

.sb-dialogflow-intent-box {
    .sb-intent-add, .sb-bot-response {
        position: relative;

        i {
            position: absolute;
            margin-left: 15px;
            top: -10px;

            &:before {
                font-size: 16px;
            }

            &.sb-loading:before {
                line-height: 30px;
                font-size: 20px;
            }
        }
    }

    .sb-intent-add {
        margin-top: 10px;

        i {
            position: absolute;
            margin-left: 15px;
            top: -10px;

            & + i {
                margin-left: 50px;
            }

            & + i + i {
                margin-left: 85px;
            }
        }
    }

    .sb-type-text + .sb-type-text {
        margin-top: 5px;
    }

    .sb-title.sb-title-search .sb-search-btn {
        right: 36px;
    }
}

.sb-bot-response.sb-disabled {
    position: relative;

    textarea {
        opacity: 0.5;
    }

    i {
        display: none !important;
    }

    &:before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
    }
}

#sb-intent-preview, #sb-qea-preview {
    position: absolute;
    top: -1px;
    right: 0;
    font-size: 18px;
    cursor: pointer;
    transition: $transition;

    &:hover {
        color: $color-blue;
    }
}

#sb-envato-box, .sb-bold-list {
    p {
        font-weight: 400;
        text-align: left;
    }

    b {
        display: inline-block;
        width: 150px;
        font-weight: 500;
    }
}

#opencart-order-details {
    padding: 20px 30px;

    .sb-title {
        text-align: left;
    }

    .sb-panel-details {
        border: none;
    }

    .sb-title-opencart-products {
        color: $color-black;
        margin-top: -30px;
        font-weight: 500;
        font-size: 16px;
    }

    .sb-list-links {
        text-align: left;

        > a {
            padding: 0;
            margin: 0;
            font-weight: 400;
            font-size: 16px;
            line-height: 30px;

            span:last-child {
                float: right;
            }
        }
    }

    .sb-panel-details {
        display: inline-block;
        width: calc(100% - 150px);
        padding: 0;
    }

    .oc-b-products {
        transform: translateY(-10px);
    }
}

#intent-preview-box, #qea-preview-box {
    max-width: 350px;
    padding: 30px;
    text-align: left;

    > p {
        font-weight: 400;
        font-size: 13px;
        text-align: left;

        span {
            display: block;
            line-height: 20px;
            overflow: hidden;

            & + span {
                margin-top: 8px;
            }
        }
    }

    .sb-btn {
        margin: 0;
    }
}

#sb-errors-list-box {
    max-width: 600px;

    pre {
        white-space: normal;
        text-align: left;
        padding: 7px 10px;
    }
}

.sb-loading-global {
    height: 36px;
    width: 36px;
    max-width: 36px;
    max-height: 36px;
    margin: -18px 0 0 -18px;
    color: $color-blue;
    border-radius: 3px;
}

.sb-admin-box {
    max-width: 500px;
    margin: 30px auto;
    padding: 48px 40px 36px;
    border-radius: 6px;
    border: 1px solid $border-color;
    background: $white;
    position: relative;

    .sb-top-bar {
        padding-bottom: 30px;

        img {
            max-width: 300px;
        }

        .sb-title {
            font-size: 20px;
            line-height: 20px;
            font-weight: 600;
            margin: 30px 0px 15px 0px;
        }
    }

    .sb-text {
        font-size: 15px;
        line-height: 25px;
        color: $color-gray;
    }

    .sb-bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 30px;
    }

    .sb-info {
        font-size: 13px;
        line-height: 20px;
        color: $white;
        background-color: $color-red;
        right: 0;
        left: 0;
        top: 0;
        margin: 0 0 40px 0;
        padding: 15px 20px;
        border-radius: 4px;
        display: none;

        &.sb-active {
            display: block;
            animation: sb-fade-animation 0.4s;
        }
    }

    .sb-text + .sb-input, .sb-input + .sb-text {
        margin-top: 30px;
    }
}

.sb-rich-login {
    max-width: 500px;

    .sb-forgot {
        font-size: 12px;
        margin-top: 15px;
        text-align: right;
        color: $color-gray;
    }

    .sb-register {
        font-weight: 600;
        font-size: 15px;
    }

    .sb-forgot,
    .sb-register {
        cursor: pointer;
        transition: $transition;
        text-decoration: none;

        &:hover {
            color: $color-blue;
        }
    }
}

#sb-error-check {
    display: none !important
}

.sb-language-switcher {
    display: flex;
    align-items: center;

    img, > i {
        display: block;
        opacity: .5;
        transition: $transition;
        cursor: pointer;
    }

    img {
        border-radius: 2px;
        margin: 0 5px;
        width: 20px;
        height: 13px;
        position: relative;
        z-index: 2;
    }

    span {
        position: relative;
        transition: $transition;

        i {
            position: absolute;
            top: 0;
            left: 5px;
            line-height: 18px;
            cursor: pointer;
            font-size: 9px;
            opacity: 0;
            z-index: 0;
            transition: $transition;

            &:hover {
                color: $color-red;
            }
        }

        &:hover {
            padding-left: 15px;
            transition-delay: 1s;

            i {
                opacity: 1;
                transition-delay: 1s;

                &:hover {
                    transition-delay: 0s;
                }
            }
        }
    }

    > i {
        font-size: 12px;
        line-height: 12px;
        height: 12px;
        margin-left: 10px;
    }

    .sb-active img, span:hover i, span:hover img {
        opacity: 1;
    }

    & > i:hover {
        opacity: 1;
        color: $color-blue;
    }
}

.sb-languages-box .sb-main {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap: 20px;
    max-height: 300px;
    margin-bottom: 30px;
    padding-bottom: 0;

    > div {
        display: flex;
        align-items: center;
        font-size: 14px;
        line-height: 14px;
        cursor: pointer;
        transition: $transition;

        > img {
            margin-right: 15px;
            border-radius: 3px;
            width: 26px;
            height: 15px;
        }

        &:hover {
            color: $color-blue;
        }
    }
}

.sb-apps {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 20px;

    > div {
        min-height: 0;
        min-width: 0;
        position: relative;
        overflow: hidden;
        padding: 30px 30px 30px 130px;
        border-radius: 6px;
        border: 1px solid rgb(205, 210, 215);
        cursor: pointer;
        transition: $transition;

        img {
            max-width: 60px;
            position: absolute;
            left: 30px;
            top: 30px;
        }

        h2 {
            font-size: 19px;
            padding: 0;
            margin: 0;
            color: $color-gray;
        }

        p {
            font-size: 14px;
            line-height: 24px;
            letter-spacing: 0.3px;
            margin: 5px 0 0 0;
            color: rgb(120, 134, 146);
        }

        i {
            position: absolute;
            right: 30px;
            top: 30px;
            color: $color-green;
        }

        &:hover {
            border-color: $color-blue;
            box-shadow: 0 0 5px rgba(39, 156, 255, 0.2);
        }

        &.sb-disabled {
            opacity: 0.5;
        }
    }
}

.sb-panel-details .sb-whmcs-link {
    margin: 15px 15px 0 15px;
}

.sb-panel-whmcs .sb-list-items {
    margin: 0 15px 0 15px;
}

.sb-panel-aecommerce, .sb-panel-martfury, .sb-panel-opencart {
    .sb-list-items > a > span:first-child {
        opacity: .6;
        margin-right: 5px;
    }

    .sb-list-links > a > span:last-child {
        float: right;
    }
}

.sb-panel-zendesk {
    .sb-list-items > a {
        margin-bottom: 5px;

        > span:first-child {
            display: block;
            line-height: 15px;
            padding-left: 22px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;

            i {
                right: auto;
                left: 15px;
                font-style: normal;
                height: 15px;
                width: 15px;
                line-height: 15px;
                text-align: center;
                border-radius: 3px;
                font-size: 9px;
                opacity: 1;
                font-weight: 500;
                color: $white;
            }
        }
    }

    .sb-btn {
        margin-left: 15px;
    }

    #sb-zendesk-update-ticket {
        top: 10px;

        &:hover {
            color: $color-blue;
        }
    }
}

.sb-zendesk-status-n {
    background-color: rgb(255, 176, 87);
    color: rgb(112, 56, 21) !important;
}

.sb-zendesk-status-o {
    background-color: rgb(227, 79, 50);
}

.sb-zendesk-status-c {
    background-color: rgb(19, 202, 126);
}

.sb-zendesk-status-p {
    background-color: rgb(48, 145, 236);
    color: rgb(255, 255, 255);
}

.sb-zendesk-status-s {
    background-color: rgb(135, 146, 157);
}

.sb-zendesk-sync:before {
    content: "";
    position: absolute;
    width: 2px;
    background: $color-blue;
    height: calc(100% - 7px);
    left: 0;
    top: 0;
}

.sb-aecommerce-orders > a > span:last-child {
    margin-left: 15px;
}

.sb-panel-details .sb-woocommerce-cart > a {
    padding-right: 35px;
}

.sb-btn-app-setting:not(.sb-active) {
    display: none;

    & + .sb-btn {
        margin-left: 0;
    }
}

#sb-embeddings-box {
    min-height: 25px;

    p {
        margin: 0;

        span {
            display: block;
            font-weight: 400;
            font-size: 14px;
            margin: 10px 0 0 0;
        }

        & + div {
            display: none !important;
        }
    }
}

/*
 *
 * -----------------------------------------------------------
 * 3TH PARTY SCRIPTS
 * -----------------------------------------------------------
 *
*/

.daterangepicker {
    position: absolute;
    color: inherit;
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #ddd;
    width: 278px;
    max-width: none;
    padding: 0;
    margin-top: 7px;
    top: 100px;
    right: 20px !important;
    left: 20px;
    z-index: 3001;
    display: none;
    font-size: 15px;
    line-height: 1em;
}

.daterangepicker.single .daterangepicker .ranges, .daterangepicker.single .drp-calendar {
    float: none;
}

.daterangepicker.single .drp-selected {
    display: none;
}

.daterangepicker.show-calendar .drp-calendar {
    display: block;
}

.daterangepicker.show-calendar .drp-buttons {
    display: block;
}

.daterangepicker.auto-apply .drp-buttons {
    display: none;
}

.daterangepicker .drp-calendar {
    display: none;
    max-width: 270px;
}

.daterangepicker .drp-calendar.left {
    padding: 8px 0 8px 8px;
}

.daterangepicker .drp-calendar.right {
    padding: 8px;
}

.daterangepicker .drp-calendar.single .calendar-table {
    border: none;
}

.daterangepicker .calendar-table .next span, .daterangepicker .calendar-table .prev span {
    color: #fff;
    border: solid black;
    border-width: 0 2px 2px 0;
    border-radius: 0;
    display: inline-block;
    padding: 3px;
}

.daterangepicker .calendar-table .next span {
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
}

.daterangepicker .calendar-table .prev span {
    transform: rotate(135deg);
    -webkit-transform: rotate(135deg);
}

.daterangepicker .calendar-table th, .daterangepicker .calendar-table td {
    white-space: nowrap;
    text-align: center;
    vertical-align: middle;
    min-width: 32px;
    width: 32px;
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    border-radius: 4px;
    border: 1px solid transparent;
    white-space: nowrap;
    cursor: pointer;
}

.daterangepicker .calendar-table th {
    font-weight: 500;
    font-size: 13px;
    letter-spacing: .3px;
}

.daterangepicker .calendar-table {
    border: 1px solid #fff;
    border-radius: 4px;
    background-color: #fff;
}

.daterangepicker .calendar-table table {
    width: 100%;
    margin: 0;
    border-spacing: 0;
    border-collapse: collapse;
}

.daterangepicker td.available:hover, .daterangepicker th.available:hover {
    background-color: #eee;
    border-color: transparent;
    color: inherit;
}

.daterangepicker td.week, .daterangepicker th.week {
    font-size: 80%;
    color: #ccc;
}

.daterangepicker td.off, .daterangepicker td.off.in-range, .daterangepicker td.off.start-date, .daterangepicker td.off.end-date {
    background-color: #fff;
    border-color: transparent;
    color: #999;
}

.daterangepicker td.in-range {
    background-color: #ebf4f8;
    border-color: transparent;
    color: #000;
    border-radius: 0;
}

.daterangepicker td.start-date {
    border-radius: 4px 0 0 4px;
}

.daterangepicker td.end-date {
    border-radius: 0 4px 4px 0;
}

.daterangepicker td.start-date.end-date {
    border-radius: 4px;
}

.daterangepicker td.active, .daterangepicker td.active:hover {
    background-color: $color-blue;
    border-color: transparent;
    color: #fff;
}

.daterangepicker th.month {
    width: auto;
    font-weight: 500;
    font-size: 13px;
    letter-spacing: .3px;
}

.daterangepicker td.disabled, .daterangepicker option.disabled {
    color: #999;
    cursor: not-allowed;
    text-decoration: line-through;
}

.daterangepicker select.monthselect, .daterangepicker select.yearselect {
    font-size: 12px;
    padding: 1px;
    height: auto;
    margin: 0;
    cursor: default;
}

.daterangepicker select.monthselect {
    margin-right: 2%;
    width: 56%;
}

.daterangepicker select.yearselect {
    width: 40%;
}

.daterangepicker select.hourselect, .daterangepicker select.minuteselect, .daterangepicker select.secondselect, .daterangepicker select.ampmselect {
    width: 50px;
    margin: 0 auto;
    background: #eee;
    border: 1px solid #eee;
    padding: 2px;
    outline: 0;
    font-size: 12px;
}

.daterangepicker .calendar-time {
    text-align: center;
    margin: 4px auto 0 auto;
    line-height: 30px;
    position: relative;
}

.daterangepicker .calendar-time select.disabled {
    color: #ccc;
    cursor: not-allowed;
}

.daterangepicker .drp-buttons {
    clear: both;
    text-align: right;
    padding: 8px;
    border-top: 1px solid #ddd;
    display: none;
    line-height: 12px;
    vertical-align: middle;
}

.daterangepicker .drp-selected {
    display: inline-block;
    font-size: 12px;
    padding-right: 8px;
}

.daterangepicker .drp-buttons .btn {
    margin-left: 8px;
    font-size: 12px;
    font-weight: bold;
    padding: 4px 8px;
}

.daterangepicker.show-ranges.single.rtl .drp-calendar.left {
    border-right: 1px solid #ddd;
}

.daterangepicker.show-ranges.single.ltr .drp-calendar.left {
    border-left: 1px solid #ddd;
}

.daterangepicker.show-ranges.rtl .drp-calendar.right {
    border-right: 1px solid #ddd;
}

.daterangepicker.show-ranges.ltr .drp-calendar.left {
    border-left: 1px solid #ddd;
}

.daterangepicker .ranges {
    float: none;
    text-align: left;
    margin: 0;
}

.daterangepicker.show-calendar .ranges {
    margin-top: 8px;
}

.daterangepicker .ranges ul {
    list-style: none;
    margin: 0 auto;
    padding: 0;
    width: 100%;
}

.daterangepicker .ranges li {
    font-size: 12px;
    padding: 8px 12px;
    cursor: pointer;
}

.daterangepicker .ranges li:hover {
    background-color: #eee;
}

.daterangepicker .ranges li.active {
    background-color: $color-blue;
    color: #fff;
}

@media (min-width: 564px) {
    .daterangepicker {
        width: auto;
    }

    .daterangepicker .ranges ul {
        width: 140px;
    }

    .daterangepicker.single .ranges ul {
        width: 100%;
    }

    .daterangepicker.single .drp-calendar.left {
        clear: none;
    }

    .daterangepicker.single .ranges, .daterangepicker.single .drp-calendar {
        float: left;
    }

    .daterangepicker {
        direction: ltr;
        text-align: left;
        box-shadow: 0 0 10px #0000001f;
    }

    .daterangepicker .drp-calendar.left {
        clear: left;
        margin-right: 0;
    }

    .daterangepicker .drp-calendar.left .calendar-table {
        border-right: none;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }

    .daterangepicker .drp-calendar.right {
        margin-left: 0;
    }

    .daterangepicker .drp-calendar.right .calendar-table {
        border-left: none;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    .daterangepicker .drp-calendar.left .calendar-table {
        padding-right: 8px;
    }

    .daterangepicker .ranges, .daterangepicker .drp-calendar {
        float: left;
    }
}

@media (min-width: 730px) {
    .daterangepicker .ranges {
        width: auto;
    }

    .daterangepicker .ranges {
        float: left;
    }

    .daterangepicker.rtl .ranges {
        float: right;
    }

    .daterangepicker .drp-calendar.left {
        clear: none !important;
    }
}

.codex-editor:after {
    content: "\4d";
    font-family: "Support Board Icons";
    position: absolute;
    left: 0;
    font-size: 9px;
    font-style: normal;
    font-weight: normal;
    bottom: 25px;
    opacity: .5;
    z-index: -1;
}

.ce-block a {
    color: $color-black;
}

.ce-header {
    font-size: 16px !important;
    color: $color-black !important;
}

.ce-toolbar__actions {
    padding-right: 15px;
}

.ce-block__content, .ce-toolbar__content {
    max-width: 100% !important;
    margin: 0;
}

.cdx-input {
    width: auto;
}

.ce-rawtool__textarea {
    min-height: 0 !important;
    background: $background-gray !important;
}

.cdx-search-field__icon {
    height: 35px !important;
}

.ct__content {
    color: $white
}

.cdx-block {
    max-width: 100% !important;
}

.image-tool__image-picture {
    border-radius: 3px;
}

/*
 *
 * -----------------------------------------------------------
 * CLOUD VERSION
 * -----------------------------------------------------------
 *
*/

#whatsapp-cloud-sync-btn, #whatsapp-cloud-reconnect-btn {
    display: none;
}

.sb-cloud-admin {
    [id="user_type"], [id="first_name"], [id="last_name"], [id="password"], [id="email"], .sb-delete {
        display: none !important;
    }
}

.sb-cloud {
    #whatsapp-cloud-sync-btn, #whatsapp-cloud-reconnect-btn {
        display: flex;
    }

    #admin-title, #login-icon, #login-message, #envato-purchase-code, #envato-validation, #email-piping-disable-cron,
    #auto-updates, #pusher, #cookie-domain, #system-requirements, #sb-path, #tab-aecommerce, #admin-icon, #push-notifications-provider, #push-notifications-id, #push-notifications-key,
    #tab-whmcs, #tab-perfex, #tab-ump, #tab-armember, #tab-martfury, #logs, .sb-version, #amazon-s3, #whatsapp-twilio-btn.sb-active + #whatsapp-cloud-sync-btn,
    #whatsapp-twilio-btn.sb-active + #whatsapp-cloud-sync-btn + #whatsapp-cloud-reconnect-btn {
        display: none !important;
    }

    #google-client-id, #google-client-secret, #google-refresh-token, #open-ai-key {
        &:not(.sb-active) {
            display: none !important;
        }
    }

    #whatsapp-cloud-key, #whatsapp-twilio-btn {
        &:not(.sb-active) {
            display: none !important;
        }
    }

    .sb-app-box {
        .sb-setting, .sb-main .sb-title, .sb-btn-app-puchase {
            display: none !important;
        }
    }

    .sb-active-app {
        .sb-btn-app-disable {
            display: block !important;
        }

        .sb-activate {
            display: none;
        }
    }
}

.sb-main:not(.sb-cloud) {
    #google-sync-mode, #open-ai-sync-mode, #whatsapp-cloud-sync-mode, #open-ai-training-cron-job {
        display: none !important;
    }
}

.sb-credits-panel-box p b a {
    display: none !important;
}

.sb-onboarding-box {
    height: calc(100% - 50px);

    .sb-setting {
        align-items: center;

        > div:first-child {
            padding-right: 30px;
            width: 100%;
        }

        > div:last-child {
            flex: 1;
        }

        h2 {
            font-size: 16px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;

            img, i {
                font-size: 18px;
                line-height: 20px;
                margin-left: 15px;
                height: 18px;
                display: block;
                transform: none;
            }

            a {
                text-decoration: none;
            }
        }

        .sb-btn {
            width: 70px;

            &.sb-active {
                background-color: $color-green;
                color: $color-green;
                cursor: default;

                &:after {
                    content: "\77";
                    font-family: "Support Board Icons";
                    position: absolute;
                    left: 0;
                    right: 0;
                    color: $white;
                }
            }
        }

        & + div {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid rgb(230, 230, 230);
        }
    }

    .sb-main > p {
        margin-bottom: 30px;
    }
}

.sb-menu [data-value="switch"] {
    position: static !important;

    > div {
        position: absolute;
        left: calc(100% - 1px);
        top: -61px;
        height: calc(100% + 45px);
        padding: 8px 15px;
        background: #FFF;
        box-shadow: 0 4px 14px 0 rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.05);
        border-radius: 4px;
        opacity: 0;
        transition: all 0.4s linear .2s;

        a {
            display: block;
            padding: 6px 0;
        }
    }

    &:hover > div {
        opacity: 1;
    }
}

/*
 *
 * -----------------------------------------------------------
 * RESPONSIVE
 * -----------------------------------------------------------
 *
*/

@media (max-width: 1600px) {
    .sb-admin > main > .sb-area-users > .sb-top-bar h2 {
        max-width: 200px;
        margin-right: 30px;
    }
}

@media (max-width: 1300px) {
    .sb-admin > main > .sb-area-users > .sb-top-bar h2 {
        max-width: 100px;
    }

    .sb-area-users .sb-menu-wide ul li {
        margin-right: 10px;
    }

    .sb-setting {
        .repeater-item > div label {
            margin: 0 0 15px 0;
        }

        .repeater-item > div, .repeater-item > div, .input > div:not([data-type="textarea"]):not(.sb-repeater-add) {
            display: block;
        }

        .input > div:not([data-type="textarea"]):not(.sb-repeater-add) > label {
            margin: 0 30px 10px 0;
        }

        > .sb-setting-content {
            flex-shrink: 1;
        }

        input, select, textarea, input, select, textarea {
            min-width: 0;
        }
    }

    [data-id="open-ai-faq-set-data"] .repeater-item > div {
        display: flex;
    }

    #sb-chatbot-qea .sb-enlarger {
        min-height: 30px;
    }

    .sb-type-timetable {
        > .input {
            overflow: hidden;
        }

        .sb-timetable > div > div {
            flex-wrap: wrap;

            [data-day] {
                margin-bottom: 5px;
            }
        }
    }

    .sb-area-reports {
        > .sb-tab > .sb-content {
            display: block;

            .sb-reports-chart {
                width: auto;
            }

            .sb-reports-sidebar {
                max-width: 100%;
            }
        }
    }

    .sb-repeater-block-data .sb-setting + .sb-setting, .sb-repeater-block-actions .sb-setting + .sb-setting, .sb-repeater-block-rest-api .sb-setting + .sb-setting {
        margin: 5px 0 0 0;
    }
}

@media (min-width: 465px) {
    .sb-menu-mobile > ul {
        box-shadow: none;
    }
}

@media (min-width: 465px) and (max-width: 1200px) {
    .sb-board > .sb-admin-list {
        min-width: 300px;
    }

    .sb-board .sb-labels .sb-status-online {
        text-indent: 999px;
        width: 10px;
        height: 10px;
        padding: 0;
        overflow: hidden;
        border-radius: 50%;
        background: $color-green;
    }

    .sb-board .sb-labels .sb-status-typing {
        padding: 0;
        text-indent: 999px;
        width: 30px;
        height: 25px;
        overflow: hidden;
        background: none;
    }

    .sb-board .sb-conversation .sb-list > div {
        max-width: calc(100% - 100px);
    }
}

@media (min-width: 465px) and (max-width: 1366px) {
    .sb-board > .sb-admin-list, .sb-board .sb-user-details {
        max-width: 330px;
        min-width: 330px;
    }

    .sb-admin-list .sb-search-btn > input {
        min-width: 225px;
    }
}

@media (min-width: 465px) and (max-width: 1140px) {
    .sb-admin > main > div > .sb-top-bar > div:first-child > ul {
        overflow-x: scroll;
    }
}

@media (min-width: 465px) and (max-width: 1024px) {
    .sb-board > .sb-admin-list, .sb-board .sb-user-details {
        max-width: 250px;
        min-width: 250px;
    }

    .sb-admin-list .sb-search-btn > input {
        min-width: 145px;
        max-width: 145px;
    }

    .sb-setting, .sb-setting {
        display: block;
    }

    .sb-setting > .sb-setting-content, .sb-setting > .sb-setting-content {
        max-width: 100%;
        width: auto;
        padding-right: 0;
        padding-bottom: 15px;
    }

    .sb-area-settings > .sb-tab > .sb-nav > ul, .sb-area-reports > .sb-tab > .sb-nav > ul {
        min-width: 180px;
    }

    .sb-area-users .sb-top-bar h2 {
        display: none;
    }

    .sb-menu-wide ul li, .sb-tab > .sb-nav > ul li {
        margin: 0 10px 0 0;
    }
}

@media (min-width: 465px) and (max-width: 912px) {
    .sb-board {

        > .sb-admin-list, .sb-user-details {
            position: fixed;
            z-index: 2;
            background: $white;
            left: 65px;
            top: 0;
            bottom: 0;
            max-width: 330px;
            min-width: 330px;
            display: none;
        }

        > .sb-admin-list.sb-active, .sb-user-details.sb-active {
            display: block;
        }

        > .sb-admin-list .sb-top {
            padding-left: 45px;
        }

        .sb-user-details {
            left: auto;
            right: 0;
        }

        .sb-conversation > .sb-top {
            padding-left: 45px;
        }

        .sb-conversation > .sb-top .sb-menu-mobile {
            right: 35px;
        }
    }

    .sb-admin-list {
        .sb-search-btn > input {
            min-width: 200px;
            max-width: 200px;
        }

        .sb-filter-btn.sb-active > div {
            min-width: 265px;
            max-width: 265px;
            left: auto;
        }
    }

    .sb-area-conversations > .sb-btn-collapse {
        display: block;
    }
}

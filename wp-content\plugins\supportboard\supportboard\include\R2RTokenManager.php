<?php

class R2RTokenManager {
    private $base_url;
    private $username;
    private $password;

    private $access_token = null;
    private $refresh_token = null;
    private $last_login_time = null;

    public function __construct($base_url, $username, $password) {
        $this->base_url = rtrim($base_url, '/');
        $this->username = $username;
        $this->password = $password;

        $this->login();
    }

    public function getToken() {
        return $this->access_token;
    }

    public function getAuthHeader() {
        return 'Authorization: Bearer ' . $this->access_token;
    }

    public function maybeRefreshTokenIfUnauthorized($http_code) {
        if ($http_code === 401) {
            return $this->refresh();
        }
        return false;
    }

    private function login() {
        $url = $this->base_url . '/v3/users/login';
        $data = http_build_query([
            'username' => $this->username,
            'password' => $this->password
        ]);

        $headers = ['Content-Type: application/x-www-form-urlencoded'];

        $response = $this->curlPost($url, $data, $headers);

        if (!isset($response['results']['access_token']['token'])) {
            throw new Exception("Login failed or invalid response");
        }

        $this->access_token = $response['results']['access_token']['token'];
        $this->refresh_token = $response['results']['refresh_token']['token'] ?? null;
        $this->last_login_time = time();
    }

    private function refresh() {
        if (!$this->refresh_token) return false;

        $url = $this->base_url . '/v3/users/refresh-token';
        $data = json_encode(['refresh_token' => $this->refresh_token]);

        $headers = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];

        $response = $this->curlPost($url, $data, $headers);

        if (!isset($response['results']['access_token']['token'])) {
            return false;
        }

        $this->access_token = $response['results']['access_token']['token'];
        $this->refresh_token = $response['results']['refresh_token']['token'] ?? null;
        return true;
    }

    private function curlPost($url, $data, $headers = []) {
        $ch = curl_init($url);
        curl_setopt_array($ch, [
            CURLOPT_POST => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 20,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_HTTPHEADER => $headers
        ]);

        $body = curl_exec($ch);
        $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        $json = json_decode($body, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("Failed to decode response: " . json_last_error_msg());
        }

        if ($code >= 400) {
            throw new Exception("TokenManager HTTP $code: " . ($body ?: 'Unknown error'));
        }

        return $json;
    }
}

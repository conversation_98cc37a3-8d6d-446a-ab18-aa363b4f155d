<?php

// wp-content/plugins/supportboard/supportboard/include/r2r-config-loader.php

/**
 * Loads and resolves R2R API configuration from a custom INI file.
 * This function processes environment variable substitutions.
 *
 * @param string $iniFilePath The path to the custom INI file.
 * @return array The resolved configuration array.
 * @throws \RuntimeException If the INI file cannot be loaded or is malformed.
 */
function load_r2r_api_config(string $iniFilePath = null): array // <-- The default is `null` or a single string
{
    static $config = null;

    if ($config !== null) {
        return $config;
    }

    $pathsToTry = [];
    if ($iniFilePath !== null) {
        // If a specific path was provided, it takes precedence
        $pathsToTry[] = $iniFilePath;
    }

    // These are your desired "OR" paths, handled as an ordered list of fallbacks
    $pathsToTry[] = '/home/<USER>/public_html/php.ini';
    $pathsToTry[] = '/usr/local/etc/php/conf.d/custom.ini';

    $settings = false;
    $loadedFilePath = null;

    foreach ($pathsToTry as $path) {
        if (file_exists($path) && is_readable($path)) {
            $currentSettings = parse_ini_file($path, true);
            if ($currentSettings !== false) {
                $settings = $currentSettings;
                $loadedFilePath = $path;
                break; // Found one that works, stop trying
            } else {
                error_log("Failed to parse INI file (malformed?): " . $path);
            }
        } else {
            error_log("INI file not found or not readable: " . $path);
        }
    }

    if ($settings === false || $loadedFilePath === null) {
        $triedPaths = implode(', ', array_unique($pathsToTry));
        throw new \RuntimeException("Failed to load any INI file for R2R API configuration from paths: " . $triedPaths);
    }

    if (!isset($settings['env'])) {
        error_log("INI file loaded from " . $loadedFilePath . ", but '[env]' section not found.");
        throw new \RuntimeException("Missing '[env]' section in R2R API INI file at " . $loadedFilePath . ".");
    }

    $envSettings = $settings['env'];
    $resolvedConfig = [];

    $resolvedConfig['r2r_base_url'] = _resolve_ini_env_string($envSettings['r2r.base_url'] ?? '');
    $resolvedConfig['r2r_username'] = _resolve_ini_env_string($envSettings['r2r.username'] ?? '');
    $resolvedConfig['r2r_password'] = _resolve_ini_env_string($envSettings['r2r.password'] ?? '');

    $config = $resolvedConfig;
    return $config;
}

/**
 * Helper function to manually resolve environment variables from an INI string like "${VAR_NAME:-default_value}".
 * This mimics PHP 8.3+'s behavior when PHP itself parses the ini file.
 *
 * @param string $iniString The literal string from the INI file.
 * @return string The resolved value (from env var or INI default).
 */
function _resolve_ini_env_string(string $iniString): string
{
    // Regex to capture the variable name and the optional default value
    // Group 1: varName (e.g., R2R_BASE_URL)
    // Group 2: defaultValue (e.g., https://rapi.primalcom.com)
    if (preg_match('/^\$\{(?<varName>[A-Z0-9_]+)(?::-(?<defaultValue>.*))?\}$/', $iniString, $matches)) {
        $envVarName = $matches['varName'];
        $iniDefaultValue = $matches['defaultValue'] ?? ''; // Default from INI or empty string if not present

        $envValue = getenv($envVarName);

        // If the environment variable is set and not an empty string, use it.
        if ($envValue !== false && $envValue !== '') {
            return $envValue;
        } else {
            // Otherwise, use the default value extracted from the INI string.
            return $iniDefaultValue;
        }
    }

    // If the string doesn't match the expected "${...}" format, return it as-is.
    return $iniString;
}
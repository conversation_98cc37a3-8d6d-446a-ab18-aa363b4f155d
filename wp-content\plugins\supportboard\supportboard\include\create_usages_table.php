
<?php

/**
 * Create sb_usages table for storing OpenAI usage data
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

function sb_create_usages_table() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'sb_usages';
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        provider varchar(50) NOT NULL DEFAULT 'openai',
        prompt_tokens int(11) NOT NULL DEFAULT 0,
        completion_tokens int(11) NOT NULL DEFAULT 0,
        cached_input_tokens int(11) NOT NULL DEFAULT 0,
        creation_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY idx_provider (provider),
        KEY idx_creation_time (creation_time)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    
    $result = dbDelta($sql);
    
    if ($wpdb->last_error) {
        error_log('SB Usages Table Creation Error: ' . $wpdb->last_error);
        return false;
    }
    
    // Check if table was created successfully
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
    
    if ($table_exists) {
        error_log('SB Usages table created successfully: ' . $table_name);
        return true;
    } else {
        error_log('Failed to create SB Usages table: ' . $table_name);
        return false;
    }
}

// Function to add a usage record
function sb_add_usage_record($provider = 'openai', $prompt_tokens = 0, $completion_tokens = 0, $cached_input_tokens = 0) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'sb_usages';
    
    $result = $wpdb->insert(
        $table_name,
        array(
            'provider' => sanitize_text_field($provider),
            'prompt_tokens' => intval($prompt_tokens),
            'completion_tokens' => intval($completion_tokens),
            'cached_input_tokens' => intval($cached_input_tokens),
            'creation_time' => current_time('mysql')
        ),
        array('%s', '%d', '%d', '%d', '%s')
    );
    
    if ($result === false) {
        error_log('Failed to insert usage record: ' . $wpdb->last_error);
        return false;
    }
    
    return $wpdb->insert_id;
}

// Function to get usage records with filtering and pagination
function sb_get_usage_records($filters = array(), $limit = 20, $offset = 0, $order_by = 'creation_time', $order = 'DESC') {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'sb_usages';
    
    $where_conditions = array();
    $values = array();
    
    // Date filtering
    if (!empty($filters['date_from'])) {
        $where_conditions[] = "creation_time >= %s";
        $values[] = $filters['date_from'] . ' 00:00:00';
    }
    
    if (!empty($filters['date_to'])) {
        $where_conditions[] = "creation_time <= %s";
        $values[] = $filters['date_to'] . ' 23:59:59';
    }
    
    // Provider filtering
    if (!empty($filters['provider'])) {
        $where_conditions[] = "provider = %s";
        $values[] = $filters['provider'];
    }
    
    $where_clause = '';
    if (!empty($where_conditions)) {
        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    }
    
    $order_by = sanitize_sql_orderby($order_by);
    $order = strtoupper($order) === 'ASC' ? 'ASC' : 'DESC';
    
    $sql = "SELECT * FROM $table_name $where_clause ORDER BY $order_by $order LIMIT %d OFFSET %d";
    $values[] = $limit;
    $values[] = $offset;
    
    if (!empty($values)) {
        $prepared_sql = $wpdb->prepare($sql, $values);
    } else {
        $prepared_sql = $sql;
    }
    
    return $wpdb->get_results($prepared_sql);
}

// Function to get total count of usage records
function sb_get_usage_records_count($filters = array()) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'sb_usages';
    
    $where_conditions = array();
    $values = array();
    
    // Date filtering
    if (!empty($filters['date_from'])) {
        $where_conditions[] = "creation_time >= %s";
        $values[] = $filters['date_from'] . ' 00:00:00';
    }
    
    if (!empty($filters['date_to'])) {
        $where_conditions[] = "creation_time <= %s";
        $values[] = $filters['date_to'] . ' 23:59:59';
    }
    
    // Provider filtering
    if (!empty($filters['provider'])) {
        $where_conditions[] = "provider = %s";
        $values[] = $filters['provider'];
    }
    
    $where_clause = '';
    if (!empty($where_conditions)) {
        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    }
    
    $sql = "SELECT COUNT(*) FROM $table_name $where_clause";
    
    if (!empty($values)) {
        $prepared_sql = $wpdb->prepare($sql, $values);
    } else {
        $prepared_sql = $sql;
    }
    
    return $wpdb->get_var($prepared_sql);
}

// Run the table creation
if (function_exists('add_action')) {
    add_action('init', 'sb_create_usages_table');
}

?>

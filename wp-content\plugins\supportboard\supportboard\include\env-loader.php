<?php

function load_r2r_env() {
    // Start from the current directory and look upwards for the root
    $dir = __DIR__;
    $root_path = null;

    // Go up directory by directory until we find wp-config.php
    while ( !$root_path && '/' !== $dir && '.' !== $dir ) {
        if ( file_exists( $dir . '/wp-config.php' ) ) {
            $root_path = $dir;
        } else {
            $dir = dirname( $dir ); // Go up one level
        }
    }

    if (!$root_path) {
        error_log("ERROR: Could not determine WordPress root path to find .env file.");
        return;
    }

    $env_file_path = $root_path . '/.env';

    if (!is_readable($env_file_path)) {
        error_log("ERROR: .env file not found or is not readable at: " . $env_file_path);
        return;
    }

    $lines = file($env_file_path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $name = trim($name);
        $value = trim($value);
        if (substr($value, 0, 1) == '"' && substr($value, -1) == '"') {
            $value = substr($value, 1, -1);
        }
        putenv(sprintf('%s=%s', $name, $value));
    }
}
<?php

/*
 * ==========================================================
 * FUNCTIONS_R2R.PHP
 * ==========================================================
 *
 * R2R integration functions for Support Board
 *
 */


if (!defined('SB_PATH')) {
    die('Direct access forbidden.');
}

// Include R2R client if not already included
if (!class_exists('SupportBoardR2RClient')) {
    require_once(__DIR__ . '/r2r-client.php');
}


// Get R2R files with pagination
function sb_r2r_get_files($page = 1, $limit = 10) {
    error_log('sb_r2r_get_files called with page: ' . $page . ', limit: ' . $limit);

    try {
        $r2r = new SupportBoardR2RClient();

        $collectionId = sb_get_external_setting('r2r_default_collection_id');
        if (!$collectionId) {
            throw new Exception('No R2R collection ID defined.');
        }

        $offset = ($page - 1) * $limit;
        $filters = [
            'offset' => $offset,
            'limit' => $limit
        ];

        error_log("Calling R2R API for collection {$collectionId} with filters: " . json_encode($filters));
        $response = $r2r->r2rListFilesByCollection($collectionId, $filters);
        error_log('R2R API response: ' . json_encode($response));

        return ['success', $response];
    } catch (Exception $e) {
        error_log('R2R API error: ' . $e->getMessage());
        return ['error', $e->getMessage()];
    }
}

// Search R2R files
function sb_r2r_search_files($query, $search_settings = []) { 

    error_log('sb_r2r_search_files called with query: ' . $query . ' at ' . date('Y-m-d H:i:s') . "\n", FILE_APPEND);

    try {
        $r2r = new SupportBoardR2RClient();

        // Ensure the limit is correctly set within search_settings
        // Prioritize the limit from search_settings sent by frontend JS
        // This logic is now vital as it's the *only* place the limit is extracted/set
        // if (!isset($search_settings['limit'])) {
        //     $search_settings['limit'] = 10; // <--- Set a default limit here if frontend doesn't send it
        //     error_log('sb_r2r_search_files: Limit not found in search_settings, defaulting to 10.' . "\n", FILE_APPEND);
        // }

        // // Calculate offset (R2R API uses offset for pagination, not directly 'page')
        // // Ensure this offset calculation uses the limit that will actually be sent to R2R.
        // $offset = ($page - 1) * $search_settings['limit'];
        // $search_settings['offset'] = $offset;

        // Now call the modified r2rSearch method with query and search_settings
        $response = $r2r->r2rSearch($query, $search_settings);
        error_log('R2R search response: ' . json_encode($response) . "\n", FILE_APPEND);

        return ['success', $response];
    } catch (Exception $e) {
        error_log('R2R search error: ' . $e->getMessage() . "\n", FILE_APPEND);
        return ['error', $e->getMessage()];
    }
}

// Delete multiple R2R files
function sb_r2r_delete_files($file_ids) {
    error_log('sb_r2r_delete_files function called with file_ids: ' . json_encode($file_ids));
    
    try {
        // Create R2R client directly
        $r2r = new SupportBoardR2RClient();
        $results = [];
        $success = true;
        
        // Process each file ID
        foreach ($file_ids as $file_id) {
            try {
                error_log('Attempting to delete R2R file ID: ' . $file_id);
                $response = $r2r->r2rDeleteFileById($file_id);
                error_log('R2R delete response for file ID ' . $file_id . ': ' . json_encode($response));
                
                $results[$file_id] = $response;
                
                // Check if the response indicates success
                if (!$response || (is_array($response) && (!isset($response['results']) || !isset($response['results']['success']) || $response['results']['success'] !== true))) {
                    error_log('R2R delete failed for file ID ' . $file_id . ': ' . json_encode($response));
                    $success = false;
                } else {
                    error_log('Successfully deleted R2R file ID: ' . $file_id);
                }
            } catch (Exception $e) {
                error_log('Exception when deleting R2R file ID ' . $file_id . ': ' . $e->getMessage());
                $results[$file_id] = ['error' => $e->getMessage()];
                $success = false;
            }
        }
        
        $final_result = [$success ? 'success' : 'error', $results];
        error_log('Final result from sb_r2r_delete_files: ' . json_encode($final_result));
        return $final_result;
    } catch (Exception $e) {
        error_log('Global exception in sb_r2r_delete_files: ' . $e->getMessage());
        return ['error', $e->getMessage()];
    }
}

// Format file size
function sb_format_size($size) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $i = 0;
    while ($size >= 1024 && $i < count($units) - 1) {
        $size /= 1024;
        $i++;
    }
    return round($size, 2) . ' ' . $units[$i];
}

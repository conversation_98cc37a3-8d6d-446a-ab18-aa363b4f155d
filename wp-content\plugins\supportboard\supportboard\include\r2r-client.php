<?php
define('R2R_LOG_FILE', '/var/www/html/wp-content/plugins/supportboard/supportboard/include/r2r-upload.log');

require_once(__DIR__ . '/config-loader.php');
require_once(__DIR__ . '/R2RTokenManager.php');

class SupportBoardR2RClient {
    private $base_url;
    private $tokenManager;

    public function __construct() {
        try {
            $config = load_r2r_api_config();

            $this->base_url = $config['r2r_base_url'];
            $username = $config['r2r_username'];
            $password = $config['r2r_password'];

            $this->tokenManager = new R2RTokenManager($this->base_url, $username, $password);
        } catch (\RuntimeException $e) {
            error_log("Failed to initialize R2R Client due to config error: " . $e->getMessage());
            throw $e;
        }
    }

    private function log($msg) {
        file_put_contents(R2R_LOG_FILE, "[" . date('Y-m-d H:i:s') . "][r2r-client.php] $msg\n", FILE_APPEND);
    }

    private function request($method, $path, $data = null, $headers = [], $isJson = true) {
        $url = rtrim($this->base_url, '/') . '/' . ltrim($path, '/');
        $defaultHeaders = ['Accept: application/json', $this->tokenManager->getAuthHeader()];
        if ($isJson) $defaultHeaders[] = 'Content-Type: application/json';

        $response = $this->executeCurl($method, $url, $data, array_merge($defaultHeaders, $headers), $isJson);

        if ($this->tokenManager->maybeRefreshTokenIfUnauthorized($response['http_code'])) {
            $this->log("Token expired. Refreshing...");
            $defaultHeaders[1] = $this->tokenManager->getAuthHeader();
            $response = $this->executeCurl($method, $url, $data, array_merge($defaultHeaders, $headers), $isJson);
        }

        if ($response['error']) throw new Exception("cURL error: {$response['error']}");
        if ($response['http_code'] >= 400) throw new Exception("HTTP {$response['http_code']}: {$response['body']}");

        $json = json_decode($response['body'], true);
        if (json_last_error() !== JSON_ERROR_NONE && $isJson) {
            throw new Exception("JSON decode error: " . json_last_error_msg());
        }

        return $json ?? $response['body'];
    }

    private function executeCurl($method, $url, $data, $headers, $isJson) {
        $ch = curl_init($url);
        curl_setopt_array($ch, [
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 300,
            CURLOPT_HTTPHEADER => $headers
        ]);

        if ($data !== null) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $isJson ? json_encode($data) : $data);
        }

        $body = curl_exec($ch);
        $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        return [
            'body' => $body,
            'http_code' => $code,
            'error' => $error
        ];
    }


    public static function get_content_type($filename, $original_path = null) {
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

        if ($original_path) {
            $original_extension = strtolower(pathinfo($original_path, PATHINFO_EXTENSION));
            if ($original_extension && $original_extension !== $extension) {
                $extension = $original_extension;
            }
        }

        $mime_types = [
            'txt'  => 'text/plain', 'html' => 'text/html', 'htm'  => 'text/html',
            'css'  => 'text/css',   'js'   => 'application/javascript', 'json' => 'application/json',
            'xml'  => 'application/xml', 'pdf' => 'application/pdf',
            'doc'  => 'application/msword', 'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls'  => 'application/vnd.ms-excel', 'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'ppt'  => 'application/vnd.ms-powerpoint', 'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'odt'  => 'application/vnd.oasis.opendocument.text', 'ods' => 'application/vnd.oasis.opendocument.spreadsheet',
            'odp'  => 'application/vnd.oasis.opendocument.presentation', 'md' => 'text/markdown',
            'jpg'  => 'image/jpeg', 'jpeg' => 'image/jpeg', 'png'  => 'image/png',
            'gif'  => 'image/gif',  'svg'  => 'image/svg+xml',
            'mp3'  => 'audio/mpeg', 'wav'  => 'audio/wav',
            'mp4'  => 'video/mp4',  'webm' => 'video/webm',
            'zip'  => 'application/zip', 'tar' => 'application/x-tar', 'gz' => 'application/gzip',
        ];

        return $mime_types[$extension] ?? 'application/octet-stream';
    }

    public function r2rCreateAndUploadFile($fileData, $fileName, $fileType = null, $options = []) {
        $fileType = $fileType ?: self::get_content_type($fileName);
        $boundary = uniqid('r2r');
        $body = '';

        if (!empty($options['metadata'])) {
            $body .= "--$boundary\r\n";
            $body .= "Content-Disposition: form-data; name=\"metadata\"\r\n";
            $body .= "Content-Type: application/json\r\n\r\n";
            $body .= json_encode($options['metadata']) . "\r\n";
        }

        if (!empty($options['collection_ids'])) {
            $body .= "--$boundary\r\n";
            $body .= "Content-Disposition: form-data; name=\"collection_ids\"\r\n";
            $body .= "Content-Type: application/json\r\n\r\n";
            $body .= json_encode($options['collection_ids']) . "\r\n";
        }

        $body .= "--$boundary\r\n";
        $body .= "Content-Disposition: form-data; name=\"file\"; filename=\"$fileName\"\r\n";
        $body .= "Content-Type: $fileType\r\n\r\n";
        $body .= $fileData . "\r\n";
        $body .= "--$boundary--\r\n";

        return $this->request('POST', '/v3/documents', $body, [
            "Content-Type: multipart/form-data; boundary=$boundary"
        ], false);
    }

    public function r2rDeleteFileById($fileId) {
        return $this->request('DELETE', "/v3/documents/{$fileId}");
    }

    public function r2rDeleteCollection($collectionId) {
        return $this->request('DELETE', "/v3/collections/{$collectionId}");
    }

    public function r2rListFiles($filters = []) {
        $query = http_build_query($filters);
        return $this->request('GET', '/v3/documents' . ($query ? "?$query" : ''));
    }

    public function r2rListFilesByCollection($collectionId, $filters = []) {
        $query = http_build_query($filters);
        return $this->request(
            'GET',
            "/v3/collections/{$collectionId}/documents" . ($query ? "?$query" : '')
        );
    }

    public function r2rListCollections($filters = []) {
        $query = http_build_query($filters);
        return $this->request('GET', '/v3/collections' . ($query ? "?$query" : ''));
    }

    public function r2rGetFileById($fileId) {
        return $this->request('GET', "/v3/documents/{$fileId}");
    }

    public function r2rFileExtract($fileId) {
        // Add required parameters for the extract endpoint
        $extractData = [
            'settings' => [
                'chunk_size' => 1000,
                'chunk_overlap' => 200
            ],
            'run_with_orchestration' => true
        ];
        
        return $this->request('POST', "/v3/documents/{$fileId}/extract", $extractData);
    }

    public function r2rSearch($query, $search_settings = []) { // Changed signature
        $payload = [
            'query' => $query,
            'search_settings' => $search_settings // Nest search settings correctly
        ];
        return $this->request('POST', '/v3/retrieval/search', $payload);
    }

    public function r2rAddFileToCollection($fileId, $collectionId) {
        return $this->request('POST', "/v3/collections/{$collectionId}/documents", [
            'document_ids' => [$fileId]
        ]);
    }

    public function r2rAddCollection($name, $description = null) {
        return $this->request('POST', '/v3/collections', [
            'name' => $name,
            'description' => $description
        ]);
    }

    public function r2rPullCollection($collectionId) {
        // The API error indicates it expects a boolean value in the body.
        // We'll send `true` to initiate the pull.
        return $this->request('POST', "/v3/graphs/{$collectionId}/pull", true); // Changed to 'true'
    }

    public function r2rGetCollectionById($collectionId) {
        try {
            return $this->request('GET', "/v3/collections/{$collectionId}");
        } catch (Exception $e) {
            // Return null if collection doesn't exist (404) or other errors
            return null;
        }
    }
}

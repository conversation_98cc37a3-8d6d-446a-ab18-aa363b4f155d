<?php

// Set up error log paths
$logFile = '/var/www/html/wp-content/plugins/supportboard/supportboard/include/r2r-upload.log';

// Log script start time
file_put_contents($logFile, "r2rUpload.php started at " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);

// // Log incoming POST and FILES data
// file_put_contents($logFile, 'POST: ' . print_r($_POST, true) . "\nFILES: " . print_r($_FILES, true) . "\n", FILE_APPEND);

// // Enable full error reporting but log errors instead of displaying
// error_reporting(E_ALL);
// ini_set('display_errors', 0);
// ini_set('log_errors', 1);
// ini_set('error_log', $errorLogFile);

// // Handle fatal errors and send JSON response
// register_shutdown_function(function () use ($logFile) {
//     $error = error_get_last();
//     if ($error && !headers_sent()) {
//         file_put_contents($logFile, 'Fatal: ' . print_r($error, true) . "\n", FILE_APPEND);
//         header('Content-Type: application/json');
//         echo json_encode(['status' => 'error', 'message' => 'Fatal error: ' . $error['message']]);
//     }
// });


// Include dependencies
require_once(__DIR__ . '/functions.php');
require_once(__DIR__ . '/r2r-client.php');

// Initialize default response
$response = ['status' => 'error', 'message' => 'Generic error.', 'r2r_status' => 'not_processed'];

try {
    // Check if file was uploaded correctly
    if (!isset($_FILES['file']) || $_FILES['file']['error'] !== 0) {
        file_put_contents($logFile, "File error code: " . ($_FILES['file']['error'] ?? 'no file') . "\n", FILE_APPEND);
        throw new Exception('File upload failed or not found.');
    }

    // Sanitize uploaded file name
    $fileName = sb_sanatize_file_name($_FILES['file']['name']);

    // Get file extension
    $extension = sb_isset(pathinfo($fileName), 'extension');

    // Validate file extension
    if (!sb_is_allowed_extension($extension)) {
        throw new Exception('Extension not allowed.');
    }

    // Determine date-based upload folder
    $dateDir = date('d-m-y');
    $uploadPath = defined('SB_UPLOAD_PATH') ? SB_UPLOAD_PATH : '../uploads';
    $uploadURL = defined('SB_UPLOAD_URL') ? SB_UPLOAD_URL : SB_URL . '/uploads';
    $dir = "$uploadPath/$dateDir";
    $urlDir = "$uploadURL/$dateDir";

    // Create upload folder if not exists
    if (!file_exists($dir) && !mkdir($dir, 0755, true)) {
        throw new Exception("Failed to create directory: $dir");
    }

    // Create a unique and slugified file name
    $slugFileName = rand(1000, 99999) . '_' . sb_string_slug($fileName);
    $fullPath = "$dir/$slugFileName";
    $fileURL = "$urlDir/$slugFileName";

    // Move file to target path
    if (!move_uploaded_file($_FILES['file']['tmp_name'], $fullPath)) {
        throw new Exception("Failed to move uploaded file to $fullPath");
    }

    // Read uploaded file content
    $fileContent = file_get_contents($fullPath);
    if ($fileContent === false) {
        throw new Exception("Unable to read uploaded file: $fullPath");
    }

    // Determine file MIME type
    $mimeType = mime_content_type($fullPath) ?: SupportBoardR2RClient::get_content_type($fileName);

    // For now supportboard doesn't have a category yet so, we will use the hardcode value 'default-category'
    $categoryId = isset($_POST['sb_category_id']) ? sanitize_text_field($_POST['sb_category_id']) : 'default-category';

    // Initialize R2R client
    $r2r = new SupportBoardR2RClient();   
    // Get collection ID from database (if exists)
    $collectionId = sb_get_external_setting('r2r_default_collection_id') ?? null;
    $siteHost = preg_replace('/^www\./', '', parse_url(sb_wp_site_url(), PHP_URL_HOST) ?: 'localhost');
    $collectionName = "$siteHost-website";
    
    // Case 1: Check if collection exists in R2R even if not in our DB
    if (!$collectionId) {
        file_put_contents($logFile, "No collection ID in database, checking if one exists in R2R\n", FILE_APPEND);
        try {
            // Try to find collection by name in R2R
            $collections = $r2r->r2rListCollections();
            if (isset($collections['results']) && is_array($collections['results'])) {
                foreach ($collections['results'] as $collection) {
                    if (isset($collection['name']) && $collection['name'] === $collectionName) {
                        $collectionId = $collection['id'];
                        file_put_contents($logFile, "Found existing collection in R2R with ID: $collectionId\n", FILE_APPEND);
                        // Save to database for future use
                        sb_save_external_setting('r2r_default_collection_id', $collectionId);
                        break;
                    }
                }
            }
        } catch (Exception $e) {
            file_put_contents($logFile, "Error checking for existing collections: " . $e->getMessage() . "\n", FILE_APPEND);
            // Continue with null collectionId - will create new one later
        }
    }
    
    // Case 2: Collection ID exists in DB but check if it exists in R2R
    if ($collectionId) {
        file_put_contents($logFile, "Verifying collection ID: $collectionId\n", FILE_APPEND);
        try {
            $collectionDetails = $r2r->r2rGetCollectionById($collectionId);
            if (!$collectionDetails || !isset($collectionDetails['results'])) {
                file_put_contents($logFile, "Collection ID $collectionId not found in R2R\n", FILE_APPEND);
                $collectionId = null;
            } else {
                file_put_contents($logFile, "Collection ID verified in R2R\n", FILE_APPEND);
            }
        } catch (Exception $e) {
            file_put_contents($logFile, "Error verifying collection: " . $e->getMessage() . "\n", FILE_APPEND);
            $collectionId = null;
        }
    }
    
    // Case 3: No valid collection ID found, create a new one
    if (!$collectionId) {
        file_put_contents($logFile, "Creating new collection...\n", FILE_APPEND);
        $description = "Auto-created collection for $siteHost";

        try {
            file_put_contents($logFile, "Attempting to create collection with name: $collectionName\n", FILE_APPEND);
            $createResp = $r2r->r2rAddCollection($collectionName, $description);
            
            if (!isset($createResp['results']['collection_id'])) {
                throw new Exception("Failed to create collection: " . json_encode($createResp));
            }
            
            $collectionId = $createResp['results']['collection_id'];
            file_put_contents($logFile, "Successfully created collection with ID: $collectionId\n", FILE_APPEND);
            sb_save_external_setting('r2r_default_collection_id', $collectionId);
        } catch (Exception $e) {
            // If creation fails, try one more time to find by name (in case of race condition)
            file_put_contents($logFile, "Error creating collection: " . $e->getMessage() . "\n", FILE_APPEND);
            file_put_contents($logFile, "Trying one more time to find existing collection...\n", FILE_APPEND);
            
            try {
                $collections = $r2r->r2rListCollections();
                if (isset($collections['results']) && is_array($collections['results'])) {
                    foreach ($collections['results'] as $collection) {
                        if (isset($collection['name']) && $collection['name'] === $collectionName) {
                            $collectionId = $collection['id'];
                            file_put_contents($logFile, "Found existing collection with ID: $collectionId\n", FILE_APPEND);
                            sb_save_external_setting('r2r_default_collection_id', $collectionId);
                            break;
                        }
                    }
                }
                
                if (!$collectionId) {
                    throw new Exception("Could not create or find a valid collection");
                }
            } catch (Exception $innerE) {
                throw new Exception("Failed to create or find R2R collection: " . $e->getMessage());
            }
        }
    }

    // Set up file metadata for R2R
    $uploadOptions = [
        'collection_ids' => [$collectionId],
        'metadata' => [
            'name' => $fileName,
            'sb_category_id' => $categoryId,
            'source_url' => $fileURL,
            'sb_file_name_slug' => $slugFileName,
            'title' => $fileName
        ]
    ];
    
    try {
        // Upload file to R2R
        $uploadResp = $r2r->r2rCreateAndUploadFile($fileContent, $fileName, $mimeType, $uploadOptions);
        $docId = $uploadResp['results']['document_id'];
    } catch (Exception $e) {
        // Handle duplicate document ID
        if (strpos($e->getMessage(), '409') !== false && preg_match('/Document ([a-f0-9-]+) already exists/', $e->getMessage(), $match)) {
            $docId = $match[1];
            $r2r->r2rDeleteFileById($docId);
            $uploadResp = $r2r->r2rCreateAndUploadFile($fileContent, $fileName, $mimeType, $uploadOptions);
            $docId = $uploadResp['results']['document_id'];
        } else {
            throw $e;
        }
    }

    // Attempt content extraction from uploaded file
    try {
        $r2r->r2rFileExtract($docId);

        $final_status = 'success'; // Overall response status (for 'status' field)
        $final_r2r_status = 'success'; // R2R specific status (for 'r2r_status' field)
        $final_r2r_message = 'File processed by R2R successfully'; // Base message

        // Attempt collection pull/knowledge graph sync
        try {
            $pullResponse = $r2r->r2rPullCollection($collectionId);
            file_put_contents($logFile, 'R2R Collection Pull Success: ' . print_r($pullResponse, true) . "\n", FILE_APPEND);
            $final_r2r_message .= ' and knowledge graph synced.';
        } catch (Exception $e) {
            file_put_contents($logFile, 'R2R Collection Pull Failed: ' . $e->getMessage() . "\n", FILE_APPEND);
            // *** CHANGE HERE: Set both final_status and final_r2r_status to 'error' if pull fails ***
            $final_status = 'error';
            $final_r2r_status = 'error';
            $final_r2r_message .= ' but knowledge graph sync failed. Please try re-uploading the file if you experience search issues.';
        }

        // Assemble the final response array based on the determined status and message
        $response = [
            'status' => $final_status, // Use the dynamically determined overall status
            'file_name' => $fileName,
            'url' => $fileURL,
            'r2r_status' => $final_r2r_status, // Use the dynamically determined R2R status
            'r2r_message' => $final_r2r_message // Use the dynamically determined message
        ];
    } catch (Exception $e) {
        // This 'catch' block handles overall extraction failure (e.g., API errors, timeouts)
        $response = [
            'status' => 'error',
            'file_name' => $fileName,
            'url' => $fileURL,
            'r2r_status' => 'error',
            'r2r_message' => 'File uploaded, but extraction failed.',
            'r2r_extraction_error' => $e->getMessage()
        ];
    }

    
} catch (Exception $e) {
    // Log and return error message
    file_put_contents($logFile, 'Error: ' . $e->getMessage() . "\n", FILE_APPEND);
    $response['message'] = $e->getMessage();
}

// Return JSON response
header('Content-Type: application/json');
// Log final response
file_put_contents($logFile, 'Final Response: ' . print_r($response, true) . "\n", FILE_APPEND);
die(json_encode($response));

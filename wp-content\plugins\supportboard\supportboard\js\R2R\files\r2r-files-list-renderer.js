/*
 * ==========================================================
 * R2R FILES LIST RENDERER
 * ==========================================================
 *
 * Renders the traditional file list table for the R2R Files tab.
 *
 */

(function ($) {
  "use strict";

  // Import utilities (assuming r2rUtils is loaded globally)
  const r2rUtils = window.r2rUtils;

  // DOM Elements (specific to this renderer)
  const $fileTable = $("#r2r-files-table-uploaded");
  const $pagination = $("#r2r-files-pagination");

  /**
   * Renders the regular file list table with its data and pagination.
   * @param {Array<object>} files Array of file objects to display.
   * @param {number} currentPage The current page number for pagination.
   * @param {number} totalPages The total number of pages for pagination.
   * @param {Function} onSelectAll Callback for select all checkbox change.
   */
  function renderFileListTable(files, currentPage, totalPages, onSelectAll) {
    if (!files || files.length === 0) {
      $fileTable.html('<tbody><tr><td colspan="8" class="r2r-files-empty">No files found</td></tr></tbody>');
      $pagination.empty(); // Clear pagination if no files
      return;
    }

    let html = `
      <thead>
        <tr>
            <th style="width: 30px; text-align: center;"><input type="checkbox" id="r2r-files-select-all" /></th>
            <th style="width: 300px;">Title</th>
            <th style="width: 120px; text-align: center;">Document Type</th>
            <th style="width: 130px; text-align: center;">Ingestion Status</th>
            <th style="width: 130px; text-align: center;">Extraction Status</th>
            <th style="width: 70px; text-align: center;">Source</th>
            <th style="width: 160px; text-align: center;">Created At</th>
            <th style="width: 160px; text-align: center;">Updated At</th>
        </tr>
      </thead>
      <tbody>`;

    files.forEach(function (file) {
      const sourceUrl = file.metadata?.source_url || "#";
      const fullTitle = file.title || "N/A";
      const shortTitle = r2rUtils.truncateText(fullTitle, 50);
      html += `
        <tr>
            <td style="text-align: center; vertical-align: middle;"><input type="checkbox" class="r2r-files-checkbox" value="${file.id}" /></td>
            <td style="max-width: 250px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;" title="${fullTitle.replace(/"/g, "&quot;")}">${shortTitle}</td>
            <td style="text-align: center; vertical-align: middle;">${file.document_type}</td>
            <td style="text-align: center; vertical-align: middle;">${r2rUtils.getIngestionBadge(file.ingestion_status)}</td>
            <td style="text-align: center; vertical-align: middle;">${r2rUtils.getExtractionBadge(file.extraction_status)}</td>
            <td style="text-align: center; vertical-align: middle;"><a href="${sourceUrl}" target="_blank" title="${sourceUrl}">📄</a></td>
            <td style="text-align: center; vertical-align: middle;">${r2rUtils.formatDate(file.created_at)}</td>
            <td style="text-align: center; vertical-align: middle;">${r2rUtils.formatDate(file.updated_at)}</td>
        </tr>
      `;
    });
    html += `</tbody>`;
    $fileTable.html(html);

    _renderPagination(currentPage, totalPages);

    // Bind select all functionality (must be done after HTML is rendered)
    $("#r2r-files-select-all").off("change").on("change", onSelectAll);
  }

  /**
   * Renders the pagination controls for the list view.
   * @param {number} currentPage The current page number.
   * @param {number} totalPages The total number of pages.
   */
  function _renderPagination(currentPage, totalPages) {
    if (totalPages <= 1) {
      $pagination.empty();
      return;
    }

    let html = '<div class="r2r-files-pagination-controls">';
    html += `<span class="r2r-files-pagination-btn ${currentPage === 1 ? "r2r-files-disabled" : ""}" data-page="prev">« Prev</span>`;

    const maxPagesToShow = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);

    if (endPage - startPage + 1 < maxPagesToShow) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      html += `<span class="r2r-files-pagination-btn ${i === currentPage ? "r2r-files-current-page" : ""}" data-page="${i}">${i}</span>`;
    }

    html += `<span class="r2r-files-pagination-btn ${currentPage === totalPages ? "r2r-files-disabled" : ""}" data-page="next">Next »</span>`;
    html += "</div>";

    $pagination.html(html);
  }

  // Export functions
  window.r2rFilesListRenderer = {
    renderFileListTable,
  };
})(jQuery);

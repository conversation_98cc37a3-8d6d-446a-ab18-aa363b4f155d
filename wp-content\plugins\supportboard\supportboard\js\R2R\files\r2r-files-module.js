/*
 * ==========================================================
 * R2R FILES MODULE
 * ==========================================================
 *
 * Main module for managing the R2R Files tab (list and search views).
 * Orchestrates data fetching, rendering, and UI interactions.
 *
 */

(function ($) {
  "use strict";

  // --- Imports ---
  const r2rUtils = window.r2rUtils;
  const r2rAjaxRequests = window.r2rAjaxRequests;
  const r2rFilesListRenderer = window.r2rFilesListRenderer;
  const r2rFilesSearchRenderer = window.r2rFilesSearchRenderer;

  // --- General Constants and State ---
  let selectedFiles = [];
  let isSearchMode = false; // Tracks if the UI is in search mode or list view mode
  let currentSearchQuery = "";

  // --- List View: Constants and State ---
  const ITEMS_PER_PAGE_LIST_VIEW = 10;
  let currentPageListView = 1;
  let totalPagesListView = 1;

  // --- Search View: Constants and State ---
  const ITEMS_PER_PAGE_SEARCH_CARDS = 5; // Number of cards to render per infinite scroll load
  const MIN_SCORE_FILTER = 0.1;
  const MAX_WORD_COUNT_SEARCH = 3000;
  let cardsCurrentLoadBatch = 0; // Tracks which batch of `ITEMS_PER_PAGE_SEARCH_CARDS` has been rendered
  let allSearchResults = []; // Stores all search results from the backend for frontend infinite scroll
  let cardsIsLoading = false; // Flag to prevent multiple simultaneous infinite scroll loads

  const PARSE_OPTIONS_FOR_SEARCH = {
    retrieveDocument: true,
    maxWordCount: MAX_WORD_COUNT_SEARCH,
    includeGraph: true,
    minScore: MIN_SCORE_FILTER,
    // No limit here, as we want to process all results returned by the backend search
  };

  // --- DOM Element Caching ---
  // General
  const $deleteButton = $("#sb-r2r-delete-files");

  // List View
  const $fileTable = $("#r2r-files-table-uploaded");
  const $fileListView = $("#r2r-file-list-view");

  // Search View
  const $searchInput = $("#r2r-files-search-input");
  const $searchButton = $("#r2r-files-search-button");
  const $clearSearchButton = $("#r2r-files-clear-search");
  const $searchStatus = $("#r2r-files-search-status");
  const $r2rFileCardsContainer = $("#r2r-file-cards-container");
  const $r2rCardsInnerContainer = $("#r2r-cards-inner-container");
  const $r2rCardsLoadMore = $("#r2r-cards-load-more");

  // --- General Event Handlers & UI Updaters ---
  function _refreshCurrentView() {
    if (isSearchMode) {
      if (currentSearchQuery) {
        _performFileSearch(currentSearchQuery, 1); // Re-run search from page 1
      }
    } else {
      _loadFileList(currentPageListView); // Refresh the regular file list
    }
  }

  // --- List View: Event Handlers ---
  function _handlePaginationClick(e) {
    e.preventDefault();
    if ($(this).hasClass("r2r-files-disabled")) return;

    const page = $(this).data("page");
    if (page === "prev") {
      currentPageListView--;
    } else if (page === "next") {
      currentPageListView++;
    } else {
      currentPageListView = parseInt(page);
    }
    _loadFileList(currentPageListView);
  }

  function _handleFileSelection() {
    const fileId = $(this).val();
    if ($(this).is(":checked")) {
      selectedFiles.push(fileId);
    } else {
      selectedFiles = selectedFiles.filter((id) => id !== fileId);
    }
    _updateDeleteButtonState();
  }

  function _handleSelectAllFiles() {
    const isChecked = $(this).is(":checked");
    $(".r2r-files-checkbox").prop("checked", isChecked);
    selectedFiles = isChecked
      ? $(this)
          .closest("table")
          .find(".r2r-files-checkbox")
          .map(function () {
            return $(this).val();
          })
          .get()
      : [];
    _updateDeleteButtonState();
  }

  async function _handleDeleteFiles() {
    if ($deleteButton.hasClass("sb-disabled") || selectedFiles.length === 0) return;

    ModalHelper.showModal("confirm", "Are you sure you want to delete the selected files?", async () => {
      try {
        await r2rAjaxRequests.deleteFiles(selectedFiles);
        ModalHelper.showModal("success", "Files deleted successfully.");
        _loadFileList(currentPageListView); // Refresh the list after deletion
      } catch (error) {
        ModalHelper.showModal("error", error.message);
      }
    });
  }
  function _updateDeleteButtonState() {
    if (selectedFiles.length > 0 && !isSearchMode) {
      // Only enable delete in list view
      $deleteButton.removeClass("sb-disabled");
    } else {
      $deleteButton.addClass("sb-disabled");
    }
  }

  // --- Search View: Event Handlers ---
  function _handleSearchButtonClick() {
    const query = $searchInput.val().trim();
    if (query.length > 0) {
      cardsCurrentLoadBatch = 0; // Reset for new search
      isSearchMode = true;
      currentSearchQuery = query;

      // Make sure container is properly set up for scrolling
      $r2rFileCardsContainer.css({
        height: "600px",
        "max-height": "600px",
        "overflow-y": "auto",
      });

      _performFileSearch(query, 1); // Start new search from backend page 1
    }
  }

  function _clearSearch() {
    $searchInput.val("");
    isSearchMode = false;
    currentSearchQuery = "";
    currentPageListView = 1; // Reset list view pagination
    cardsCurrentLoadBatch = 0; // Reset card view infinite scroll batch
    allSearchResults = []; // Clear previous search results

    $searchStatus.hide();
    $r2rCardsInnerContainer.empty(); // Clear existing cards
    r2rUtils.hideLoading($r2rCardsLoadMore); // Hide card loading indicator

    // Show the list view, hide the card view
    $fileListView.show();
    $r2rFileCardsContainer.removeClass("r2r-files-card-view-active").addClass("r2r-files-card-view-hidden");

    _loadFileList(currentPageListView); // Reload the regular file list
  }

  // --- List View: Data Loading and Rendering ---

  // Loads and renders the paginated list of files.

  async function _loadFileList(page) {
    $fileListView.show();
    $r2rFileCardsContainer.removeClass("r2r-files-card-view-active").addClass("r2r-files-card-view-hidden");

    r2rUtils.showLoading($fileTable, "Loading files...", 8);
    selectedFiles = [];
    _updateDeleteButtonState();
    $deleteButton.show(); // Delete button is always visible in list view

    try {
      const data = await r2rAjaxRequests.fetchFiles(page, ITEMS_PER_PAGE_LIST_VIEW);
      const files = data.results || [];

      // --- FIX: Read total_entries instead of total_count ---
      let totalItems = data.total_entries; // Corrected from data.total_count
      // --- END FIX ---

      if (typeof totalItems !== "number" || isNaN(totalItems)) {
        // Fallback if backend total_entries is unreliable. Infer strictly based on current page results.
        if (files.length < ITEMS_PER_PAGE_LIST_VIEW) {
          totalItems = currentPageListView * ITEMS_PER_PAGE_LIST_VIEW;
        } else {
          // if files.length === ITEMS_PER_PAGE_LIST_VIEW
          totalItems = currentPageListView * ITEMS_PER_PAGE_LIST_VIEW + 1;
        }
      }
      totalPagesListView = Math.ceil(totalItems / ITEMS_PER_PAGE_LIST_VIEW) || 1;

      // Ensure totalPages is at least 1 if there are any files, or if on page 1
      if (totalPagesListView === 0 && (files.length > 0 || currentPageListView === 1)) {
        totalPagesListView = 1;
      }

      r2rFilesListRenderer.renderFileListTable(files, currentPageListView, totalPagesListView, _handleSelectAllFiles);
    } catch (error) {
      r2rUtils.showError($fileTable, error.message, 8);
    }
  }

  // --- Search View: Data Loading and Rendering ---

  /**
   * Creates a mock client object for the parseR2rResult function.
   * This mock client provides a way to look up document details from the chunk search results.
   * @param {Array<object>} chunkSearchResults - The array of chunk search results from the backend.
   * @returns {object} A mock client object with a documents.list method.
   */
  function _createDocumentProviderForParsing(chunkSearchResults) {
    return {
      documents: {
        list: async ({ ids }) => {
          const documents = ids
            .map((id) => {
              const foundChunk = chunkSearchResults.find((c) => (c.document_id || c.documentId) === id);
              if (foundChunk) {
                return {
                  id: id,
                  title: foundChunk.metadata?.title || foundChunk.metadata?.name || "Untitled Document",
                  name: foundChunk.metadata?.name || foundChunk.metadata?.title || "Untitled Document",
                  metadata: { source_url: foundChunk.metadata?.source_url },
                };
              }
              return null;
            })
            .filter(Boolean);
          return { results: documents };
        },
      },
    };
  }

  // Performs a file search and renders the results as cards.
  async function _performFileSearch(query, backendPage) {
    // Hide list view, show card view
    $fileListView.hide();
    $r2rFileCardsContainer.removeClass("r2r-files-card-view-hidden").addClass("r2r-files-card-view-active");

    if (backendPage === 1) {
      r2rFilesSearchRenderer.showSearchingMessage(); // Renderer handles "Searching..." message
      allSearchResults = []; // Clear previous search results for a new search
      cardsCurrentLoadBatch = 0; // Reset batch counter
    }

    cardsIsLoading = true;

    selectedFiles = []; // Clear selected files in search mode
    _updateDeleteButtonState(); // Update button state
    $deleteButton.hide(); // Hide delete button in search mode

    try {
      const resultsData = await r2rAjaxRequests.searchFiles(query, window.r2rCollectionIdFromPHP);
      const chunkSearchResults = resultsData.results?.chunk_search_results || [];
      const graphSearchResults = resultsData.results?.graph_search_results || [];

      const documentProvider = _createDocumentProviderForParsing(chunkSearchResults);
      const parsedResults = await r2rAjaxRequests.parseR2rResult(documentProvider, chunkSearchResults, graphSearchResults, PARSE_OPTIONS_FOR_SEARCH);

      // Store all new results for frontend infinite scroll
      allSearchResults = allSearchResults.concat(parsedResults.texts);

      // Render the first batch of cards using the renderer's function
      const newBatch = r2rFilesSearchRenderer.renderCurrentCardsBatch(
        cardsCurrentLoadBatch, // which is 0 for a new search
        ITEMS_PER_PAGE_SEARCH_CARDS,
        allSearchResults
      );
      cardsCurrentLoadBatch = newBatch; // Update batch counter

      $searchStatus.html(`Found ${allSearchResults.length} result${allSearchResults.length !== 1 ? "s" : ""} for "${query}"`).show();
    } catch (error) {
      r2rFilesSearchRenderer.showSearchError(error.message); // Renderer handles error display
    } finally {
      r2rUtils.hideLoading($r2rCardsLoadMore);
      cardsIsLoading = false;
    }
  }

  // --- Core Event Binding ---
  function _bindEvents() {
    // List View Events
    $(document).on("click", "#r2r-files-pagination .r2r-files-pagination-btn", _handlePaginationClick);
    $(document).on("change", ".r2r-files-checkbox", _handleFileSelection);

    // Search View Events
    $searchButton.on("click", _handleSearchButtonClick);
    $searchInput.on("keypress", function (e) {
      if (e.which === 13) {
        _handleSearchButtonClick();
      }
    });
    $clearSearchButton.on("click", _clearSearch);
    $r2rFileCardsContainer.off("scroll");
    $r2rFileCardsContainer.on("scroll", function () {
      r2rFilesSearchRenderer.handleInfiniteScroll(
        isSearchMode,
        cardsIsLoading,
        $r2rFileCardsContainer,
        cardsCurrentLoadBatch,
        ITEMS_PER_PAGE_SEARCH_CARDS,
        allSearchResults,
        (isLoading) => {
          cardsIsLoading = isLoading;
        },
        (newBatch) => {
          cardsCurrentLoadBatch = newBatch;
        }
      );
    });
    $(document).on("click", ".r2r-files-collapse-btn", r2rFilesSearchRenderer.handleCardCollapseExpand);

    // General Events
    $deleteButton.on("click", _handleDeleteFiles);
    $(document).on("r2r-files-updated", _refreshCurrentView);
  }

  // --- Initialization ---
  function _loadInitialView() {
    _loadFileList(currentPageListView); // Default to list view
  }

  // Self-initialization when the DOM is ready.
  // This will be called once the DOM is ready and all module dependencies are loaded.
  $(document).ready(function () {
    ModalHelper.createModals();
    _bindEvents(); // Setup event listeners
    _loadInitialView(); // Load the default view (file list)
  });
})(jQuery);

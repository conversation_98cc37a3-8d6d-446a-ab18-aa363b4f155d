/*
 * ==========================================================
 * R2R FILES SEARCH RENDERER
 * ==========================================================
 *
 * Renders search results as cards for the R2R Files tab.
 *
 */

(function ($) {
  "use strict";

  // Import utilities (assuming r2rUtils is loaded globally)
  const r2rUtils = window.r2rUtils;

  // DOM Elements (specific to this renderer)
  const $r2rCardsInnerContainer = $("#r2r-cards-inner-container");
  const $r2rCardsLoadMore = $("#r2r-cards-load-more");
  // Ensure $r2rFileCardsContainer is accessible if needed by moved functions, or pass as param.
  // For handleInfiniteScroll, it will be passed.

  // --- Module State ---
  let allResultsDisplayed = false; // Tracks if the "end of results" has been reached and signaled

  // --- Card Rendering Functions ---

  /**
   * Displays a "Searching..." message in the cards container.
   * Called when a new search is initiated.
   */
  function showSearchingMessage() {
    $r2rCardsInnerContainer.html('<div class="r2r-files-loading">Searching...</div>');
    allResultsDisplayed = false; // Reset for a new search
    r2rUtils.hideLoading($r2rCardsLoadMore); // Ensure "load more" is hidden
  }

  /**
   * Displays an error message in the cards container.
   * @param {string} message The error message to display.
   */
  function showSearchError(message) {
    $r2rCardsInnerContainer.html(`<div class="r2r-files-error">${r2rUtils.escapeHtml(message)}</div>`);
    allResultsDisplayed = true; // Consider search ended due to error
    r2rUtils.hideLoading($r2rCardsLoadMore); // Ensure "load more" is hidden
  }

  /**
   * Renders a batch of search results as cards.
   * @param {Array<object>} cardsToAppend Array of document objects to append as cards.
   * @param {boolean} isInitialRender True if this is the first render of a search, clears container.
   * @param {boolean} isLastBatch Optional. True if this is the last batch of results.
   */
  function renderSearchResultsCards(cardsToAppend, isInitialRender, isLastBatch) {
    if (isInitialRender) {
      $r2rCardsInnerContainer.empty(); // Clear container on initial render
      allResultsDisplayed = false; // Reset flag for a new search/initial render
      r2rUtils.hideLoading($r2rCardsLoadMore); // Hide the loader for initial empty state if any
    }

    if (cardsToAppend.length === 0) {
      if (isInitialRender) {
        // Only show 'No results' if it's the very first render and still empty
        $r2rCardsInnerContainer.html('<div class="r2r-files-empty">No search results found.</div>');
      }
      r2rUtils.hideLoading($r2rCardsLoadMore); // Hide loader if no more cards
      return;
    }

    let cardsHtml = "";
    cardsToAppend.forEach((doc) => {
      cardsHtml += _createCardHtml(doc);
    });

    $r2rCardsInnerContainer.append(cardsHtml);

    // Apply fade-in animation to newly added cards
    $(".r2r-files-card-set:not(.r2r-files-fade-in-done)").each(function () {
      $(this).addClass("r2r-files-fade-in-done").addClass("r2r-files-fade-in");
    });

    // Add end of results message if this is the last batch
    if (isLastBatch) {
      _addEndOfResultsMessage();
      allResultsDisplayed = true; // Set flag indicating all results are shown
    }
  }

  /**
   * Generates the HTML string for a single document card.
   * @param {object} doc The document object with search result details.
   * @returns {string} HTML string for the card.
   */
  function _createCardHtml(doc) {
    const fullContent = doc.text || "N/A";
    const extractedContent = r2rUtils.extractTextContent(fullContent);
    const scoreValue = doc.score ? (doc.score * 100).toFixed(2) : null;
    const scoreColor = scoreValue >= 80 ? "#2e7d32" : scoreValue >= 50 ? "#f9a825" : scoreValue !== null ? "#c62828" : "#999";
    const documentName = doc.document_name || "N/A";
    const sourceUrl = doc.url || "#";
    const displayUrl = sourceUrl !== "#" ? `<a href="${sourceUrl}" target="_blank" title="${sourceUrl}">View Source</a>` : "N/A";
    const docType = doc.type || "N/A"; // 'vector' or 'graph' from parseR2rResult

    // Calculate word count from the extracted content
    const wordCount = r2rUtils.countWords(extractedContent);

    // Generate a unique ID for the card using a client-side UUID.
    const clientSideUUID = r2rUtils.generateClientSideUUID();

    // Create source badge with icon
    let sourceDisplay = "";
    if (documentName !== "N/A" || sourceUrl !== "#") {
      sourceDisplay = `<span class="r2r-files-document-source-badge">
        <i class="r2r-files-source-icon"></i>
        ${sourceUrl !== "#" ? `<a href="${sourceUrl}" target="_blank" title="${sourceUrl}">Source</a>` : documentName}
      </span>`;
    }

    return `
      <div class="r2r-files-card-set">
          <div class="r2r-files-header-card">
              <div class="r2r-files-header-content">
                  <h2 class="r2r-files-document-title">${doc.title || documentName}</h2>
                  <div class="r2r-files-document-meta">
                      <span class="r2r-files-document-type">Type: ${docType}</span>
                      <span class="r2r-files-document-score">Score: <span style="color: ${scoreColor}; font-weight: bold;">${scoreValue !== null ? scoreValue + "%" : "N/A"}</span></span>
                      <span class="r2r-files-document-words">Words: ${wordCount}</span>
                      ${sourceDisplay}
                  </div>
              </div>
              <button class="r2r-files-collapse-btn" data-target-id="${clientSideUUID}">Collapse</button>
          </div>
          <div class="r2r-files-body-card r2r-files-expanded" id="r2r-body-${clientSideUUID}">
              <div class="r2r-files-content-text">
                  <p>${extractedContent}</p>
              </div>
          </div>
      </div>
    `;
  }

  /**
   * Adds an "end of results" message after the last card.
   * This is shown when all search results have been loaded.
   */
  function _addEndOfResultsMessage() {
    console.log("Adding end of results message to container");

    // Remove any existing end message first to avoid duplicates
    $(".r2r-files-end-results").remove();

    const endMessageHtml = `
      <div class="r2r-files-end-results">
        <div class="r2r-files-end-results-icon">✓</div>
        <div class="r2r-files-end-results-text">
          <p>You've reached the end of the search results</p>
          <small>All matching documents have been displayed</small>
        </div>
      </div>
    `;

    // Make sure we're appending to the right container
    $("#r2r-cards-inner-container").append(endMessageHtml);

    // Add fade-in class after a small delay to trigger animation
    setTimeout(() => {
      $(".r2r-files-end-results").addClass("r2r-files-fade-in");
    }, 50);
  }

  // --- Moved and Adapted Functions from r2r-files-module.js ---

  /**
   * Handles the collapse/expand functionality of a search result card.
   * 'this' context is expected to be the clicked button element (due to jQuery event binding).
   */
  function handleCardCollapseExpand() {
    const targetId = $(this).data("target-id");
    const $bodyCard = $(`#r2r-body-${targetId}`);
    const $button = $(this);

    if ($bodyCard.hasClass("r2r-files-expanded")) {
      $bodyCard.removeClass("r2r-files-expanded").addClass("r2r-files-collapsed");
      $button.text("Expand").addClass("r2r-files-collapsed");
    } else {
      $bodyCard.removeClass("r2r-files-collapsed").addClass("r2r-files-expanded");
      $button.text("Collapse").removeClass("r2r-files-collapsed");
    }
  }

  /**
   * Renders the current batch of search result cards.
   * @param {number} currentBatch - The current batch number to render.
   * @param {number} itemsPerPage - How many items constitute a batch.
   * @param {Array<object>} allSearchResultsArray - The complete list of search results.
   * @returns {number} The next batch number.
   */
  function renderCurrentCardsBatch(currentBatch, itemsPerPage, allSearchResultsArray) {
    const startIndex = currentBatch * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const cardsToRender = allSearchResultsArray.slice(startIndex, endIndex);

    const isLastBatch = endIndex >= allSearchResultsArray.length;

    renderSearchResultsCards(cardsToRender, currentBatch === 0, isLastBatch);

    let nextBatch = currentBatch;
    if (cardsToRender.length === itemsPerPage && !isLastBatch) {
      // Only increment if a full batch was rendered and it's not the last one
      nextBatch++;
    } else {
      // If it's the last batch or not a full batch, ensure loader is hidden.
      // renderSearchResultsCards might show "No results" or _addEndOfResultsMessage will be called.
      r2rUtils.hideLoading($r2rCardsLoadMore);
    }
    return nextBatch;
  }

  /**
   * Loads and renders the next batch of search result cards.
   * @param {boolean} isLoading - Current loading state (passed by value).
   * @param {number} currentBatch - Current batch number.
   * @param {number} itemsPerPage - Items per batch.
   * @param {Array<object>} allSearchResultsArray - All search results.
   * @param {function(boolean):void} setIsLoadingCallback - Callback to update loading state in the module.
   * @param {function(number):void} setCurrentBatchCallback - Callback to update current batch in the module.
   */
  function loadMoreCardsForSearch(isLoading, currentBatch, itemsPerPage, allSearchResultsArray, setIsLoadingCallback, setCurrentBatchCallback) {
    if (isLoading) return;

    // This check is secondary; primary check is in handleInfiniteScroll
    const displayedItems = currentBatch * itemsPerPage;
    if (displayedItems >= allSearchResultsArray.length && allSearchResultsArray.length > 0) {
      r2rUtils.hideLoading($r2rCardsLoadMore); // Ensure loader is hidden
      if (!$r2rCardsInnerContainer.find(".r2r-files-end-results").length && allSearchResultsArray.length > 0) {
        _addEndOfResultsMessage(); // Show end message if not already there
      }
      return;
    }

    setIsLoadingCallback(true);
    r2rUtils.showLoading($r2rCardsLoadMore, "Loading more documents...");

    // setTimeout(() => { // Removed simulated network delay
    const newBatch = renderCurrentCardsBatch(currentBatch, itemsPerPage, allSearchResultsArray);
    setCurrentBatchCallback(newBatch);
    setIsLoadingCallback(false);
    // }, 800);
  }

  /**
   * Handles the infinite scroll logic for search results.
   * @param {boolean} isCurrentlySearchMode - Whether search mode is active.
   * @param {boolean} isLoading - Current loading state.
   * @param {jQuery} $scrollContainer - The jQuery object for the scrollable container.
   * @param {number} currentBatch - Current batch number.
   * @param {number} itemsPerPage - Items per batch.
   * @param {Array<object>} allSearchResultsArray - All search results.
   * @param {function(boolean):void} setIsLoadingCallback - Callback to update loading state.
   * @param {function(number):void} setCurrentBatchCallback - Callback to update current batch.
   */
  function handleInfiniteScroll(isCurrentlySearchMode, isLoading, $scrollContainer, currentBatch, itemsPerPage, allSearchResultsArray, setIsLoadingCallback, setCurrentBatchCallback) {
    // Stop if not in search mode, already loading, or if all results have already been displayed
    if (!isCurrentlySearchMode || isLoading || allResultsDisplayed) {
      return;
    }

    // Calculate the starting index for the batch that `currentBatch` refers to
    const startIndexForCurrentBatch = currentBatch * itemsPerPage;

    // Only proceed if this batch could potentially contain more items
    if (startIndexForCurrentBatch < allSearchResultsArray.length) {
      const scrollTop = $scrollContainer.scrollTop();
      const scrollHeight = $scrollContainer[0].scrollHeight;
      const containerHeight = $scrollContainer.height();
      if (scrollTop + containerHeight >= scrollHeight - 100) {
        // Standard scroll threshold check
        loadMoreCardsForSearch(isLoading, currentBatch, itemsPerPage, allSearchResultsArray, setIsLoadingCallback, setCurrentBatchCallback);
      }
    }
  }

  // Export functions
  window.r2rFilesSearchRenderer = {
    showSearchingMessage,
    renderSearchResultsCards,
    handleCardCollapseExpand,
    handleInfiniteScroll,
    renderCurrentCardsBatch, // Exposed for initial render call from the module
    showSearchError,
  };
})(jQuery);

/*
 * ==========================================================
 * R2R AJAX REQUESTS
 * ==========================================================
 *
 * Centralized functions for making R2R-specific AJAX calls to the backend.
 *
 */

(function ($) {
  "use strict";

  // Import utilities from r2r-utils.js (assuming it's loaded globally via window.r2rUtils)
  const r2rUtils = window.r2rUtils;

  // --- API Constants (These are assumed to be globally defined in components.php) ---
  // DO NOT redeclare as const/let here. Access them directly from 'window' when needed.

  /**
   * Fetches a list of R2R files from the backend.
   * @param {number} page The page number to retrieve.
   * @param {number} limit The number of items per page.
   * @returns {Promise<object>} A promise that resolves with file data or rejects with an error.
   */
  async function fetchFiles(page, limit) {
    try {
      const response = await $.ajax({
        url: window.SB_AJAX_URL, // Access directly from window
        type: "POST",
        data: {
          function: "r2r-get-files",
          page: page,
          limit: limit,
          token: window.SB_NONCE, // Access directly from window
        },
        xhrFields: {
          withCredentials: true,
        },
      });

      const data = typeof response === "string" ? JSON.parse(response) : response;
      if (data[0] === "success") {
        let resultsData = data[1];
        if (Array.isArray(resultsData) && resultsData[0] === "success") {
          resultsData = resultsData[1];
        }
        return resultsData;
      } else {
        throw new Error(data[1] || "API returned error.");
      }
    } catch (e) {
      console.error("Error fetching files:", e);
      throw new Error(`Failed to load files: ${e.statusText || e.message || "Unknown error"}`);
    }
  }

  /**
   * Performs a search for R2R content (chunks and graphs) from the backend.
   * @param {string} query The search query.
   * @param {string} collectionId The R2R collection ID.
   * @returns {Promise<object>} A promise that resolves with search results or rejects with an error.
   */
  async function searchFiles(query, collectionId) {
    try {
      const searchData = {
        function: "r2r-search-files",
        query: query,
        search_mode: "custom",
        search_settings: {
          filters: collectionId ? { collection_ids: { $in: [collectionId] } } : undefined,
          // No limit parameter
        },
        token: window.SB_NONCE,
      };

      const response = await $.ajax({
        url: window.SB_AJAX_URL,
        type: "POST",
        data: searchData,
        xhrFields: {
          withCredentials: true,
        },
      });

      const data = typeof response === "string" ? JSON.parse(response) : response;

      if (data && Array.isArray(data) && data[0] === "success") {
        let resultsData = data[1];

        // Handle nested success array if present
        if (Array.isArray(resultsData) && resultsData[0] === "success") {
          resultsData = resultsData[1];
        }

        return resultsData;
      } else {
        console.error("Search API error:", data);
        throw new Error(data[1] || "API returned error.");
      }
    } catch (e) {
      console.error("Error searching files:", e);
      throw new Error(`Failed to search files: ${e.statusText || e.message || "Unknown error"}`);
    }
  }

  /**
   * Deletes selected R2R files.
   * @param {string[]} fileIds An array of file IDs to delete.
   * @returns {Promise<object>} A promise that resolves on success or rejects with an error.
   */
  async function deleteFiles(fileIds) {
    try {
      const response = await $.ajax({
        url: window.SB_AJAX_URL,
        type: "POST",
        data: {
          function: "r2r-delete-files",
          file_ids: fileIds,
          token: window.SB_NONCE,
        },
        xhrFields: {
          withCredentials: true,
        },
      });

      const data = typeof response === "string" ? JSON.parse(response) : response;

      if (data && Array.isArray(data) && data[0] === "success") {
        return data;
      } else {
        throw new Error(data[1] || "API returned error.");
      }
    } catch (e) {
      console.error("Error deleting files:", e);
      throw new Error(`Failed to delete files: ${e.statusText || e.message || "Unknown error"}`);
    }
  }

  /**
   * Helper function to parse R2R client results into a unified format.
   * This logic remains here as it's part of processing raw API responses.
   * @param {object} mockClient A mock R2R client (for document retrieval in current setup).
   * @param {Array<object>} chunks Array of chunk search results.
   * @param {Array<object>} graph Array of graph search results.
   * @param {object} options Parsing options.
   * @returns {Promise<object>} An object containing filtered and sorted texts.
   */
  async function parseR2rResult(mockClient, chunks, graph, options) {
    const documents = await (async () => {
      if (!options.retrieveDocument) {
        return [];
      }
      const documentIds = Array.from(new Set(chunks.filter((c) => c.document_id).map((c) => c.document_id)));

      if (documentIds.length === 0) {
        return [];
      }

      try {
        const documentResults = await mockClient.documents.list({
          ids: documentIds,
          limit: documentIds.length,
        });
        return documentResults?.results || [];
      } catch (error) {
        console.error("Error retrieving documents for parsing:", error);
        return [];
      }
    })();

    let texts = [
      ...chunks.map((c) => ({
        ...c,
        text: c.text || c.description,
        type: "vector",
        document_id: c.document_id,
        document_name: documents.find((d) => d.id === c.document_id)?.title || documents.find((d) => d.id === c.document_id)?.name,
        url: documents.find((d) => d.id === c.document_id)?.metadata?.source_url,
        wordCount: r2rUtils.countWords(c.text || c.description),
        id: c.document_id, // Ensure a consistent ID for card targeting
      })),
      ...(options.includeGraph
        ? graph.map((g) => ({
            ...g,
            text: g.content?.description || g.content?.summary,
            type: g.result_type,
            document_id: null,
            wordCount: r2rUtils.countWords(g.content?.description || g.content?.summary),
            id: g.id, // Ensure a consistent ID for card targeting (using graph's own ID if available)
          }))
        : []),
    ];

    if (options.minScore) {
      texts = texts.filter((text) => text.score >= options.minScore);
    }
    texts.sort((a, b) => (b.score || 0) - (a.score || 0));

    let filteredTexts = [];
    let currentWordCount = 0;
    for (let i = 0; i < texts.length; i++) {
      const text = texts[i];
      const itemWordCount = r2rUtils.countWords(text.text);
      if (options.maxWordCount && currentWordCount + itemWordCount > options.maxWordCount) {
        break;
      }
      filteredTexts.push(text);
      currentWordCount += itemWordCount;
    }

    return {
      chunks: chunks,
      graph: graph,
      texts: options?.limit ? filteredTexts.slice(0, options.limit) : filteredTexts,
    };
  }

  // Export functions globally
  window.r2rAjaxRequests = {
    fetchFiles,
    searchFiles,
    deleteFiles,
    parseR2rResult,
  };
})(jQuery);

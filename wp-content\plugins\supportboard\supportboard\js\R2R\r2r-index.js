/*
 * ==========================================================
 * R2R INDEX JS
 * ==========================================================
 *
 * Main entry point for all R2R JavaScript modules.
 * Imports and initializes feature-specific modules.
 *
 */

// Import global utility and AJAX request modules.
// These are not directly assigned to window here because they already assign themselves to window,
// but explicitly importing them ensures they are part of the module graph and loaded.
import "../R2R/r2r-utils.js";
import "../R2R/r2r-ajax-requests.js";

// Import feature-specific renderer modules for the Files tab.
import "../R2R/files/r2r-files-list-renderer.js";
import "../R2R/files/r2r-files-search-renderer.js"; // Renamed

// Import the main feature module for the Files tab.
// This module contains the primary initialization logic for the Files UI.
import "../R2R/files/r2r-files-module.js";

// --- Future Modules would be imported here ---
// import '../R2R/Website/r2r-website-module.js';
// import '../R2R/Qea/r2r-qea-module.js';

/*
 * ==========================================================
 * R2R GLOBAL UTILITIES
 * ==========================================================
 *
 * Reusable utility functions for all R2R JavaScript modules.
 *
 */

(function ($) {
  "use strict";

  // --- Constants ---
  const DEFAULT_TRUNCATE_LENGTH = 50;

  // --- Utility Functions ---

  /**
   * Generates a simple client-side UUID (Version 4).
   * @returns {string} A UUID string.
   */
  function generateClientSideUUID() {
    // Corrected UUID v4 generation
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
      const r = crypto.getRandomValues(new Uint8Array(1))[0] % 16 | 0;
      // eslint-disable-next-line eqeqeq
      const v = c == "x" ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  }

  /**
   * Counts words in a given text.
   * @param {string} text The text to count words from.
   * @returns {number} Word count.
   */
  function countWords(text) {
    if (!text) return 0;
    return text.trim().split(/\s+/).length;
  }

  /**
   * Formats a date string for display.
   * @param {string} dateString The date string.
   * @returns {string} Formatted date.
   */
  function formatDate(dateString) {
    if (!dateString) return "N/A";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString() + " " + date.toLocaleTimeString();
    } catch (e) {
      return dateString;
    }
  }

  /**
   * Truncates text to a specified maximum length.
   * @param {string} text The text to truncate.
   * @param {number} [maxLength=DEFAULT_TRUNCATE_LENGTH] The maximum length.
   * @returns {string} Truncated text with ellipsis if needed.
   */
  function truncateText(text, maxLength = DEFAULT_TRUNCATE_LENGTH) {
    if (!text) return "N/A";
    return text.length > maxLength ? text.slice(0, maxLength) + "..." : text;
  }

  function extractTextContent(text) {
    if (!text) return "N/A";

    // Check if "Text:" exists anywhere in the content
    const textIndex = text.indexOf("Text:");

    if (textIndex !== -1) {
      // If "Text:" is found, extract everything after it (skip "Text:" + any whitespace)
      const afterText = text.substring(textIndex + 5).trim();
      return afterText;
    } else {
      // If "Text:" is not found, check for "Full content:" anywhere in the content
      const fullContentIndex = text.indexOf("Full content:");

      if (fullContentIndex !== -1) {
        // Extract everything after "Full content:" (skip "Full content:" + any whitespace)
        const afterFullContent = text.substring(fullContentIndex + 13).trim();
        return afterFullContent;
      } else {
        // If neither marker is found, return the original text
        return text;
      }
    }
  }

  /**
   * Generates an ingestion status badge HTML.
   * @param {string} status The ingestion status.
   * @returns {string} HTML for the badge.
   */
  function getIngestionBadge(status) {
    switch (status) {
      case "success":
        return '<span class="r2r-status-badge r2r-status-success">Success</span>';
      case "failed":
        return '<span class="r2r-status-badge r2r-status-error">Failed</span>';
      case "parsing":
        return '<span class="r2r-status-badge r2r-status-parsing">Parsing</span>';
      case "extracting":
        return '<span class="r2r-status-badge r2r-status-extracting">Extracting</span>';
      case "chunking":
        return '<span class="r2r-status-badge r2r-status-chunking">Chunking</span>';
      case "embedding":
        return '<span class="r2r-status-badge r2r-status-embedding">Embedding</span>';
      case "augmenting":
        return '<span class="r2r-status-badge r2r-status-augmenting">Augmenting</span>';
      case "storing":
        return '<span class="r2r-status-badge r2r-status-storing">Storing</span>';
      case "enriching":
        return '<span class="r2r-status-badge r2r-status-enriching">Enriching</span>';
      case "pending":
      default:
        return '<span class="r2r-status-badge r2r-status-pending">Pending</span>';
    }
  }

  /**
   * Generates an extraction status badge HTML.
   * @param {string} status The extraction status.
   * @returns {string} HTML for the badge.
   */
  function getExtractionBadge(status) {
    switch (status) {
      case "success":
        return '<span class="r2r-status-badge r2r-status-success">Success</span>';
      case "enriched":
        return '<span class="r2r-status-badge r2r-status-success">Enriched</span>';
      case "failed":
        return '<span class="r2r-status-badge r2r-status-error">Failed</span>';
      case "processing":
        return '<span class="r2r-status-badge r2r-status-processing">Processing</span>';
      case "pending":
      default:
        return '<span class="r2r-status-badge r2r-status-pending">Pending</span>';
    }
  }

  /**
   * Shows a loading indicator.
   * @param {jQuery} $targetElement The jQuery object of the element where the loading message should be shown.
   * @param {string} message The loading message.
   * @param {number} [colspan=1] The colspan for table loading messages (if target is a table row).
   */
  function showLoading($targetElement, message, colspan = 1) {
    if ($targetElement.is("table") || $targetElement.is("tbody")) {
      // Uses the specific class name for files module tables for styling
      $targetElement.html(`<tbody><tr><td colspan="${colspan}" class="r2r-files-loading">${message}</td></tr></tbody>`);
    } else {
      // For non-table elements (like #r2r-cards-load-more), assume it already has styling class via HTML
      $targetElement.text(message).show();
    }
  }

  /**
   * Hides a loading indicator.
   * @param {jQuery} $targetElement The jQuery object of the element where the loading message was shown.
   */
  function hideLoading($targetElement) {
    $targetElement.hide();
  }

  /**
   * Displays an error message.
   * @param {jQuery} $targetElement The jQuery object of the element where the error message should be shown.
   * @param {string} message The error message.
   * @param {number} [colspan=1] The colspan for table error messages (if target is a table row).
   */
  function showError($targetElement, message, colspan = 1) {
    if ($targetElement.is("table") || $targetElement.is("tbody")) {
      // Uses the specific class name for files module tables for styling
      $targetElement.html(`<tbody><tr><td colspan="${colspan}" class="r2r-files-error">${message}</td></tr></tbody>`);
    } else {
      // For non-table elements (like #r2r-cards-inner-container), assume it already has styling class via HTML
      $targetElement.text(message).show();
    }
  }

  // Expose utilities globally
  window.r2rUtils = {
    generateClientSideUUID,
    countWords,
    formatDate,
    truncateText,
    extractTextContent,
    getIngestionBadge,
    getExtractionBadge,
    showLoading,
    hideLoading,
    showError,
  };

  // Add escapeHtml function to r2rUtils
  if (!r2rUtils.escapeHtml) {
    r2rUtils.escapeHtml = function (text) {
      if (!text) return "";
      return String(text).replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#039;");
    };
  }
})(jQuery);

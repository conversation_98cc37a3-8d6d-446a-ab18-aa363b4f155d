(function (global, $) {
  const modalHelper = {
    // 🔧 Creates both confirmation and success modals (run once in init)
    createModals: function () {
      if (!$("#r2r-alert-modal").length) {
        $("body").append(`
        <div id="r2r-alert-modal" class="sb-dialog-box sb-lightbox" style="display:none; z-index:9999;">
            <div id="r2r-alert-title" class="sb-title">Notice</div>
            <p id="r2r-alert-message">Message goes here</p>
            <div><a id="r2r-alert-close" class="sb-close sb-btn">Close</a></div>
        </div>
        <div id="r2r-overlay" class="sb-overlay" style="display:none; z-index:9998;"></div>
        `);
        $("#r2r-alert-close").on("click", () => $("#r2r-alert-modal, #r2r-overlay").hide());
      }

      if (!$("#r2r-confirm-modal").length) {
        $("body").append(`
        <div id="r2r-confirm-modal" class="sb-dialog-box sb-lightbox" style="display:none; z-index:9999;">
            <div class="sb-title">🧾 Confirm</div>
            <p id="r2r-confirm-message">Are you sure?</p>
            <div>
            <a id="r2r-confirm-yes" class="sb-confirm sb-btn">Yes</a>
            <a id="r2r-confirm-no" class="sb-cancel sb-btn sb-btn-red">Cancel</a>
            </div>
        </div>
        <div id="r2r-confirm-overlay" class="sb-overlay" style="display:none; z-index:9998;"></div>
        `);
        $("#r2r-confirm-no").on("click", () => $("#r2r-confirm-modal, #r2r-confirm-overlay").hide());
      }
    },

    // 👇 Unified modal handler for 4 types: success, failed, warning, confirm
    showModal: function (type, message, onConfirm = null) {
      // ✅ SUCCESS / ❌ FAILED / ⚠️ WARNING types (share same alert box)
      if (["success", "failed", "warning"].includes(type)) {
        const icons = {
          success: "✅", // emoji icon
          failed: "❌",
          warning: "⚠️",
        };
        const titles = {
          success: "Success", // text title
          failed: "Failed",
          warning: "Warning",
        };
        const colors = {
          success: "#2e7d32", // green
          failed: "#c62828", // red
          warning: "#f9a825", // yellow
        };

        // 🖊 Set modal title + icon + color
        $("#r2r-alert-title").text(`${icons[type]} ${titles[type]}`).css("color", colors[type]);

        // 📝 Set message
        $("#r2r-alert-message").text(message);

        // 📦 Show modal with colored border
        $("#r2r-alert-modal").css({
          display: "block",
          "margin-top": "-100px",
          "margin-left": "-250px",
          //"border-left": `6px solid ${colors[type]}`, // color bar
        });

        // 🔲 Show background overlay
        $("#r2r-overlay").show();
      }

      // 🧾 CONFIRM type — Yes/Cancel modal
      if (type === "confirm") {
        // 📝 Set confirmation message
        $("#r2r-confirm-message").text(message);

        // 📦 Show confirmation box
        $("#r2r-confirm-modal").css({
          display: "block",
          "margin-top": "-100px",
          "margin-left": "-250px",
        });

        // 🔲 Show background overlay
        $("#r2r-confirm-overlay").show();

        // 🎯 Bind Yes click to callback
        $("#r2r-confirm-yes")
          .off("click")
          .on("click", function () {
            $("#r2r-confirm-modal, #r2r-confirm-overlay").hide();
            if (onConfirm) onConfirm();
          });
      }
    },
  };

  global.ModalHelper = modalHelper;
})(window, jQuery);

/*
|--------------------------------------------------------------------------
| MODAL USAGE REFERENCE (ModalHelper.js)
|--------------------------------------------------------------------------
| This helper file provides a reusable system for modal popups.
| It supports 4 types: "success", "failed", "warning", and "confirm".
|
| Usage:
|    ModalHelper.showModal(type, message, onConfirm?);
|
| Supported types:
| 1. "success" → ModalHelper.showModal("success", "Your success message");
| 2. "failed"  → ModalHelper.showModal("failed", "Your error message");
| 3. "warning" → ModalHelper.showModal("warning", "Your warning message");
| 4. "confirm" → ModalHelper.showModal("confirm", "Your confirm message", function() {
|                     // run if user clicks YES
|                 });
|
| Example:
|    ModalHelper.showModal("success", "File saved successfully.");
|    ModalHelper.showModal("failed", "Upload failed. Please try again.");
|    ModalHelper.showModal("warning", "Large file — may take time.");
|    ModalHelper.showModal("confirm", "Are you sure?", function() {
|        deleteFile();
|    });
|
| IMPORTANT:
| - Make sure to call ModalHelper.createModals() once on page load (e.g., inside init()).
| - Include modalHelper.js BEFORE any scripts that use showModal().
*/

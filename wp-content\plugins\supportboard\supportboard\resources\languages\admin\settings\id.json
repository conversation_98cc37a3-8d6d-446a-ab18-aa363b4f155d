{"{product_name} has no {product_attribute_name} variants.": "{product_name} tidak memiliki {product_attribute_name} varian.", "360dialog settings": "Pengaturan dialog 360", "360dialog template": "Templat dialog 360", "Abandoned cart notification": "Pemberitahuan keranjang terbengkalai", "Abandoned cart notification - Admin email": "Notifikasi keranjang terbengkalai - <PERSON>ail admin", "Abandoned cart notification - First email": "Notifikasi keranjang terbengkalai - <PERSON><PERSON> pertama", "Abandoned cart notification - Second email": "Notifikasi keranjang terbengkalai - <PERSON><PERSON>", "Accept button text": "Terima teks tombol", "Account SID": "SID akun", "Activate the Right-To-Left (RTL) reading layout for the admin area.": "Aktifkan tata letak pembacaan Kanan-Ke-Kiri (RTL) untuk area admin.", "Activate the Right-To-Left (RTL) reading layout.": "Aktifkan tata letak pembacaan <PERSON> (RTL).", "Activate the Slack integration.": "Aktifkan integrasi Slack.", "Activate the Zendesk integration": "Aktifkan integrasi Zendesk", "Activate this option if you don't want to translate the settings area.": "Aktifkan opsi ini jika Anda tidak ingin menerjemahkan area pengaturan.", "Active": "Aktif", "Active - admin": "Aktif - admin", "Active eCommerce CMS URL. Ex. https://shop.com/": "Active eCommerce URL CMS. Mantan. https://toko.com/", "Active eCommerce URL": "Active eCommerce URL", "Active for agents": "Aktif untuk agen", "Active for users": "Aktif untuk pengguna", "Active webhooks": "Webhook aktif", "Add a delay (ms) to the bot's responses. Default is 2000.": "Tambah<PERSON> penundaan (md) ke respons bot. Standarnya adalah 2000.", "Add and manage additional support departments.": "Tambahkan dan kelola departemen dukungan tambahan.", "Add and manage saved replies that can be used by agents in the chat editor. Saved replies can be printed by typing # followed by the reply name plus space. Use \\n to do a line break.": "Tambahkan dan kelola balasan tersimpan yang dapat digunakan oleh agen di editor obrolan. Balasan tersimpan dapat dicetak dengan mengetikkan # diikuti dengan nama balasan ditambah spasi. Gunakan \\n untuk melakukan jeda baris.", "Add and manage tags.": "Tambahkan dan kelola tag.", "Add comma separated WordPress user roles. The Support Board administration area will be available for new roles, in addition to the default one: editor, administrator, author.": "Tambahkan peran pengguna WordPress yang dipisahkan koma. Area administrasi Dewan Du<PERSON>ngan akan tersedia untuk peran baru, selain yang default: editor, administrator, penulis.", "Add custom fields to the new ticket form.": "Tambahkan bidang khusus ke formulir tiket baru.", "Add custom fields to the user profile details.": "Tambahkan bidang khusus ke detail profil pengguna.", "Add Intents": "Tambahkan Intents", "Add Intents to saved replies": "Tambahkan Intents ke balasan yang disimpan", "Add WhatsApp phone number details here.": "Tambahkan detail nomor telepon WhatsApp di sini.", "Adjust the chat button position. Values are in px.": "Sesuaikan posisi tombol obrolan. <PERSON><PERSON> dalam px.", "Admin icon": "Ikon admin", "Admin IDs": "ID Admin", "Admin login logo": "Logo masuk admin", "Admin login message": "<PERSON><PERSON> masuk admin", "Admin notifications": "Notifikasi admin", "Admin title": "<PERSON><PERSON><PERSON> admin", "Agent area": "Area agen", "Agent details": "Detail agen", "Agent email notifications": "Pemberitahuan email agen", "Agent ID": "ID Agen", "Agent linking": "<PERSON><PERSON><PERSON> agen", "Agent message template": "Templat pesan agen", "Agent notification email": "Email pemberitahuan agen", "Agent privileges": "<PERSON>k is<PERSON>wa agen", "Agents": "Agen", "Agents and admins tab": "Tab agen dan admin", "Agents menu": "<PERSON>u agen", "Agents only": "<PERSON><PERSON> agen", "All": "<PERSON><PERSON><PERSON>", "All channels": "<PERSON><PERSON><PERSON>", "All messages": "<PERSON><PERSON><PERSON> pesan", "All questions": "<PERSON><PERSON><PERSON>", "Allow only extended licenses": "Izinkan hanya lisensi yang diperpanjang", "Allow only one conversation": "Izinkan hanya satu percakapan", "Allow only one conversation per user.": "Izinkan hanya satu percakapan per pengguna.", "Allow the chatbot to reply to the user's emails if the answer is known and email piping is active.": "Izinkan chatbot untuk membalas email pengguna jika jawabannya diketahui dan perpipaan email aktif.", "Allow the chatbot to reply to the user's text messages if the answer is known.": "Izinkan chatbot untuk membalas pesan teks pengguna jika jawabannya diketahui.", "Allow the user to archive a conversation and hide archived conversations.": "Izinkan pengguna untuk mengarsipkan percakapan dan menyembunyikan percakapan yang diarsipkan.", "Allow users to contact you via their favorite messaging apps.": "Izinkan pengguna menghubungi Anda melalui aplikasi perpesanan favorit mereka.", "Allow users to select a product on ticket creation.": "Izinkan pengguna untuk memilih produk pada pembuatan tiket.", "Always all messages": "<PERSON><PERSON><PERSON> semua pesan", "Always incoming messages only": "<PERSON><PERSON> pesan yang selalu masuk", "Always sort conversations by date in the admin area.": "Selalu urutkan percakapan berdasarkan tanggal di area admin.", "API key": "Kunci API", "Append the registration user details to the success message.": "Tambahkan detail pengguna pendaftaran ke pesan sukses.", "Apply a custom background image for the header area.": "Terapkan gambar latar belakang khusus untuk area header.", "Apply changes": "<PERSON><PERSON><PERSON><PERSON>", "Apply to": "Terapkan ke", "Archive all user channels in the Slack app. This operation may take a long time to complete. Important: All of your slack channels will be archived.": "Ars<PERSON><PERSON> semua saluran pengguna di aplikasi Slack. Operasi ini mungkin membutuhkan waktu lama untuk diselesaikan. Penting: <PERSON><PERSON><PERSON> saluran slack Anda akan diarsipkan.", "Archive automatically the conversations marked as read every 24h.": "<PERSON><PERSON><PERSON><PERSON> secara otomatis percakapan yang ditandai sebagai telah dibaca setiap 24 jam.", "Archive channels": "<PERSON><PERSON><PERSON> arsip", "Archive channels now": "<PERSON><PERSON><PERSON><PERSON> saluran se<PERSON>ng", "Articles": "Artikel", "Articles area": "Area artikel", "Articles button link": "<PERSON><PERSON> tombol artikel", "Articles page URL": "URL halaman artikel", "Artificial Intelligence": "Kecerdasan buatan", "Assign a department to all conversations started from Google Business Messages. Enter the department ID.": "Tetapkan departemen untuk semua percakapan yang dimulai dari Google Business Messages. Masukkan ID departemen.", "Assign a department to all conversations started from Twitter. Enter the department ID.": "Tetapkan departemen untuk semua percakapan yang dimulai dari Twitter. Masukkan ID departemen.", "Assign a department to all conversations started from Viber. Enter the department ID.": "Tetapkan departemen untuk semua percakapan yang dimulai dari Viber. Masukkan ID departemen.", "Assign a department to all conversations started from WeChat. Enter the department ID.": "Tetapkan departemen untuk semua percakapan yang dimulai dari WeChat. Masukkan ID departemen.", "Assign different departments to conversations started from different Google Business Messages locations. This setting overrides the default department.": "Tetapkan departemen yang berbeda untuk percakapan yang dimulai dari lokasi Google Business Messages yang berbeda. Pengaturan ini mengesampingkan departemen default.", "Assistant": "<PERSON><PERSON><PERSON>", "Assistant ID": "ID Asisten", "Attachments list": "<PERSON><PERSON><PERSON>", "Audio file URL - admin": "URL file audio - admin", "Automatic": "<PERSON><PERSON><PERSON><PERSON>", "Automatic human takeover": "<PERSON><PERSON><PERSON><PERSON><PERSON> manusia secara otomatis", "Automatic translation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Automatic updates": "Pembaruan otomatis", "Automatically archive conversations": "Mengarsipkan percakapan secara otomatis", "Automatically assigns a department based on the user's active plans. Insert -1 as plan ID for users without any plan.": "Secara otomatis menetapkan departemen berdasarkan rencana aktif pengguna. Masukkan -1 sebagai ID paket untuk pengguna tanpa paket apa pun.", "Automatically check and install new updates. A valid Envato Purchase Code and valid apps's license keys are required.": "Periksa dan instal pembaruan baru secara otomatis. Kode Pembelian Envato yang valid dan kunci lisensi aplikasi yang valid diperlukan.", "Automatically collapse the conversation details panel, and other panels, of the admin area.": "Secara otomatis menutup panel detail per<PERSON><PERSON><PERSON>, dan panel la<PERSON>ya, di area admin.", "Automatically create a department for each website and route the conversations of each website to the right department. This setting requires a WordPress Multisite installation.": "Secara otomatis membuat departemen untuk setiap situs web dan mengarahkan percakapan setiap situs web ke departemen yang tepat. Pengaturan ini memerlukan instalasi WordPress Multisite.", "Automatically hide the conversation details panel.": "Sembunyikan panel detail per<PERSON><PERSON><PERSON> secara otomatis.", "Automatically send cart reminders to customers with products in their carts. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "<PERSON><PERSON>a otomatis mengirim pengingat keranjang ke pelanggan dengan produk di keranjang mereka. Anda dapat menggunakan bidang gabungan berikut dan lainnya: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.", "Automatically sync Zendesk customers with {R}, view Zendesk tickets, or create new ones without leaving {R}.": "Sinkronkan pelanggan Zendesk secara otomatis dengan {R}, lihat tiket Zendesk, atau buat tiket baru tanpa meninggalkan {R}.", "Automatically synchronize products, categories, tags, and more with Dialogflow, and enable the bot to answer autonomously to questions related to your shop.": "Sinkronkan produk, ka<PERSON><PERSON><PERSON>, tag, dan la<PERSON>ya secara otomatis dengan <PERSON>, dan aktifkan bot untuk menjawab pertanyaan terkait toko Anda secara mandiri.", "Automatically translate admin area": "Terjemahkan area admin secara otomatis", "Automatically translate the admin area to match the agent profile language or browser language.": "Terjemahkan area admin secara otomatis agar sesuai dengan bahasa profil agen atau bahasa browser.", "Avatar image": "<PERSON><PERSON><PERSON> avatar", "Away mode": "Modus tandang", "Before initiating the chat, the user must accept a privacy message in order to gain access.": "Sebelum memu<PERSON>, pengguna harus menerima pesan privasi untuk mendapatkan akses.", "Birthday": "<PERSON>hun", "Body variables": "<PERSON><PERSON><PERSON>", "Bot name": "<PERSON><PERSON> bot", "Bot profile image": "Gambar profil bot", "Bot response delay": "<PERSON><PERSON><PERSON><PERSON><PERSON> respons bot", "Bottom": "<PERSON>wa<PERSON>", "Brand": "<PERSON><PERSON>", "Built-in chat button icons": "<PERSON>kon tombol obrolan bawaan", "Business Account ID": "ID Akun <PERSON>", "Button action": "Tindakan tombol", "Button name": "<PERSON><PERSON> tombol", "Button text": "Teks tombol", "Button variables": "<PERSON><PERSON><PERSON>ol", "Cancel button text": "Batalkan teks tombol", "Cart": "Keranjang", "Cart follow up message": "Pesan tindak lanjut keranjang", "Catalogue details": "Detail katalog", "Catalogue ID": "ID katalog", "Change the chat button image with a custom one.": "Ubah gambar tombol obrolan dengan yang khusus.", "Change the default field names.": "Ubah nama bidang default.", "Change the message text in the header area of the chat widget. This text will be replaced by the agent headline once the first reply is sent.": "Ubah teks pesan di area header widget obrolan. Teks ini akan diganti dengan judul agen setelah balasan pertama dikirim.", "Change the title text in the header area of the chat widget. This text will be replaced by the agent's name once the first reply is sent.": "Ubah teks judul di area header widget obrolan. Teks ini akan diganti dengan nama agen setelah balasan pertama dikirim.", "Channel ID": "ID saluran", "Channels": "<PERSON><PERSON><PERSON>", "Channels filter": "Filter saluran", "Chat": "Mengobrol", "Chat and admin": "<PERSON><PERSON><PERSON> dan admin", "Chat background": "Latar belakang o<PERSON>lan", "Chat button icon": "Ikon tombol obrolan", "Chat button offset": "Offset tombol obrolan", "Chat message": "<PERSON><PERSON> o<PERSON>", "Chat only": "<PERSON><PERSON>", "Chat position": "<PERSON><PERSON>i obro<PERSON>", "Chatbot": "<PERSON><PERSON>lan", "Chatbot mode": "Modus bot obrolan", "Check Requirements": "<PERSON><PERSON><PERSON>", "Check the server configurations and make sure it has all the requirements.": "Periksa konfigurasi server dan pastikan memiliki semua persy<PERSON>.", "Checkout": "Periksa", "Choose a background texture for the chat header and conversation area.": "Pilih tekstur latar belakang untuk header obrolan dan area percakapan.", "Choose where to display the chat. Enter the values separated by commas.": "Pilih tempat untuk menampilkan obrolan. <PERSON><PERSON>kkan nilai yang dipisahkan dengan koma.", "Choose which fields to disable from the tickets area.": "<PERSON><PERSON>h bidang mana yang akan dinonaktifkan dari area tiket.", "Choose which fields to include in the new ticket form.": "<PERSON><PERSON><PERSON> bidang mana yang akan disertakan dalam formulir tiket baru.", "Choose which fields to include in the registration form. The name field is included by default.": "<PERSON><PERSON><PERSON> kolom yang akan disertakan dalam formulir pendaftaran. Kolom nama disertakan secara default.", "Choose which user system the front-end chat will use to register and log in users.": "Pilih sistem pengguna mana yang akan digunakan obrolan front-end untuk mendaftar dan masuk pengguna.", "City": "Kota", "Clear flows": "<PERSON><PERSON> yang j<PERSON>s", "Click the button to start the Dialogflow synchronization.": "Klik tombol untuk memulai sinkronisasi Dialogflow.", "Click the button to start the Slack synchronization. Localhost cannot and does not receive messages. Log in with another account or as a visitor to perform your tests.": "Klik tombol untuk memulai sinkronisa<PERSON> Slack. Localhost tidak dapat dan tidak menerima pesan. Masuk dengan akun lain atau sebagai pengunjung untuk melakukan pengujian Anda.", "Client email": "<PERSON><PERSON> k<PERSON>n", "Client ID": "ID Klien", "Client token": "Token klien", "Close chat": "<PERSON><PERSON><PERSON>", "Close message": "<PERSON><PERSON><PERSON> pesan", "Cloud API numbers": "Nomor API awan", "Cloud API settings": "Setelan Cloud API", "Cloud API template fallback": "Penggantian templat Cloud API", "Code": "<PERSON><PERSON>", "Collapse panels": "Ciutkan panel", "Color": "<PERSON><PERSON>", "Communicate with your users right from Slack. Send and receive messages and attachments, use emojis, and much more.": "Berkomunikasi dengan pengguna Anda langsung dari Slack. <PERSON><PERSON> dan terima pesan dan lampiran, gunakan emoji, dan banyak lagi.", "Company": "<PERSON><PERSON><PERSON><PERSON>", "Concurrent chats": "<PERSON><PERSON> pesaing", "Configuration URL": "URL konfigurasi", "Confirm button text": "Konfirmasi teks tombol", "Confirmation message": "<PERSON><PERSON> kon<PERSON>", "Connect smart chatbots and automate conversations by using one of the most advanced forms of artificial intelligence in the world.": "Hubungkan chatbot pintar dan otomatisasi percakapan dengan menggunakan salah satu bentuk kecerdasan buatan tercanggih di dunia.", "Connect stores to agents.": "Hubungkan toko ke agen.", "Connect your Telegram bot to {R} to read and reply to all messages sent to your Telegram bot directly in {R}.": "Hubungkan bot Telegram Anda ke {R} untuk membaca dan membalas semua pesan yang dikirim ke bot Telegram Anda langsung di {R}.", "Connect your Viber bot to {R} to read and reply to all messages sent to your Viber bot directly in {R}.": "Hubungkan bot Viber Anda ke {R} untuk membaca dan membalas semua pesan yang dikirim ke bot Viber Anda langsung di {R}.", "Connect your Zalo Official Account to {R} to read and reply to all messages sent to your Zalo Official Account directly in {R}.": "Hubungkan Akun Resmi <PERSON> ke {R} untuk membaca dan membalas semua pesan yang dikirim ke Akun Resmi Zalo Anda langsung di {R}.", "Content": "<PERSON><PERSON>", "Content template SID": "SID templat konten", "Conversation profile": "Profil <PERSON>", "Conversations data": "Data percakapan", "Convert all emails": "Konversi semua email", "Cookie domain": "Domain cookie", "Country": "Negara", "Coupon discount (%)": "Diskon kupon (%)", "Coupon expiration (days)": "<PERSON><PERSON><PERSON><PERSON><PERSON> kupon (hari)", "Coupon expiration (seconds)": "<PERSON><PERSON><PERSON><PERSON><PERSON> kupon (detik)", "Create a WordPress user upon registration.": "Buat pengguna WordPress saat mendaftar.", "Create Intents now": "Buat Intents sekarang", "Currency symbol": "Simbol mata uang", "Custom CSS": "CSS khusus", "Custom fields": "<PERSON><PERSON>ng k<PERSON>", "Custom JS": "JS Khusus", "Custom model ID": "ID model k<PERSON><PERSON>", "Custom parameters": "Parameter khusus", "Customize the link for the 'All articles' button.": "<PERSON><PERSON><PERSON><PERSON> tautan untuk tombol &#39;<PERSON><PERSON><PERSON>&#39;.", "Dashboard display": "<PERSON><PERSON><PERSON>", "Dashboard title": "<PERSON><PERSON><PERSON>", "Database details": "Detail basis data", "Database host": "<PERSON>an rumah basis data", "Database name": "Nama basis data", "Database password": "Kata sandi basis data", "Database prefix": "Awalan basis data", "Database user": "Pengguna basis data", "Decline button text": "Tolak teks tombol", "Declined message": "<PERSON><PERSON> yang di<PERSON>lak", "Default": "<PERSON><PERSON><PERSON>", "Default body text": "Teks isi default", "Default conversation name": "Nama percakapan default", "Default department": "Departemen default", "Default department ID": "ID departemen bawaan", "Default form": "<PERSON><PERSON><PERSON> bawaan", "Default header text": "Teks tajuk default", "Delay (ms)": "<PERSON><PERSON><PERSON> (md)", "Delete all leads and all messages and conversations linked to them.": "<PERSON><PERSON> semua prospek dan semua pesan serta percakapan yang tertaut dengannya.", "Delete conversation": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "Delete leads": "<PERSON><PERSON> prospek", "Delete message": "<PERSON><PERSON> pesan", "Delete the built-in flows.": "<PERSON><PERSON> bawaan.", "Delimiter": "Pembatas", "Department": "Departemen", "Department ID": "ID Departemen", "Departments": "Departemen", "Departments settings": "Pengaturan departemen", "Desktop notifications": "Pemberitahuan desktop", "Dialogflow - Department linking": "Dialogflow - Tautan departemen", "Dialogflow chatbot": "Obrolan dialogflow", "Dialogflow edition": "Edisi Dialogflow", "Dialogflow Intent detection confidence": "<PERSON><PERSON><PERSON> deteksi maks<PERSON>", "Dialogflow location": "Lokasi alur dialog", "Dialogflow spelling correction": "<PERSON><PERSON><PERSON> ejaan alur dialog", "Dialogflow welcome Intent": "Dialogflow menyambut Intent", "Disable agents check": "Nonaktifkan pemeriksaan agen", "Disable and hide the chat widget if all agents are offline.": "Nonaktifkan dan sembunyikan widget obrolan jika semua agen sedang offline.", "Disable and hide the chat widget outside of scheduled office hours.": "Nonaktifkan dan sembunyikan widget obrolan di luar jam kantor yang di<PERSON>.", "Disable any features that you don't need.": "Nonaktifkan fitur apa pun yang tidak <PERSON>a perlukan.", "Disable auto-initialization of the chat widget. When this setting is active you must initialize the chat widget with a custom JavaScript API code written by you. If the chat doesn't appear and this setting is enabled, disable it.": "Nonaktifkan inisialisasi otomatis widget obrolan. Saat pengaturan ini aktif, Anda harus menginisialisasi widget obrolan dengan kode JavaScript API khusus yang Anda tulis. Jika obrolan tidak muncul dan pengaturan ini diaktifkan, nonaktifkan.", "Disable auto-initialization of the tickets area. When this setting is active you must initialize the tickets area with a custom JavaScript API code written by you. If the tickets area doesn't appear and this setting is enabled, disable it.": "Nonaktifkan inisialisasi otomatis area tiket. Saat pengaturan ini aktif, Anda harus menginisialisasi area tiket dengan kode JavaScript API khusus yang Anda tulis. Jika area tiket tidak muncul dan pengaturan ini diaktifkan, nonaktifkan.", "Disable chatbot": "Nonak<PERSON><PERSON><PERSON> chatbot", "Disable cron job": "Nonaktifkan tugas cron", "Disable dashboard": "<PERSON>ak<PERSON><PERSON><PERSON>", "Disable during office hours": "Nonaktifkan selama jam kantor", "Disable features": "Nonak<PERSON><PERSON><PERSON> fitur", "Disable features you don't use and improve the chat performance.": "Nonaktifkan fitur yang tidak Anda gunakan dan tingkatkan kinerja obrolan.", "Disable file uploading capabilities within the chat.": "Nonaktifkan kemampuan mengunggah file dalam obrolan.", "Disable for messaging channels": "Nonaktifkan untuk saluran perpesanan", "Disable for the tickets area": "Nonaktifkan untuk area tiket", "Disable invitation": "Nonaktifkan undangan", "Disable online status check": "Nonaktifkan pemeriksaan status online", "Disable outside of office hours": "Nonaktifkan di luar jam kantor", "Disable password": "Nonaktifkan kata sandi", "Disable registration during office hours": "Nonaktifkan pendaftaran selama jam kantor", "Disable registration if agents online": "Nonaktifkan pendaftaran jika agen online", "Disable the automatic invitation of agents to the channels.": "Nonaktifkan undangan otomatis agen ke saluran.", "Disable the channels filter.": "Nonaktifkan filter saluran.", "Disable the chatbot for the tickets area.": "Nonaktifkan chatbot untuk area tiket.", "Disable the chatbot for this channel only.": "Nonaktifkan chatbot untuk saluran ini saja.", "Disable the dashboard, and allow only one conversation per user.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, dan izinkan hanya satu percakapan per pengguna.", "Disable the login and remove the password field from the registration form.": "Nonaktifkan login dan hapus bidang kata sandi dari formulir pendaftaran.", "Disable uploads": "Nonak<PERSON><PERSON><PERSON>", "Disable voice message capabilities within the chat.": "Nonaktifkan kemampuan pesan suara dalam obrolan.", "Disable voice messages": "Nonaktifkan pesan suara", "Disabled": "Dengan disabilitas", "Display a brand image in the header area. This only applies for the 'brand' header type.": "Menampilkan citra merek di area header. Ini hanya berlaku untuk jenis header &#39;merek&#39;.", "Display categories": "<PERSON><PERSON><PERSON><PERSON> kate<PERSON>i", "Display images": "Tampilkan gambar", "Display in conversation list": "<PERSON><PERSON><PERSON><PERSON> dalam daftar perca<PERSON>pan", "Display in dashboard": "Tampilan di dasbor", "Display online agents only": "Tampilkan agen online saja", "Display the articles section in the right area.": "Tampilkan bagian artikel di area yang tepat.", "Display the dashboard instead of the chat area on initialization.": "Tampilkan dasbor alih-alih area obrolan saat inisialisasi.", "Display the feedback form to rate the conversation when it is archived.": "<PERSON><PERSON><PERSON><PERSON> formulir umpan balik untuk menilai percakapan saat diarsipkan.", "Display the user full name in the left panel instead of the conversation title.": "Tampilkan nama lengkap pengguna di panel kiri alih-alih judul percakapan.", "Display the user's profile image within the chat.": "Menampilkan gambar profil pengguna dalam obrolan.", "Display user name in header": "<PERSON><PERSON>lkan nama pengguna di header", "Display user's profile image": "<PERSON><PERSON><PERSON>an gambar profil pengguna", "Displays additional columns in the user table. Enter the name of the fields to add.": "Menampilkan kolom tambahan di tabel pengguna. Masukkan nama bidang yang akan ditambahkan.", "Distribute conversations proportionately between agents and notify visitors of their position within the queue. Response time is in minutes. You can use the following merge fields in the message: {position}, {minutes}. They will be replaced by the real values in real-time.": "Bagikan percakapan secara proporsional antara agen dan beri tahu pengunjung tentang posisi mereka dalam antrian. Waktu respons dalam menit. <PERSON>a dapat menggunakan bidang gabungan berikut dalam pesan: {position}, {minutes}. Mereka akan digantikan oleh nilai-nilai nyata secara real-time.", "Distribute conversations proportionately between agents, and block an agent from viewing the conversations of the other agents.": "Distribusikan percakapan secara proporsional antar agen, dan blokir agen agar tidak melihat percakapan agen lain.", "Do not send email notifications to admins": "<PERSON><PERSON> notifikasi email ke admin", "Do not show tickets in chat": "<PERSON><PERSON> tampilkan tiket dalam o<PERSON>lan", "Do not translate settings area": "Jangan terjemahkan area pengaturan", "Download": "<PERSON><PERSON><PERSON>", "Edit profile": "Sunting profil", "Edit user": "<PERSON> pen<PERSON>una", "Email address": "<PERSON><PERSON><PERSON> email", "Email and ticket": "<PERSON><PERSON> dan tiket", "Email header": "<PERSON><PERSON><PERSON> email", "Email notification delay (hours)": "Penundaan pemberitahuan email (jam)", "Email notifications via cron job": "Notifikasi email melalui tugas cron", "Email only": "<PERSON><PERSON> email", "Email piping": "Perpipaan email", "Email piping server information and more settings.": "Informasi server perpipaan email dan pengaturan lainnya.", "Email request message": "<PERSON><PERSON> permintaan email", "Email signature": "Tanda tangan email", "Email template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "Template email untuk email yang dikirim ke pengguna saat agen membalas. Anda dapat menggunakan teks, HTML, dan kolom gabungan berikut: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Email template for the email sent to an agent when a user sends a new message. You can use text, HTML, and the following merge fields: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "Template email untuk email yang dikirim ke agen saat pengguna mengirim pesan baru. Anda dapat menggunakan teks, HTML, dan kolom gabungan berikut: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Email template for the email sent to the user after submitting their email through the follow-up message form. You can use text, HTML, and the following merge fields: {user_name}, {user_email}.": "Template email untuk email yang dikirim ke pengguna setelah mengirimkan email mereka melalui formulir pesan tindak lanjut. Anda dapat menggunakan teks, HTML, dan bidang gabungan berikut: {user_name}, {user_email}.", "Email template for the email sent to the user to verify their email address. Include the {code} merge field within your content, it will be replaced with the one-time code.": "Template email untuk email yang dikirim ke pengguna untuk memverifikasi alamat email mereka. Sertakan kolom gabungan {code} dalam konten <PERSON>, kolom tersebut akan diganti dengan kode satu kali.", "Email verification": "Verifikasi email", "Email verification content": "Konten verifikasi email", "Enable email verification with OTP.": "Aktifkan verifikasi email dengan OTP.", "Enable logging of agent activity": "Aktifkan pencatatan aktivitas agen", "Enable logs": "Aktifkan log", "Enable the chatbot outside of scheduled office hours only.": "Aktifkan chatbot di luar jam kantor yang dija<PERSON>an saja.", "Enable the registration only if all agents are offline.": "Aktifkan pendaftaran hanya jika semua agen offline.", "Enable the registration outside of scheduled office hours only.": "Aktifkan pendaftaran di luar jam kantor yang dijad<PERSON>an saja.", "Enable this option if email notifications are sent via cron job.": "Aktifkan opsi ini jika notifikasi email dikirim melalui tugas cron.", "Enable ticket and chat support for subscribers only, view member profile details and subscription details in the admin area.": "Aktifkan dukungan tiket dan obrolan untuk pelanggan saja, lihat detail profil anggota dan detail langganan di area admin.", "Enter the bot token and click the button to synchronize the Telegram bot. Localhost cannot receive messages.": "Masukkan token bot dan klik tombol untuk menyinkronkan bot Telegram. Localhost tidak dapat menerima pesan.", "Enter the bot token and click the button to synchronize the Viber bot. Localhost cannot receive messages.": "Masukkan token bot dan klik tombol untuk menyinkronkan bot Viber. Localhost tidak dapat menerima pesan.", "Enter the database details of the Active eCommerce CMS database.": "Masukkan detail database dari Active eCommerce database CMS.", "Enter the database details of the Martfury database.": "Masukkan detail database dari database Martfury.", "Enter the database details of the Perfex database.": "Masukkan detail basis data dari basis data Perfex.", "Enter the database details of the WHMCS database.": "Masukkan detail basis data dari basis data WHMCS.", "Enter the default messages used by the chatbot when user question requires a dynamic answer.": "Masukkan pesan default yang digunakan oleh chatbot saat pertanyaan pengguna membutuhkan jawaban yang dinamis.", "Enter the details of your Google Business Messages.": "Masukkan detail Pesan Google Bisnis Anda.", "Enter the details of your Twitter app.": "Masukkan detail aplikasi Twitter Anda.", "Enter the LINE details to start using it. Localhost cannot receive messages.": "Masukkan detail LINE untuk mulai menggunakannya. Localhost tidak dapat menerima pesan.", "Enter the URL of a .css file, to load it automatically in the admin area.": "Masukkan URL file .css, untuk memuatnya secara otomatis di area admin.", "Enter the URL of a .js file, to load it automatically in the admin area.": "Masukkan URL file .js, untuk memuatnya secara otomatis di area admin.", "Enter the URL of the articles page.": "Masukkan URL halaman artikel.", "Enter the URLs of your shop": "Masukkan URL toko Anda", "Enter the WeChat official account token. See the docs for more details.": "Masukkan token akun resmi <PERSON>. Lihat dokumen untuk detail lebih lanjut.", "Enter the Zalo details to start using it. Localhost cannot receive messages.": "Masukkan detail Zalo untuk mulai menggunakan<PERSON>. Localhost tidak dapat menerima pesan.", "Enter your 360dialog account settings information.": "Masukkan informasi pengaturan akun 360dialog Anda.", "Enter your Envato Purchase Code to activate automatic updates and unlock all the features.": "Masukkan Kode Pembelian Envato Anda untuk mengaktifkan pembaruan otomatis dan membuka kunci semua fitur.", "Enter your Twilio account details. You can use text and the following merge fields: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.": "Masukkan detail akun <PERSON>. Anda dapat menggunakan teks dan bidang gabungan berikut: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.", "Enter your Twilio account settings information.": "Masukkan informasi pengaturan akun <PERSON>.", "Enter your WeChat Official Account information.": "Ma<PERSON>kkan informasi Akun Resmi WeChat Anda.", "Enter your Zendesk information.": "Masukkan informasi Zendesk Anda.", "Entities": "Entities", "Envato Purchase Code": "<PERSON><PERSON>", "Envato purchase code validation": "Validasi kode pembelian Envato", "Exclude products": "Kecualikan produk", "Export all settings.": "Ekspor semua pengaturan.", "Export settings": "Ekspor pengaturan", "Facebook pages": "Halaman Facebook", "Fallback message": "<PERSON><PERSON> mundur", "Filters": "Filter", "First chat message": "<PERSON><PERSON> obrolan pertama", "First reminder delay (hours)": "Penundaan pengingat pertama (jam)", "First ticket form": "<PERSON><PERSON><PERSON> tiket pertama", "Flash notifications": "Pemberitahuan kilat", "Follow up - Email": "Tindak lanjut - Email", "Follow up email": "<PERSON><PERSON> tin<PERSON> lan<PERSON>t", "Follow up message": "<PERSON><PERSON> tindak lanjut", "Follows a conversation between a human agent and an end user and provide response suggestions to the human agent in real-time.": "Mengi<PERSON><PERSON> perca<PERSON>pan antara agen manusia dan pengguna akhir dan memberikan saran tanggapan kepada agen manusia secara real-time.", "Follow-up email template. You can use text, HTML, and the following merge fields and more: {coupon}, {product_names}, {user_name}.": "Template email tindak lanjut. Anda dapat menggunakan teks, HTML, dan bidang gabungan berikut dan banyak lagi: {coupon}, {product_names}, {user_name}.", "Force language": "<PERSON><PERSON> bah<PERSON>", "Force log out": "Paksa logout", "Force the chat to ignore the language preferences, and to use always the same language.": "<PERSON>sa o<PERSON>lan untuk mengabaikan preferensi bahasa, dan untuk selalu menggunakan bahasa yang sama.", "Force the loggout of Support Board agents if they are not logged in WordPress.": "Paksa logout agen Support Board jika mereka tidak login di WordPress.", "Force users to use a different conversation for each store and hide conversations from other stores from store administrators.": "Paksa pengguna untuk menggunakan percakapan yang berbeda untuk setiap toko dan sembunyikan percakapan dari toko lain dari administrator toko.", "Force users to use only one phone country code.": "Paksa pengguna untuk hanya menggunakan satu kode negara telepon.", "Form message": "<PERSON><PERSON> formulir", "Form title": "<PERSON><PERSON><PERSON>", "Frequency penalty": "<PERSON><PERSON><PERSON>", "Full visitor details": "Detail pengunjung lengkap", "Function name": "<PERSON><PERSON> fungsi", "Generate conversations data": "Hasilkan data percakapan", "Generate user questions": "<PERSON><PERSON><PERSON> pengguna", "Get configuration URL": "Dapatkan URL konfigurasi", "Get it from the APP_KEY value of the file .env located in the root directory of Active eCommerce.": "Dapatkan dari nilai APP_KEY dari file .env yang terletak di direktori root Active eCommerce.", "Get it from the APP_KEY value of the file .env located in the root directory of Martfury.": "Dapatkan dari nilai APP_KEY dari file .env yang terletak di direktori root Martfury.", "Get Path": "Dapatkan Jalan", "Get Service Worker path": "Dapatkan jalur Service Worker", "Get URL": "Dapatkan URL", "Google and Dialogflow settings.": "Pengaturan Google dan Dialogflow.", "Google search": "Pencarian Google", "Header": "<PERSON><PERSON><PERSON>", "Header background image": "Gambar latar belakang tajuk", "Header brand image": "Gambar merek tajuk", "Header message": "<PERSON><PERSON> ta<PERSON>k", "Header title": "<PERSON><PERSON><PERSON>", "Header type": "<PERSON><PERSON>", "Header variables": "<PERSON><PERSON><PERSON>", "Hide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Hide agent's profile image": "Sembunyikan gambar profil agen", "Hide archived tickets": "Sembunyikan tiket yang diarsipkan", "Hide archived tickets from users.": "Sembunyikan tiket yang diarsipkan dari pengguna.", "Hide chat if no agents online": "Sembunyikan obrolan jika tidak ada agen online", "Hide chat outside of office hours": "Sembunyikan obrolan di luar jam kantor", "Hide conversation details panel": "Sembunyikan panel detail percakapan", "Hide conversations of other agents": "Sembunyikan percakapan agen lain", "Hide on mobile": "Sembunyikan di ponsel", "Hide the agent's profile image within the chat.": "Sembunyikan gambar profil agen dalam o<PERSON>lan.", "Hide tickets from the chat widget and chats from the ticket area.": "Sembunyikan tiket dari widget obrolan dan obrolan dari area tiket.", "Hide timetable": "<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>wal", "Host": "<PERSON><PERSON> r<PERSON>", "Human takeover": "Pengam<PERSON>alihan manusia", "If no agents respond within the specified time interval, a message will be sent to request the user's details, such as their email.": "Jika tidak ada agen yang merespons dalam interval waktu yang ditentukan, sebuah pesan akan dikirim untuk meminta detail pengguna, seperti email mereka.", "If the chatbot doesn't understand a user's question, forwards the conversation to an agent.": "<PERSON><PERSON> chatbot tidak memahami per<PERSON>aan pengguna, teruskan percakapan ke agen.", "Image": "Gambar", "Import admins": "Impor admin", "Import all settings.": "<PERSON><PERSON><PERSON> semua pen<PERSON>.", "Import articles": "<PERSON><PERSON><PERSON> artikel", "Import contacts": "<PERSON><PERSON><PERSON> k<PERSON>", "Import customers": "<PERSON><PERSON><PERSON>", "Import customers into Support Board. Only new customers will be imported.": "Impor pelanggan ke Support Board. Hanya pelanggan baru yang akan diimpor.", "Import settings": "<PERSON><PERSON><PERSON>", "Import users": "I<PERSON>r pen<PERSON>una", "Import users from a CSV file.": "Impor pengguna dari berkas CSV.", "Import vendors": "Impor vendor", "Import vendors into Support Board as agents. Only new vendors will be imported.": "Impor vendor ke Support Board sebagai agen. Hanya vendor baru yang akan diimpor.", "Improve chat performance with Pusher and WebSockets. This setting stops all AJAX/HTTP real-time requests that slow down your server and use instead the WebSockets.": "Tingkatkan kinerja obrolan dengan Pusher dan WebSockets. Pengaturan ini menghentikan semua permintaan waktu nyata AJAX/HTTP yang memperlambat server Anda dan sebagai gantinya menggunakan WebSockets.", "Include custom fields": "Sertakan bidang khusus", "Include custom fields in the registration form.": "Sertakan bidang khusus dalam formulir pendaftaran.", "Include the password field in the registration form.": "Sertakan bidang kata sandi dalam formulir pendaftaran.", "Incoming conversations and messages": "<PERSON><PERSON>ka<PERSON> dan pesan masuk", "Incoming conversations only": "Percakapan masuk saja", "Incoming messages only": "<PERSON><PERSON> pesan masuk", "Increase sales and connect you and sellers with customers in real-time by integrating Active eCommerce with Support Board.": "Tingkatkan penjualan dan hubungkan Anda dan penjual dengan pelanggan secara real-time dengan mengintegrasikan Active eCommerce dengan Support Board.", "Increase sales, provide better support, and faster solutions, by integrating WooCommerce with Support Board.": "<PERSON><PERSON><PERSON><PERSON>, berikan dukungan yang lebih baik, dan solusi yang lebih cepat, dengan mengintegrasikan WooCommerce dengan Support Board.", "Info message": "Pesan info", "Initialize and display the chat widget and tickets only for members.": "Inisialisasi dan tampilkan widget obrolan dan tiket hanya untuk anggota.", "Initialize and display the chat widget only when the user is logged in.": "Inisialisasi dan tampilkan widget obrolan hanya saat pengguna masuk.", "Instance ID": "ID Instans", "Integrate OpenCart with {R} for real-time syncing of customers, order history access, and customer cart visibility.": "Integrasikan OpenCart dengan {R} untuk sinkronisasi pelanggan secara real-time, aks<PERSON> ri<PERSON> pesanan, dan visibilitas keranjang pelanggan.", "Interval (sec)": "Interval (detik)", "IP banning": "Larangan IP", "Label": "Label", "Language": "Bahasa", "Language detection": "<PERSON><PERSON><PERSON> bahasa", "Language detection message": "<PERSON><PERSON> deteksi bahasa", "Last name": "<PERSON><PERSON> k<PERSON>", "Leave it blank if you don't know what this setting is! Entering an incorrect value will break the chat. Sets the main domain where chat is used to enable login and conversations sharing between the main domain and sub domains.": "Biarkan kosong jika Anda tidak tahu apa pengaturan ini! Memasukkan nilai yang salah akan memutus obrolan. Menyetel domain utama tempat obrolan digunakan untuk mengaktifkan login dan berbagi percakapan antara domain utama dan subdomain.", "Left": "<PERSON><PERSON>", "Left panel": "Panel kiri", "Left profile image": "Gambar profil kiri", "Let the bot to search on Google to find answers to user questions.": "Biarkan bot mencari di Google untuk menemukan jawaban atas pertanyaan pengguna.", "Let the chatbot search on Google to find answers to user questions.": "Biarkan chatbot mencari di Google untuk menemukan jawaban atas pertanyaan pengguna.", "Lets your users reach you via Twitter. Read and reply to messages sent to your Twitter account directly from {R}.": "Memungkinkan pengguna menghubungi Anda melalui Twitter. Baca dan balas pesan yang dikirim ke akun Twitter Anda langsung dari {R}.", "Lets your users reach you via WeChat. Read and reply to all messages sent to your WeChat official account directly from {R}.": "Memungkinkan pengguna menghubungi Anda melalui WeChat. Baca dan balas semua pesan yang dikirim ke akun resmi WeChat Anda langsung dari {R}.", "Lets your users reach you via WhatsApp. Read and reply to all messages sent to your WhatsApp Business account directly from {R}.": "Memungkinkan pengguna menghubungi Anda melalui WhatsApp. Baca dan balas semua pesan yang dikirim ke akun WhatsApp Business Anda langsung dari {R}.", "Link each agent with the corresponding Slack user, so when an agent replies via Slack it will be displayed as the assigned agent.": "<PERSON><PERSON><PERSON> setiap agen dengan pengguna Slack yang sesuai, jadi ketika agen membalas melalui <PERSON>, itu akan ditampilkan sebagai agen yang ditugaskan.", "Link name": "<PERSON><PERSON>", "Login form": "<PERSON><PERSON><PERSON> ma<PERSON>", "Login initialization": "Inisialisasi masuk", "Login verification URL": "URL verifikasi masuk", "Logit bias": "Bias logit", "Make a backup of your Dialogflow agent first. This operation can take several minutes.": "Buat cadangan agen Dialogflow Anda terlebih dahulu. Operasi ini dapat memakan waktu beberapa menit.", "Make the registration phone field mandatory.": "Jadikan bidang telepon pendaftaran wajib.", "Manage": "Mengelola", "Manage here the departments settings.": "Kelola di sini pengaturan departemen.", "Manage the tags settings.": "<PERSON><PERSON><PERSON> pengaturan tag.", "Manifest file URL": "Manifest file URL", "Manual": "Petunjuk", "Manual initialization": "Inisialisasi manual", "Martfury root directory path, e.g. /var/www/": "Jalur direktori root Martfury, misalnya /var/www/", "Martfury shop URL, e.g. https://shop.com": "URL toko Martfury, misalnya https://shop.com", "Max message limit": "Batas pesan maks", "Max tokens": "Token maks", "Members only": "<PERSON><PERSON>", "Members with an active paid plan only": "<PERSON><PERSON><PERSON> dengan paket berbayar aktif saja", "Message": "<PERSON><PERSON>", "Message area": "Area pesan", "Message rewrite button": "<PERSON>ol tulis ulang pesan", "Message template": "Templat pesan", "Message type": "<PERSON><PERSON> p<PERSON>", "Messaging channels": "<PERSON><PERSON><PERSON> pesan", "Messenger and Instagram settings": "Messenger dan pen<PERSON><PERSON>n <PERSON>gram", "Minify JS": "Perkecil JS", "Minimal": "Minimal", "Model": "Model", "Multilingual": "Multibahasa", "Multilingual plugin": "Plugin multibahasa", "Multilingual via translation": "Multibahasa melalui ter<PERSON>an", "Multlilingual training sources": "Sumber pelatihan multibahasa", "Name": "<PERSON><PERSON>", "Namespace": "<PERSON><PERSON>", "New conversation email": "<PERSON>ail <PERSON> baru", "New conversation notification": "Notifikasi perca<PERSON>pan baru", "New ticket button": "<PERSON><PERSON> tiket baru", "Newsletter": "<PERSON><PERSON><PERSON>", "No delay": "Tidak ada penundaan", "No results found.": "Tidak ada hasil yang di<PERSON>n.", "No, we don't ship in": "<PERSON><PERSON><PERSON>, kami tidak mengirim", "None": "Tidak ada", "Note data scraping": "Catat pengikisan data", "Notes": "Catatan", "Notifications icon": "<PERSON><PERSON>", "Notify the user when their message is sent outside of the scheduled office hours or all agents are offline.": "Memberi tahu pengguna ketika pesan mereka dikirim di luar jam kantor yang di<PERSON> atau semua agen sedang offline.", "OA secret key": "Kunci rahasia OA", "Offline message": "Pesan offline", "Offset": "Mengimbangi", "On chat open": "Di obrolan terbuka", "On page load": "Pada pemuatan halaman", "One conversation per agent": "Satu percakapan per agen", "One conversation per department": "<PERSON>tu perca<PERSON>pan per departemen", "Online users notification": "Pemberitahuan pengguna online", "Only desktop": "Hanya desktop", "Only general questions": "<PERSON><PERSON> umum", "Only mobile devices": "<PERSON><PERSON> seluler", "Only questions related to your sources": "<PERSON><PERSON> per<PERSON>aan yang terkait dengan sumber Anda", "Open automatically": "<PERSON><PERSON> secara otomatis", "Open chat": "<PERSON><PERSON> o<PERSON>", "Open the chat window automatically when a new message is received.": "<PERSON><PERSON> jendela obrolan secara otomatis ketika pesan baru diterima.", "OpenAI Assistants - Department linking": "Asisten OpenAI - Tautan departemen", "OpenAI settings.": "Pengaturan OpenAI.", "Optional link": "Tautan opsional", "Order webhook": "<PERSON><PERSON> webhook", "Other": "<PERSON><PERSON><PERSON>", "Outgoing SMTP server information.": "Informasi server SMTP keluar.", "Page ID": "ID halaman", "Page IDs": "ID halaman", "Page name": "<PERSON><PERSON>", "Page token": "<PERSON><PERSON> halaman", "Panel height": "Tinggi panel", "Panel name": "<PERSON><PERSON> panel", "Panel title": "Ju<PERSON><PERSON> panel", "Panels arrows": "Panah panel", "Password": "<PERSON>a sandi", "Perfex URL": "URL Sempurna", "Performance optimization": "Optimal<PERSON><PERSON> kinerja", "Phone": "Telepon", "Phone number ID": "Identitas nomor telepon", "Phone required": "Telepon diperlukan", "Place ID": "ID tempat", "Placeholder text": "Teks tempat penampung", "Play a sound for new messages and conversations.": "Memutar suara untuk pesan dan percakapan baru.", "Popup message": "Pesan pop-up", "Port": "Pelabuhan", "Post Type slugs": "Post Type siput", "Presence penalty": "<PERSON><PERSON><PERSON>", "Prevent admins from receiving email notifications.": "Cegah admin menerima pemberitahuan email.", "Prevent agents from viewing conversations assigned to other agents. This setting is automatically enabled if routing or queue is active.": "Mencegah agen melihat percakapan yang ditugaskan ke agen lain. Pengaturan ini secara otomatis diaktifkan jika perutean atau antrian aktif.", "Prevent any abuse from users by limiting the number of messages sent to the chatbot from one device.": "<PERSON><PERSON><PERSON> penyalah<PERSON>aan dari pengguna dengan membatasi jumlah pesan yang dikirim ke chatbot dari satu perangkat.", "Primary color": "Warna primer", "Priority": "Prioritas", "Privacy link": "Tautan privasi", "Privacy message": "<PERSON><PERSON> privasi", "Private chat": "Obrolan pribadi", "Private chat linking": "<PERSON><PERSON> o<PERSON> p<PERSON>i", "Private key": "<PERSON><PERSON><PERSON> pribadi", "Product IDs": "ID Produk", "Product removed notification": "Pemberitahuan produk dihapus", "Product removed notification - Email": "Pemberitahuan produk <PERSON><PERSON> - Email", "Profile image": "Gambar profil", "Project ID": "ID proyek", "Project ID or Agent Name": "ID Proyek atau Nama Agen", "Prompt": "Mengingatkan", "Prompt - Message rewriting": "Prompt - <PERSON><PERSON><PERSON> pesan", "Protect the tickets area from spam and abuse with Google reCAPTCHA.": "Lindungi area tiket dari spam dan penyalahgunaan dengan Google reCAPTCHA.", "Provide help desk support to your customers by including a ticket area, with all chat features included, on any web page in seconds.": "Berikan dukungan meja bantuan kepada pelanggan Anda dengan menyertakan area tiket, dengan semua fitur obrolan disertakan, di halaman web mana pun dalam hitungan detik.", "Provider": "P<PERSON>ber<PERSON>", "Purchase button text": "Teks tombol beli", "Push notifications": "Pemberitahuan push", "Push notifications settings.": "Pengaturan pemberitahuan push.", "Queue": "<PERSON><PERSON>", "Rating": "<PERSON><PERSON><PERSON>", "Read and reply to messages sent from Google Search, Maps and brand-owned channels directly in {R}.": "Baca dan balas pesan yang dikirim dari Google Penelusuran, Maps, dan saluran milik merek langsung di {R}.", "Read, manage and reply to all messages sent to your Facebook pages and Instagram accounts directly from {R}.": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, dan balas semua pesan yang dikirim ke halaman Facebook dan akun Instagram Anda langsung dari {R}.", "Reconnect": "Hubungkan kembali", "Redirect the user to the registration link instead of showing the registration form.": "<PERSON><PERSON><PERSON> pengguna ke tautan pendaftaran alih-alih menu<PERSON>n formulir pendaftaran.", "Redirect the user to the specified URL if the registration is required and the user is not logged in. Leave blank to use the default registration form.": "Arahkan pengguna ke URL yang ditentukan jika pendaftaran diperlukan dan pengguna tidak masuk. Biarkan kosong untuk menggunakan formulir pendaftaran default.", "Refresh token": "Per<PERSON><PERSON> token", "Register all visitors": "<PERSON>ftark<PERSON> semua <PERSON>", "Register all visitors automatically. When this option is not active, only the visitors that start a chat will be registered.": "Daftarkan semua pengunjung secara otomatis. Saat opsi ini tidak aktif, hanya pengunjung yang memulai obrolan yang akan didaftarkan.", "Registration / Login": "Pendaftaran / Masuk", "Registration and login form": "<PERSON><PERSON><PERSON> pendaftaran dan login", "Registration fields": "Bidang pendaftaran", "Registration form": "<PERSON><PERSON><PERSON> penda<PERSON>", "Registration link": "<PERSON><PERSON> penda<PERSON>", "Registration redirect": "<PERSON><PERSON><PERSON><PERSON> pen<PERSON>", "Rename the chat bot. Default is 'Bot'.": "Ganti nama bot obrolan. Standarnya adalah &#39;Bot&#39;.", "Rename the visitor name prefix. Default is 'User'.": "<PERSON>anti nama awalan nama pengunjung. Standarnya adalah &#39;Pengguna&#39;.", "Repeat": "Ulang", "Repeat - admin": "Ulangi - admin", "Replace the admin login page message.": "Ganti pesan halaman login admin.", "Replace the brand logo on the admin login page.": "Ganti logo brand pada halaman login admin.", "Replace the header title with the user's first name and last name when available.": "Ganti judul header dengan nama depan dan nama belakang pengguna jika tersedia.", "Replace the top-left brand icon on the admin area and the browser favicon.": "Ganti ikon merek kiri atas di area admin dan favicon browser.", "Reply to user emails": "Balas ke email pengguna", "Reply to user text messages": "Membalas pesan teks pengguna", "Reports": "<PERSON><PERSON><PERSON>", "Reports area": "<PERSON><PERSON><PERSON>", "Request a valid Envato purchase code for registration.": "Minta kode pembelian Envato yang valid untuk pendaftaran.", "Request the user to provide their email address and then send a confirmation email to the user.": "Minta pengguna untuk memberikan alamat emailnya dan kemudian mengirimkan email konfirmasi kepada pengguna.", "Require phone": "Perlu telepon", "Require registration": "<PERSON><PERSON>", "Require the user registration or login before start a chat. To enable the login area the password field must be included.": "Memerlukan pendaftaran pengguna atau login sebelum memulai obrolan. Untuk mengaktifkan area login, bidang kata sandi harus disertakan.", "Require the user registration or login in order to use the tickets area.": "Memerlukan pendaftaran pengguna atau login untuk menggunakan area tiket.", "Required": "<PERSON>g di<PERSON>", "Response time": "<PERSON><PERSON><PERSON>", "Restrict chat access by blocking IPs. List IPs with commas.": "Batasi akses obrolan dengan memblokir IP. Daftar IP dengan koma.", "Returning visitor message": "Ke<PERSON><PERSON> pesan pengunjung", "Rich messages": "<PERSON><PERSON> kaya", "Rich messages are code snippets that can be utilized within a chat message. They can contain HTML code and are automatically rendered in the chat. Rich messages can be used with the following syntax: [rich-message-name]. There are a tonne of built-in rich messages to choose from.": "Pesan kaya adalah cuplikan kode yang dapat digunakan dalam pesan obrolan. <PERSON>reka dapat berisi kode HTML dan secara otomatis ditampilkan dalam obrolan. Pesan kaya dapat digunakan dengan sintaks berikut: [rich-message-name]. Ada banyak sekali pesan kaya bawaan untuk dipilih.", "Right": "<PERSON><PERSON>", "Right panel": "<PERSON> kanan", "Routing": "Rute", "Routing if offline": "Perutean jika offline", "RTL": "RTL", "Save useful information like user country and language also for visitors.": "Simpan informasi yang berguna seperti negara pengguna dan bahasa juga untuk pengunjung.", "Saved replies": "<PERSON><PERSON><PERSON>", "Scheduled office hours": "<PERSON><PERSON><PERSON> jam kantor", "Search engine ID": "ID mesin pencari", "Second chat message": "<PERSON><PERSON> o<PERSON>ua", "Second reminder delay (hours)": "Penundaan pengingat kedua (jam)", "Secondary color": "<PERSON><PERSON>", "Secret key": "<PERSON><PERSON><PERSON> rahasia", "Send a message to allow customers to be notified when they can purchase a product they are interested in, but that is currently out of stock. You can use the following merge fields: {user_name}, {product_name}.": "<PERSON><PERSON> pesan untuk memungkinkan pelanggan diberi tahu saat mereka dapat membeli produk yang mereka minati, tetapi saat ini stoknya habis. Anda dapat menggunakan bidang gabungan berikut: {user_name}, {product_name}.", "Send a message to new users when they create the first ticket. Text formatting and merge fields are supported.": "<PERSON><PERSON> pesan ke pengguna baru saat mereka membuat tiket pertama. Pemformatan teks dan bidang gabungan didukung.", "Send a message to new users when they visit the website for the first time.": "<PERSON><PERSON> pesan ke pengguna baru saat mereka mengunjungi situs web untuk pertama kalinya.", "Send a message to the customer after a product has been removed from the cart. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.": "<PERSON><PERSON> pesan ke pelanggan setelah produk dikeluarkan dari keranjang. Anda dapat menggunakan bidang gabungan berikut dan lainnya: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.", "Send a message to the customers who complete a purchase asking to share the product they just bought. You can use the following merge fields and more: {product_name}, {user_name}.": "<PERSON><PERSON> pesan ke pelanggan yang menyelesaikan pembelian meminta untuk membagikan produk yang baru saja mereka beli. Anda dapat menggunakan bidang gabungan berikut dan lainnya: {product_name}, {user_name}.", "Send a message to the customers who complete a purchase. You can use the following merge fields and more: {coupon}, {product_names}, {user_name}.": "<PERSON><PERSON> pesan ke pelanggan yang menyelesaikan pembelian. <PERSON>a dapat menggunakan bidang gabungan berikut dan lainnya: {coupon}, {product_names}, {user_name}.", "Send a message to the user when the agent archive the conversation.": "<PERSON><PERSON> pesan ke pengguna saat agen mengarsipkan percakapan.", "Send a message to users who visit the website again after at least 24 hours. You can use the following merge fields and more: {coupon}, {user_name}. See the docs for more details.": "<PERSON><PERSON> pesan kepada pengguna yang mengunjungi situs web lagi setelah setidaknya 24 jam. Anda dapat menggunakan bidang gabungan berikut dan lainnya: {coupon}, {user_name}. Lihat dokumen untuk lebih jelasnya.", "Send a test agent notification email to verify email settings.": "Kirim email pemberitahuan agen pengujian untuk memverifikasi pengaturan email.", "Send a test message to your Slack channel. This only tests the sending functionality of outgoing messages.": "<PERSON><PERSON> pesan percobaan ke saluran Slack Anda. Ini hanya menguji fungsionalitas pengiriman pesan keluar.", "Send a test user notification email to verify email settings.": "Kirim email pemberitahuan pengguna uji untuk memverifikasi pengaturan email.", "Send a text message to the provided phone number.": "<PERSON><PERSON> pesan teks ke nomor telepon yang disediakan.", "Send a user email notification": "Kirim pemberitahuan email pengguna", "Send a user text message notifcation": "Kirim pemberitahuan pesan teks pengguna", "Send a user text message notification": "Kirim pemberitahuan pesan teks pengguna", "Send an agent email notification": "<PERSON><PERSON> pemberitahuan email agen", "Send an agent text message notification": "<PERSON><PERSON> pemberitahuan pesan teks agen", "Send an agent user text notification": "Kirim pemberitahuan teks pengguna agen", "Send an email notification to the provided email address.": "<PERSON><PERSON><PERSON> email ke alamat email yang diberikan.", "Send an email to an agent when a user replies and the agent is offline. An email is automatically sent to all agents for new conversations.": "<PERSON><PERSON> email ke agen saat pengguna membalas dan agen sedang offline. Email secara otomatis dikirim ke semua agen untuk percakapan baru.", "Send an email to the user when a new conversation is created.": "Ki<PERSON> email ke pengguna saat percakapan baru dibuat.", "Send an email to the user when a new conversation or ticket is created": "Kirim email ke pengguna saat percakapan atau tiket baru dibuat", "Send an email to the user when an agent replies and the user is offline.": "<PERSON><PERSON> email ke pengguna saat agen membalas dan pengguna offline.", "Send email": "Mengirim email", "Send login details to the specified URL and allow access only if the response is positive.": "Kirim detail login ke URL yang ditentukan dan izinkan akses hanya jika responsnya positif.", "Send message": "Mengirim pesan", "Send message to Slack": "<PERSON><PERSON> pesan ke <PERSON>ck", "Send message via enter button": "<PERSON><PERSON> pesan melalui tombol enter", "Send text message": "<PERSON><PERSON> pesan teks", "Send the message template to a WhatsApp number.": "Kirim template pesan ke nomor WhatsApp.", "Send the message via the ENTER keyboard button.": "<PERSON><PERSON> pesan melalui tombol keyboard ENTER.", "Send the user details of the registration form and email rich messages to Dialogflow.": "Kirimkan detail pengguna formulir pendaftaran dan pesan kaya email ke Dialogflow.", "Send the WhatsApp order details to the URL provided.": "<PERSON><PERSON> <PERSON> pesanan <PERSON>sApp ke URL yang disediakan.", "Send to user's email": "<PERSON><PERSON> ke email pengguna", "Send transcript to user's email": "<PERSON><PERSON> transkrip ke email pengguna", "Send user details": "Kirim detail pengguna", "Sender": "Pengirim", "Sender email": "Mengirim email", "Sender name": "<PERSON><PERSON>m", "Sender number": "<PERSON>mor pen<PERSON>m", "Sends a text message if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.": "Mengirim pesan teks jika pengiriman pesan WhatsApp gagal. Anda dapat menggunakan teks dan bidang gabungan berikut: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.", "Sends a WhatsApp Template notification if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {recipient_name}, {recipient_email}.": "Mengirim pemberitahuan Template WhatsApp jika pengiriman pesan WhatsApp gagal. Anda dapat menggunakan teks dan bidang gabungan berikut: {conversation_url_parameter}, {recipient_name}, {recipient_email}.", "Service": "<PERSON><PERSON><PERSON>", "Service Worker path": "Service Worker jalur", "Service Worker URL": "Service Worker URL", "Set a dedicated Dialogflow agent for each department.": "Tetapkan agen Dialogflow khusus untuk setiap departemen.", "Set a dedicated OpenAI Assistants for each department.": "Tetapkan Asisten OpenAI khusus untuk setiap departemen.", "Set a dedicated Slack channel for each department.": "Tetapkan saluran Slack khusus untuk setiap departemen.", "Set a profile image for the chat bot.": "Atur gambar profil untuk bot obrolan.", "Set the articles panel title. Default is 'Help Center'.": "Tetapkan judul panel artikel. Standarnya adalah &#39;Pusa<PERSON>&#39;.", "Set the avatar image shown next to the message. It must be a JPG image of 1024x1024px with a maximum size of 50KB.": "Atur gambar avatar yang ditampilkan di sebelah pesan. Harus berupa gambar JPG berukuran 1024x1024 piksel dengan ukuran maksimum 50 KB.", "Set the chat language or translate it automatically to match the user language. Default is English.": "Atur bahasa obrolan atau terjemahkan secara otomatis agar sesuai dengan bahasa pengguna. Bahasa Ingg<PERSON> adalah bahasa default.", "Set the currency symbol of the membership prices.": "Tetapkan simbol mata uang dari harga keanggotaan.", "Set the currency symbol used by your system.": "Atur simbol mata uang yang digunakan oleh sistem Anda.", "Set the default departments for all tickets. Enter the department ID.": "Tetapkan departemen default untuk semua tiket. Masukkan ID departemen.", "Set the default email header that will be prepended to automated emails and direct emails.": "Atur header email default yang akan ditambahkan ke email otomatis dan email langsung.", "Set the default email signature that will be appended to automated emails and direct emails.": "Atur tanda tangan email default yang akan ditambahkan ke email otomatis dan email langsung.", "Set the default form to display if the registraion is required.": "Atur formulir default untuk ditampilkan jika pendaftaran diperlukan.", "Set the default name to use for conversations without a name.": "Setel nama default yang akan digunakan untuk percakapan tanpa nama.", "Set the default notifications icon. The icon will be used as a profile image if the user doesn't have one.": "Setel ikon notifikasi default. Ikon akan digunakan sebagai gambar profil jika pengguna tidak memilikinya.", "Set the default office hours for when agents are shown as available. These settings are also used for all other settings that rely on office hours.": "Atur jam kantor default saat agen ditampilkan sebagai tersedia. Pengaturan ini juga digunakan untuk semua pengaturan lain yang bergantung pada jam kantor.", "Set the default username to use in bot messages and emails when the user doesn't have a name.": "Setel nama pengguna default untuk digunakan dalam pesan bot dan email saat pengguna tidak memiliki nama.", "Set the header appearance.": "Atur tampilan tajuk.", "Set the maximum height of the tickets panel.": "Atur ketinggian maksimum panel tiket.", "Set the multilingual plugin you're using, or leave it disabled if your site uses only one language.": "Setel plugin multibahasa yang <PERSON>, atau biarkan dinonaktifkan jika situs Anda hanya menggunakan satu bahasa.", "Set the offline status automatically when the agent or admin remains inactive in the admin area for at least 10 minutes.": "Atur status offline secara otomatis ketika agen atau admin tidak aktif di area admin setidaknya selama 10 menit.", "Set the position of the chat widget.": "Atur posisi widget obrolan.", "Set the primary color of the admin area.": "Tetapkan warna utama area admin.", "Set the primary color of the chat widget.": "Atur warna utama widget obrolan.", "Set the secondary color of the admin area.": "Atur warna sekunder area admin.", "Set the secondary color of the chat widget.": "Atur warna sekunder widget obrolan.", "Set the tertiary color of the chat widget.": "Atur warna tersier dari widget obrolan.", "Set the title of the administration area.": "Menetapkan judul wilayah administrasi.", "Set the title of the conversations panel.": "Setel judul panel percakapan.", "Set the UTC offset of the office hours timetable. The correct value can be negative, and it's generated automatically once you click this input field, if it's empty.": "Atur offset UTC dari jadwal jam kantor. <PERSON><PERSON> yang benar bisa negatif, dan di<PERSON><PERSON>kan secara otomatis setelah <PERSON>a mengklik kolom input ini, jika kosong.", "Set which actions to allow agents.": "Tetapkan tindakan mana yang mengizinkan agen.", "Set which actions to allow supervisors.": "Tetapkan tindakan mana yang memungkinkan supervisor.", "Set which user details to send to the main channel. Add comma separated values.": "Setel detail pengguna mana yang akan dikirim ke saluran utama. Tambahkan nilai yang dipisahkan koma.", "Settings area": "Bidang pengaturan", "settings information": "informasi pengaturan", "Shop": "<PERSON><PERSON>", "Show": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Show a browser tab notification when a new message is received.": "Tampilkan pemberitahuan tab browser ketika pesan baru diterima.", "Show a desktop notification when a new message is received.": "Tampilkan pemberitahuan desktop saat pesan baru diterima.", "Show a notification and play a sound when a new user is online.": "<PERSON><PERSON><PERSON><PERSON> notifikasi dan putar suara saat pengguna baru online.", "Show a pop-up notification to all users.": "<PERSON><PERSON><PERSON><PERSON> pemberitahuan pop-up ke semua pengguna.", "Show profile images": "<PERSON><PERSON><PERSON><PERSON> gambar profil", "Show sender's name": "<PERSON><PERSON><PERSON><PERSON> nama pen<PERSON>m", "Show the agents menu in the dashboard and force the user to choose an agent to start a conversation.": "Tampilkan menu agen di dasbor dan paksa pengguna untuk memilih agen untuk memulai percakapan.", "Show the articles panel on the chat dashboard.": "Tampilkan panel artikel di dasbor obrolan.", "Show the categories instead of the articles list.": "<PERSON><PERSON><PERSON><PERSON>, bukan daftar artikel.", "Show the follow up message when a visitor add an item to the cart. The message is sent only if the user has not provided an email yet.": "<PERSON><PERSON><PERSON><PERSON> pesan tindak lanjut ketika pengunjung menambahkan item ke keranjang. Pesan dikirim hanya jika pengguna belum memberikan email.", "Show the list of all Slack channels.": "<PERSON><PERSON><PERSON><PERSON> daftar semua saluran <PERSON>.", "Show the profile image of agents and users within the conversation.": "<PERSON><PERSON><PERSON><PERSON> gambar profil agen dan pengguna dalam percakapan.", "Show the sender's name in every message.": "<PERSON><PERSON><PERSON><PERSON> nama pengirim di set<PERSON>p pesan.", "Single label": "Label tunggal", "Single phone country code": "Kode negara telepon tunggal", "Site key": "Kunci situs", "Slug": "Siput", "Social share message": "<PERSON><PERSON> berbagi sosial", "Sort conversations by date": "Urutkan percakapan berdasarkan tanggal", "Sound": "<PERSON><PERSON>", "Sound settings": "<PERSON><PERSON><PERSON><PERSON> suara", "Sounds": "<PERSON><PERSON>", "Sounds - admin": "<PERSON><PERSON><PERSON><PERSON><PERSON> - admin", "Source links": "Tautan sumber", "Speech recognition": "Pengenalan suara", "Spelling correction": "<PERSON><PERSON><PERSON>", "Starred tag": "Tag berbintang", "Start importing": "<PERSON><PERSON>", "Store name": "<PERSON><PERSON> toko", "Subject": "Subjek", "Subscribe": "<PERSON><PERSON><PERSON>", "Subscribe users to your preferred newsletter service when they provide an email.": "<PERSON><PERSON><PERSON><PERSON><PERSON> pengguna ke layanan buletin pilihan Anda saat mereka memberikan email.", "Subtract the offset value from the height value.": "Kurangi nilai offset dari nilai k<PERSON>gian.", "Success message": "<PERSON><PERSON> sukses", "Supervisors": "Pengawas", "Support Board path": "<PERSON><PERSON><PERSON>", "Sync admin and staff accounts with Support Board. Staff users will be registered as agents, while admins as admins. Only new users will be imported.": "Sinkronkan akun admin dan staf dengan <PERSON>. Pengguna staf akan terdaftar sebagai agen, sedangkan admin sebagai admin. <PERSON><PERSON> pengguna baru yang akan diimpor.", "Sync all contacts of all clients with Support Board. Only new contacts will be imported.": "Sinkronkan semua kontak semua klien dengan Papan Dukungan. Hanya kontak baru yang akan diimpor.", "Sync all users with Support Board. Only new users will be imported.": "Sinkronkan semua pengguna dengan Papan Dukungan. <PERSON><PERSON> pengguna baru yang akan diimpor.", "Sync all WordPress users with Support Board. Only new users will be imported.": "Sinkronkan semua pengguna WordPress dengan Support Board. <PERSON>ya pengguna baru yang akan diimpor.", "Sync knowledge base articles with Support Board. Only new articles will be imported.": "Sinkronkan artikel basis pen<PERSON><PERSON><PERSON> dengan <PERSON>. Hanya artikel baru yang akan diimpor.", "Sync mode": "<PERSON><PERSON>", "Synchronization": "Sink<PERSON><PERSON><PERSON>", "Synchronize": "Sinkronkan", "Synchronize customers, enable ticket and chat support for subscribers only, view subscription plans in the admin area.": "<PERSON><PERSON><PERSON><PERSON><PERSON> pelanggan, aktifkan dukungan tiket dan obrolan hanya untuk pelanggan, lihat paket berlangganan di area admin.", "Synchronize emails": "Sinkronkan email", "Synchronize Entities": "Sinkronkan Entities", "Synchronize Entities now": "Sinkronkan Entities sekarang", "Synchronize now": "Sinkronkan sekarang", "Synchronize users": "Sinkronkan pengguna", "Synchronize your customers in real-time, chat with them and boost their engagement, or provide a better and faster support.": "Sinkronkan pelanggan Anda secara real-time, mengobrol dengan mereka dan tingkatkan keterlibatan mereka, atau berikan dukungan yang lebih baik dan lebih cepat.", "Synchronize your Messenger and Instagram accounts.": "Sinkronkan akun Messenger dan <PERSON>.", "Synchronize your Perfex customers in real-time and let them contact you via chat! View profile details, proactively engage them, and more.": "Sinkronkan pelanggan Perfex Anda secara real-time dan biarkan mereka menghubungi Anda melalui obrolan! Lihat detail profil, libatkan mereka secara proaktif, dan banyak lagi.", "Synchronize your WhatsApp Cloud API account.": "Sinkronkan akun WhatsApp Cloud API Anda.", "System requirements": "Persyaratan sistem", "Tags": "Tag", "Tags settings": "Pengaturan tag", "Template default language": "Bahasa default template", "Template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "Template untuk email yang dikirim ke pengguna saat agen membalas. Anda dapat menggunakan teks, HTML, dan bidang gabungan berikut: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Template for the email sent to the user when a new conversation is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}.": "Templat untuk email yang dikirim ke pengguna saat percakapan baru dibuat. Anda dapat menggunakan teks, HTML, dan kolom gabungan berikut: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}.", "Template for the email sent to the user when a new conversation or ticket is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}.": "Template untuk email yang dikirim ke pengguna saat percakapan atau tiket baru dibuat. Anda dapat menggunakan teks, HTML, dan bidang gabungan berikut: {conversation_url_parameter}, {user_name}, {message}, {attachments}.", "Template languages": "Bahasa templat", "Template name": "<PERSON>a template", "Template of the admin notification email. You can use text, HTML, and the following merge field and more: {carts}. Enter the email you want to send notifications to in the email address field.": "Template email pemberitahuan admin. Anda dapat menggunakan teks, HTML, dan bidang gabungan berikut dan banyak lagi: {carts}. Masukkan email yang ingin Anda kirimi pemberitahuan di bidang alamat email.", "Template of the email sent to the customer after a product has been removed from the cart. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "Template email yang dikirim ke pelanggan setelah produk dikeluarkan dari keranjang. Anda dapat menggunakan teks, HTML, dan bidang gabungan berikut dan banyak lagi: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the first notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "Template email pemberitahuan pertama. Anda dapat menggunakan teks, HTML, dan bidang gabungan berikut dan banyak lagi: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the second notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "Template email pemberitahuan kedua. Anda dapat menggunakan teks, HTML, dan bidang gabungan berikut dan banyak lagi: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the waiting list notification email. You can use text, HTML, and the following merge field and more: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}.": "Template email pemberitahuan daftar tunggu. Anda dapat menggunakan teks, HTML, dan bidang gabungan berikut dan banyak lagi: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}.", "Terms link": "Tautan <PERSON>", "Tertiary color": "<PERSON><PERSON>", "Test Slack": "<PERSON><PERSON>", "Test template": "<PERSON><PERSON><PERSON>", "Text": "Teks", "Text message fallback": "Penggantian pesan teks", "Text message notifications": "Pemberitahuan pesan teks", "Text messages": "Pesan teks", "The product is not in the cart.": "Produk tidak ada di keranjang.", "The workspace name you are using to synchronize Slack.": "Nama workspace yang Anda gunakan untuk menyinkronkan Slack.", "This is your main Slack channel ID, which is usually the #general channel. You will get this code by completing the Slack synchronization.": "Ini adalah ID saluran Slack u<PERSON>a <PERSON>, yang biasanya merupakan saluran #general. <PERSON><PERSON> akan mendapatkan kode ini dengan menyelesaikan sinkronisasi Slack.", "This returns the Support Board path of your server.": "<PERSON><PERSON> men<PERSON>mbalikan jalur Support Board dari server <PERSON><PERSON>.", "Ticket custom fields": "<PERSON><PERSON>ng khus<PERSON> tiket", "Ticket email": "<PERSON><PERSON> tiket", "Ticket field names": "<PERSON><PERSON> bidang tiket", "Ticket fields": "Bidang tiket", "Ticket only": "<PERSON><PERSON> tiket", "Ticket products selector": "<PERSON><PERSON><PERSON><PERSON> produk tiket", "Title": "<PERSON><PERSON><PERSON>", "Top": "Atas", "Top bar": "<PERSON><PERSON><PERSON> atas", "Training via cron job": "<PERSON><PERSON><PERSON><PERSON> melalui p<PERSON> cron", "Transcript": "Salinan", "Transcript settings.": "Pengaturan transkrip.", "Trigger": "<PERSON><PERSON><PERSON><PERSON>", "Trigger the Dialogflow Welcome Intent for new visitors when the welcome message is active.": "Picu Dialogflow Welcome Intent untuk pengunjung baru saat pesan selamat datang aktif.", "Troubleshoot": "Memecahkan masalah", "Troubleshoot problems": "Memecahkan masalah", "Twilio settings": "<PERSON><PERSON><PERSON><PERSON>", "Twilio template": "<PERSON><PERSON><PERSON>", "Unsubscribe": "<PERSON><PERSON><PERSON><PERSON>", "Upload attachments to Amazon S3.": "<PERSON><PERSON><PERSON>n ke Amazon S3.", "Usage Limit": "<PERSON><PERSON>", "Use this option to change the PWA icon. See the docs for more details.": "Gunakan opsi ini untuk mengubah ikon PWA. Lihat dokumen untuk lebih jelasnya.", "User details": "Detail pengguna", "User details in success message": "Detail pengguna dalam pesan sukses", "User email notifications": "Notifikasi email pengguna", "User login form information.": "Informasi formulir login pengguna.", "User message template": "Templat pesan pengguna", "User name as title": "<PERSON><PERSON> pen<PERSON>una sebagai judul", "User notification email": "Email pemberitahuan pengguna", "User registration form information.": "Informasi formulir pendaftaran pengguna.", "User roles": "<PERSON><PERSON>", "User system": "Sistem pengguna", "Username": "<PERSON><PERSON>", "Users and agents": "<PERSON><PERSON><PERSON> dan agen", "Users area": "Area pengguna", "Users only": "<PERSON><PERSON>", "Users table additional columns": "<PERSON><PERSON><PERSON> tambahan tabel pengguna", "UTC offset": "Offset UTC", "Variables": "Variabel", "View channels": "<PERSON><PERSON>", "View unassigned conversations": "<PERSON>hat percakapan yang belum ditetapkan", "Visibility": "Visibilitas", "Visitor default name": "Nama default pengunjung", "Visitor name prefix": "<PERSON><PERSON><PERSON> nama <PERSON>g", "Volume": "Volume", "Volume - admin": "Volume - admin", "Waiting list": "<PERSON><PERSON><PERSON> tunggu", "Waiting list - Email": "Daftar tunggu - Email", "Webhook URL": "URL webhook", "Webhooks": "Webhook", "Webhooks are information sent in background to a unique URL defined by you when something happens.": "Webhook adalah informasi yang dikirim di latar belakang ke URL unik yang Anda tentukan ketika sesuatu terjadi.", "Website": "Situs web", "WeChat settings": "Pengaturan WeChat", "Welcome message": "Pesan selamat datang", "Whmcs admin URL": "URL admin Whmcs", "Whmcs admin URL. Ex. https://example.com/whmcs/admin/": "URL admin Whmcs. Mantan. https://example.com/whmcs/admin/", "WordPress registration": "Pendaftaran WordPress", "Yes, we ship in": "<PERSON>, kami mengirim", "You haven't placed an order yet.": "Anda belum melakukan pemesanan.", "You will get this code by completing the Dialogflow synchronization.": "Anda akan mendapatkan kode ini dengan menyelesaikan sinkronisasi Dialogflow.", "You will get this code by completing the Slack synchronization.": "<PERSON>a akan mendapatkan kode ini dengan menyelesaikan sinkronisasi S<PERSON>ck.", "You will get this information by completing the synchronization.": "<PERSON>a akan mendapatkan informasi ini dengan menyelesaikan sinkronisasi.", "Your cart is empty.": "Keranjang Anda kosong.", "Your turn message": "<PERSON><PERSON> g<PERSON>", "Your username": "<PERSON><PERSON>", "Your WhatsApp catalogue details.": "Detail katalog WhatsApp Anda.", "Zendesk settings": "Pengaturan Zendesk", "Smart Reply": "<PERSON><PERSON><PERSON>", "This returns your Support Board URL.": "Ini mengembalikan URL Support Board Anda.", "Update conversation department": "<PERSON><PERSON><PERSON> departemen per<PERSON>"}
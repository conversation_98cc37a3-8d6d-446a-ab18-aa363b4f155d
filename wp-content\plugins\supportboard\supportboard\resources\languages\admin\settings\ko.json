{"{product_name} has no {product_attribute_name} variants.": "{product_name}에는 {product_attribute_name} 변형이 없습니다.", "360dialog settings": "360도 대화 설정", "360dialog template": "360대화 템플릿", "Abandoned cart notification": "버려진 장바구니 알림", "Abandoned cart notification - Admin email": "버려진 장바구니 알림 - 관리자 이메일", "Abandoned cart notification - First email": "버려진 장바구니 알림 - 첫 번째 이메일", "Abandoned cart notification - Second email": "버려진 장바구니 알림 - 두 번째 이메일", "Accept button text": "버튼 텍스트 수락", "Account SID": "계정 SID", "Activate the Right-To-Left (RTL) reading layout for the admin area.": "관리 영역에 대한 RTL(오른쪽에서 왼쪽) 읽기 레이아웃을 활성화합니다.", "Activate the Right-To-Left (RTL) reading layout.": "RTL(오른쪽에서 왼쪽) 읽기 레이아웃을 활성화합니다.", "Activate the Slack integration.": "Slack 통합을 활성화합니다.", "Activate the Zendesk integration": "Zendesk 통합 활성화", "Activate this option if you don't want to translate the settings area.": "설정 영역을 번역하지 않으려면 이 옵션을 활성화하세요.", "Active": "활동적인", "Active - admin": "활성 - 관리자", "Active eCommerce CMS URL. Ex. https://shop.com/": "Active eCommerce CMS URL. 전. https://shop.com/", "Active eCommerce URL": "Active eCommerce URL", "Active for agents": "에이전트에 대해 활성", "Active for users": "사용자에 대해 활성", "Active webhooks": "활성 웹훅", "Add a delay (ms) to the bot's responses. Default is 2000.": "봇의 응답에 지연(ms)을 추가합니다. 기본값은 2000입니다.", "Add and manage additional support departments.": "추가 지원 부서를 추가하고 관리합니다.", "Add and manage saved replies that can be used by agents in the chat editor. Saved replies can be printed by typing # followed by the reply name plus space. Use \\n to do a line break.": "채팅 편집기에서 상담원이 사용할 수 있는 저장된 답장을 추가하고 관리합니다. 저장된 답글은 # 뒤에 답글 이름과 공백을 입력하여 인쇄할 수 있습니다. 줄 바꿈을 하려면 \\n을 사용하십시오.", "Add and manage tags.": "태그를 추가하고 관리하세요.", "Add comma separated WordPress user roles. The Support Board administration area will be available for new roles, in addition to the default one: editor, administrator, author.": "쉼표로 구분된 WordPress 사용자 역할을 추가합니다. 지원 보드 관리 영역은 기본 역할인 편집자, 관리자, 작성자 외에 새로운 역할에 사용할 수 있습니다.", "Add custom fields to the new ticket form.": "새 티켓 양식에 사용자 정의 필드를 추가하십시오.", "Add custom fields to the user profile details.": "사용자 프로필 세부 정보에 사용자 정의 필드를 추가합니다.", "Add Intents": "Intents 추가", "Add Intents to saved replies": "저장된 답장에 Intents 추가", "Add WhatsApp phone number details here.": "여기에 WhatsApp 전화번호 세부정보를 추가하세요.", "Adjust the chat button position. Values are in px.": "채팅 버튼 위치를 조정합니다. 값은 px 단위입니다.", "Admin icon": "관리자 아이콘", "Admin IDs": "관리자 ID", "Admin login logo": "관리자 로그인 로고", "Admin login message": "관리자 로그인 메시지", "Admin notifications": "관리자 알림", "Admin title": "관리자 직함", "Agent area": "에이전트 영역", "Agent details": "에이전트 세부정보", "Agent email notifications": "에이전트 이메일 알림", "Agent ID": "에이전트 ID", "Agent linking": "에이전트 연결", "Agent message template": "에이전트 메시지 템플릿", "Agent notification email": "에이전트 알림 이메일", "Agent privileges": "에이전트 권한", "Agents": "자치령 대표", "Agents and admins tab": "상담원 및 관리자 탭", "Agents menu": "에이전트 메뉴", "Agents only": "에이전트 전용", "All": "모두", "All channels": "모든 채널", "All messages": "모든 메시지", "All questions": "모든 질문", "Allow only extended licenses": "확장 라이선스만 허용", "Allow only one conversation": "하나의 대화만 허용", "Allow only one conversation per user.": "사용자당 하나의 대화만 허용합니다.", "Allow the chatbot to reply to the user's emails if the answer is known and email piping is active.": "답을 알고 이메일 파이핑이 활성화된 경우 챗봇이 사용자의 이메일에 답장하도록 허용합니다.", "Allow the chatbot to reply to the user's text messages if the answer is known.": "답을 알고 있는 경우 챗봇이 사용자의 문자 메시지에 답장하도록 허용합니다.", "Allow the user to archive a conversation and hide archived conversations.": "사용자가 대화를 보관처리하고 보관된 대화를 숨길 수 있도록 허용합니다.", "Allow users to contact you via their favorite messaging apps.": "사용자가 즐겨 사용하는 메시징 앱을 통해 연락할 수 있습니다.", "Allow users to select a product on ticket creation.": "사용자가 티켓 생성 시 제품을 선택할 수 있도록 합니다.", "Always all messages": "항상 모든 메시지", "Always incoming messages only": "항상 들어오는 메시지만", "Always sort conversations by date in the admin area.": "항상 관리 영역에서 날짜별로 대화를 정렬하십시오.", "API key": "API 키", "Append the registration user details to the success message.": "등록 사용자 세부 정보를 성공 메시지에 추가합니다.", "Apply a custom background image for the header area.": "헤더 영역에 사용자 정의 배경 이미지를 적용합니다.", "Apply changes": "변경 승인", "Apply to": "적용하다", "Archive all user channels in the Slack app. This operation may take a long time to complete. Important: All of your slack channels will be archived.": "Slack 앱에서 모든 사용자 채널을 보관합니다. 이 작업을 완료하는 데 시간이 오래 걸릴 수 있습니다. 중요: 모든 slack 채널이 보관됩니다.", "Archive automatically the conversations marked as read every 24h.": "24시간마다 읽은 것으로 표시된 대화를 자동으로 보관처리합니다.", "Archive channels": "아카이브 채널", "Archive channels now": "지금 채널 보관", "Articles": "조항", "Articles area": "기사 영역", "Articles button link": "기사 버튼 링크", "Articles page URL": "기사 페이지 URL", "Artificial Intelligence": "인공 지능", "Assign a department to all conversations started from Google Business Messages. Enter the department ID.": "Google Business Messages에서 시작된 모든 대화에 부서를 지정하세요. 부서 ID를 입력하세요.", "Assign a department to all conversations started from Twitter. Enter the department ID.": "Twitter에서 시작된 모든 대화에 부서를 할당합니다. 부서 ID를 입력하세요.", "Assign a department to all conversations started from Viber. Enter the department ID.": "Viber에서 시작된 모든 대화에 부서를 지정하세요. 부서 ID를 입력하세요.", "Assign a department to all conversations started from WeChat. Enter the department ID.": "WeChat에서 시작된 모든 대화에 부서를 지정합니다. 부서 ID를 입력하세요.", "Assign different departments to conversations started from different Google Business Messages locations. This setting overrides the default department.": "서로 다른 Google Business Messages 위치에서 시작된 대화에 서로 다른 부서를 할당합니다. 이 설정은 기본 부서보다 우선합니다.", "Assistant": "어시스턴트", "Assistant ID": "어시스턴트 ID", "Attachments list": "첨부파일 목록", "Audio file URL - admin": "오디오 파일 URL - 관리자", "Automatic": "자동적 인", "Automatic human takeover": "자동 인적 인수", "Automatic translation": "자동 번역", "Automatic updates": "자동 업데이트", "Automatically archive conversations": "자동으로 대화 보관", "Automatically assigns a department based on the user's active plans. Insert -1 as plan ID for users without any plan.": "사용자의 활성 계획을 기반으로 부서를 자동으로 할당합니다. 계획이 없는 사용자의 경우 계획 ID로 -1을 삽입합니다.", "Automatically check and install new updates. A valid Envato Purchase Code and valid apps's license keys are required.": "새 업데이트를 자동으로 확인하고 설치합니다. 유효한 Envato 구매 코드와 유효한 앱의 라이선스 키가 필요합니다.", "Automatically collapse the conversation details panel, and other panels, of the admin area.": "관리 영역의 대화 세부정보 패널과 기타 패널을 자동으로 축소합니다.", "Automatically create a department for each website and route the conversations of each website to the right department. This setting requires a WordPress Multisite installation.": "각 웹사이트에 대한 부서를 자동으로 생성하고 각 웹사이트의 대화를 적절한 부서로 라우팅합니다. 이 설정은 WordPress Multisite 설치가 필요합니다.", "Automatically hide the conversation details panel.": "대화 세부정보 패널을 자동으로 숨깁니다.", "Automatically send cart reminders to customers with products in their carts. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "장바구니에 제품이 있는 고객에게 장바구니 알림을 자동으로 보냅니다. 다음 병합 필드 등을 사용할 수 있습니다: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.", "Automatically sync Zendesk customers with {R}, view Zendesk tickets, or create new ones without leaving {R}.": "{R}을(를) 떠나 Zendesk 고객을 자동으로 동기화하고, Zendesk 티켓을 보거나, {R}을(를) 떠나지 않고도 새 티켓을 만드세요.", "Automatically synchronize products, categories, tags, and more with Dialogflow, and enable the bot to answer autonomously to questions related to your shop.": "제품, 카테고리, 태그 등을 Dialogflow와 자동으로 동기화하고 봇이 매장과 관련된 질문에 자율적으로 답변할 수 있도록 합니다.", "Automatically translate admin area": "관리 영역 자동 번역", "Automatically translate the admin area to match the agent profile language or browser language.": "에이전트 프로필 언어 또는 브라우저 언어와 일치하도록 관리 영역을 자동으로 번역합니다.", "Avatar image": "아바타 이미지", "Away mode": "자리 비움 모드", "Before initiating the chat, the user must accept a privacy message in order to gain access.": "채팅을 시작하기 전에 사용자는 액세스 권한을 얻기 위해 개인 정보 보호 메시지를 수락해야 합니다.", "Birthday": "생신", "Body variables": "신체 변수", "Bot name": "봇 이름", "Bot profile image": "봇 프로필 이미지", "Bot response delay": "봇 응답 지연", "Bottom": "맨 아래", "Brand": "상표", "Built-in chat button icons": "내장 채팅 버튼 아이콘", "Business Account ID": "비즈니스 계정 ID", "Button action": "버튼 동작", "Button name": "버튼 이름", "Button text": "버튼 텍스트", "Button variables": "버튼 변수", "Cancel button text": "취소 버튼 텍스트", "Cart": "카트", "Cart follow up message": "장바구니 후속 메시지", "Catalogue details": "카탈로그 세부정보", "Catalogue ID": "카탈로그 ID", "Change the chat button image with a custom one.": "채팅 버튼 이미지를 사용자 지정 이미지로 변경합니다.", "Change the default field names.": "기본 필드 이름을 변경합니다.", "Change the message text in the header area of the chat widget. This text will be replaced by the agent headline once the first reply is sent.": "채팅 위젯의 헤더 영역에서 메시지 텍스트를 변경합니다. 이 텍스트는 첫 번째 회신이 전송되면 상담원 헤드라인으로 대체됩니다.", "Change the title text in the header area of the chat widget. This text will be replaced by the agent's name once the first reply is sent.": "채팅 위젯의 헤더 영역에서 제목 텍스트를 변경합니다. 이 텍스트는 첫 번째 응답이 전송되면 상담원의 이름으로 대체됩니다.", "Channel ID": "채널 ID", "Channels": "채널", "Channels filter": "채널 필터", "Chat": "채팅", "Chat and admin": "채팅 및 관리자", "Chat background": "채팅 배경", "Chat button icon": "채팅 버튼 아이콘", "Chat button offset": "채팅 버튼 오프셋", "Chat message": "채팅 메시지", "Chat only": "채팅만", "Chat position": "채팅 위치", "Chatbot": "챗봇", "Chatbot mode": "챗봇 모드", "Check Requirements": "요구 사항 확인", "Check the server configurations and make sure it has all the requirements.": "서버 구성을 확인하고 모든 요구 사항이 있는지 확인하십시오.", "Checkout": "점검", "Choose a background texture for the chat header and conversation area.": "채팅 헤더 및 대화 영역의 배경 텍스처를 선택합니다.", "Choose where to display the chat. Enter the values separated by commas.": "채팅을 표시할 위치를 선택합니다. 쉼표로 구분된 값을 입력합니다.", "Choose which fields to disable from the tickets area.": "티켓 영역에서 비활성화할 필드를 선택하십시오.", "Choose which fields to include in the new ticket form.": "새 티켓 양식에 포함할 필드를 선택하십시오.", "Choose which fields to include in the registration form. The name field is included by default.": "등록 양식에 포함할 필드를 선택하세요. 이름 필드는 기본적으로 포함됩니다.", "Choose which user system the front-end chat will use to register and log in users.": "프런트 엔드 채팅에서 사용자를 등록하고 로그인하는 데 사용할 사용자 시스템을 선택합니다.", "City": "도시", "Clear flows": "명확한 흐름", "Click the button to start the Dialogflow synchronization.": "버튼을 클릭하여 Dialogflow 동기화를 시작합니다.", "Click the button to start the Slack synchronization. Localhost cannot and does not receive messages. Log in with another account or as a visitor to perform your tests.": "버튼을 클릭하여 Slack 동기화를 시작합니다. Localhost는 메시지를 수신할 수 없으며 수신하지 않습니다. 다른 계정으로 로그인하거나 방문자로 로그인하여 테스트를 수행하십시오.", "Client email": "클라이언트 이메일", "Client ID": "클라이언트 ID", "Client token": "클라이언트 토큰", "Close chat": "채팅 닫기", "Close message": "메시지 닫기", "Cloud API numbers": "클라우드 API 번호", "Cloud API settings": "클라우드 API 설정", "Cloud API template fallback": "Cloud API 템플릿 대체", "Code": "암호", "Collapse panels": "패널 축소", "Color": "색상", "Communicate with your users right from Slack. Send and receive messages and attachments, use emojis, and much more.": "Slack에서 바로 사용자와 소통하세요. 메시지와 첨부 파일을 주고받고, 이모티콘을 사용하는 등의 작업을 할 수 있습니다.", "Company": "회사", "Concurrent chats": "경쟁 고양이", "Configuration URL": "구성 URL", "Confirm button text": "버튼 텍스트 확인", "Confirmation message": "확인 메시지", "Connect smart chatbots and automate conversations by using one of the most advanced forms of artificial intelligence in the world.": "세계에서 가장 발전된 형태의 인공 지능 중 하나를 사용하여 스마트 챗봇을 연결하고 대화를 자동화하세요.", "Connect stores to agents.": "매장을 에이전트에 연결합니다.", "Connect your Telegram bot to {R} to read and reply to all messages sent to your Telegram bot directly in {R}.": "Telegram 봇을 {R}에 연결하여 {R}에서 직접 Telegram 봇으로 보낸 모든 메시지를 읽고 답장하세요.", "Connect your Viber bot to {R} to read and reply to all messages sent to your Viber bot directly in {R}.": "Viber 봇을 {R}에 연결하여 Viber 봇에 전송된 모든 메시지를 {R}에서 직접 읽고 답장하세요.", "Connect your Zalo Official Account to {R} to read and reply to all messages sent to your Zalo Official Account directly in {R}.": "Zalo 공식 계정을 {R}에 연결하면 {R}에서 Zalo 공식 계정으로 전송된 모든 메시지를 직접 읽고 답장할 수 있습니다.", "Content": "콘텐츠", "Content template SID": "콘텐츠 템플릿 SID", "Conversation profile": "대화 프로필", "Conversations data": "대화 데이터", "Convert all emails": "모든 이메일 변환", "Cookie domain": "쿠키 도메인", "Country": "국가", "Coupon discount (%)": "쿠폰 할인(%)", "Coupon expiration (days)": "쿠폰 만료(일)", "Coupon expiration (seconds)": "쿠폰 만료(초)", "Create a WordPress user upon registration.": "등록 시 WordPress 사용자를 만듭니다.", "Create Intents now": "지금 Intents 만들기", "Currency symbol": "통화 기호", "Custom CSS": "맞춤 CSS", "Custom fields": "사용자 정의 필드", "Custom JS": "커스텀 JS", "Custom model ID": "맞춤 모델 ID", "Custom parameters": "맞춤 매개변수", "Customize the link for the 'All articles' button.": "&#39;모든 기사&#39; 버튼의 링크를 맞춤설정하세요.", "Dashboard display": "대시보드 표시", "Dashboard title": "대시보드 제목", "Database details": "데이터베이스 세부정보", "Database host": "데이터베이스 호스트", "Database name": "데이터베이스 이름", "Database password": "데이터베이스 비밀번호", "Database prefix": "데이터베이스 접두사", "Database user": "데이터베이스 사용자", "Decline button text": "거절 버튼 텍스트", "Declined message": "거부된 메시지", "Default": "기본", "Default body text": "기본 본문 텍스트", "Default conversation name": "기본 대화 이름", "Default department": "기본 부서", "Default department ID": "기본 부서 ID", "Default form": "기본 양식", "Default header text": "기본 헤더 텍스트", "Delay (ms)": "지연(ms)", "Delete all leads and all messages and conversations linked to them.": "모든 리드와 연결된 모든 메시지 및 대화를 삭제합니다.", "Delete conversation": "대화 삭제", "Delete leads": "리드 삭제", "Delete message": "메시지 삭제", "Delete the built-in flows.": "기본 제공 흐름을 삭제합니다.", "Delimiter": "구분 기호", "Department": "학과", "Department ID": "부서 ID", "Departments": "부서", "Departments settings": "부서 설정", "Desktop notifications": "데스크탑 알림", "Dialogflow - Department linking": "Dialogflow - 부서 연결", "Dialogflow chatbot": "Dialogflow 챗봇", "Dialogflow edition": "Dialogflow 버전", "Dialogflow Intent detection confidence": "Dialogflow 인텐트 감지 신뢰도", "Dialogflow location": "Dialogflow 위치", "Dialogflow spelling correction": "Dialogflow 맞춤법 수정", "Dialogflow welcome Intent": "Dialogflow 환영 인텐트", "Disable agents check": "에이전트 확인 비활성화", "Disable and hide the chat widget if all agents are offline.": "모든 상담원이 오프라인인 경우 채팅 위젯을 비활성화하고 숨깁니다.", "Disable and hide the chat widget outside of scheduled office hours.": "예약된 근무 시간 외에 채팅 위젯을 비활성화하고 숨깁니다.", "Disable any features that you don't need.": "필요하지 않은 기능을 비활성화하십시오.", "Disable auto-initialization of the chat widget. When this setting is active you must initialize the chat widget with a custom JavaScript API code written by you. If the chat doesn't appear and this setting is enabled, disable it.": "채팅 위젯의 자동 초기화를 비활성화합니다. 이 설정이 활성화되면 사용자가 작성한 사용자 정의 JavaScript API 코드로 채팅 위젯을 초기화해야 합니다. 채팅이 나타나지 않고 이 설정이 활성화되어 있으면 비활성화하세요.", "Disable auto-initialization of the tickets area. When this setting is active you must initialize the tickets area with a custom JavaScript API code written by you. If the tickets area doesn't appear and this setting is enabled, disable it.": "티켓 영역의 자동 초기화를 비활성화합니다. 이 설정이 활성화되면 사용자가 작성한 사용자 정의 JavaScript API 코드로 티켓 영역을 초기화해야 합니다. 티켓 영역이 나타나지 않고 이 설정이 활성화된 경우 비활성화합니다.", "Disable chatbot": "챗봇 비활성화", "Disable cron job": "크론 작업 비활성화", "Disable dashboard": "대시보드 비활성화", "Disable during office hours": "근무 시간 동안 비활성화", "Disable features": "기능 비활성화", "Disable features you don't use and improve the chat performance.": "사용하지 않는 기능을 비활성화하고 채팅 성능을 개선하세요.", "Disable file uploading capabilities within the chat.": "채팅 내에서 파일 업로드 기능을 비활성화합니다.", "Disable for messaging channels": "메시징 채널에 대해 비활성화", "Disable for the tickets area": "티켓 영역에 대해 비활성화", "Disable invitation": "초대 비활성화", "Disable online status check": "온라인 상태 확인 비활성화", "Disable outside of office hours": "근무 시간 외 사용 중지", "Disable password": "비밀번호 비활성화", "Disable registration during office hours": "근무 시간 동안 등록 비활성화", "Disable registration if agents online": "상담원이 온라인인 경우 등록 비활성화", "Disable the automatic invitation of agents to the channels.": "채널에 에이전트 자동 초대를 비활성화합니다.", "Disable the channels filter.": "채널 필터를 비활성화합니다.", "Disable the chatbot for the tickets area.": "티켓 영역의 챗봇을 비활성화합니다.", "Disable the chatbot for this channel only.": "이 채널에 대해서만 챗봇을 비활성화합니다.", "Disable the dashboard, and allow only one conversation per user.": "대시보드를 비활성화하고 사용자당 하나의 대화만 허용합니다.", "Disable the login and remove the password field from the registration form.": "로그인을 비활성화하고 등록 양식에서 비밀번호 필드를 제거하십시오.", "Disable uploads": "업로드 비활성화", "Disable voice message capabilities within the chat.": "채팅 내에서 음성 메시지 기능을 비활성화합니다.", "Disable voice messages": "음성 메시지 비활성화", "Disabled": "장애가있는", "Display a brand image in the header area. This only applies for the 'brand' header type.": "헤더 영역에 브랜드 이미지를 표시합니다. 이는 &#39;브랜드&#39; 헤더 유형에만 적용됩니다.", "Display categories": "디스플레이 카테고리", "Display images": "이미지 표시", "Display in conversation list": "대화 목록에 표시", "Display in dashboard": "대시보드에 표시", "Display online agents only": "온라인 상담원만 표시", "Display the articles section in the right area.": "오른쪽 영역에 기사 섹션을 표시합니다.", "Display the dashboard instead of the chat area on initialization.": "초기화 시 채팅 영역 대신 대시보드를 표시합니다.", "Display the feedback form to rate the conversation when it is archived.": "대화가 보관되면 피드백 양식을 표시하여 대화를 평가합니다.", "Display the user full name in the left panel instead of the conversation title.": "대화 제목 대신 왼쪽 패널에 사용자 전체 이름을 표시합니다.", "Display the user's profile image within the chat.": "채팅 내에서 사용자의 프로필 이미지를 표시합니다.", "Display user name in header": "헤더에 사용자 이름 표시", "Display user's profile image": "사용자 프로필 이미지 표시", "Displays additional columns in the user table. Enter the name of the fields to add.": "사용자 테이블에 추가 열을 표시합니다. 추가할 필드의 이름을 입력합니다.", "Distribute conversations proportionately between agents and notify visitors of their position within the queue. Response time is in minutes. You can use the following merge fields in the message: {position}, {minutes}. They will be replaced by the real values in real-time.": "상담원 간에 비례적으로 대화를 배포하고 방문자에게 대기열 내 위치를 알립니다. 응답 시간은 분 단위입니다. 메시지에서 {position}, {minutes} 병합 필드를 사용할 수 있습니다. 실시간으로 실제 값으로 대체됩니다.", "Distribute conversations proportionately between agents, and block an agent from viewing the conversations of the other agents.": "에이전트 간에 대화를 비례적으로 분배하고 에이전트가 다른 에이전트의 대화를 볼 수 없도록 차단합니다.", "Do not send email notifications to admins": "관리자에게 이메일 알림을 보내지 마세요.", "Do not show tickets in chat": "채팅에 티켓을 표시하지 않음", "Do not translate settings area": "설정 영역을 번역하지 않음", "Download": "다운로드", "Edit profile": "프로필 편집", "Edit user": "사용자 수정", "Email address": "이메일 주소", "Email and ticket": "이메일 및 티켓", "Email header": "이메일 헤더", "Email notification delay (hours)": "이메일 알림 지연(시간)", "Email notifications via cron job": "크론 작업을 통한 이메일 알림", "Email only": "이메일만", "Email piping": "이메일 파이핑", "Email piping server information and more settings.": "파이핑 서버 정보 및 추가 설정을 이메일로 보냅니다.", "Email request message": "이메일 요청 메시지", "Email signature": "이메일 서명", "Email template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "에이전트가 답변할 때 사용자에게 전송되는 이메일용 이메일 템플릿입니다. 텍스트, HTML 및 다음 병합 필드를 사용할 수 있습니다: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Email template for the email sent to an agent when a user sends a new message. You can use text, HTML, and the following merge fields: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "사용자가 새 메시지를 보낼 때 에이전트에게 보내는 이메일용 이메일 템플릿입니다. 텍스트, HTML 및 다음 병합 필드를 사용할 수 있습니다: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Email template for the email sent to the user after submitting their email through the follow-up message form. You can use text, HTML, and the following merge fields: {user_name}, {user_email}.": "팔로업 메시지 양식을 통해 이메일을 제출한 후 사용자에게 전송되는 이메일용 이메일 템플릿입니다. 텍스트, HTML 및 다음 병합 필드를 사용할 수 있습니다: {user_name}, {user_email}.", "Email template for the email sent to the user to verify their email address. Include the {code} merge field within your content, it will be replaced with the one-time code.": "사용자의 이메일 주소를 확인하기 위해 사용자에게 전송된 이메일용 이메일 템플릿입니다. 콘텐츠 내에 {code} 병합 필드를 포함하면 일회용 코드로 대체됩니다.", "Email verification": "이메일 확인", "Email verification content": "이메일 확인 내용", "Enable email verification with OTP.": "OTP를 사용하여 이메일 인증을 활성화하세요.", "Enable logging of agent activity": "에이전트 활동 로깅 활성화", "Enable logs": "로그 사용", "Enable the chatbot outside of scheduled office hours only.": "예약된 근무 시간 외에만 챗봇을 활성화합니다.", "Enable the registration only if all agents are offline.": "모든 에이전트가 오프라인인 경우에만 등록을 활성화하십시오.", "Enable the registration outside of scheduled office hours only.": "예약된 근무 시간 외에만 등록을 활성화합니다.", "Enable this option if email notifications are sent via cron job.": "이메일 알림이 cron 작업을 통해 전송되는 경우 이 옵션을 활성화합니다.", "Enable ticket and chat support for subscribers only, view member profile details and subscription details in the admin area.": "구독자에 대해서만 티켓 및 채팅 지원을 활성화하고 관리 영역에서 회원 프로필 세부 정보 및 구독 세부 정보를 봅니다.", "Enter the bot token and click the button to synchronize the Telegram bot. Localhost cannot receive messages.": "봇 토큰을 입력하고 버튼을 클릭하여 텔레그램 봇을 동기화합니다. Localhost는 메시지를 받을 수 없습니다.", "Enter the bot token and click the button to synchronize the Viber bot. Localhost cannot receive messages.": "봇 토큰을 입력하고 버튼을 클릭하면 Viber 봇이 동기화됩니다. 로컬 호스트가 메시지를 받을 수 없습니다.", "Enter the database details of the Active eCommerce CMS database.": "Active eCommerce CMS 데이터베이스의 데이터베이스 세부 정보를 입력합니다.", "Enter the database details of the Martfury database.": "Martfury 데이터베이스의 데이터베이스 세부 정보를 입력합니다.", "Enter the database details of the Perfex database.": "Perfex 데이터베이스의 데이터베이스 세부 정보를 입력합니다.", "Enter the database details of the WHMCS database.": "WHMCS 데이터베이스의 데이터베이스 세부 정보를 입력합니다.", "Enter the default messages used by the chatbot when user question requires a dynamic answer.": "사용자 질문에 동적 답변이 필요한 경우 챗봇이 사용하는 기본 메시지를 입력합니다.", "Enter the details of your Google Business Messages.": "Google 비즈니스 메시지의 세부정보를 입력합니다.", "Enter the details of your Twitter app.": "Twitter 앱의 세부 정보를 입력합니다.", "Enter the LINE details to start using it. Localhost cannot receive messages.": "사용을 시작하려면 LINE 세부 정보를 입력하세요. Localhost는 메시지를 받을 수 없습니다.", "Enter the URL of a .css file, to load it automatically in the admin area.": ".css 파일의 URL을 입력하면 관리 영역에 자동으로 로드됩니다.", "Enter the URL of a .js file, to load it automatically in the admin area.": ".js 파일의 URL을 입력하면 관리 영역에 자동으로 로드됩니다.", "Enter the URL of the articles page.": "기사 페이지의 URL을 입력하세요.", "Enter the URLs of your shop": "상점의 URL을 입력하세요.", "Enter the WeChat official account token. See the docs for more details.": "WeChat 공식 계정 토큰을 입력하세요. 자세한 내용은 문서를 참조하세요.", "Enter the Zalo details to start using it. Localhost cannot receive messages.": "Zalo 세부 정보를 입력하여 사용을 시작하세요. 로컬호스트는 메시지를 수신할 수 없습니다.", "Enter your 360dialog account settings information.": "360dialog 계정 설정 정보를 입력하세요.", "Enter your Envato Purchase Code to activate automatic updates and unlock all the features.": "Envato 구매 코드를 입력하여 자동 업데이트를 활성화하고 모든 기능을 잠금 해제하십시오.", "Enter your Twilio account details. You can use text and the following merge fields: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.": "<PERSON><PERSON><PERSON> 계정 세부 정보를 입력합니다. 텍스트 및 다음 병합 필드를 사용할 수 있습니다: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.", "Enter your Twilio account settings information.": "<PERSON><PERSON><PERSON> 계정 설정 정보를 입력합니다.", "Enter your WeChat Official Account information.": "WeChat 공식 계정 정보를 입력합니다.", "Enter your Zendesk information.": "Zendesk 정보를 입력합니다.", "Entities": "Entities", "Envato Purchase Code": "Envato 구매 코드", "Envato purchase code validation": "Envato 구매 코드 확인", "Exclude products": "제품 제외", "Export all settings.": "모든 설정을 내보냅니다.", "Export settings": "내보내기 설정", "Facebook pages": "페이스북 페이지", "Fallback message": "대체 메시지", "Filters": "필터", "First chat message": "첫 채팅 메시지", "First reminder delay (hours)": "첫 번째 알림 지연(시간)", "First ticket form": "첫 번째 티켓 양식", "Flash notifications": "플래시 알림", "Follow up - Email": "후속 조치 - 이메일", "Follow up email": "후속 이메일", "Follow up message": "후속 메시지", "Follows a conversation between a human agent and an end user and provide response suggestions to the human agent in real-time.": "상담원과 최종 사용자 간의 대화를 추적하고 상담원에게 실시간으로 응답 제안을 제공합니다.", "Follow-up email template. You can use text, HTML, and the following merge fields and more: {coupon}, {product_names}, {user_name}.": "후속 이메일 템플릿. 텍스트, HTML 및 {coupon}, {product_names}, {user_name} 병합 필드 등을 사용할 수 있습니다.", "Force language": "강제 언어", "Force log out": "강제 로그아웃", "Force the chat to ignore the language preferences, and to use always the same language.": "채팅에서 언어 기본 설정을 무시하고 항상 동일한 언어를 사용하도록 합니다.", "Force the loggout of Support Board agents if they are not logged in WordPress.": "Support Board 에이전트가 WordPress에 로그인하지 않은 경우 강제로 로그아웃합니다.", "Force users to use a different conversation for each store and hide conversations from other stores from store administrators.": "사용자가 각 상점에 대해 다른 대화를 사용하도록 강제하고 상점 관리자로부터 다른 상점의 대화를 숨깁니다.", "Force users to use only one phone country code.": "사용자가 하나의 전화 국가 코드만 사용하도록 합니다.", "Form message": "양식 메시지", "Form title": "양식 제목", "Frequency penalty": "빈도 페널티", "Full visitor details": "전체 방문자 세부정보", "Function name": "함수 이름", "Generate conversations data": "대화 데이터 생성", "Generate user questions": "사용자 질문 생성", "Get configuration URL": "구성 URL 가져오기", "Get it from the APP_KEY value of the file .env located in the root directory of Active eCommerce.": "Active eCommerce의 루트 디렉터리에 있는 .env 파일의 APP_KEY 값에서 가져옵니다.", "Get it from the APP_KEY value of the file .env located in the root directory of Martfury.": "Martfury의 루트 디렉터리에 있는 .env 파일의 APP_KEY 값에서 가져옵니다.", "Get Path": "경로 가져오기", "Get Service Worker path": "Service Worker 경로 가져오기", "Get URL": "URL 가져오기", "Google and Dialogflow settings.": "Google 및 Dialogflow 설정.", "Google search": "구글 검색", "Header": "헤더", "Header background image": "헤더 배경 이미지", "Header brand image": "헤더 브랜드 이미지", "Header message": "헤더 메시지", "Header title": "헤더 제목", "Header type": "헤더 유형", "Header variables": "헤더 변수", "Hide": "숨다", "Hide agent's profile image": "상담원 프로필 이미지 숨기기", "Hide archived tickets": "보관된 티켓 숨기기", "Hide archived tickets from users.": "사용자에게 보관된 티켓을 숨깁니다.", "Hide chat if no agents online": "온라인 상담원이 없는 경우 채팅 숨기기", "Hide chat outside of office hours": "근무 시간 외 채팅 숨기기", "Hide conversation details panel": "대화 세부정보 패널 숨기기", "Hide conversations of other agents": "다른 상담원의 대화 숨기기", "Hide on mobile": "모바일에서 숨기기", "Hide the agent's profile image within the chat.": "채팅 내에서 상담원의 프로필 이미지를 숨깁니다.", "Hide tickets from the chat widget and chats from the ticket area.": "채팅 위젯에서 티켓을 숨기고 티켓 영역에서 채팅을 숨깁니다.", "Hide timetable": "시간표 숨기기", "Host": "주인", "Human takeover": "인적 인수", "If no agents respond within the specified time interval, a message will be sent to request the user's details, such as their email.": "지정된 시간 간격 내에 상담원이 응답하지 않으면 이메일과 같은 사용자 세부정보를 요청하는 메시지가 전송됩니다.", "If the chatbot doesn't understand a user's question, forwards the conversation to an agent.": "챗봇이 사용자의 질문을 이해하지 못하는 경우 상담원에게 대화를 전달합니다.", "Image": "영상", "Import admins": "관리자 가져오기", "Import all settings.": "모든 설정을 가져옵니다.", "Import articles": "기사 가져오기", "Import contacts": "연락처 가져오기", "Import customers": "수입 고객", "Import customers into Support Board. Only new customers will be imported.": "고객을 Support Board(으)로 가져옵니다. 신규 고객만 가져옵니다.", "Import settings": "설정 가져오기", "Import users": "사용자 가져오기", "Import users from a CSV file.": "CSV 파일에서 사용자를 가져옵니다.", "Import vendors": "수입업체", "Import vendors into Support Board as agents. Only new vendors will be imported.": "공급업체를 에이전트로 Support Board에 가져옵니다. 새로운 공급업체만 가져올 것입니다.", "Improve chat performance with Pusher and WebSockets. This setting stops all AJAX/HTTP real-time requests that slow down your server and use instead the WebSockets.": "Pusher 및 WebSocket으로 채팅 성능을 개선합니다. 이 설정은 서버 속도를 늦추고 대신 WebSocket을 사용하는 모든 AJAX/HTTP 실시간 요청을 중지합니다.", "Include custom fields": "사용자 정의 필드 포함", "Include custom fields in the registration form.": "등록 양식에 사용자 정의 필드를 포함하십시오.", "Include the password field in the registration form.": "등록 양식에 비밀번호 필드를 포함하십시오.", "Incoming conversations and messages": "수신 대화 및 메시지", "Incoming conversations only": "들어오는 대화만", "Incoming messages only": "수신 메시지만", "Increase sales and connect you and sellers with customers in real-time by integrating Active eCommerce with Support Board.": "Active eCommerce을(를) 지원 보드와 통합하여 판매를 늘리고 귀하와 판매자를 고객과 실시간으로 연결하십시오.", "Increase sales, provide better support, and faster solutions, by integrating WooCommerce with Support Board.": "WooCommerce를 Support Board와 통합하여 판매를 늘리고 더 나은 지원을 제공하며 더 빠른 솔루션을 제공합니다.", "Info message": "정보 메시지", "Initialize and display the chat widget and tickets only for members.": "회원 전용 채팅 위젯과 티켓을 초기화하여 표시합니다.", "Initialize and display the chat widget only when the user is logged in.": "사용자가 로그인한 경우에만 채팅 위젯을 초기화하고 표시합니다.", "Instance ID": "인스턴스 ID", "Integrate OpenCart with {R} for real-time syncing of customers, order history access, and customer cart visibility.": "고객 실시간 동기화, 주문 내역 액세스 및 고객 카트 가시성을 위해 OpenCart를 {R}와 통합하세요.", "Interval (sec)": "간격(초)", "IP banning": "IP 차단", "Label": "상표", "Language": "언어", "Language detection": "언어 감지", "Language detection message": "언어 감지 메시지", "Last name": "성", "Leave it blank if you don't know what this setting is! Entering an incorrect value will break the chat. Sets the main domain where chat is used to enable login and conversations sharing between the main domain and sub domains.": "이 설정이 무엇인지 모르는 경우 비워 두십시오! 잘못된 값을 입력하면 채팅이 중단됩니다. 기본 도메인과 하위 도메인 간의 로그인 및 대화 공유를 활성화하기 위해 채팅을 사용하는 기본 도메인을 설정합니다.", "Left": "왼쪽", "Left panel": "왼쪽 패널", "Left profile image": "왼쪽 프로필 이미지", "Let the bot to search on Google to find answers to user questions.": "봇이 Google에서 검색하여 사용자 질문에 대한 답변을 찾도록 합니다.", "Let the chatbot search on Google to find answers to user questions.": "챗봇이 Google에서 검색하여 사용자 질문에 대한 답변을 찾도록 하세요.", "Lets your users reach you via Twitter. Read and reply to messages sent to your Twitter account directly from {R}.": "사용자가 Twitter를 통해 귀하에게 연락할 수 있습니다. {R}에서 직접 Twitter 계정으로 보낸 메시지를 읽고 답장하세요.", "Lets your users reach you via WeChat. Read and reply to all messages sent to your WeChat official account directly from {R}.": "사용자가 WeChat을 통해 귀하에게 연락할 수 있습니다. {R}에서 직접 WeChat 공식 계정으로 보낸 모든 메시지를 읽고 답장하세요.", "Lets your users reach you via WhatsApp. Read and reply to all messages sent to your WhatsApp Business account directly from {R}.": "사용자가 WhatsApp을 통해 귀하에게 연락할 수 있습니다. {R}에서 직접 WhatsApp Business 계정으로 보낸 모든 메시지를 읽고 답장하세요.", "Link each agent with the corresponding Slack user, so when an agent replies via Slack it will be displayed as the assigned agent.": "각 에이전트를 해당 Slack 사용자와 연결하면 에이전트가 Slack을 통해 응답할 때 할당된 에이전트로 표시됩니다.", "Link name": "링크 이름", "Login form": "로그인 양식", "Login initialization": "로그인 초기화", "Login verification URL": "로그인 확인 URL", "Logit bias": "로짓 편향", "Make a backup of your Dialogflow agent first. This operation can take several minutes.": "먼저 Dialogflow 에이전트를 백업합니다. 이 작업은 몇 분이 걸릴 수 있습니다.", "Make the registration phone field mandatory.": "등록 전화 필드를 필수로 설정하십시오.", "Manage": "관리하다", "Manage here the departments settings.": "여기에서 부서 설정을 관리합니다.", "Manage the tags settings.": "태그 설정을 관리합니다.", "Manifest file URL": "Manifest 파일 URL", "Manual": "수동", "Manual initialization": "수동 초기화", "Martfury root directory path, e.g. /var/www/": "Martfury 루트 디렉토리 경로, 예: /var/www/", "Martfury shop URL, e.g. https://shop.com": "마트퓨리 상점 URL, 예: https://shop.com", "Max message limit": "최대 메시지 한도", "Max tokens": "최대 토큰", "Members only": "회원 전용", "Members with an active paid plan only": "활성 유료 요금제를 사용하는 회원만", "Message": "메세지", "Message area": "메시지 영역", "Message rewrite button": "메시지 재작성 버튼", "Message template": "메시지 템플릿", "Message type": "메시지 유형", "Messaging channels": "메시징 채널", "Messenger and Instagram settings": "Messenger 및 Instagram 설정", "Minify JS": "JS 축소", "Minimal": "최소한의", "Model": "모델", "Multilingual": "다국어", "Multilingual plugin": "다국어 플러그인", "Multilingual via translation": "번역을 통한 다국어", "Multlilingual training sources": "다국어 교육 소스", "Name": "이름", "Namespace": "네임스페이스", "New conversation email": "새 대화 이메일", "New conversation notification": "새 대화 알림", "New ticket button": "새 티켓 버튼", "Newsletter": "뉴스 레터", "No delay": "지체없이", "No results found.": "검색 결과가 없습니다.", "No, we don't ship in": "아니요, 배송하지 않습니다.", "None": "없음", "Note data scraping": "노트 데이터 스크래핑", "Notes": "노트", "Notifications icon": "알림 아이콘", "Notify the user when their message is sent outside of the scheduled office hours or all agents are offline.": "예정된 근무 시간 외에 메시지가 전송되거나 모든 상담원이 오프라인 상태인 경우 사용자에게 알립니다.", "OA secret key": "OA 비밀키", "Offline message": "오프라인 메시지", "Offset": "오프셋", "On chat open": "채팅 오픈 시", "On page load": "페이지 로드 시", "One conversation per agent": "상담원당 대화 1개", "One conversation per department": "부서당 하나의 대화", "Online users notification": "온라인 사용자 알림", "Only desktop": "데스크탑 전용", "Only general questions": "일반적인 질문만", "Only mobile devices": "모바일 기기만", "Only questions related to your sources": "출처와 관련된 질문만", "Open automatically": "자동으로 열기", "Open chat": "채팅 열기", "Open the chat window automatically when a new message is received.": "새 메시지가 수신되면 자동으로 채팅 창을 엽니다.", "OpenAI Assistants - Department linking": "OpenAI 도우미 - 부서 연결", "OpenAI settings.": "OpenAI 설정.", "Optional link": "선택적 링크", "Order webhook": "웹훅 주문", "Other": "다른", "Outgoing SMTP server information.": "발신 SMTP 서버 정보.", "Page ID": "페이지 ID", "Page IDs": "페이지 ID", "Page name": "페이지 이름", "Page token": "페이지 토큰", "Panel height": "패널 높이", "Panel name": "패널 이름", "Panel title": "패널 제목", "Panels arrows": "패널 화살표", "Password": "비밀번호", "Perfex URL": "퍼펙스 URL", "Performance optimization": "성능 최적화", "Phone": "핸드폰", "Phone number ID": "전화번호 아이디", "Phone required": "전화 필요", "Place ID": "장소 ID", "Placeholder text": "자리 표시자 텍스트", "Play a sound for new messages and conversations.": "새 메시지와 대화에 대한 소리를 재생합니다.", "Popup message": "팝업 메시지", "Port": "포트", "Post Type slugs": "Post Type 슬러그", "Presence penalty": "출석 패널티", "Prevent admins from receiving email notifications.": "관리자가 이메일 알림을 받지 못하도록 합니다.", "Prevent agents from viewing conversations assigned to other agents. This setting is automatically enabled if routing or queue is active.": "상담원이 다른 상담원에게 할당된 대화를 볼 수 없도록 합니다. 이 설정은 라우팅 또는 대기열이 활성화된 경우 자동으로 활성화됩니다.", "Prevent any abuse from users by limiting the number of messages sent to the chatbot from one device.": "하나의 디바이스에서 챗봇으로 보내는 메시지의 수를 제한하여 사용자의 악용을 방지합니다.", "Primary color": "원색", "Priority": "우선 사항", "Privacy link": "개인정보 보호 링크", "Privacy message": "개인정보 보호 메시지", "Private chat": "개인 채팅", "Private chat linking": "비공개 채팅 연결", "Private key": "개인 키", "Product IDs": "제품 ID", "Product removed notification": "제품 삭제 알림", "Product removed notification - Email": "제품 제거 알림 - 이메일", "Profile image": "프로필 이미지", "Project ID": "프로젝트 ID", "Project ID or Agent Name": "프로젝트 ID 또는 에이전트 이름", "Prompt": "즉각적인", "Prompt - Message rewriting": "프롬프트 - 메시지 재작성", "Protect the tickets area from spam and abuse with Google reCAPTCHA.": "Google reCAPTCHA을(를) 사용하여 스팸 및 남용으로부터 티켓 구역을 보호하세요.", "Provide help desk support to your customers by including a ticket area, with all chat features included, on any web page in seconds.": "모든 채팅 기능이 포함된 티켓 영역을 모든 웹 페이지에 몇 초 안에 포함하여 고객에게 헬프 데스크 지원을 제공하십시오.", "Provider": "공급자", "Purchase button text": "구매 버튼 텍스트", "Push notifications": "푸시 알림", "Push notifications settings.": "푸시 알림 설정.", "Queue": "대기 줄", "Rating": "평가", "Read and reply to messages sent from Google Search, Maps and brand-owned channels directly in {R}.": "{R}에서 Google 검색, 지도 및 브랜드 소유 채널에서 보낸 메시지를 직접 읽고 답장하세요.", "Read, manage and reply to all messages sent to your Facebook pages and Instagram accounts directly from {R}.": "{R}에서 직접 Facebook 페이지와 Instagram 계정으로 전송된 모든 메시지를 읽고 관리하고 답장하세요.", "Reconnect": "다시 연결", "Redirect the user to the registration link instead of showing the registration form.": "등록 양식을 표시하는 대신 사용자를 등록 링크로 리디렉션합니다.", "Redirect the user to the specified URL if the registration is required and the user is not logged in. Leave blank to use the default registration form.": "등록이 필요하고 사용자가 로그인하지 않은 경우 사용자를 지정된 URL로 리디렉션합니다. 기본 등록 양식을 사용하려면 비워 둡니다.", "Refresh token": "토큰 새로 고침", "Register all visitors": "모든 방문자 등록", "Register all visitors automatically. When this option is not active, only the visitors that start a chat will be registered.": "모든 방문자를 자동으로 등록합니다. 이 옵션이 활성화되어 있지 않으면 채팅을 시작한 방문자만 등록됩니다.", "Registration / Login": "등록 / 로그인", "Registration and login form": "등록 및 로그인 양식", "Registration fields": "등록 필드", "Registration form": "등록 양식", "Registration link": "등록 링크", "Registration redirect": "등록 리디렉션", "Rename the chat bot. Default is 'Bot'.": "채팅 봇의 이름을 바꿉니다. 기본값은 &#39;봇&#39;입니다.", "Rename the visitor name prefix. Default is 'User'.": "방문자 이름 접두사의 이름을 바꿉니다. 기본값은 &#39;사용자&#39;입니다.", "Repeat": "반복하다", "Repeat - admin": "반복 - 관리자", "Replace the admin login page message.": "관리자 로그인 페이지 메시지를 교체합니다.", "Replace the brand logo on the admin login page.": "관리자 로그인 페이지에서 브랜드 로고를 교체하십시오.", "Replace the header title with the user's first name and last name when available.": "헤더 제목을 사용 가능한 경우 사용자의 이름과 성으로 바꿉니다.", "Replace the top-left brand icon on the admin area and the browser favicon.": "관리 영역의 왼쪽 상단 브랜드 아이콘과 브라우저 파비콘을 교체합니다.", "Reply to user emails": "사용자 이메일에 답장", "Reply to user text messages": "사용자 문자 메시지에 답장", "Reports": "보고서", "Reports area": "보고서 영역", "Request a valid Envato purchase code for registration.": "등록을 위해 유효한 Envato 구매 코드를 요청하십시오.", "Request the user to provide their email address and then send a confirmation email to the user.": "사용자에게 이메일 주소를 제공하도록 요청한 다음 사용자에게 확인 이메일을 보냅니다.", "Require phone": "전화 필요", "Require registration": "등록 필요", "Require the user registration or login before start a chat. To enable the login area the password field must be included.": "채팅을 시작하기 전에 사용자 등록 또는 로그인이 필요합니다. 로그인 영역을 활성화하려면 비밀번호 필드가 포함되어야 합니다.", "Require the user registration or login in order to use the tickets area.": "티켓 영역을 사용하려면 사용자 등록 또는 로그인이 필요합니다.", "Required": "필수의", "Response time": "응답 시간", "Restrict chat access by blocking IPs. List IPs with commas.": "IP를 차단하여 채팅 접근을 제한하세요. IP를 쉼표로 나열하세요.", "Returning visitor message": "방문자 메시지 반환", "Rich messages": "리치 메시지", "Rich messages are code snippets that can be utilized within a chat message. They can contain HTML code and are automatically rendered in the chat. Rich messages can be used with the following syntax: [rich-message-name]. There are a tonne of built-in rich messages to choose from.": "리치 메시지는 채팅 메시지 내에서 활용할 수 있는 코드 스니펫입니다. HTML 코드를 포함할 수 있으며 채팅에서 자동으로 렌더링됩니다. 서식 있는 메시지는 [rich-message-name] 구문과 함께 사용할 수 있습니다. 선택할 수 있는 내장된 풍부한 메시지가 많이 있습니다.", "Right": "오른쪽", "Right panel": "오른쪽 패널", "Routing": "라우팅", "Routing if offline": "오프라인인 경우 라우팅", "RTL": "RTL", "Save useful information like user country and language also for visitors.": "방문자에게도 사용자 국가 및 언어와 같은 유용한 정보를 저장합니다.", "Saved replies": "저장된 답글", "Scheduled office hours": "예정된 근무 시간", "Search engine ID": "검색 엔진 ID", "Second chat message": "두 번째 채팅 메시지", "Second reminder delay (hours)": "두 번째 알림 지연(시간)", "Secondary color": "보조 색상", "Secret key": "비밀 키", "Send a message to allow customers to be notified when they can purchase a product they are interested in, but that is currently out of stock. You can use the following merge fields: {user_name}, {product_name}.": "고객이 관심이 있지만 현재 품절된 제품을 구매할 수 있을 때 알림을 받을 수 있도록 메시지를 보냅니다. 다음 병합 필드를 사용할 수 있습니다: {user_name}, {product_name}.", "Send a message to new users when they create the first ticket. Text formatting and merge fields are supported.": "새 사용자가 첫 번째 티켓을 만들 때 메시지를 보냅니다. 텍스트 서식 및 병합 필드가 지원됩니다.", "Send a message to new users when they visit the website for the first time.": "신규 사용자가 웹사이트를 처음 방문할 때 메시지를 보냅니다.", "Send a message to the customer after a product has been removed from the cart. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.": "장바구니에서 제품을 제거한 후 고객에게 메시지를 보냅니다. 다음 병합 필드 등을 사용할 수 있습니다: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.", "Send a message to the customers who complete a purchase asking to share the product they just bought. You can use the following merge fields and more: {product_name}, {user_name}.": "구매를 완료한 고객에게 방금 구매한 제품을 공유해 달라는 메시지를 보냅니다. 다음 병합 필드 등을 사용할 수 있습니다: {product_name}, {user_name}.", "Send a message to the customers who complete a purchase. You can use the following merge fields and more: {coupon}, {product_names}, {user_name}.": "구매를 완료한 고객에게 메시지를 보냅니다. 다음 병합 필드 등을 사용할 수 있습니다: {coupon}, {product_names}, {user_name}.", "Send a message to the user when the agent archive the conversation.": "에이전트가 대화를 보관할 때 사용자에게 메시지를 보냅니다.", "Send a message to users who visit the website again after at least 24 hours. You can use the following merge fields and more: {coupon}, {user_name}. See the docs for more details.": "최소 24시간 후에 웹사이트를 다시 방문하는 사용자에게 메시지를 보냅니다. 다음 병합 필드 등을 사용할 수 있습니다: {coupon}, {user_name}. 자세한 내용은 문서를 참조하세요.", "Send a test agent notification email to verify email settings.": "테스트 에이전트 알림 이메일을 보내 이메일 설정을 확인합니다.", "Send a test message to your Slack channel. This only tests the sending functionality of outgoing messages.": "Slack 채널에 테스트 메시지를 보냅니다. 이것은 보내는 메시지의 전송 기능만 테스트합니다.", "Send a test user notification email to verify email settings.": "이메일 설정을 확인하기 위해 테스트 사용자 알림 이메일을 보냅니다.", "Send a text message to the provided phone number.": "제공된 전화번호로 문자 메시지를 보냅니다.", "Send a user email notification": "사용자 이메일 알림 보내기", "Send a user text message notifcation": "사용자에게 문자 메시지 알림 보내기", "Send a user text message notification": "사용자에게 문자 메시지 알림 보내기", "Send an agent email notification": "상담원 이메일 알림 보내기", "Send an agent text message notification": "상담원 문자 메시지 알림 보내기", "Send an agent user text notification": "상담원 사용자에게 문자 알림 보내기", "Send an email notification to the provided email address.": "제공된 이메일 주소로 이메일 알림을 보냅니다.", "Send an email to an agent when a user replies and the agent is offline. An email is automatically sent to all agents for new conversations.": "사용자가 응답하고 에이전트가 오프라인일 때 에이전트에게 이메일을 보냅니다. 새 대화에 대한 이메일이 모든 상담원에게 자동으로 전송됩니다.", "Send an email to the user when a new conversation is created.": "새 대화가 생성되면 사용자에게 이메일을 보냅니다.", "Send an email to the user when a new conversation or ticket is created": "새 대화 또는 티켓이 생성되면 사용자에게 이메일 보내기", "Send an email to the user when an agent replies and the user is offline.": "에이전트가 응답하고 사용자가 오프라인일 때 사용자에게 이메일을 보냅니다.", "Send email": "이메일을 보내", "Send login details to the specified URL and allow access only if the response is positive.": "지정된 URL로 로그인 정보를 보내고 응답이 긍정적인 경우에만 액세스를 허용합니다.", "Send message": "문자 보내", "Send message to Slack": "Slack에 메시지 보내기", "Send message via enter button": "Enter 버튼을 통해 메시지 보내기", "Send text message": "문자 메시지 보내기", "Send the message template to a WhatsApp number.": "메시지 템플릿을 WhatsApp 번호로 보냅니다.", "Send the message via the ENTER keyboard button.": "ENTER 키보드 버튼을 통해 메시지를 보냅니다.", "Send the user details of the registration form and email rich messages to Dialogflow.": "Dialogflow에 등록 양식 및 이메일 풍부한 메시지의 사용자 세부 정보를 보냅니다.", "Send the WhatsApp order details to the URL provided.": "제공된 URL로 WhatsApp 주문 세부 정보를 보냅니다.", "Send to user's email": "사용자의 이메일로 보내기", "Send transcript to user's email": "사용자의 이메일로 스크립트 보내기", "Send user details": "사용자 세부 정보 보내기", "Sender": "보내는 사람", "Sender email": "이메일 보내기", "Sender name": "발신자 이름", "Sender number": "발신자 번호", "Sends a text message if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.": "WhatsApp 메시지 전송에 실패하면 문자 메시지를 보냅니다. 텍스트 및 다음 병합 필드를 사용할 수 있습니다: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.", "Sends a WhatsApp Template notification if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {recipient_name}, {recipient_email}.": "WhatsApp 메시지 전송에 실패하면 WhatsApp 템플릿 알림을 보냅니다. 텍스트와 {conversation_url_parameter}, {recipient_name}, {recipient_email} 병합 필드를 사용할 수 있습니다.", "Service": "서비스", "Service Worker path": "Service Worker 경로", "Service Worker URL": "Service Worker URL", "Set a dedicated Dialogflow agent for each department.": "부서별로 전용 Dialogflow 에이전트를 설정합니다.", "Set a dedicated OpenAI Assistants for each department.": "부서별로 전용 OpenAI 도우미를 설정하세요.", "Set a dedicated Slack channel for each department.": "부서별 Slack 전용 채널을 설정합니다.", "Set a profile image for the chat bot.": "챗봇의 프로필 이미지를 설정합니다.", "Set the articles panel title. Default is 'Help Center'.": "기사 패널 제목을 설정합니다. 기본값은 &#39;도움말 센터&#39;입니다.", "Set the avatar image shown next to the message. It must be a JPG image of 1024x1024px with a maximum size of 50KB.": "메시지 옆에 표시되는 아바타 이미지를 설정합니다. 1024x1024픽셀의 JPG 이미지여야 하며 최대 크기는 50입니다.", "Set the chat language or translate it automatically to match the user language. Default is English.": "채팅 언어를 설정하거나 사용자 언어와 일치하도록 자동으로 번역합니다. 기본값은 영어입니다.", "Set the currency symbol of the membership prices.": "멤버십 가격의 통화 기호를 설정합니다.", "Set the currency symbol used by your system.": "시스템에서 사용하는 통화 기호를 설정합니다.", "Set the default departments for all tickets. Enter the department ID.": "모든 티켓의 기본 부서를 설정합니다. 부서 ID를 입력합니다.", "Set the default email header that will be prepended to automated emails and direct emails.": "자동 이메일 및 다이렉트 이메일 앞에 추가될 기본 이메일 헤더를 설정합니다.", "Set the default email signature that will be appended to automated emails and direct emails.": "자동 이메일 및 다이렉트 이메일에 추가될 기본 이메일 서명을 설정합니다.", "Set the default form to display if the registraion is required.": "등록이 필요한 경우 표시할 기본 양식을 설정합니다.", "Set the default name to use for conversations without a name.": "이름이 없는 대화에 사용할 기본 이름을 설정합니다.", "Set the default notifications icon. The icon will be used as a profile image if the user doesn't have one.": "기본 알림 아이콘을 설정합니다. 사용자에게 프로필 이미지가 없는 경우 아이콘이 프로필 이미지로 사용됩니다.", "Set the default office hours for when agents are shown as available. These settings are also used for all other settings that rely on office hours.": "상담원이 사용 가능한 것으로 표시되는 기본 근무 시간을 설정합니다. 이러한 설정은 근무 시간에 의존하는 다른 모든 설정에도 사용됩니다.", "Set the default username to use in bot messages and emails when the user doesn't have a name.": "사용자에게 이름이 없을 때 봇 메시지 및 이메일에 사용할 기본 사용자 이름을 설정합니다.", "Set the header appearance.": "헤더 모양을 설정합니다.", "Set the maximum height of the tickets panel.": "티켓 패널의 최대 높이를 설정합니다.", "Set the multilingual plugin you're using, or leave it disabled if your site uses only one language.": "사용 중인 다국어 플러그인을 설정하거나 사이트에서 하나의 언어만 사용하는 경우 비활성화된 상태로 두십시오.", "Set the offline status automatically when the agent or admin remains inactive in the admin area for at least 10 minutes.": "상담원이나 관리자가 관리 영역에서 최소 10분 동안 활동하지 않으면 자동으로 오프라인 상태를 설정합니다.", "Set the position of the chat widget.": "채팅 위젯의 위치를 설정합니다.", "Set the primary color of the admin area.": "관리 영역의 기본 색상을 설정합니다.", "Set the primary color of the chat widget.": "채팅 위젯의 기본 색상을 설정합니다.", "Set the secondary color of the admin area.": "관리 영역의 보조 색상을 설정합니다.", "Set the secondary color of the chat widget.": "채팅 위젯의 보조 색상을 설정합니다.", "Set the tertiary color of the chat widget.": "채팅 위젯의 3차 색상을 설정합니다.", "Set the title of the administration area.": "관리 영역의 제목을 설정합니다.", "Set the title of the conversations panel.": "대화 패널의 제목을 설정합니다.", "Set the UTC offset of the office hours timetable. The correct value can be negative, and it's generated automatically once you click this input field, if it's empty.": "근무 시간 시간표의 UTC 오프셋을 설정합니다. 올바른 값은 음수일 수 있으며 비어 있는 경우 이 입력 필드를 클릭하면 자동으로 생성됩니다.", "Set which actions to allow agents.": "에이전트를 허용할 작업을 설정합니다.", "Set which actions to allow supervisors.": "감독자를 허용할 작업을 설정합니다.", "Set which user details to send to the main channel. Add comma separated values.": "메인 채널로 보낼 사용자 정보를 설정합니다. 쉼표로 구분된 값을 추가합니다.", "Settings area": "설정 영역", "settings information": "설정 정보", "Shop": "가게", "Show": "보여 주다", "Show a browser tab notification when a new message is received.": "새 메시지가 수신되면 브라우저 탭 알림을 표시합니다.", "Show a desktop notification when a new message is received.": "새 메시지가 수신되면 바탕 화면 알림을 표시합니다.", "Show a notification and play a sound when a new user is online.": "새 사용자가 온라인 상태일 때 알림을 표시하고 소리를 재생합니다.", "Show a pop-up notification to all users.": "모든 사용자에게 팝업 알림을 표시합니다.", "Show profile images": "프로필 이미지 표시", "Show sender's name": "발신자 이름 표시", "Show the agents menu in the dashboard and force the user to choose an agent to start a conversation.": "대시보드에 상담원 메뉴를 표시하고 사용자가 대화를 시작할 상담원을 선택하도록 합니다.", "Show the articles panel on the chat dashboard.": "채팅 대시보드에 기사 패널을 표시합니다.", "Show the categories instead of the articles list.": "기사 목록 대신 카테고리를 표시합니다.", "Show the follow up message when a visitor add an item to the cart. The message is sent only if the user has not provided an email yet.": "방문자가 장바구니에 항목을 추가할 때 후속 메시지를 표시합니다. 사용자가 아직 이메일을 제공하지 않은 경우에만 메시지가 전송됩니다.", "Show the list of all Slack channels.": "모든 Slack 채널 목록을 표시합니다.", "Show the profile image of agents and users within the conversation.": "대화 내 에이전트 및 사용자의 프로필 이미지를 표시합니다.", "Show the sender's name in every message.": "모든 메시지에 보낸 사람의 이름을 표시합니다.", "Single label": "단일 라벨", "Single phone country code": "단일 전화 국가 코드", "Site key": "사이트 키", "Slug": "강타", "Social share message": "소셜 공유 메시지", "Sort conversations by date": "날짜별로 대화 정렬", "Sound": "소리", "Sound settings": "사운드 설정", "Sounds": "소리", "Sounds - admin": "소리 - 관리자", "Source links": "소스 링크", "Speech recognition": "음성 인식", "Spelling correction": "맞춤법 수정", "Starred tag": "별표 표시된 태그", "Start importing": "가져오기 시작", "Store name": "가게 이름", "Subject": "주제", "Subscribe": "구독하다", "Subscribe users to your preferred newsletter service when they provide an email.": "사용자가 이메일을 제공할 때 선호하는 뉴스레터 서비스를 구독하십시오.", "Subtract the offset value from the height value.": "높이 값에서 오프셋 값을 뺍니다.", "Success message": "성공 메시지", "Supervisors": "감독자", "Support Board path": "지원 보드 경로", "Sync admin and staff accounts with Support Board. Staff users will be registered as agents, while admins as admins. Only new users will be imported.": "관리자 및 직원 계정을 지원 보드와 동기화합니다. 직원 사용자는 에이전트로 등록되고 관리자는 관리자로 등록됩니다. 새 사용자만 가져옵니다.", "Sync all contacts of all clients with Support Board. Only new contacts will be imported.": "지원 보드와 모든 클라이언트의 모든 연락처를 동기화합니다. 새 연락처만 가져옵니다.", "Sync all users with Support Board. Only new users will be imported.": "모든 사용자를 지원 보드와 동기화합니다. 새 사용자만 가져옵니다.", "Sync all WordPress users with Support Board. Only new users will be imported.": "모든 WordPress 사용자를 지원 보드와 동기화하십시오. 새 사용자만 가져옵니다.", "Sync knowledge base articles with Support Board. Only new articles will be imported.": "기술 자료 문서를 지원 보드와 동기화합니다. 새 기사만 가져옵니다.", "Sync mode": "동기화 모드", "Synchronization": "동기화", "Synchronize": "동기화", "Synchronize customers, enable ticket and chat support for subscribers only, view subscription plans in the admin area.": "고객을 동기화하고, 구독자에 대해서만 티켓 및 채팅 지원을 활성화하고, 관리 영역에서 구독 계획을 봅니다.", "Synchronize emails": "이메일 동기화", "Synchronize Entities": "Entities 동기화", "Synchronize Entities now": "지금 Entities 동기화", "Synchronize now": "지금 동기화", "Synchronize users": "사용자 동기화", "Synchronize your customers in real-time, chat with them and boost their engagement, or provide a better and faster support.": "실시간으로 고객을 동기화하고, 고객과 채팅하고, 참여도를 높이거나, 더 빠르고 더 나은 지원을 제공하세요.", "Synchronize your Messenger and Instagram accounts.": "Messenger 계정과 Instagram 계정을 동기화하세요.", "Synchronize your Perfex customers in real-time and let them contact you via chat! View profile details, proactively engage them, and more.": "실시간으로 Perfex 고객을 동기화하고 채팅을 통해 연락하도록 하십시오! 프로필 세부 정보를 보고 적극적으로 참여하는 등의 작업을 수행합니다.", "Synchronize your WhatsApp Cloud API account.": "WhatsApp Cloud API 계정을 동기화하세요.", "System requirements": "시스템 요구 사항", "Tags": "태그", "Tags settings": "태그 설정", "Template default language": "템플릿 기본 언어", "Template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "상담원이 회신할 때 사용자에게 전송되는 이메일의 템플릿입니다. 텍스트, HTML 및 {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments} 병합 필드를 사용할 수 있습니다.", "Template for the email sent to the user when a new conversation is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}.": "새 대화가 생성될 때 사용자에게 전송되는 이메일의 템플릿입니다. 텍스트, HTML 및 {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id} 병합 필드를 사용할 수 있습니다.", "Template for the email sent to the user when a new conversation or ticket is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}.": "새 대화 또는 티켓이 생성될 때 사용자에게 전송되는 이메일의 템플릿입니다. 텍스트, HTML 및 {conversation_url_parameter}, {user_name}, {message}, {attachments} 병합 필드를 사용할 수 있습니다.", "Template languages": "템플릿 언어", "Template name": "템플릿 이름", "Template of the admin notification email. You can use text, HTML, and the following merge field and more: {carts}. Enter the email you want to send notifications to in the email address field.": "관리자 알림 이메일의 템플릿입니다. 텍스트, HTML 및 다음 병합 필드 등을 사용할 수 있습니다. {carts}. 이메일 주소 필드에 알림을 보낼 이메일을 입력합니다.", "Template of the email sent to the customer after a product has been removed from the cart. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "장바구니에서 제품이 제거된 후 고객에게 전송되는 이메일 템플릿입니다. 텍스트, HTML 및 다음 병합 필드 등을 사용할 수 있습니다. {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the first notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "첫 번째 알림 이메일의 템플릿입니다. 텍스트, HTML 및 다음 병합 필드 등을 사용할 수 있습니다. {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the second notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "두 번째 알림 이메일의 템플릿입니다. 텍스트, HTML 및 다음 병합 필드 등을 사용할 수 있습니다. {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the waiting list notification email. You can use text, HTML, and the following merge field and more: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}.": "대기자 명단 알림 이메일 템플릿입니다. 텍스트, HTML 및 {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link} 병합 필드 등을 사용할 수 있습니다.", "Terms link": "약관 링크", "Tertiary color": "3차 색상", "Test Slack": "테스트 슬랙", "Test template": "테스트 템플릿", "Text": "텍스트", "Text message fallback": "문자 메시지 대체", "Text message notifications": "문자 메시지 알림", "Text messages": "문자 메시지", "The product is not in the cart.": "상품이 장바구니에 없습니다.", "The workspace name you are using to synchronize Slack.": "Slack을 동기화하는 데 사용하는 workspace 이름입니다.", "This is your main Slack channel ID, which is usually the #general channel. You will get this code by completing the Slack synchronization.": "이것은 일반적으로 #general 채널인 기본 Slack 채널 ID입니다. Slack 동기화를 완료하면 이 코드를 얻을 수 있습니다.", "This returns the Support Board path of your server.": "서버의 Support Board 경로를 반환합니다.", "Ticket custom fields": "티켓 사용자 정의 필드", "Ticket email": "티켓 이메일", "Ticket field names": "티켓 필드 이름", "Ticket fields": "티켓 필드", "Ticket only": "티켓만", "Ticket products selector": "티켓 상품 선택기", "Title": "제목", "Top": "맨 위", "Top bar": "상단바", "Training via cron job": "Cron job을 통한 훈련", "Transcript": "성적 증명서", "Transcript settings.": "스크립트 설정.", "Trigger": "방아쇠", "Trigger the Dialogflow Welcome Intent for new visitors when the welcome message is active.": "환영 메시지가 활성화되면 새 방문자에 대해 Dialogflow Welcome Intent를 트리거합니다.", "Troubleshoot": "문제 해결", "Troubleshoot problems": "문제 해결", "Twilio settings": "트윌리오 설정", "Twilio template": "<PERSON><PERSON><PERSON> 템플릿", "Unsubscribe": "구독 취소", "Upload attachments to Amazon S3.": "첨부파일을 Amazon S3에 업로드합니다.", "Usage Limit": "사용 제한", "Use this option to change the PWA icon. See the docs for more details.": "PWA 아이콘을 변경하려면 이 옵션을 사용하십시오. 자세한 내용은 문서를 참조하세요.", "User details": "사용자 세부정보", "User details in success message": "성공 메시지의 사용자 세부 정보", "User email notifications": "사용자 이메일 알림", "User login form information.": "사용자 로그인 양식 정보입니다.", "User message template": "사용자 메시지 템플릿", "User name as title": "사용자 이름을 제목으로", "User notification email": "사용자 알림 이메일", "User registration form information.": "사용자 등록 양식 정보입니다.", "User roles": "사용자 역할", "User system": "사용자 시스템", "Username": "사용자 이름", "Users and agents": "사용자 및 에이전트", "Users area": "사용자 영역", "Users only": "사용자만", "Users table additional columns": "사용자 테이블 추가 열", "UTC offset": "UTC 오프셋", "Variables": "변수", "View channels": "채널 보기", "View unassigned conversations": "할당되지 않은 대화 보기", "Visibility": "시계", "Visitor default name": "방문자 기본 이름", "Visitor name prefix": "방문자 이름 접두사", "Volume": "용량", "Volume - admin": "볼륨 - 관리자", "Waiting list": "대기자 명단", "Waiting list - Email": "대기자 명단 - 이메일", "Webhook URL": "웹훅 URL", "Webhooks": "웹훅", "Webhooks are information sent in background to a unique URL defined by you when something happens.": "웹훅은 어떤 일이 발생했을 때 사용자가 정의한 고유한 URL로 백그라운드에서 전송되는 정보입니다.", "Website": "웹사이트", "WeChat settings": "위챗 설정", "Welcome message": "환영 메시지", "Whmcs admin URL": "Whmcs 관리자 URL", "Whmcs admin URL. Ex. https://example.com/whmcs/admin/": "Whmcs 관리자 URL. 전. https://example.com/whmcs/admin/", "WordPress registration": "워드프레스 등록", "Yes, we ship in": "예, 배송됩니다.", "You haven't placed an order yet.": "아직 주문하지 않았습니다.", "You will get this code by completing the Dialogflow synchronization.": "Dialogflow 동기화를 완료하면 이 코드를 얻을 수 있습니다.", "You will get this code by completing the Slack synchronization.": "Slack 동기화를 완료하면 이 코드를 얻을 수 있습니다.", "You will get this information by completing the synchronization.": "동기화를 완료하면 이 정보를 얻을 수 있습니다.", "Your cart is empty.": "장바구니가 비어 있습니다.", "Your turn message": "당신의 차례 메시지", "Your username": "귀하의 사용자 이름", "Your WhatsApp catalogue details.": "WhatsApp 카탈로그 세부 정보.", "Zendesk settings": "Zendesk 설정", "Smart Reply": "스마트 답장", "This returns your Support Board URL.": "Support Board URL이 반환됩니다.", "Update conversation department": "대화 부서 업데이트"}
{"{product_name} has no {product_attribute_name} variants.": "{product_name} neturi {product_attribute_name} variantų.", "360dialog settings": "360 dialogo nustatymai", "360dialog template": "360 dialogo šablonas", "Abandoned cart notification": "Pranešimas apie paliktą krepšelį", "Abandoned cart notification - Admin email": "Pranešimas apie paliktą krepšelį – administratoriaus el", "Abandoned cart notification - First email": "Pranešimas apie paliktą krepšelį – pirmasis el", "Abandoned cart notification - Second email": "Pranešimas apie paliktą krepšelį – antrasis el", "Accept button text": "<PERSON><PERSON><PERSON><PERSON> my<PERSON> te<PERSON>", "Account SID": "Paskyros SID", "Activate the Right-To-Left (RTL) reading layout for the admin area.": "Suaktyvinkite administratoriaus srities skaitymo iš <PERSON> į kairę (RTL) išdėstymą.", "Activate the Right-To-Left (RTL) reading layout.": "Suaktyvinkite skaitymo iš deš<PERSON> į kairę (RTL) išdėstymą.", "Activate the Slack integration.": "Suaktyvinkite „Slack“ integraciją.", "Activate the Zendesk integration": "Suaktyvinkite „Zendesk“ integraciją", "Activate this option if you don't want to translate the settings area.": "Suaktyvinkite šią parinktį, jei nenorite versti nustatymų srities.", "Active": "Aktyvus", "Active - admin": "<PERSON><PERSON>y<PERSON><PERSON> – admin", "Active eCommerce CMS URL. Ex. https://shop.com/": "Active eCommerce TVS URL. Pvz. https://shop.com/", "Active eCommerce URL": "Active eCommerce URL", "Active for agents": "Aktyvus agentams", "Active for users": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Active webhooks": "Aktyvūs internetiniai ka<PERSON>ukai", "Add a delay (ms) to the bot's responses. Default is 2000.": "Pridėkite delsą (ms) prie roboto atsakymų. Numatytoji vertė yra 2000.", "Add and manage additional support departments.": "Pridėkite ir valdykite papildomus palaikymo skyrius.", "Add and manage saved replies that can be used by agents in the chat editor. Saved replies can be printed by typing # followed by the reply name plus space. Use \\n to do a line break.": "Pridėkite ir tvarkykite išsaugotus atsakymus, kuriuos agentai gali naudoti pokalbių rengyklėje. Išsaugotus atsakymus galima atspausdinti įvedus #, po to atsakymo pavadinimą ir tarpą. Naudokite \\n, kad padarytumėte eilutės lūžį.", "Add and manage tags.": "Pridėkite ir tvarkykite ž<PERSON>.", "Add comma separated WordPress user roles. The Support Board administration area will be available for new roles, in addition to the default one: editor, administrator, author.": "Pridėkite kableliais atskirtus „WordPress“ vartotojo vaidmenis. Support Board administravimo sritis bus prieinama naujiems vaidmenims, be numatytojo: redaktoriaus, administrator<PERSON>us, autoriaus.", "Add custom fields to the new ticket form.": "<PERSON><PERSON><PERSON><PERSON><PERSON> tinkintus laukus prie naujos bilieto formos.", "Add custom fields to the user profile details.": "Prid<PERSON><PERSON><PERSON> tinkintus laukus prie vartotojo profilio informacijos.", "Add Intents": "<PERSON><PERSON><PERSON><PERSON>", "Add Intents to saved replies": "Pridėkite Intents prie išsaugotų atsakymų", "Add WhatsApp phone number details here.": "Čia pridėkite išsamią „WhatsApp“ telefono numerio informaciją.", "Adjust the chat button position. Values are in px.": "Sureguliuokite pokalbio mygtuko padėtį. Reikšmės nurodytos px.", "Admin icon": "<PERSON><PERSON><PERSON> p<PERSON>a", "Admin IDs": "Administratoriaus ID", "Admin login logo": "Administratoriaus prisijungimo logotipas", "Admin login message": "<PERSON><PERSON>us prisijun<PERSON>", "Admin notifications": "<PERSON><PERSON><PERSON>", "Admin title": "<PERSON><PERSON><PERSON> titulas", "Agent area": "<PERSON><PERSON> sritis", "Agent details": "Agento duomenys", "Agent email notifications": "Agento pranešimai el. paštu", "Agent ID": "Agento ID", "Agent linking": "<PERSON><PERSON>", "Agent message template": "<PERSON><PERSON>", "Agent notification email": "Agento praneš<PERSON>", "Agent privileges": "Agento privilegijos", "Agents": "Agentai", "Agents and admins tab": "<PERSON><PERSON><PERSON><PERSON>", "Agents menu": "Agent<PERSON> meniu", "Agents only": "<PERSON>ik <PERSON>ai", "All": "Visi", "All channels": "Visi kanalai", "All messages": "Visi pranešimai", "All questions": "Visi k<PERSON>i", "Allow only extended licenses": "Leisti tik pratęstas licencijas", "Allow only one conversation": "Leisti tik vieną pokalbį", "Allow only one conversation per user.": "<PERSON><PERSON>i tik vieną pokalbį vienam vartotojui.", "Allow the chatbot to reply to the user's emails if the answer is known and email piping is active.": "Leiskite pokalbių robotui atsakyti į vartotojo el. <PERSON>, jei atsakymas žinomas ir el. pa<PERSON>to siunt<PERSON> aktyvus.", "Allow the chatbot to reply to the user's text messages if the answer is known.": "Leiskite pokalbių robotui atsakyti į vartoto<PERSON> tekstin<PERSON> p<PERSON>, jei atsa<PERSON>.", "Allow the user to archive a conversation and hide archived conversations.": "Leiskite vartotojui archyvuoti pokalbį ir slėpti archyvuotus pokalbius.", "Allow users to contact you via their favorite messaging apps.": "Leiskite vartotojams susisiekti su jumis per mėgstamas pranešimų siuntimo programas.", "Allow users to select a product on ticket creation.": "Leiskite vartotojams pasirinkti produktą kuriant bilietą.", "Always all messages": "Visada visi pranešimai", "Always incoming messages only": "Visada tik gaunami p<PERSON>šimai", "Always sort conversations by date in the admin area.": "Visada rūšiuokite pokalbius pagal dat<PERSON>us <PERSON>.", "API key": "API raktas", "Append the registration user details to the success message.": "Pridėkite registracijos vartotojo informaciją prie sėkmės pranešimo.", "Apply a custom background image for the header area.": "Antraštės sričiai pritaikykite tinkintą fono vaizdą.", "Apply changes": "<PERSON><PERSON><PERSON>", "Apply to": "Rašyti paraišką į", "Archive all user channels in the Slack app. This operation may take a long time to complete. Important: All of your slack channels will be archived.": "Archyvuokite visus vartotojų kanalus „Slack“ programoje. Ši operacija gali užtrukti ilgai. Svarbu: visi jūsų neveikiantys kanalai bus archyvuojami.", "Archive automatically the conversations marked as read every 24h.": "Automatiškai archyvuoki<PERSON> pokalbius, p<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>, kas 24 val.", "Archive channels": "Archy<PERSON><PERSON><PERSON> kanalus", "Archive channels now": "<PERSON>yvuoki<PERSON> kanalus dabar", "Articles": "Straipsniai", "Articles area": "Straipsnių sritis", "Articles button link": "Straipsnių mygtuko nuoroda", "Articles page URL": "Straipsnių puslapio URL", "Artificial Intelligence": "<PERSON><PERSON><PERSON><PERSON> intelek<PERSON>", "Assign a department to all conversations started from Google Business Messages. Enter the department ID.": "Priskirkite skyrių visiems pokalbiams, pradėtiems iš „Google Business Messages“. Įveskite skyriaus ID.", "Assign a department to all conversations started from Twitter. Enter the department ID.": "Priskirkite skyrių visiems pokalbiams, pradėtiems iš Twitter. Įveskite skyriaus ID.", "Assign a department to all conversations started from Viber. Enter the department ID.": "Priskirkite skyrių visiems pokalbiams, pradėtiems iš <PERSON>. Įveskite skyriaus ID.", "Assign a department to all conversations started from WeChat. Enter the department ID.": "Priskirkite skyrių visiems pokalbiams, pradėtiems iš WeChat. Įveskite skyriaus ID.", "Assign different departments to conversations started from different Google Business Messages locations. This setting overrides the default department.": "Priskirkite skirtingus skyrius poka<PERSON>, prad<PERSON><PERSON><PERSON> iš skirtingų „Google Business Messages“ vietų. Šis nustatymas nepaiso numatytojo skyriaus.", "Assistant": "<PERSON><PERSON><PERSON><PERSON>", "Assistant ID": "Asistento ID", "Attachments list": "<PERSON><PERSON><PERSON> s<PERSON>ša<PERSON>", "Audio file URL - admin": "Garso failo URL – admin", "Automatic": "Automatinis", "Automatic human takeover": "Automatinis žmogaus <PERSON>", "Automatic translation": "Automatinis vertimas", "Automatic updates": "Automatiniai atnaujinimai", "Automatically archive conversations": "Automatiškai archyvuoti pokalbius", "Automatically assigns a department based on the user's active plans. Insert -1 as plan ID for users without any plan.": "Automatiškai priskiria skyrių pagal aktyvius vartotojo planus. Įterpti -1 kaip plano ID vartotojams be jokio plano.", "Automatically check and install new updates. A valid Envato Purchase Code and valid apps's license keys are required.": "Automatiškai patikrinkite ir įdiekite naujus naujinimus. Reikalingas galiojantis „Envato“ pirkimo kodas ir galiojantys programų licencijos raktai.", "Automatically collapse the conversation details panel, and other panels, of the admin area.": "Automatiškai sutraukite išsamios pokalbio informacijos skydelį ir kitus administratoriaus srities skydelius.", "Automatically create a department for each website and route the conversations of each website to the right department. This setting requires a WordPress Multisite installation.": "Automatiškai sukurkite skyrių kiekvienai svetainei ir nukreipkite kiekvienos svetainės pokalbius į reikiamą skyrių. <PERSON>int atlikti šį nustatymą, reikia įdiegti „WordPress Multisite“.", "Automatically hide the conversation details panel.": "Automatiškai slėpti pokalbio informacijos skydelį.", "Automatically send cart reminders to customers with products in their carts. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "Automatiškai siųsti priminimus apie krepšelį klientams, kuri<PERSON> krepšelyje yra produktų. Galite naudoti šiuos sujungimo laukus ir daugiau: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.", "Automatically sync Zendesk customers with {R}, view Zendesk tickets, or create new ones without leaving {R}.": "Automatiškai sinchronizuokite „Zendesk“ klientus su {R}, peržiūrėkite „Zendesk“ bilietus arba kurkite naujus neišeidami iš {R}.", "Automatically synchronize products, categories, tags, and more with Dialogflow, and enable the bot to answer autonomously to questions related to your shop.": "Automatiškai sinchronizuokite produktus, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> ir daugiau su Dialogflow ir įgalinkite robotą savarankiškai atsakyti į klausimus, susijusius su jūsų parduotuve.", "Automatically translate admin area": "Automatiškai išversti administratoriaus sritį", "Automatically translate the admin area to match the agent profile language or browser language.": "Automatiškai išverskite administratoriaus sritį, kad ji atitiktų agento profilio kalbą arba naršyklė<PERSON> kalbą.", "Avatar image": "Avataro vaizdas", "Away mode": "Išvykimo re<PERSON>", "Before initiating the chat, the user must accept a privacy message in order to gain access.": "<PERSON><PERSON>š <PERSON> pokalbį, vartotojas turi priimti privatumo p<PERSON>, kad gautų prieigą.", "Birthday": "Gimtadienis", "Body variables": "<PERSON><PERSON><PERSON> kin<PERSON>ji", "Bot name": "<PERSON><PERSON> p<PERSON>", "Bot profile image": "<PERSON><PERSON> profilio <PERSON>", "Bot response delay": "<PERSON><PERSON> at<PERSON>", "Bottom": "Apačia", "Brand": "Prekės ženkla<PERSON>", "Built-in chat button icons": "Integruotos pokalbių mygtukų piktogramos", "Business Account ID": "Įmonės paskyros ID", "Button action": "<PERSON><PERSON><PERSON><PERSON>", "Button name": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "Button text": "Mygtuko te<PERSON>", "Button variables": "Mygtukų kintamieji", "Cancel button text": "<PERSON><PERSON><PERSON><PERSON> my<PERSON> te<PERSON>", "Cart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Cart follow up message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Catalogue details": "<PERSON><PERSON><PERSON>", "Catalogue ID": "Katalogo ID", "Change the chat button image with a custom one.": "Pakeiskite pokalbio mygtuko vaizdą tinkintu.", "Change the default field names.": "Pakeiskite numatytuosius lauk<PERSON> pavadinimus.", "Change the message text in the header area of the chat widget. This text will be replaced by the agent headline once the first reply is sent.": "Pakeiskite pranešimo tekstą pokalbių valdiklio antraštės srityje. Kai bus išsiųstas pirmasis atsakymas, šis tekstas bus pakeistas agento antrašte.", "Change the title text in the header area of the chat widget. This text will be replaced by the agent's name once the first reply is sent.": "Pakeiskite pavadinimo tekstą pokalbio valdiklio antraštės srity<PERSON>. Kai bus išsiųstas pirmasis atsakymas, šis tekstas bus pakeistas agento vardu.", "Channel ID": "Kanalo ID", "Channels": "Kanalai", "Channels filter": "Kanalų filtras", "Chat": "Pokalbis", "Chat and admin": "Chat ir admin", "Chat background": "Pokalbio fonas", "Chat button icon": "Pokalbio mygtuko p<PERSON>a", "Chat button offset": "Pokalbio mygtuk<PERSON> p<PERSON>", "Chat message": "Pokalbio žinutė", "Chat only": "Tik <PERSON>i", "Chat position": "<PERSON><PERSON><PERSON>", "Chatbot": "<PERSON><PERSON><PERSON>", "Chatbot mode": "<PERSON><PERSON><PERSON>", "Check Requirements": "<PERSON><PERSON><PERSON><PERSON><PERSON> re<PERSON>", "Check the server configurations and make sure it has all the requirements.": "Patikrinkite serverio konfigūracijas ir įsitikinkite, kad jis atitinka visus reikalavimus.", "Checkout": "<PERSON><PERSON><PERSON>", "Choose a background texture for the chat header and conversation area.": "Pasirinkite pokalbio antraštės ir pokalbio srities fono tekstūrą.", "Choose where to display the chat. Enter the values separated by commas.": "<PERSON><PERSON><PERSON><PERSON>, kur rodyti pokalbį. Įveskite <PERSON><PERSON><PERSON><PERSON>, atskirtas ka<PERSON>.", "Choose which fields to disable from the tickets area.": "Bilietų srityje p<PERSON>, k<PERSON><PERSON><PERSON> nor<PERSON> išjun<PERSON>.", "Choose which fields to include in the new ticket form.": "<PERSON>sir<PERSON><PERSON>, kuri<PERSON>s laukus įtraukti į naują bilieto formą.", "Choose which fields to include in the registration form. The name field is included by default.": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> lauku<PERSON> įtraukti į registracijos formą. <PERSON><PERSON><PERSON> lauka<PERSON> įtrauktas pagal numatytu<PERSON>ius nustatymus.", "Choose which user system the front-end chat will use to register and log in users.": "<PERSON><PERSON><PERSON><PERSON>, kurią naudotojo sistemą priekiniame pokalbyje naudos naudotojams registruotis ir prisijungti.", "City": "Miestas", "Clear flows": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Click the button to start the Dialogflow synchronization.": "Spustelėkite mygtuką, kad pradėtumėte Dialogflow sinchronizavimą.", "Click the button to start the Slack synchronization. Localhost cannot and does not receive messages. Log in with another account or as a visitor to perform your tests.": "Spustelėkite mygtuką, kad prad<PERSON>tum<PERSON>te „Slack“ sinchronizavimą. Localhost negali ir nepriima pranešimų. Prisijunkite naudodami kitą paskyrą arba ka<PERSON>, kad atliktum<PERSON> testus.", "Client email": "Kliento el", "Client ID": "Kliento ID", "Client token": "Kliento prieigos raktas", "Close chat": "Uždaryti pokalbį", "Close message": "Uždaryti pranešimą", "Cloud API numbers": "Cloud API numeriai", "Cloud API settings": "Cloud API nustatymai", "Cloud API template fallback": "Atsarginis debesies API šablonas", "Code": "<PERSON><PERSON>", "Collapse panels": "Sutraukti <PERSON>", "Color": "Spalva", "Communicate with your users right from Slack. Send and receive messages and attachments, use emojis, and much more.": "Bendraukite su savo vartotojais tiesiai iš „Slack“. Siųskite ir gaukite pranešimus bei priedus, naudokite jaustukus ir dar daugiau.", "Company": "Bendrovė", "Concurrent chats": "<PERSON><PERSON><PERSON> metu v<PERSON> poka<PERSON>i", "Configuration URL": "Konfigūracijos URL", "Confirm button text": "Patvirtinkite mygtuko tekstą", "Confirmation message": "<PERSON><PERSON><PERSON><PERSON>", "Connect smart chatbots and automate conversations by using one of the most advanced forms of artificial intelligence in the world.": "Prijunkite išmaniuosius pokalbių robotus ir automatizuokite pokalbius naudodami vieną pažangiausių dirbtinio intelekto formų pasaulyje.", "Connect stores to agents.": "Sujunkite parduotuves su agentais.", "Connect your Telegram bot to {R} to read and reply to all messages sent to your Telegram bot directly in {R}.": "Prijunkite savo „Telegram“ robotą prie {R}, kad gal<PERSON>te skaityti ir atsakyti į visus pranešimus, išsiųstus į „Telegram“ robotą tiesiogiai {R}.", "Connect your Viber bot to {R} to read and reply to all messages sent to your Viber bot directly in {R}.": "Prijunkite „Viber“ robotą prie {R}, kad gal<PERSON>te skaityti ir atsakyti į visus pranešimus, išsiųstus jūsų Viber robotui tiesiogiai {R}.", "Connect your Zalo Official Account to {R} to read and reply to all messages sent to your Zalo Official Account directly in {R}.": "Prijunkite oficialią Zalo paskyrą prie {R}, kad gal<PERSON>te skaityti ir atsakyti į visus pranešimus, išsiųstus į oficialią <PERSON>alo paskyrą, tiesiogiai {R}.", "Content": "Turinys", "Content template SID": "Turinio šablono SID", "Conversation profile": "Pokalbio profilis", "Conversations data": "Pokalbių duomenys", "Convert all emails": "Konvertuoti visus el", "Cookie domain": "Slapukų domenas", "Country": "<PERSON><PERSON>", "Coupon discount (%)": "<PERSON><PERSON><PERSON> (%)", "Coupon expiration (days)": "<PERSON><PERSON><PERSON> la<PERSON> (dienomis)", "Coupon expiration (seconds)": "<PERSON><PERSON><PERSON> la<PERSON> (sekundėmis)", "Create a WordPress user upon registration.": "Registruodamiesi sukurkite WordPress vartotoją.", "Create Intents now": "Sukurti Intents dabar", "Currency symbol": "<PERSON><PERSON><PERSON> simbo<PERSON>", "Custom CSS": "Tinkintas CSS", "Custom fields": "Pasirinktiniai laukai", "Custom JS": "Pasirinktinis JS", "Custom model ID": "Pasirinktinis modelio ID", "Custom parameters": "Tinkinti parametrai", "Customize the link for the 'All articles' button.": "Tinkinkite mygtuko „Visi straipsniai“ nuorodą.", "Dashboard display": "Prietaisų skydelio ekranas", "Dashboard title": "Prietaisų skydelio pavadinimas", "Database details": "Informacija apie duomenų bazę", "Database host": "Duomenų bazės priegloba", "Database name": "Duomenų bazės pavadinimas", "Database password": "Duomenų bazės <PERSON>", "Database prefix": "Duomenų bazės p<PERSON>", "Database user": "Duomenų bazės <PERSON>", "Decline button text": "<PERSON><PERSON><PERSON> te<PERSON>", "Declined message": "Pranešimas atmestas", "Default": "<PERSON><PERSON><PERSON><PERSON>", "Default body text": "Numatytasis teksto tekstas", "Default conversation name": "Numaty<PERSON><PERSON> pokal<PERSON> pavadin<PERSON>s", "Default department": "<PERSON>uma<PERSON><PERSON><PERSON> skyrius", "Default department ID": "Numatytasis <PERSON>", "Default form": "<PERSON><PERSON><PERSON><PERSON><PERSON> forma", "Default header text": "Numaty<PERSON><PERSON>", "Delay (ms)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (ms)", "Delete all leads and all messages and conversations linked to them.": "Ištrink<PERSON> visus potencialius klientus ir visus su jais susietus pranešimus bei pokalbius.", "Delete conversation": "<PERSON><PERSON><PERSON><PERSON> pokalbį", "Delete leads": "Ištrinkite potencialius k<PERSON>us", "Delete message": "<PERSON><PERSON><PERSON><PERSON>", "Delete the built-in flows.": "Ištrinkite integruotus srautus.", "Delimiter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Department": "Skyrius", "Department ID": "Skyriaus <PERSON>", "Departments": "<PERSON><PERSON><PERSON>", "Departments settings": "Skyrių nustatymai", "Desktop notifications": "Darbalauki<PERSON>", "Dialogflow - Department linking": "Dialogflow – skyrių su<PERSON>s", "Dialogflow chatbot": "Dialogflow pokalbių robotas", "Dialogflow edition": "Dialogflow leidimas", "Dialogflow Intent detection confidence": "Dialogflow Intent aptikimo patikimumas", "Dialogflow location": "Dialogo srauto vieta", "Dialogflow spelling correction": "Dialogflow rašybos taisymas", "Dialogflow welcome Intent": "Dialogflow sveikinimo ketinimas", "Disable agents check": "Išjungti agent<PERSON> patik<PERSON>", "Disable and hide the chat widget if all agents are offline.": "Išjunkite ir slėpkite pokalbių valdiklį, jei visi agentai neprisijungę.", "Disable and hide the chat widget outside of scheduled office hours.": "Išjunkite ir paslėpkite pokalbių valdiklį ne numatytomis darbo valandomis.", "Disable any features that you don't need.": "Išjunkite visas funkcijas, kurių jums nereikia.", "Disable auto-initialization of the chat widget. When this setting is active you must initialize the chat widget with a custom JavaScript API code written by you. If the chat doesn't appear and this setting is enabled, disable it.": "Išjungti automatinį pokalbių valdiklio inicijavimą. Kai šis nustatymas aktyvus, turite inicijuoti pokalbių valdiklį naudodami jūsų parašytą tinkintą JavaScript API kodą. Jei pokalbis nepasirodo ir šis nustatymas įjungtas, išjunkite jį.", "Disable auto-initialization of the tickets area. When this setting is active you must initialize the tickets area with a custom JavaScript API code written by you. If the tickets area doesn't appear and this setting is enabled, disable it.": "Išjungti automatinį bilietų srities inicijavimą. Kai šis nustatymas aktyvus, turite inicijuoti bilietų sritį naudodami pasirinktinį JavaScript API kodą, parašytą jūsų. Jei bilietų sritis nerodoma ir šis nustatymas įjungtas, išjunkite jį.", "Disable chatbot": "Išjungti pokalbių robotą", "Disable cron job": "Išjungti cron darbą", "Disable dashboard": "Išjungti prietaisų skydelį", "Disable during office hours": "Išjungti darbo valandomis", "Disable features": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Disable features you don't use and improve the chat performance.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON>, ir pagerinkite pokalbių našumą.", "Disable file uploading capabilities within the chat.": "Išjunkite failų įkėlimo galimybes pokalbyje.", "Disable for messaging channels": "Išjungti pranešimų kanalams", "Disable for the tickets area": "Išjungti bilietų srityje", "Disable invitation": "Išjungti kvietimą", "Disable online status check": "Išjungti būsenos tikrinimą internete", "Disable outside of office hours": "Išjungti ne darbo valandomis", "Disable password": "<PERSON><PERSON><PERSON><PERSON><PERSON> slaptažodį", "Disable registration during office hours": "Išjungti registraciją darbo valandomis", "Disable registration if agents online": "<PERSON><PERSON><PERSON><PERSON><PERSON> registra<PERSON>j<PERSON>, jei agentai prisijungę", "Disable the automatic invitation of agents to the channels.": "Išjungti automatinį agentų k<PERSON>timą į kanalus.", "Disable the channels filter.": "Išjungti kanalų filtrą.", "Disable the chatbot for the tickets area.": "Išjunkite pokalbių robotą bilietų srityje.", "Disable the chatbot for this channel only.": "Išjungti pokalbių robotą tik šiame kanale.", "Disable the dashboard, and allow only one conversation per user.": "Išjunkite prietaisų skydelį ir leiskite vienam vartotojui tik vieną pokalbį.", "Disable the login and remove the password field from the registration form.": "Išjunkite prisijungimą ir pašalinkite slaptažodžio lauką iš registracijos formos.", "Disable uploads": "<PERSON><PERSON><PERSON><PERSON><PERSON> įkėlimus", "Disable voice message capabilities within the chat.": "Išjunkite balso pranešimų galimybes pokalbyje.", "Disable voice messages": "<PERSON><PERSON><PERSON><PERSON><PERSON> balso p<PERSON>", "Disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Display a brand image in the header area. This only applies for the 'brand' header type.": "Rodyti prekės ženklo vaizdą antraštės s<PERSON>. Tai taikoma tik „prekės ženklo“ antraštės tipui.", "Display categories": "<PERSON><PERSON><PERSON>", "Display images": "<PERSON><PERSON><PERSON> v<PERSON>", "Display in conversation list": "Rodyti pokalbių sąraše", "Display in dashboard": "Rodyti prietaisų skydelyje", "Display online agents only": "<PERSON><PERSON><PERSON> tik internetinius agentus", "Display the articles section in the right area.": "Rodyti straipsnių skyrių dešinėje srityje.", "Display the dashboard instead of the chat area on initialization.": "Pradėjus rodyti p<PERSON>taisų skydelį, o ne pokalbio sritį.", "Display the feedback form to rate the conversation when it is archived.": "<PERSON><PERSON><PERSON> atsiliepimų formą, kad įvertintumėte pokalbį, kai jis arch<PERSON>.", "Display the user full name in the left panel instead of the conversation title.": "<PERSON><PERSON><PERSON>me skydelyje vietoj pokalbio pavadinimo rodykite visą vartotojo vardą.", "Display the user's profile image within the chat.": "<PERSON><PERSON><PERSON> vartotojo profilio vaizd<PERSON> p<PERSON>lby<PERSON>.", "Display user name in header": "<PERSON><PERSON><PERSON> var<PERSON><PERSON> vardą <PERSON>raštė<PERSON>", "Display user's profile image": "<PERSON><PERSON><PERSON> vartotojo profilio v<PERSON>", "Displays additional columns in the user table. Enter the name of the fields to add.": "Vartotojų lentelėje rodomi papildomi stulpeliai. Įveskite norimų pridėti laukų pavadinimus.", "Distribute conversations proportionately between agents and notify visitors of their position within the queue. Response time is in minutes. You can use the following merge fields in the message: {position}, {minutes}. They will be replaced by the real values in real-time.": "Proporcingai paskirstykite pokalbius tarp agentų ir praneškite lankytojams apie jų vietą eilėje. Atsakymo laikas yra minutės. Pranešime galite naudoti šiuos sujungimo laukus: {position}, {minutes}. Juos pakeis tikrosios vertės realiu laiku.", "Distribute conversations proportionately between agents, and block an agent from viewing the conversations of the other agents.": "Proporcingai paskirstykite pokalbius tarp agentų ir neleiskite agentui peržiūrėti kitų agentų pokalbių.", "Do not send email notifications to admins": "Nesiųskite el. pašto pranešimų administratoriams", "Do not show tickets in chat": "Nerodyti bilietų pokalbyje", "Do not translate settings area": "Neversti nustatymų srities", "Download": "Parsisiųsti", "Edit profile": "Redaguoti profilį", "Edit user": "Redaguoti naudotoją", "Email address": "Elektroninio pašto ad<PERSON>", "Email and ticket": "Paštas ir bilietas", "Email header": "<PERSON><PERSON> p<PERSON><PERSON><PERSON>", "Email notification delay (hours)": "Pranešimo el. paš<PERSON> vėlavimas (valandomis)", "Email notifications via cron job": "Pranešimai el. paštu per cron darbą", "Email only": "Tik el. paš<PERSON>", "Email piping": "El. p<PERSON><PERSON> v<PERSON>", "Email piping server information and more settings.": "El. pašto vamzdynų serverio informacija ir daugiau nustatymų.", "Email request message": "El. paš<PERSON> užkla<PERSON> praneš<PERSON>s", "Email signature": "Elektroninio p<PERSON>", "Email template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "<PERSON>. pa<PERSON><PERSON> el<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kai agentas atsako. Galite naudoti te<PERSON>t<PERSON>, HTML ir šiuos sujungimo laukus: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Email template for the email sent to an agent when a user sends a new message. You can use text, HTML, and the following merge fields: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, skirtas agent<PERSON>, kai vartotojas siunčia naują prane<PERSON>im<PERSON>. Galite naudoti tekstą, HTML ir šiuos sujungimo laukus: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Email template for the email sent to the user after submitting their email through the follow-up message form. You can use text, HTML, and the following merge fields: {user_name}, {user_email}.": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, skirtas el. <PERSON>, išsiųstam vartotojui pateikus el. laišką naudojant tolesnio pranešimo formą. Galite naudoti tekstą, HTML ir šiuos sujungimo laukus: {user_name}, {user_email}.", "Email template for the email sent to the user to verify their email address. Include the {code} merge field within your content, it will be replaced with the one-time code.": "El. pa<PERSON><PERSON> el. la<PERSON>, išsiųstam vartotojui patvirtinti el. pašto adresą. Į turinį įtraukite sujungimo lauką {code}, jis bus pakeistas vienkartiniu kodu.", "Email verification": "<PERSON><PERSON> p<PERSON><PERSON><PERSON>", "Email verification content": "<PERSON><PERSON> pa<PERSON><PERSON> pat<PERSON><PERSON> turi<PERSON>", "Enable email verification with OTP.": "Įgalinti el. pa<PERSON><PERSON> naudojant O<PERSON>.", "Enable logging of agent activity": "Įgalinti agento veiklos registravimą", "Enable logs": "Įgalinti žurnalus", "Enable the chatbot outside of scheduled office hours only.": "Įjunkite pokalbių robotą tik ne numatytomis darbo valandomis.", "Enable the registration only if all agents are offline.": "Įgalinkite registracij<PERSON>, tik jei visi agentai neprisijungę.", "Enable the registration outside of scheduled office hours only.": "Įgalinkite registraciją tik ne numatytomis darbo valandomis.", "Enable this option if email notifications are sent via cron job.": "Įjunkite šią parinktį, jei el. pa<PERSON>to p<PERSON>i siunčiami naudojant cron užduotį.", "Enable ticket and chat support for subscribers only, view member profile details and subscription details in the admin area.": "Įgalinkite bilietų ir pokalbių palaikymą tik prenumeratoriams, peržiūrėkite nario profilio informaciją ir prenumeratos informaciją administratoriaus srityje.", "Enter the bot token and click the button to synchronize the Telegram bot. Localhost cannot receive messages.": "Įveskite roboto prieigos raktą ir spustelėkite mygtuką, kad sinchronizuotumėte „Telegram“ robotą. Localhost negali priimti pranešimų.", "Enter the bot token and click the button to synchronize the Viber bot. Localhost cannot receive messages.": "Įveskite roboto prieigos raktą ir spustelėkite mygtuką, kad sinchronizuotumėte Viber robotą. Localhost negali priimti pranešimų.", "Enter the database details of the Active eCommerce CMS database.": "Įveskite išsamią Active eCommerce TVS duomenų bazės duomenų bazę.", "Enter the database details of the Martfury database.": "Įveskite Martfury duomenų bazės duomenis.", "Enter the database details of the Perfex database.": "Įveskite Perfex duomenų bazės duomenis.", "Enter the database details of the WHMCS database.": "Įveskite WHMCS duomenų bazės duomenis.", "Enter the default messages used by the chatbot when user question requires a dynamic answer.": "Įveskite numa<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> naudoja pokalbi<PERSON> robotas, kai į vartoto<PERSON> klausim<PERSON> reikia dinamiš<PERSON> at<PERSON>ky<PERSON>.", "Enter the details of your Google Business Messages.": "Įveskite i<PERSON><PERSON><PERSON><PERSON> „Google“ verslo pranešimų informaciją.", "Enter the details of your Twitter app.": "Įveskite savo „Twitter“ programos informaciją.", "Enter the LINE details to start using it. Localhost cannot receive messages.": "Įveskite išsamią LINE informaciją, kad pradėtumėte ją naudoti. Localhost negali priimti pranešimų.", "Enter the URL of a .css file, to load it automatically in the admin area.": "Įveskite .css failo URL, kad jis būtų automatiškai įkeltas administratoriaus srityje.", "Enter the URL of a .js file, to load it automatically in the admin area.": "Įveskite .js <PERSON>o <PERSON>, kad jis būtų automatiškai įkeltas administratoriaus srityje.", "Enter the URL of the articles page.": "Įveskite straipsnių puslapio URL.", "Enter the URLs of your shop": "Įveskite savo parduotuvės URL adresus", "Enter the WeChat official account token. See the docs for more details.": "Įveskite oficialų „WeChat“ paskyros prieigos raktą. Norėdami gauti daugiau informacijos, žr. dokumentus.", "Enter the Zalo details to start using it. Localhost cannot receive messages.": "Norėdami p<PERSON>ėti naudoti, įveskite išsa<PERSON>ą Zalo informaciją. Localhost negali priimti pranešimų.", "Enter your 360dialog account settings information.": "Įveskite 360 dialogo paskyros nustatymų informaciją.", "Enter your Envato Purchase Code to activate automatic updates and unlock all the features.": "Įveskite „Envato“ p<PERSON>, kad suakty<PERSON>te automatinius naujinimus ir atrakintumėte visas funkcijas.", "Enter your Twilio account details. You can use text and the following merge fields: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.": "Įveskite savo „Twilio“ paskyros informaciją. Galite naudoti tekstą ir šiuos sujungimo laukus: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.", "Enter your Twilio account settings information.": "Įveskite „<PERSON><PERSON><PERSON>“ paskyros nustatymų informaciją.", "Enter your WeChat Official Account information.": "Įveskite oficialios WeChat paskyros informaciją.", "Enter your Zendesk information.": "Įveskite savo Zendesk informaciją.", "Entities": "Entities", "Envato Purchase Code": "<PERSON><PERSON><PERSON> p<PERSON> k<PERSON>s", "Envato purchase code validation": "<PERSON>vato pirkimo kodo pat<PERSON>", "Exclude products": "Išskirkite produktus", "Export all settings.": "Eksportuoti visus nustatymus.", "Export settings": "Eksportuoti nustatymus", "Facebook pages": "Facebook puslapiai", "Fallback message": "Atsargin<PERSON>", "Filters": "Filtrai", "First chat message": "<PERSON><PERSON> p<PERSON>", "First reminder delay (hours)": "Pirmojo priminimo delsa (valandomis)", "First ticket form": "Pirmoji bilieto forma", "Flash notifications": "„Flash“ pranešimai", "Follow up - Email": "Tolesni veiksmai – el", "Follow up email": "Sekite el. paštą", "Follow up message": "<PERSON><PERSON><PERSON>", "Follows a conversation between a human agent and an end user and provide response suggestions to the human agent in real-time.": "Stebi žmogaus agento ir galutinio vartotojo pokalbį ir realiuoju laiku teikia agentui atsakymų pasiūlymus.", "Follow-up email template. You can use text, HTML, and the following merge fields and more: {coupon}, {product_names}, {user_name}.": "Tolesnio el. pašto šablonas. Galite naudoti te<PERSON>tą, HTML ir šiuos sujungimo laukus ir dar daugiau: {coupon}, {product_names}, {user_name}.", "Force language": "<PERSON><PERSON><PERSON><PERSON> kal<PERSON>", "Force log out": "Priverst<PERSON><PERSON>", "Force the chat to ignore the language preferences, and to use always the same language.": "Priverskite pokalbį nepaisyti kalbos nuostatų ir visada naudoti tą pačią kalbą.", "Force the loggout of Support Board agents if they are not logged in WordPress.": "Priverstinai atsijungti nuo Support Board agentų, jei jie nėra prisijungę prie „WordPress“.", "Force users to use a different conversation for each store and hide conversations from other stores from store administrators.": "Priverskite vartotojus naudoti skirtingą pokalbį kiekvienai parduotuvei ir paslėpti pokalbius iš kitų parduotuvių nuo parduotuvės administratorių.", "Force users to use only one phone country code.": "Priverskite vartotojus naudoti tik vieną telefono šalies kodą.", "Form message": "<PERSON><PERSON>", "Form title": "Formos pavadinimas", "Frequency penalty": "<PERSON><PERSON><PERSON>", "Full visitor details": "Visa informacija apie lankytoją", "Function name": "Funkcijos pavadin<PERSON>", "Generate conversations data": "Generuokite pokalbių duomenis", "Generate user questions": "Generuokite vartotojų klausimus", "Get configuration URL": "Gaukite konfigūracijos URL", "Get it from the APP_KEY value of the file .env located in the root directory of Active eCommerce.": "Gaukite jį iš failo .env, esančio Active eCommerce šakniniame kataloge, APP_KEY re<PERSON>.", "Get it from the APP_KEY value of the file .env located in the root directory of Martfury.": "Gaukite jį iš failo .env, <PERSON><PERSON><PERSON><PERSON> ka<PERSON>, APP_KEY vertės.", "Get Path": "Gaukite keli<PERSON>", "Get Service Worker path": "Gaukite Service Worker kelią", "Get URL": "Gaukite URL", "Google and Dialogflow settings.": "„Google“ ir „Dialogflow“ nustatymai.", "Google search": "Google paieška", "Header": "Antraštė", "Header background image": "An<PERSON>š<PERSON><PERSON><PERSON> fono vaizdas", "Header brand image": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ženk<PERSON> v<PERSON>", "Header message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Header title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "Header type": "An<PERSON>š<PERSON><PERSON><PERSON>", "Header variables": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Hide": "Slėpti", "Hide agent's profile image": "Slėpti agento profilio v<PERSON>", "Hide archived tickets": "<PERSON><PERSON><PERSON><PERSON><PERSON> archyvuotus bilietus", "Hide archived tickets from users.": "<PERSON><PERSON>ėpti archyvuotus bilietus nuo vartotoj<PERSON>.", "Hide chat if no agents online": "S<PERSON>ė<PERSON>i pokalbį, jei nėra agentų prisijungę", "Hide chat outside of office hours": "Slėpti pokalbį ne darbo valandomis", "Hide conversation details panel": "Slėpti pokalbio informacijos skydelį", "Hide conversations of other agents": "Slėpti kitų agentų pokalbius", "Hide on mobile": "Slėpti mobiliajame telefone", "Hide the agent's profile image within the chat.": "Paslėpkite agento profilio vaizdą pokalbyje.", "Hide tickets from the chat widget and chats from the ticket area.": "Slėpti bilietus pokalbių valdiklyje ir pokalbius bilietų srityje.", "Hide timetable": "Slėpti tvarkaraštį", "Host": "Šeimininkas", "Human takeover": "Žmo<PERSON><PERSON>", "If no agents respond within the specified time interval, a message will be sent to request the user's details, such as their email.": "Jei per nurodytą laiko intervalą agentai neatsako, bus išsiųstas pranešimas, kuriame bus prašoma išsamios vartotojo informacijos, pvz., el. pašto.", "If the chatbot doesn't understand a user's question, forwards the conversation to an agent.": "Jei pokalbių robotas nesupranta var<PERSON><PERSON> klaus<PERSON>, pokalbį persiunčia agentui.", "Image": "Vaizdas", "Import admins": "I<PERSON>rt<PERSON><PERSON><PERSON>", "Import all settings.": "Importuokite visus nustatymus.", "Import articles": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "Import contacts": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "Import customers": "<PERSON><PERSON>rt<PERSON><PERSON> k<PERSON>", "Import customers into Support Board. Only new customers will be imported.": "Importuokite klientus į Support Board. Bus importuojami tik nauji klientai.", "Import settings": "Importuo<PERSON> nustatymus", "Import users": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Import users from a CSV file.": "Importuokite naudotojus iš CSV failo.", "Import vendors": "I<PERSON>rt<PERSON>", "Import vendors into Support Board as agents. Only new vendors will be imported.": "Importuokite tiekėjus į Support Board kaip agentus. Bus importuojami tik nauji pardavėjai.", "Improve chat performance with Pusher and WebSockets. This setting stops all AJAX/HTTP real-time requests that slow down your server and use instead the WebSockets.": "Pagerinkite pokalbių našumą naudodami Pusher ir WebSockets. Šis nustatymas sustabdo visas AJAX / HTTP užklausas realiuoju laiku, kurios sul<PERSON> jūsų serverį ir vietoj to naudoja WebSockets.", "Include custom fields": "Įtraukite pasi<PERSON><PERSON><PERSON><PERSON> laukus", "Include custom fields in the registration form.": "Į registracijos formą įtraukite pasirinktinius laukus.", "Include the password field in the registration form.": "Į registracijos formą įtraukite slaptažodžio laukelį.", "Incoming conversations and messages": "Įeinantys pokalbiai ir ž<PERSON>s", "Incoming conversations only": "Tik gaunami pokal<PERSON>i", "Incoming messages only": "Tik gaunami pranešimai", "Increase sales and connect you and sellers with customers in real-time by integrating Active eCommerce with Support Board.": "Padidinkite pardavimą ir sujunkite jus bei pardavėjus su klientais realiuoju laiku integruodami Active eCommerce su Support Board.", "Increase sales, provide better support, and faster solutions, by integrating WooCommerce with Support Board.": "Padidinkite pardavimą, suteikite geresnį palaikymą ir greitesnius sprendimus integruodami „WooCommerce“ su Support Board.", "Info message": "Informacinis <PERSON>", "Initialize and display the chat widget and tickets only for members.": "Inicijuoti ir rodyti tik nariams skirtą pokalbio valdiklį ir bilietus.", "Initialize and display the chat widget only when the user is logged in.": "Inicijuoti ir rodyti pokalbių valdiklį tik tada, kai vartoto<PERSON> yra prisijun<PERSON>.", "Instance ID": "Atvejo ID", "Integrate OpenCart with {R} for real-time syncing of customers, order history access, and customer cart visibility.": "Integruokite OpenCart su {R}, kad gal<PERSON>tumėte sinchronizuoti klientus realiuoju laiku, pasiekti užsakymų istoriją ir matyti klientų krepšelį.", "Interval (sec)": "Intervalas (sek.)", "IP banning": "IP uždraudimas", "Label": "Etiketė", "Language": "Kalba", "Language detection": "<PERSON><PERSON><PERSON> a<PERSON>iki<PERSON>", "Language detection message": "Kalbos aptikimo pranešimas", "Last name": "Pa<PERSON><PERSON>", "Leave it blank if you don't know what this setting is! Entering an incorrect value will break the chat. Sets the main domain where chat is used to enable login and conversations sharing between the main domain and sub domains.": "Palik<PERSON> tu<PERSON>, jei <PERSON>, kas yra šis nustatymas! Įvedus neteisingą reikšmę, pokalbis bus nutrauktas. Nustato pagrindinį domeną, kuriame naudoja<PERSON> pokalbis, kad būtų galima įjungti prisijungimą ir bendrinti pokalbius tarp pagrindinio domeno ir antrinių domenų.", "Left": "Kair<PERSON>", "Left panel": "<PERSON><PERSON><PERSON>", "Left profile image": "Kairiojo profilio <PERSON>", "Let the bot to search on Google to find answers to user questions.": "Leiskite robotui i<PERSON>š<PERSON> „Google“, kad rastų atsakymus į vartotojų klausimus.", "Let the chatbot search on Google to find answers to user questions.": "Leiskite pokalbių robotui ieškoti „Google“, kad rastų atsakymus į vartotojų klausimus.", "Lets your users reach you via Twitter. Read and reply to messages sent to your Twitter account directly from {R}.": "Leidžia vartotojams susisiekti su jumis per „Twitter“. Skaitykite ir atsakykite į jūsų „Twitter“ paskyrą išsiųstus pranešimus tiesiai iš {R}.", "Lets your users reach you via WeChat. Read and reply to all messages sent to your WeChat official account directly from {R}.": "Leid<PERSON>ia vartotojams susisiekti su jumis per WeChat. Skaitykite ir atsakykite į visus pranešimus, išsiųstus į oficialią „WeChat“ paskyrą tiesiai iš {R}.", "Lets your users reach you via WhatsApp. Read and reply to all messages sent to your WhatsApp Business account directly from {R}.": "Leidžia vartotojams susisiekti su jumis per WhatsApp. Skaitykite ir atsakykite į visus pranešimus, išsiųstus į „WhatsApp Business“ paskyrą tiesiai iš {R}.", "Link each agent with the corresponding Slack user, so when an agent replies via Slack it will be displayed as the assigned agent.": "Susiekite kiekvieną agentą su atitinkamu „Slack“ vartotoju, todėl agentui atsakius per „S<PERSON>ck“, jis bus rodomas kaip priski<PERSON>s agentas.", "Link name": "Nuorod<PERSON> pavadin<PERSON>", "Login form": "Prisijungimo forma", "Login initialization": "Prisijungi<PERSON> in<PERSON>", "Login verification URL": "Prisijungi<PERSON> pat<PERSON><PERSON>", "Logit bias": "Logit <PERSON>", "Make a backup of your Dialogflow agent first. This operation can take several minutes.": "Pirmiausia sukurkite savo Dialogflow agento atsarginę kopiją. Ši operacija gali užtrukti kelias minutes.", "Make the registration phone field mandatory.": "Padaryti registracijos telefono lauką privalomą.", "Manage": "<PERSON><PERSON><PERSON><PERSON>", "Manage here the departments settings.": "Čia tvarkykite skyrių nustatymus.", "Manage the tags settings.": "Tvarkykite žymų nustatymus.", "Manifest file URL": "Manifest failo URL", "Manual": "<PERSON><PERSON><PERSON>", "Manual initialization": "<PERSON><PERSON><PERSON> in<PERSON>", "Martfury root directory path, e.g. /var/www/": "<PERSON><PERSON><PERSON>, pvz. /var/www/", "Martfury shop URL, e.g. https://shop.com": "Martfury parduotuvės URL, pvz., https://shop.com", "Max message limit": "Maks<PERSON>lus p<PERSON>š<PERSON> limitas", "Max tokens": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "Members only": "<PERSON><PERSON> nariai", "Members with an active paid plan only": "Tik aktyvų mokamą planą turintys nariai", "Message": "Pranešimas", "Message area": "Pranešimų sritis", "Message rewrite button": "<PERSON><PERSON><PERSON><PERSON>", "Message template": "<PERSON><PERSON><PERSON><PERSON>", "Message type": "<PERSON><PERSON><PERSON><PERSON>", "Messaging channels": "Pranešimų kanalai", "Messenger and Instagram settings": "Messenger ir „Instagram“ nustatymus", "Minify JS": "Sumažinti JS", "Minimal": "Minimal<PERSON>", "Model": "Modelis", "Multilingual": "Daugiakalbis", "Multilingual plugin": "Daugiakal<PERSON> papildinys", "Multilingual via translation": "Daugiakalbis vertimo būdu", "Multlilingual training sources": "Daugiakalbiai mokymo š<PERSON>i", "Name": "Vardas", "Namespace": "Vardų erdvė", "New conversation email": "Naujas pokalbio el. paštas", "New conversation notification": "Pranešimas apie naują pokalbį", "New ticket button": "<PERSON><PERSON><PERSON>", "Newsletter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "No delay": "<PERSON><PERSON><PERSON>", "No results found.": "<PERSON><PERSON><PERSON>.", "No, we don't ship in": "<PERSON><PERSON>, mes ne<PERSON>e", "None": "<PERSON>ė vienas", "Note data scraping": "Atkreipkite dėmesį į duomenų nubraukimą", "Notes": "Pastabos", "Notifications icon": "Pranešimų piktograma", "Notify the user when their message is sent outside of the scheduled office hours or all agents are offline.": "Praneš<PERSON><PERSON>, kai jo praneš<PERSON> iš<PERSON>ų<PERSON>s ne suplanuotomis darbo valandomis arba visi agentai yra neprisijungę.", "OA secret key": "OA slaptas raktas", "Offline message": "Praneš<PERSON><PERSON>", "Offset": "Užskaita", "On chat open": "<PERSON><PERSON><PERSON><PERSON>", "On page load": "Įkeliant puslapį", "One conversation per agent": "Vienas pokalbis vienam agentui", "One conversation per department": "Vienas pokalbis kiekviename skyriuje", "Online users notification": "Internetinių vartotojų pranešimas", "Only desktop": "Tik darbalaukis", "Only general questions": "<PERSON>ik bend<PERSON> k<PERSON>", "Only mobile devices": "Tik mobilieji įrenginiai", "Only questions related to your sources": "<PERSON><PERSON>, susij<PERSON> su jū<PERSON>ų šaltiniais", "Open automatically": "Atidaryti automatiškai", "Open chat": "At<PERSON><PERSON><PERSON> pokalbį", "Open the chat window automatically when a new message is received.": "Atidarykite pokalbių langą automatiškai, kai gaunate naują žinutę.", "OpenAI Assistants - Department linking": "OpenAI asistentai – skyrių su<PERSON>jimas", "OpenAI settings.": "OpenAI nustatymai.", "Optional link": "Neprival<PERSON> nuoroda", "Order webhook": "Užsisakykite „webhook“.", "Other": "<PERSON><PERSON>", "Outgoing SMTP server information.": "Siunčiama SMTP serverio informacija.", "Page ID": "Puslapio ID", "Page IDs": "Puslapių ID", "Page name": "Puslapio pavadinimas", "Page token": "Puslapio prieigos raktas", "Panel height": "<PERSON><PERSON>", "Panel name": "Skydelio pavadin<PERSON>s", "Panel title": "Skydelio pavadin<PERSON>s", "Panels arrows": "Skydelių <PERSON>", "Password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Perfex URL": "Perfex URL", "Performance optimization": "<PERSON><PERSON><PERSON><PERSON> opti<PERSON>", "Phone": "Telefonas", "Phone number ID": "Telefono numerio ID", "Phone required": "Reikalingas telefonas", "Place ID": "Vietos ID", "Placeholder text": "Vietos rezervavimo tekstas", "Play a sound for new messages and conversations.": "Leiskite naujų pranešimų ir pokalbių garsą.", "Popup message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Port": "Uostas", "Post Type slugs": "Post Type šliužai", "Presence penalty": "Bauda už buvimą", "Prevent admins from receiving email notifications.": "Neleiskite administratoriams gauti p<PERSON>ų el. paštu.", "Prevent agents from viewing conversations assigned to other agents. This setting is automatically enabled if routing or queue is active.": "Neleiskite agentams peržiūrėti pokalbių, priskirtų kitiems agentams. Šis nustatymas automatiškai įjungiamas, jei aktyvus maršrutas arba eilė.", "Prevent any abuse from users by limiting the number of messages sent to the chatbot from one device.": "Apribokite pranešimų, siunčiamų į pokalbių robotą iš vieno įrenginio, s<PERSON><PERSON><PERSON><PERSON>, kad išvengtumėte vartotojų piktnaudžiavimo.", "Primary color": "<PERSON><PERSON><PERSON><PERSON> spalva", "Priority": "Pirmenybė", "Privacy link": "Privatumo nuoroda", "Privacy message": "Privatumo <PERSON>", "Private chat": "<PERSON>ri<PERSON><PERSON> pokalbis", "Private chat linking": "Privataus pokalbio <PERSON>", "Private key": "<PERSON><PERSON><PERSON><PERSON> raktas", "Product IDs": "Produktų ID", "Product removed notification": "Pranešimas apie produkto pašalinimą", "Product removed notification - Email": "Pranešimas apie produkto pašalinimą – el", "Profile image": "<PERSON><PERSON><PERSON>", "Project ID": "Projekto ID", "Project ID or Agent Name": "Projekto ID arba agento vardas", "Prompt": "Ra<PERSON><PERSON><PERSON>", "Prompt - Message rewriting": "Raginimas <PERSON> <PERSON><PERSON><PERSON><PERSON>", "Protect the tickets area from spam and abuse with Google reCAPTCHA.": "Apsaugokite bilietų sritį nuo šlamšto ir piktnaudžiavimo „Google“ reCAPTCHA.", "Provide help desk support to your customers by including a ticket area, with all chat features included, on any web page in seconds.": "Teikkite pagalbos tarnybos palaikymą savo klientams per kelias sekundes įtraukdami bilietų sritį su visomis pokalbių funkcijomis bet kuriame tinklalapyje.", "Provider": "<PERSON><PERSON><PERSON><PERSON>", "Purchase button text": "Pirkimo my<PERSON> te<PERSON>", "Push notifications": "<PERSON><PERSON>", "Push notifications settings.": "Push pranešimų nustatymai.", "Queue": "Eilė", "Rating": "Įvertinimas", "Read and reply to messages sent from Google Search, Maps and brand-owned channels directly in {R}.": "Skaitykite ir atsakykite į pranešimus, siun<PERSON><PERSON><PERSON> iš „Google“ paieškos, Žemėlapių ir prekės ženklams priklausančių kanalų, tiesiogiai {R}.", "Read, manage and reply to all messages sent to your Facebook pages and Instagram accounts directly from {R}.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, tvarkykite ir atsakykite į visus pranešimus, siunčiamus į jūsų „Facebook“ puslapius ir „Instagram“ paskyras tiesiai iš {R}.", "Reconnect": "Prisijunkite iš naujo", "Redirect the user to the registration link instead of showing the registration form.": "Nukreipkite vartotoją į registracijos nuorodą, o ne rodydami registracijos formą.", "Redirect the user to the specified URL if the registration is required and the user is not logged in. Leave blank to use the default registration form.": "Nukreipkite vartotoją į nurodytą URL, jei reikalinga registracija ir vartotojas neprisijungęs. Palikite tuščią, jei norite naudoti numatytąją registracijos formą.", "Refresh token": "Atnaujinti prieigos rakt<PERSON>", "Register all visitors": "Registruok visus lankytojus", "Register all visitors automatically. When this option is not active, only the visitors that start a chat will be registered.": "Užregistruokite visus lankytojus automatiškai. Kai <PERSON> par<PERSON>, bus registruojami tik pokalbį pradėję lankytojai.", "Registration / Login": "Registracija / Prisijungimas", "Registration and login form": "Registracijos ir prisijungimo forma", "Registration fields": "Registracijos <PERSON>", "Registration form": "Registracijos forma", "Registration link": "Registracijos nuoroda", "Registration redirect": "Regis<PERSON>ci<PERSON>", "Rename the chat bot. Default is 'Bot'.": "Pervardykite pokalbių robotą. Numatytoji reikšmė yra „Bot“.", "Rename the visitor name prefix. Default is 'User'.": "Pervardykite lankytojo vardo priešdėlį. Numatytoji reikšmė yra „Vartotojas“.", "Repeat": "Pakartokite", "Repeat - admin": "<PERSON><PERSON><PERSON><PERSON> – admin", "Replace the admin login page message.": "Pakeiskite administratoriaus prisijungimo puslapio p<PERSON>.", "Replace the brand logo on the admin login page.": "Pakeiskite prekės ženklo logotipą administratoriaus prisijungimo pusla<PERSON>.", "Replace the header title with the user's first name and last name when available.": "<PERSON><PERSON> įmanoma, pakeiskite antraštė<PERSON> pavadin<PERSON> vartotojo vardu ir pavarde.", "Replace the top-left brand icon on the admin area and the browser favicon.": "Pakeiskite viršuje kairėje esančią prekės ženklo piktogramą administratoriaus srityje ir naršyklės piktogramą.", "Reply to user emails": "Atsakyti į vartotojų el", "Reply to user text messages": "Atsakyti į vartoto<PERSON> tekstinius p<PERSON>", "Reports": "Ataskaitos", "Reports area": "Ataskaitų sritis", "Request a valid Envato purchase code for registration.": "Registracijai paprašykite galiojančio Envato pirkimo kodo.", "Request the user to provide their email address and then send a confirmation email to the user.": "Paprašykite vartotojo nurodyti savo el. pa<PERSON>to ad<PERSON> ir tada nusiųskite vartotojui patvirtinimo el. la<PERSON>ą.", "Require phone": "Reikalauti telefono", "Require registration": "Re<PERSON><PERSON>ti registracijos", "Require the user registration or login before start a chat. To enable the login area the password field must be included.": "<PERSON><PERSON><PERSON> p<PERSON> pokalbį, re<PERSON>lau<PERSON>te vartotojo registracijos arba prisijungimo. Norint įjungti prisijungimo sritį, turi būti įtrauktas slapta<PERSON><PERSON><PERSON><PERSON>.", "Require the user registration or login in order to use the tickets area.": "<PERSON><PERSON> naudotis bi<PERSON> sritimi, re<PERSON>linga vartotojo registracija arba prisijungimas.", "Required": "<PERSON><PERSON><PERSON><PERSON>", "Response time": "<PERSON><PERSON><PERSON><PERSON>", "Restrict chat access by blocking IPs. List IPs with commas.": "Apribokite prieigą prie pokalbių blokuodami IP. Išvardykite IP adresus kableliais.", "Returning visitor message": "Grįžtančio lanky<PERSON>jo p<PERSON>s", "Rich messages": "Rich messages", "Rich messages are code snippets that can be utilized within a chat message. They can contain HTML code and are automatically rendered in the chat. Rich messages can be used with the following syntax: [rich-message-name]. There are a tonne of built-in rich messages to choose from.": "Rich messages yra kodo <PERSON>, kuri<PERSON>s galima panaudoti pokalbio pranešime. Juose gali būti HTML kodas ir jie automatiškai pateikiami pokalbyje. Rich messages gali būti naudojamas su tokia sintaksė: [rich-message-name]. <PERSON><PERSON><PERSON> rink<PERSON> iš daugybės įmontuotų rich messages.", "Right": "Teisingai", "Right panel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Routing": "<PERSON><PERSON><PERSON><PERSON>", "Routing if offline": "<PERSON><PERSON><PERSON><PERSON>, jei ne<PERSON>", "RTL": "RTL", "Save useful information like user country and language also for visitors.": "Išsaugokite lankytojams naudingą informaciją, pvz., vartotojo šalį ir kalbą.", "Saved replies": "Išsaugoti atsakymai", "Scheduled office hours": "Suplanuotas darbo laikas", "Search engine ID": "Paieškos variklio ID", "Second chat message": "<PERSON><PERSON><PERSON> poka<PERSON>", "Second reminder delay (hours)": "Antrojo priminimo delsa (valandomis)", "Secondary color": "<PERSON><PERSON><PERSON><PERSON> spalva", "Secret key": "<PERSON><PERSON><PERSON> raktas", "Send a message to allow customers to be notified when they can purchase a product they are interested in, but that is currently out of stock. You can use the following merge fields: {user_name}, {product_name}.": "Išsiųskite pranešimą, kad k<PERSON>ai būtų informuoti, kai jie gali įsigyti juos dominanči<PERSON> prekę, tačiau šiuo metu jos nėra sand<PERSON>ly<PERSON>. Galite naudoti šiuos sujungimo laukus: {user_name}, {product_name}.", "Send a message to new users when they create the first ticket. Text formatting and merge fields are supported.": "Siųsti pranešimą naujiems vartotojams, kai jie sukuria pirmąjį bilietą. Palaikomi teksto formatavimo ir sujungimo laukai.", "Send a message to new users when they visit the website for the first time.": "Siųsti pranešimą naujiems vartotojams, kai jie apsilanko svetainėje pirmą kartą.", "Send a message to the customer after a product has been removed from the cart. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.": "<PERSON><PERSON><PERSON><PERSON> pre<PERSON>ę i<PERSON>, išsiųskite pranešimą klientui. Galite naudoti šiuos sujungimo laukus ir daugiau: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.", "Send a message to the customers who complete a purchase asking to share the product they just bought. You can use the following merge fields and more: {product_name}, {user_name}.": "Nusiųskite praneš<PERSON>ą k<PERSON>, kurie už<PERSON>igia pirkimą, prašydami pasidalinti ką tik nusipirktu produktu. Galite naudoti šiuos sujungimo laukus ir daugiau: {product_name}, {user_name}.", "Send a message to the customers who complete a purchase. You can use the following merge fields and more: {coupon}, {product_names}, {user_name}.": "Nusiųskite pranešimą k<PERSON>, kurie užbaigia pirkimą. Galite naudoti šiuos sujungimo laukus ir daugiau: {coupon}, {product_names}, {user_name}.", "Send a message to the user when the agent archive the conversation.": "Siųsti praneš<PERSON>, kai agentas archyvuoja pokalbį.", "Send a message to users who visit the website again after at least 24 hours. You can use the following merge fields and more: {coupon}, {user_name}. See the docs for more details.": "Siųsti <PERSON>, k<PERSON><PERSON> v<PERSON><PERSON> apsilanko svetainėje p<PERSON> bent 24 val. Galite naudoti šiuos sujungimo laukus ir daugiau: {coupon}, {user_name}. Norėdami gauti daugiau informacijos, žr. dokumentus.", "Send a test agent notification email to verify email settings.": "Išsiųskite bandomojo agento pranešimo el. la<PERSON>, kad pat<PERSON>te el. pašto nustatymus.", "Send a test message to your Slack channel. This only tests the sending functionality of outgoing messages.": "Išsiųskite bandomąjį pranešimą savo „Slack“ kanalui. Tai tik patikrina siunčiamų pranešimų siuntimo funkcionalumą.", "Send a test user notification email to verify email settings.": "Išsiųskite bandomojo vartotojo pranešimo el. la<PERSON>, kad patvirtintum<PERSON>te el. pašto nustatymus.", "Send a text message to the provided phone number.": "Siųsti tekstinę žinutę nurodytu telefono numeriu.", "Send a user email notification": "Siųsti vartotojo pranešimą el. paštu", "Send a user text message notifcation": "Siųsti vartotojo teksto p<PERSON>", "Send a user text message notification": "Siųsti vartotojo teksto p<PERSON>", "Send an agent email notification": "Siųsti agento pranešimą el. paštu", "Send an agent text message notification": "Siųsti agento teksto p<PERSON>", "Send an agent user text notification": "Siųsti agento vartotojo tekstinį pranešimą", "Send an email notification to the provided email address.": "Siųsti el. laišką nurodytu el. pašto adresu.", "Send an email to an agent when a user replies and the agent is offline. An email is automatically sent to all agents for new conversations.": "Siųsti el. la<PERSON> agentui, kai vartoto<PERSON> atsako ir agentas yra neprisijungęs. El. laiškas automatiškai siunčiamas visiems agentams dėl naujų pokalbių.", "Send an email to the user when a new conversation is created.": "Siųsti el. la<PERSON>, kai sukuriamas naujas pokalbis.", "Send an email to the user when a new conversation or ticket is created": "Kai sukuriamas naujas pokalbis ar bilietas, siųskite el. la<PERSON>i", "Send an email to the user when an agent replies and the user is offline.": "Siųsti el. <PERSON>, kai agentas atsako ir vartotojas yra neprisijungęs.", "Send email": "Siųsti laišką", "Send login details to the specified URL and allow access only if the response is positive.": "Siųskite prisijungimo duomenis nurodytu URL ir leiskite prieigą tik tada, kai atsaky<PERSON> yra te<PERSON>.", "Send message": "Siųsti žinutę", "Send message to Slack": "Siųsti ž<PERSON><PERSON>", "Send message via enter button": "Siųsti žinutę s<PERSON>tel<PERSON> įvesties mygtuką", "Send text message": "Siųsti tekstinį pranešimą", "Send the message template to a WhatsApp number.": "Nusiųskite pranešimo šabloną WhatsApp numeriu.", "Send the message via the ENTER keyboard button.": "Išsiųskite pranešimą klaviatūros mygtuku ENTER.", "Send the user details of the registration form and email rich messages to Dialogflow.": "Išsiųskite registracijos formos naudotojo informaciją ir el. paštu rich messages į „Dialogflow“.", "Send the WhatsApp order details to the URL provided.": "Nusiųskite „WhatsApp“ užsakymo informaciją nurodytu URL.", "Send to user's email": "Siųsti į vartotojo el", "Send transcript to user's email": "Siųsti nuorašą į vartotojo el", "Send user details": "<PERSON><PERSON><PERSON><PERSON> vartotojo duomenis", "Sender": "<PERSON><PERSON><PERSON><PERSON>", "Sender email": "Siuntėjo el", "Sender name": "<PERSON><PERSON><PERSON><PERSON>", "Sender number": "<PERSON>unt<PERSON>jo numeris", "Sends a text message if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.": "Siunčia tekstinį pranešimą, jei nepa<PERSON> išsiųsti WhatsApp pranešimo. Galite naudoti tekstą ir šiuos sujungimo laukus: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.", "Sends a WhatsApp Template notification if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {recipient_name}, {recipient_email}.": "Siunčia WhatsApp šablono p<PERSON>, jei ne<PERSON> iš<PERSON>ųsti WhatsApp pranešimo. Galite naudoti tekstą ir šiuos sujungimo laukus: {conversation_url_parameter}, {recipient_name}, {recipient_email}.", "Service": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Service Worker path": "Service Worker kelias", "Service Worker URL": "Service Worker URL", "Set a dedicated Dialogflow agent for each department.": "Kiekvienam skyriui nustatykite skirtą Dialogflow agentą.", "Set a dedicated OpenAI Assistants for each department.": "Kiekvienam skyriui nustatykite specialius OpenAI asistentus.", "Set a dedicated Slack channel for each department.": "Kiekvienam skyriui nustatykite specialų „Slack“ kanalą.", "Set a profile image for the chat bot.": "Nustatykite pokalbių roboto profilio vaizdą.", "Set the articles panel title. Default is 'Help Center'.": "Nustatykite straipsnių skydelio pavadinimą. Numatytasis yra „Pagalbos centras“.", "Set the avatar image shown next to the message. It must be a JPG image of 1024x1024px with a maximum size of 50KB.": "Nustatykite pseudoportreto vaizdą, rod<PERSON><PERSON> p<PERSON>. Tai turi būti 1024 x 1024 pikselių JPG vaizdas, ma<PERSON><PERSON><PERSON> dyd<PERSON> – 50 KB.", "Set the chat language or translate it automatically to match the user language. Default is English.": "Nustatykite pokalbio kalbą arba išverskite ją automatiškai, kad ji atitiktų vartotojo kalbą. Pagal nutylėjimą yra anglų kalba.", "Set the currency symbol of the membership prices.": "Nustatykite narystės kainų valiutos simbolį.", "Set the currency symbol used by your system.": "Nustatykite valiutos simbolį, kurį naudoja jūsų sistema.", "Set the default departments for all tickets. Enter the department ID.": "Nustatykite numatytuosius visų bilietų skyrius. Įveskite skyriaus ID.", "Set the default email header that will be prepended to automated emails and direct emails.": "Nustatykite numatytąją el. pašto antraštę, kuri bus pridėta prie automatinių ir tiesioginių el. laiškų.", "Set the default email signature that will be appended to automated emails and direct emails.": "Nustatykite numatytąjį el. pa<PERSON><PERSON>, kuris bus pridėtas prie automatinių ir tiesioginių el. laiškų.", "Set the default form to display if the registraion is required.": "Nustatykite numatytą<PERSON>, kuri bū<PERSON> rod<PERSON>, jei reikalinga registracija.", "Set the default name to use for conversations without a name.": "Nustatykite numatytąjį pavadinimą, kuris bus naudojamas pokalbiams be pavadinimo.", "Set the default notifications icon. The icon will be used as a profile image if the user doesn't have one.": "Nustatykite numatytąją pranešimų piktogramą. Piktograma bus naudojama kaip profilio <PERSON>, jei varto<PERSON> jo neturi.", "Set the default office hours for when agents are shown as available. These settings are also used for all other settings that rely on office hours.": "Nustatykite numatytąsias darbo valan<PERSON>, kai agentai rodomi kaip p<PERSON>. Šie nustatymai taip pat naudojami visiems kitiems nustatymams, kurie p<PERSON>lauso nuo darbo valandų.", "Set the default username to use in bot messages and emails when the user doesn't have a name.": "Nustatykite numatytąjį vartotojo vardą, kuris bus naudojamas roboto pranešimuose ir el. la<PERSON>, kai vartotojas neturi vardo.", "Set the header appearance.": "Nustatykite antraštės iš<PERSON>izdą.", "Set the maximum height of the tickets panel.": "Nustatykite maksimalų bilietų skydelio aukštį.", "Set the multilingual plugin you're using, or leave it disabled if your site uses only one language.": "Nustatykite naudojamą daugiakalbį papildinį arba palikite jį išjungtą, jei svetain<PERSON> naudojama tik viena kalba.", "Set the offline status automatically when the agent or admin remains inactive in the admin area for at least 10 minutes.": "Automatiškai nustatykite neprisijungus b<PERSON>, kai agentas arba administratorius lieka neaktyvus administratoriaus s<PERSON>je bent 10 minučių.", "Set the position of the chat widget.": "Nustatykite pokalbio valdiklio padėtį.", "Set the primary color of the admin area.": "Nustatykite pagrindinę administravimo srities spalvą.", "Set the primary color of the chat widget.": "Nustatykite pagrindinę pokalbių valdiklio spalvą.", "Set the secondary color of the admin area.": "Nustatykite antrinę administravimo srities spalvą.", "Set the secondary color of the chat widget.": "Nustatykite antrinę pokalbio valdiklio spalvą.", "Set the tertiary color of the chat widget.": "Nustatykite trečiąją pokalbio valdiklio spalvą.", "Set the title of the administration area.": "Nustatykite administravimo srities pavadinimą.", "Set the title of the conversations panel.": "Nustatykite pokalbių skydelio pavadinimą.", "Set the UTC offset of the office hours timetable. The correct value can be negative, and it's generated automatically once you click this input field, if it's empty.": "Nustatykite darbo valandų grafiko UTC poslinkį. Teisinga re<PERSON>šmė gali būti neigiama ir ji sugeneruojama automatiškai, kai spustel<PERSON>site šį įvesties lauką, jei jis tu<PERSON>.", "Set which actions to allow agents.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kuriuos veiksmus leisti agentams.", "Set which actions to allow supervisors.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kokius veiksmus leisti prižiūrėtojams.", "Set which user details to send to the main channel. Add comma separated values.": "<PERSON>ustatyk<PERSON>, kurią vartotojo informaciją siųsti į pagrindinį kanalą. Pridėkite kableliais atskirtas vertes.", "Settings area": "Nustatymų sritis", "settings information": "nustatymų informacija", "Shop": "Parduotuvė", "Show": "<PERSON><PERSON><PERSON>", "Show a browser tab notification when a new message is received.": "Rod<PERSON><PERSON> narš<PERSON>, kai gaunamas naujas praneš<PERSON>.", "Show a desktop notification when a new message is received.": "<PERSON><PERSON><PERSON> darbalauki<PERSON>, kai gaunamas naujas praneš<PERSON>s.", "Show a notification and play a sound when a new user is online.": "<PERSON><PERSON><PERSON> p<PERSON> ir paleisti garsą, kai prisijungia naujas vartotojas.", "Show a pop-up notification to all users.": "Rodyti iššokantįjį pranešimą visiems vartotojams.", "Show profile images": "<PERSON><PERSON><PERSON> profilio v<PERSON>", "Show sender's name": "<PERSON><PERSON><PERSON> var<PERSON>", "Show the agents menu in the dashboard and force the user to choose an agent to start a conversation.": "Informacijos suvestinėje parodykite agentų meniu ir priverskite vartotoją pasirinkti agentą pokalbiui pradėti.", "Show the articles panel on the chat dashboard.": "Rodyti straipsnių skydelį pokalbio informacijos suvestinėje.", "Show the categories instead of the articles list.": "<PERSON><PERSON><PERSON> ka<PERSON>gor<PERSON>, o ne straipsnių sąrašą.", "Show the follow up message when a visitor add an item to the cart. The message is sent only if the user has not provided an email yet.": "Rodyti tolesnį praneš<PERSON>ą, kai la<PERSON> įdeda prekę į krepšelį. Pranešimas siunč<PERSON> tik tuo atveju, jei varto<PERSON> dar nepateik<PERSON> el.", "Show the list of all Slack channels.": "Rodyti visų „Slack“ kanalų sąrašą.", "Show the profile image of agents and users within the conversation.": "Rodyti agentų ir naudotojų profilio vaizdą pokalbyje.", "Show the sender's name in every message.": "Kiekviename pranešime nurodykite siuntėjo vardą.", "Single label": "Viena etiketė", "Single phone country code": "Vieno telefono šalies kodas", "Site key": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "Slug": "Šliužas", "Social share message": "Socialinio bendrinimo p<PERSON>š<PERSON>s", "Sort conversations by date": "Rūšiuoti pokalbius pagal datą", "Sound": "<PERSON><PERSON><PERSON>", "Sound settings": "Gars<PERSON> n<PERSON>", "Sounds": "Garsai", "Sounds - admin": "<PERSON><PERSON><PERSON> – admin", "Source links": "Šaltinių nuorodos", "Speech recognition": "<PERSON><PERSON><PERSON>", "Spelling correction": "<PERSON><PERSON><PERSON><PERSON>", "Starred tag": "Žvaigždute pažymėta žyma", "Start importing": "Pradėki<PERSON> import<PERSON>ti", "Store name": "Parduotu<PERSON><PERSON><PERSON> p<PERSON>", "Subject": "<PERSON><PERSON>", "Subscribe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Subscribe users to your preferred newsletter service when they provide an email.": "Prenumeruokite naudotojams pageidaujamą naujienlaiškio paslaug<PERSON>, kai jie pateikia el.", "Subtract the offset value from the height value.": "<PERSON><PERSON><PERSON> vertės atimkite poslinkio vertę.", "Success message": "Sėk<PERSON><PERSON><PERSON>", "Supervisors": "Prižiūrėtojai", "Support Board path": "Support Board kelias", "Sync admin and staff accounts with Support Board. Staff users will be registered as agents, while admins as admins. Only new users will be imported.": "Sinchronizuokite administratoriaus ir darbuotojų paskyras su Support Board. Personalo vartotojai bus registruojami kaip agent<PERSON>, o administratoriai – kaip administratoriai. Bus importuojami tik nauji vartotojai.", "Sync all contacts of all clients with Support Board. Only new contacts will be imported.": "Sinchronizuoti visus visų klientų kontaktus su Support Board. Bus importuojami tik nauji kontaktai.", "Sync all users with Support Board. Only new users will be imported.": "Sinchronizuoti visus naudotojus su Support Board. Bus importuojami tik nauji var<PERSON>i.", "Sync all WordPress users with Support Board. Only new users will be imported.": "Sinchronizuokite visus „WordPress“ naudotojus su Support Board. Bus importuojami tik nauji <PERSON>i.", "Sync knowledge base articles with Support Board. Only new articles will be imported.": "Sinchronizuokite žinių bazės straipsnius su Support Board. Bus importuojami tik nauji straipsniai.", "Sync mode": "Sinchronizavimo <PERSON>", "Synchronization": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Synchronize": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Synchronize customers, enable ticket and chat support for subscribers only, view subscription plans in the admin area.": "Sinchronizuokite klientus, įgalinkite bilietų ir pokalbių palaikymą tik abonentams, peržiūrėkite prenumeratos planus administravimo srityje.", "Synchronize emails": "Sinchronizuoti el", "Synchronize Entities": "Sinchronizuoti Entities", "Synchronize Entities now": "Sinchronizuoti Entities dabar", "Synchronize now": "Sinchron<PERSON><PERSON><PERSON> da<PERSON>", "Synchronize users": "Sin<PERSON><PERSON><PERSON><PERSON><PERSON>", "Synchronize your customers in real-time, chat with them and boost their engagement, or provide a better and faster support.": "Sinchronizuokite savo klientus realiuoju laiku, kalbėkite su jais ir padidinkite jų įsitraukimą arba suteikite geresnę ir greitesnę pagalbą.", "Synchronize your Messenger and Instagram accounts.": "Sinchronizuokite savo Messenger ir Instagram paskyras.", "Synchronize your Perfex customers in real-time and let them contact you via chat! View profile details, proactively engage them, and more.": "Sinchronizuokite savo „Perfex“ klientus realiuoju laiku ir leiskite jiems susisiekti su jumis per pokalbį! Peržiūrėkite išsamią profilio informaciją, aktyviai įtraukite juos ir dar daugiau.", "Synchronize your WhatsApp Cloud API account.": "Sinchronizuokite savo WhatsApp Cloud API paskyrą.", "System requirements": "<PERSON>ste<PERSON>", "Tags": "<PERSON><PERSON><PERSON>", "Tags settings": "Žymų nustatymai", "Template default language": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, kai agent<PERSON> at<PERSON>, <PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON> na<PERSON> te<PERSON>, HTML ir šiuos sujungimo laukus: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Template for the email sent to the user when a new conversation is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}.": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, kai su<PERSON><PERSON> nau<PERSON> pokalbis, šab<PERSON><PERSON>. Galite naudoti te<PERSON>t<PERSON>, HTML ir šiuos sujungimo laukus: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}.", "Template for the email sent to the user when a new conversation or ticket is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}.": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, kai su<PERSON>ria<PERSON> naujas pokalbis arba bilietas, šablonas. Galite naudoti te<PERSON>, HTML ir šiuos sujungimo laukus: {conversation_url_parameter}, {user_name}, {message}, {attachments}.", "Template languages": "Šablonų kalbos", "Template name": "Šablono pavadinimas", "Template of the admin notification email. You can use text, HTML, and the following merge field and more: {carts}. Enter the email you want to send notifications to in the email address field.": "Administratoriaus pranešimo el. pa<PERSON>to <PERSON>. Galite naudoti te<PERSON>, HTML ir šį sujungimo lauką ir dar daugiau: {carts}. El. pašto adreso laukelyje įveskite el. pašto ad<PERSON>, kuriam norite siųsti prane<PERSON>.", "Template of the email sent to the customer after a product has been removed from the cart. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON> po to, kai pre<PERSON> buvo pa<PERSON><PERSON>a i<PERSON>, <PERSON><PERSON><PERSON><PERSON>. Galite naudoti tekstą, HTML ir šiuos sujungimo laukus ir dar daugiau: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the first notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "Pirmojo pranešimo el. pašto šablonas. Galite naudoti tekstą, HTML ir šiuos sujungimo laukus ir dar daugiau: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the second notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "Antrojo pranešimo el. laiško šablonas. Galite naudoti tekstą, HTML ir šiuos sujungimo laukus ir dar daugiau: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the waiting list notification email. You can use text, HTML, and the following merge field and more: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}.": "Pranešimo a<PERSON> laukiančiųjų sąrašą el. pašto šabloną. Galite naudoti tekstą, HTML ir šį sujungimo lauką ir dar daugiau: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}.", "Terms link": "Sąlygų nuoroda", "Tertiary color": "<PERSON><PERSON><PERSON><PERSON> spalva", "Test Slack": "Išbandykite Slack", "Test template": "<PERSON><PERSON><PERSON>", "Text": "Tekstas", "Text message fallback": "Atsarginis te<PERSON>", "Text message notifications": "Pranešimai tekstinėmis žinutėmis", "Text messages": "<PERSON><PERSON><PERSON>", "The product is not in the cart.": "Prekė<PERSON> n<PERSON>.", "The workspace name you are using to synchronize Slack.": "Vardas workspace, kurį naudojate sinchronizuodami „Slack“.", "This is your main Slack channel ID, which is usually the #general channel. You will get this code by completing the Slack synchronization.": "Tai yra jūs<PERSON> pagrindinis „Slack“ kanalo ID, kuris paprastai yra #general kanalas. Šį kodą gausite užbaigę Slack sinchronizavimą.", "This returns the Support Board path of your server.": "Tai grąžina jūsų serverio Support Board kelią.", "Ticket custom fields": "Bilietų pasirinktiniai laukai", "Ticket email": "Bilietų el", "Ticket field names": "Bilietų laukų pavadinimai", "Ticket fields": "Bilietų laukeliai", "Ticket only": "Tik bilietas", "Ticket products selector": "Bilietų prekių parinkėjas", "Title": "Pavadinimas", "Top": "Į viršų", "Top bar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> juosta", "Training via cron job": "<PERSON><PERSON><PERSON> naudo<PERSON> cron darb<PERSON>", "Transcript": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transcript settings.": "Nuoraš<PERSON> nustatym<PERSON>.", "Trigger": "Triger<PERSON>", "Trigger the Dialogflow Welcome Intent for new visitors when the welcome message is active.": "Suaktyvinkite dialogo eigą Welcome Intent naujiems lankytojams, kai pasveikinimo pranešimas yra aktyvu<PERSON>.", "Troubleshoot": "Trikčių šalinimas", "Troubleshoot problems": "Spręsti problemas", "Twilio settings": "„<PERSON><PERSON><PERSON>“ nustatymai", "Twilio template": "<PERSON><PERSON><PERSON>", "Unsubscribe": "Atsisakyti prenumeratos", "Upload attachments to Amazon S3.": "Įkelkite priedus į Amazon S3.", "Usage Limit": "<PERSON><PERSON><PERSON><PERSON>", "Use this option to change the PWA icon. See the docs for more details.": "Naudokite šią parinktį norėdami pakeisti PWA piktogramą. Norėdami gauti daugiau informacijos, žr. dokumentus.", "User details": "Vartotojo informacija", "User details in success message": "Vartotojo informacija sėkmės p<PERSON>", "User email notifications": "Vartotojo el. paš<PERSON>i", "User login form information.": "Vartotojo prisijungimo formos informacija.", "User message template": "Vartotojo prane<PERSON>", "User name as title": "Vartotojo vardas kaip pavadin<PERSON>s", "User notification email": "Vartotojo pranešimo el", "User registration form information.": "Vartotojo registracijos formos informacija.", "User roles": "Vartotojų vaidmenys", "User system": "Vartotojo sistema", "Username": "<PERSON><PERSON><PERSON><PERSON> var<PERSON>", "Users and agents": "Vartotojai ir <PERSON>ai", "Users area": "Vartotojų sritis", "Users only": "<PERSON><PERSON>", "Users table additional columns": "Vartotojai pateikia papildomų stulpelių", "UTC offset": "UTC poslinkis", "Variables": "<PERSON><PERSON><PERSON><PERSON>", "View channels": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kanal<PERSON>", "View unassigned conversations": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nepriskirtus pokalbius", "Visibility": "<PERSON><PERSON><PERSON><PERSON>", "Visitor default name": "<PERSON><PERSON><PERSON><PERSON> numaty<PERSON><PERSON> vardas", "Visitor name prefix": "<PERSON><PERSON><PERSON><PERSON> var<PERSON>", "Volume": "<PERSON><PERSON><PERSON><PERSON>", "Volume - admin": "<PERSON><PERSON><PERSON><PERSON> – admin", "Waiting list": "Laukiančiųj<PERSON> są<PERSON>ša<PERSON>", "Waiting list - Email": "Laukiančiųj<PERSON> s<PERSON>ša<PERSON> – el", "Webhook URL": "Webhook URL", "Webhooks": "Webhooks", "Webhooks are information sent in background to a unique URL defined by you when something happens.": "Žiniatinklio kabliukai yra informacija, siunčiama fone į unikalų URL, kurį nustatote, kai kas nors nutinka.", "Website": "<PERSON>o s<PERSON>", "WeChat settings": "„WeChat“ nustatymai", "Welcome message": "Sveikinimo žinutė", "Whmcs admin URL": "Whmcs administratoriaus URL", "Whmcs admin URL. Ex. https://example.com/whmcs/admin/": "Whmcs administratoriaus URL. Pvz. https://example.com/whmcs/admin/", "WordPress registration": "WordPress registracija", "Yes, we ship in": "Taip, pristatome", "You haven't placed an order yet.": "<PERSON>pateik<PERSON> užsakymo.", "You will get this code by completing the Dialogflow synchronization.": "Šį kodą gausite užbaigę Dialogflow sinchronizavimą.", "You will get this code by completing the Slack synchronization.": "Šį kodą gausite užbaigę Slack sinchronizavimą.", "You will get this information by completing the synchronization.": "<PERSON><PERSON>ą informaciją gausite užbaigę sinchronizavimą.", "Your cart is empty.": "<PERSON>ū<PERSON><PERSON> krepš<PERSON> t<PERSON>.", "Your turn message": "Jūsų eilė<PERSON>", "Your username": "<PERSON><PERSON><PERSON><PERSON>", "Your WhatsApp catalogue details.": "<PERSON>šsami j<PERSON>ų WhatsApp katalogo informacija.", "Zendesk settings": "Zendesk nustatymai", "Smart Reply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "This returns your Support Board URL.": "Tai grąžina jūsų Support Board URL.", "Update conversation department": "Atnaujinkite pokalbių skyrių"}
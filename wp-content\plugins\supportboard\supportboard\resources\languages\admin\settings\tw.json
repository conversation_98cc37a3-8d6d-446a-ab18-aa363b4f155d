{"{product_name} has no {product_attribute_name} variants.": "{product_name} 沒有 {product_attribute_name} 變體。", "360dialog settings": "360度對話框設置", "360dialog template": "360度對話框模板", "Abandoned cart notification": "廢棄購物車通知", "Abandoned cart notification - Admin email": "放棄購物車通知 - 管理員電子郵件", "Abandoned cart notification - First email": "廢棄購物車通知 - 第一封電子郵件", "Abandoned cart notification - Second email": "廢棄購物車通知 - 第二封電子郵件", "Accept button text": "接受按鈕文字", "Account SID": "帳戶SID", "Activate the Right-To-Left (RTL) reading layout for the admin area.": "啟動管理區域的從右到左 (RTL) 閱讀佈局。", "Activate the Right-To-Left (RTL) reading layout.": "啟動從右到左 (RTL) 閱讀佈局。", "Activate the Slack integration.": "啟動 Slack 整合。", "Activate the Zendesk integration": "激活 Zendesk 集成", "Activate this option if you don't want to translate the settings area.": "如果您不想翻譯設定區域，請啟動此選項。", "Active": "積極的", "Active eCommerce CMS URL. Ex. https://shop.com/": "Active eCommerce CMS 網址。前任。 https://shop.com/", "Active eCommerce URL": "Active eCommerce 網址", "Active for agents": "代理活躍", "Active for users": "對用戶活躍", "Active webhooks": "活動網路鉤子", "Add a delay (ms) to the bot's responses. Default is 2000.": "為機器人的反應添加延遲（毫秒）。預設值為 2000。", "Add a link to the articles button.": "新增文章按鈕的連結。", "Add and manage additional support departments.": "新增和管理其他支援部門。", "Add and manage saved replies that can be used by agents in the chat editor. Saved replies can be printed by typing # followed by the reply name plus space. Use \\n to do a line break.": "新增和管理已儲存的回复，可供代理在聊天編輯器中使用。可以鍵入 # 後面跟著回應名稱加空格來列印已儲存的回應。使用 \\n 進行換行。", "Add and manage tags.": "新增和管理標籤。", "Add comma separated WordPress user roles. The Support Board administration area will be available for new roles, in addition to the default one: editor, administrator, author.": "新增逗號分隔的 WordPress 使用者角色。除了預設角色之外，Support Board 管理區域也可供新角色使用：編輯者、管理員、作者。", "Add custom fields to the new ticket form.": "將自訂欄位新增至新的工單表單。", "Add custom fields to the user profile details.": "將自訂欄位新增至使用者個人資料詳細資料。", "Add Intents": "添加Intents", "Add Intents to saved replies": "將 Intents 加入已儲存的回覆中", "Add WhatsApp phone number details here.": "在此處新增 WhatsApp 電話號碼詳細資訊。", "Adjust sound settings.": "調整聲音設定。", "Adjust the chat button position. Values are in px.": "調整聊天按鈕位置。值以 px 為單位。", "Admin icon": "管理圖示", "Admin IDs": "管理員 ID", "Admin login logo": "管理員登入標誌", "Admin login message": "管理員登入訊息", "Admin notifications": "管理員通知", "Admin title": "行政人員職稱", "Agent area": "代理專區", "Agent details": "代理詳情", "Agent email notifications": "代理電子郵件通知", "Agent ID": "代理 ID", "Agent linking": "代理連結", "Agent message template": "代理訊息模板", "Agent notification email": "代理通知電子郵件", "Agent privileges": "代理特權", "Agents": "代理商", "Agents and admins tab": "代理和管理員選項卡", "Agents menu": "代理選單", "Agents only": "僅限代理商", "All": "全部", "All channels": "所有頻道", "All messages": "所有訊息", "All questions": "所有問題", "Allow duplicate emails and phone numbers": "允許重複的電子郵件和電話號碼", "Allow only extended licenses": "僅允許擴展許可證", "Allow only one conversation": "只允許一次對話", "Allow only one conversation per user.": "每個用戶僅允許一次對話。", "Allow registration with an email and a phone number already registered.": "允許使用已註冊的電子郵件和電話號碼進行註冊。", "Allow the chatbot to reply to the user's emails if the answer is known and email piping is active.": "如果答案已知且電子郵件管道處於活動狀態，則允許聊天機器人回覆使用者的電子郵件。", "Allow the chatbot to reply to the user's text messages if the answer is known.": "如果答案已知，則允許聊天機器人回覆用戶的簡訊。", "Allow the user to archive a conversation and hide archived conversations.": "允許使用者存檔對話並隱藏已存檔的對話。", "Allow users to contact you via their favorite messaging apps.": "允許用戶透過他們最喜歡的訊息應用程式與您聯繫。", "Allow users to select a product on ticket creation.": "允許使用者在建立票證時選擇產品。", "Always all messages": "始終是所有訊息", "Always incoming messages only": "始終僅接收傳入訊息", "Always sort conversations by date in the admin area.": "始終在管理區域中按日期對對話進行排序。", "API key": "API金鑰", "Append the registration user details to the success message.": "將註冊用戶詳細資訊附加到成功訊息中。", "Apply a custom background image for the header area.": "為標題區域套用自訂背景圖像。", "Apply changes": "應用程式變更", "Apply to": "適用於", "Archive all user channels in the Slack app. This operation may take a long time to complete. Important: All of your slack channels will be archived.": "檔案 Slack 應用程式中的所有使用者頻道。此操作可能需要很長時間才能完成。重要提示：您的所有 Slack 頻道都將存檔。", "Archive automatically the conversations marked as read every 24h.": "每 24 小時自動存檔標記為已讀的對話。", "Archive channels": "存檔頻道", "Archive channels now": "立即存檔頻道", "Articles": "文章", "Articles button link": "文章按鈕連結", "Articles title": "文章標題", "Artificial Intelligence": "人工智慧", "Assign a department to all conversations started from Google Business Messages. Enter the department ID.": "為從 Google Business Messages 發起的所有對話指派一個部門。輸入部門 ID。", "Assign a department to all conversations started from Twitter. Enter the department ID.": "為從 Twitter 發起的所有對話分配一個部門。輸入部門 ID。", "Assign a department to all conversations started from Viber. Enter the department ID.": "為從 Viber 發起的所有對話分配一個部門。輸入部門 ID。", "Assign a department to all conversations started from WeChat. Enter the department ID.": "為所有從微信發起的對話分配一個部門。輸入部門 ID。", "Assign different departments to conversations started from different Google Business Messages locations. This setting overrides the default department.": "將不同部門指派給從不同 Google Business Messages 位置發起的對話。此設定會覆蓋預設部門。", "Assistant": "助理", "Assistant ID": "助理ID", "Attachments list": "附件清單", "Audio file URL - admin": "音訊檔案 URL - 管理", "Automatic": "自動的", "Automatic human takeover": "自動人工接管", "Automatic translation": "自動翻譯", "Automatic updates": "自動更新", "Automatically archive conversations": "自動存檔對話", "Automatically assigns a department based on the user's active plans. Insert -1 as plan ID for users without any plan.": "根據使用者的活動計劃自動分配部門。對於沒有任何計劃的用戶，插入 -1 作為計劃 ID。", "Automatically check and install new updates. A valid Envato Purchase Code and valid apps's license keys are required.": "自動檢查並安裝新更新。需要有效的 Envato 購買代碼和有效的應用程式許可證金鑰。", "Automatically collapse the profile details panel, and other panels, of the admin area.": "自動折疊管理區域的個人資料詳細資料面板和其他面板。", "Automatically create a department for each website and route the conversations of each website to the right department. This setting requires a WordPress Multisite installation.": "自動為每個網站建立一個部門，並將每個網站的對話路由到正確的部門。此設定需要安裝 WordPress 多網站。", "Automatically send cart reminders to customers with products in their carts. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "自動向購物車中有產品的顧客發送購物車提醒。您可以使用下列合併欄位及更多欄位：{coupon}、{discount_price}、{original_price}、{product_names}、{user_name}。", "Automatically sync Zendesk customers with Support Board, view Zendesk tickets, or create new ones without leaving Support Board.": "自動將 Zendesk 客戶與 Support Board 同步、查看 Zendesk 票證或建立新票證，而無需離開 Support Board。", "Automatically synchronize products, categories, tags, and more with Dialogflow, and enable the bot to answer autonomously to questions related to your shop.": "自動將產品、類別、標籤等與 Dialogflow 同步，並使機器人能夠自主回答與您的商店相關的問題。", "Automatically translate admin area": "自動翻譯管理區域", "Automatically translate the admin area to match the agent profile language or browser language.": "自動翻譯管理區域以符合代理程式設定檔語言或瀏覽器語言。", "Avatar image": "頭像圖片", "Away mode": "離開模式", "Before initiating the chat, the user must accept a privacy message in order to gain access.": "在開始聊天之前，用戶必須接受隱私訊息才能獲得存取權限。", "Birthday": "生日", "Body parameters": "機身參數", "Bot name": "機器人名稱", "Bot profile image": "機器人個人資料圖片", "Bot response delay": "機器人回應延遲", "Bottom": "底部", "Brand": "品牌", "Built-in chat button icons": "內建聊天按鈕圖標", "Business Account ID": "企業帳號 ID", "Button action": "按鈕動作", "Button name": "按鈕名稱", "Button text": "按鈕文字", "Cancel button text": "取消按鈕文字", "Cart": "大車", "Cart follow up message": "購物車跟進消息", "Catalogue details": "目錄詳情", "Catalogue ID": "目錄號", "Change the chat button image with a custom one.": "將聊天按鈕圖像變更為自訂圖像。", "Change the default field names.": "更改預設欄位名稱。", "Change the message text in the header area of the chat widget. This text will be replaced by the agent headline once the first reply is sent.": "變更聊天小工具標題區域中的訊息文字。發送第一個回覆後，此文字將被代理標題取代。", "Change the title text in the header area of the chat widget. This text will be replaced by the agent's name once the first reply is sent.": "變更聊天小工具標題區域中的標題文字。發送第一個回覆後，此文字將替換為客服人員的姓名。", "Channels": "頻道", "Channels filter": "頻道過濾器", "Chat": "聊天", "Chat and admin": "聊天和管理", "Chat background": "聊天背景", "Chat button icon": "聊天按鈕圖標", "Chat button offset": "聊天按鈕偏移", "Chat message": "聊天訊息", "Chat only": "僅限聊天", "Chat position": "聊天位置", "Chatbot": "聊天機器人", "Chatbot mode": "聊天機器人模式", "Chatbot training": "聊天機器人培訓", "Check Requirements": "檢查要求", "Check the server configurations and make sure it has all the requirements.": "檢查伺服器配置並確保其滿足所有要求。", "Checkout": "查看", "Choose a background texture for the chat header and conversation area.": "為聊天標題和對話區域選擇背景紋理。", "Choose where to display the chat. Enter the values separated by commas.": "選擇顯示聊天的位置。輸入以逗號分隔的值。", "Choose which fields to disable from the tickets area.": "選擇要從票證區域停用哪些欄位。", "Choose which fields to include in the new ticket form.": "選擇要包含在新工單表單中的欄位。", "Choose which optional fields to include in the registration form. Name, email, and password fields are all included by default.": "選擇要包含在登錄中的可選欄位。預設情況下，姓名、電子郵件和密碼欄位均包含在內。", "Choose which user system the front-end chat will use to register and log in users.": "選擇前端聊天將使用哪個使用者係統來註冊和登入使用者。", "City": "城市", "Click the button to start the Dialogflow synchronization.": "點選按鈕開始 Dialogflow 同步。", "Click the button to start the Slack synchronization. Localhost cannot and does not receive messages. Log in with another account or as a visitor to perform your tests.": "點選按鈕開始 Slack 同步。本機不能也不會接收訊息。使用其他帳戶或以訪客身分登入以執行測試。", "Client email": "客戶信箱", "Client ID": "客戶ID", "Client token": "客戶端令牌", "Close chat": "關閉聊天", "Close message": "關閉訊息", "Cloud API numbers": "雲端 API 編號", "Cloud API settings": "雲端API設定", "Cloud API template fallback": "雲端 API 範本回退", "Collapse panels": "折疊面板", "Color": "顏色", "Communicate with your users right from Slack. Send and receive messages and attachments, use emojis, and much more.": "直接透過 Slack 與您的用戶進行交流。發送和接收訊息和附件、使用表情符號等等。", "Company": "公司", "Concurrent chats": "並發聊天", "Configuration URL": "設定網址", "Confirm button text": "確認按鈕文字", "Confirmation message": "確認訊息", "Connect smart chatbots and automate conversations by using one of the most advanced forms of artificial intelligence in the world.": "使用世界上最先進的人工智慧形式之一連接智慧聊天機器人並自動進行對話。", "Connect stores to agents.": "將商店與代理商連接起來。", "Connect your Telegram bot to Support Board to read and reply to all messages sent to your Telegram bot directly in Support Board.": "將您的 Telegram 機器人連接到 Support Board，以閱讀並回覆直接在 Support Board 中發送到 Telegram 機器人的所有訊息。", "Connect your Viber bot to Support Board to read and reply to all messages sent to your Viber bot directly in Support Board.": "將您的 Viber 機器人連接到 Support Board，以閱讀並回覆直接在 Support Board 中發送到您的 Viber 機器人的所有訊息。", "Content": "內容", "Conversation profile": "對話簡介", "Conversations data": "對話數據", "Convert all emails": "轉換所有電子郵件", "Cookie domain": "<PERSON><PERSON> 域", "Country": "國家", "Coupon discount (%)": "優惠券折扣 (%)", "Coupon expiration (days)": "優惠券有效期限（天）", "Coupon expiration (seconds)": "優惠券有效期限（秒）", "Create a WordPress user upon registration.": "註冊後建立 WordPress 用戶。", "Create Intents now": "立即建立 Intents", "Currency symbol": "貨幣符號", "Custom CSS": "自訂CSS", "Custom fields": "自訂字段", "Custom JS": "自訂JS", "Custom model ID": "自訂型號 ID", "Custom parameters": "自訂參數", "Dashboard display": "儀表板顯示", "Dashboard title": "儀表板標題", "Database details": "資料庫詳細信息", "Database host": "資料庫主機", "Database name": "資料庫名稱", "Database password": "資料庫密碼", "Database prefix": "資料庫前綴", "Database user": "資料庫用戶", "Decline button text": "拒絕按鈕文字", "Declined message": "拒絕留言", "Default": "預設", "Default body text": "預設正文", "Default conversation name": "預設對話名稱", "Default department": "預設部門", "Default department ID": "預設部門 ID", "Default form": "預設形式", "Default header text": "預設標題文本", "Delay (ms)": "延遲（毫秒）", "Delete all leads and all messages and conversations linked to them.": "刪除所有潛在客戶以及與其連結的所有訊息和對話。", "Delete conversation": "刪除對話", "Delete leads": "刪除潛在客戶", "Delete message": "刪除留言", "Delete training": "刪除訓練", "Delimiter": "分隔符", "Department": "部門", "Department ID": "部門編號", "Departments": "部門", "Departments settings": "部門設定", "Desktop notifications": "桌面通知", "Dialogflow - Department linking": "Dialogflow - 部門鏈接", "Dialogflow chatbot": "Dialogflow 聊天機器人", "Dialogflow edition": "Dialogflow版本", "Dialogflow Intent detection confidence": "Dialogflow 意圖偵測置信度", "Dialogflow location": "Dialogflow 位置", "Dialogflow spelling correction": "Dialogflow 拼字更正", "Dialogflow welcome Intent": "Dialogflow 歡迎意圖", "Disable agents check": "禁用代理檢查", "Disable and hide the chat widget if all agents are offline.": "如果所有客服人員均處於離線狀態，則停用並隱藏聊天小工具。", "Disable and hide the chat widget outside of scheduled office hours.": "在預定辦公時間之外停用並隱藏聊天小工具。", "Disable any features that you don't need.": "停用任何您不需要的功能。", "Disable auto-initialization of the chat widget. When this setting is active you must initialize the chat widget with a custom JavaScript API code written by you. If the chat doesn't appear and this setting is enabled, disable it.": "停用聊天小工具的自動初始化。當此設定處於活動狀態時，您必須使用您編寫的自訂 JavaScript API 程式碼初始化聊天小工具。如果聊天未出現且此設定已啟用，請將其停用。", "Disable auto-initialization of the tickets area. When this setting is active you must initialize the tickets area with a custom JavaScript API code written by you. If the tickets area doesn't appear and this setting is enabled, disable it.": "停用票證區域的自動初始化。當此設定處於活動狀態時，您必須使用您編寫的自訂 JavaScript API 程式碼初始化票證區域。如果票證區域未出現且此設定已啟用，請將其停用。", "Disable chatbot": "停用聊天機器人", "Disable cron job": "禁用 cron 作業", "Disable dashboard": "禁用儀表板", "Disable during office hours": "辦公時間禁用", "Disable email": "停用電子郵件", "Disable features": "停用功能", "Disable features you don't use and improve the chat performance.": "停用您不使用的功能並提高聊天效能。", "Disable file uploading capabilities within the chat.": "停用聊天中的檔案上傳功能。", "Disable for the tickets area": "門票區禁用", "Disable invitation": "禁用邀請", "Disable outside of office hours": "辦公時間外禁用", "Disable password": "停用密碼", "Disable registration during office hours": "辦公時間內禁止登記", "Disable registration if agents online": "如果代理在線則禁用註冊", "Disable the automatic invitation of agents to the channels.": "停用自動邀請座席加入頻道。", "Disable the channels filter.": "禁用通道過濾器。", "Disable the chatbot for the tickets area.": "禁用門票區域的聊天機器人。", "Disable the dashboard, and allow only one conversation per user.": "停用儀表板，並只允許每個使用者進行一次對話。", "Disable the login and remove the password field from the registration form.": "停用登入並從註冊表中刪除密碼欄位。", "Disable uploads": "禁用上傳", "Disable voice message capabilities within the chat.": "停用聊天中的語音訊息功能。", "Disable voice messages": "停用語音訊息", "Disabled": "殘障人士", "Display a brand image in the header area. This only applies for the 'brand' header type.": "在標題區域顯示品牌形象。這僅適用於“品牌”標題類型。", "Display in conversation list": "顯示在對話清單中", "Display in dashboard": "顯示在儀表板中", "Display the articles section in the dashboard area.": "在儀表板區域中顯示文章部分。", "Display the articles section in the right area.": "在右側區域顯示文章部分。", "Display the dashboard instead of the chat area on initialization.": "初始化時顯示儀表板而不是聊天區域。", "Display the user full name in the left panel instead of the conversation title.": "在左側面板中顯示使用者全名而不是對話標題。", "Display the user's profile image within the chat.": "在聊天中顯示使用者的個人資料圖像。", "Display user name in header": "在標題中顯示使用者名稱", "Display user's profile image": "顯示使用者的個人資料圖片", "Displays additional columns in the user table. Enter the name of the fields to add.": "顯示使用者表中的附加列。輸入要新增的欄位的名稱。", "Distribute conversations proportionately between agents and notify visitors of their position within the queue. Response time is in minutes. You can use the following merge fields in the message: {position}, {minutes}. They will be replaced by the real values in real-time.": "在代理商之間按比例分配對話，並通知訪客他們在隊列中的位置。響應時間以分鐘為單位。您可以在訊息中使用以下合併欄位：{位置}、{分鐘}。它們將被即時替換為真實值。", "Distribute conversations proportionately between agents, and block an agent from viewing the conversations of the other agents.": "在座席之間按比例分配對話，並阻止一個座席查看其他座席的對話。", "Do not send email notifications to admins": "不要向管理員發送電子郵件通知", "Do not show tickets in chat": "不要在聊天中顯示門票", "Do not translate settings area": "不翻譯設定區域", "Download": "下載", "Edit profile": "編輯個人資料", "Edit user": "編輯使用者", "Email address": "電子郵件地址", "Email and ticket": "電子郵件和票證", "Email header": "電子郵件標頭", "Email notification delay (hours)": "電子郵件通知延遲（小時）", "Email notifications via cron job": "透過 cron 作業發送電子郵件通知", "Email only": "僅電子郵件", "Email piping": "電子郵件管道", "Email piping server information and more settings.": "透過電子郵件發送管道伺服器資訊和更多設定。", "Email request message": "電子郵件請求訊息", "Email signature": "電子郵件簽名", "Email template for the notification email that is sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "代理回覆時發送給使用者的通知電子郵件的電子郵件範本。您可以使用文字、HTML 和以下合併欄位：{conversation_url_parameter}、{recipient_name}、{sender_name}、{sender_profile_image}、{message}、{attachments}。", "Enable logging of agent activity": "啟用代理活動記錄", "Enable logs": "啟用日誌", "Enable the chatbot outside of scheduled office hours only.": "僅在預定辦公時間之外啟用聊天機器人。", "Enable the registration only if all agents are offline.": "僅當所有座席均離線時才啟用註冊。", "Enable the registration outside of scheduled office hours only.": "僅允許在預定辦公時間之外進行註冊。", "Enable this option if email notifications are sent via cron job.": "如果透過 cron 作業傳送電子郵件通知，請啟用此選項。", "Enable ticket and chat support for subscribers only, view member profile details and subscription details in the admin area.": "僅為訂閱者啟用票證和聊天支持，在管理區域中查看會員個人資料詳細資訊和訂閱詳細資訊。", "Enter the bot token and click the button to synchronize the Telegram bot. Localhost cannot receive messages.": "輸入機器人令牌並點擊按鈕以同步 Telegram 機器人。本機無法接收訊息。", "Enter the bot token and click the button to synchronize the Viber bot. Localhost cannot receive messages.": "輸入機器人令牌並點擊按鈕以同步 Viber 機器人。本機無法接收訊息。", "Enter the database details of the Active eCommerce CMS database.": "輸入 Active eCommerce CMS 資料庫的資料庫詳細資訊。", "Enter the database details of the Martfury database.": "輸入 Martfury 資料庫的資料庫詳細資訊。", "Enter the database details of the Perfex database.": "輸入 Perfex 資料庫的資料庫詳細資訊。", "Enter the database details of the WHMCS database.": "輸入 WHMCS 資料庫的資料庫詳細資訊。", "Enter the default messages used by the chatbot when user question requires a dynamic answer.": "輸入當使用者問題需要動態答案時聊天機器人使用的預設訊息。", "Enter the details of your Google Business Messages.": "輸入您的 Google 商家資訊的詳細資訊。", "Enter the details of your Twitter app.": "輸入您的 Twitter 應用程式的詳細資訊。", "Enter the LINE details to start using it. Localhost cannot receive messages.": "輸入 LINE 詳細資訊以開始使用它。本機無法接收訊息。", "Enter the URL of a .css file, to load it automatically in the admin area.": "輸入 .css 檔案的 URL，以將其自動載入到管理區域中。", "Enter the URL of a .js file, to load it automatically in the admin area.": "輸入 .js 檔案的 URL，以將其自動載入到管理區域中。", "Enter the URL of the sources you want to train the OpenAI chatbot from.": "輸入您想要訓練 OpenAI 聊天機器人的來源的 URL。", "Enter the URLs of your shop": "輸入您商店的網址", "Enter the WeChat official account token. See the docs for more details.": "輸入微信公眾號token。請參閱文件以了解更多詳細資訊。", "Enter your 360dialog account settings information.": "輸入您的 360dialog 帳戶設定資訊。", "Enter your Envato Purchase Code to activate automatic updates and unlock all the features.": "輸入您的 Envato 購買代碼以啟動自動更新並解鎖所有功能。", "Enter your Twilio account details. You can use text and the following merge fields: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.": "輸入您的 Twilio 帳戶詳細資料。您可以使用文字和下列合併欄位：{message}、{recipient_name}、{sender_name}、{recipient_email}、{sender_email}、{conversation_url_parameter}。", "Enter your Twilio account settings information.": "輸入您的 Twilio 帳戶設定資訊。", "Enter your WeChat Official Account information.": "輸入您的微信公眾號資訊。", "Enter your Zendesk information.": "輸入您的 Zendesk 資訊。", "Entities": "Entities", "Envato Purchase Code": "Envato 購買代碼", "Envato purchase code validation": "Envato 購買代碼驗證", "Exclude products": "排除產品", "Export all settings.": "導出所有設定。", "Export settings": "匯出設定", "Facebook pages": "臉書頁面", "Fallback message": "後備訊息", "Filters": "過濾器", "First chat message": "第一條聊天訊息", "First reminder delay (hours)": "首次提醒延遲（小時）", "First ticket form": "第一張門票表格", "Flash notifications": "閃光通知", "Follow up - Email": "跟進 - 電子郵件", "Follow up message": "跟進消息", "Follows a conversation between a human agent and an end user and provide response suggestions to the human agent in real-time.": "追蹤人工代理和最終用戶之間的對話，並向人工代理即時提供回應建議。", "Follow-up email template. You can use text, HTML, and the following merge fields and more: {coupon}, {product_names}, {user_name}.": "後續電子郵件範本。您可以使用文字、HTML 和以下合併欄位等：{coupon}、{product_names}、{user_name}。", "Force language": "力語", "Force log out": "強制註銷", "Force the chat to ignore the language preferences, and to use always the same language.": "強制聊天忽略語言首選項，並始終使用相同的語言。", "Force the loggout of Support Board agents if they are not logged in WordPress.": "如果 Support Board 代理程式未登入 WordPress，則強制其登出。", "Force users to use a different conversation for each store and hide conversations from other stores from store administrators.": "強制使用者對每個商店使用不同的對話，並向商店管理員隱藏其他商店的對話。", "Force users to use only one phone country code.": "強制使用者僅使用一種電話國家/地區代碼。", "Form message": "表單訊息", "Form title": "表格標題", "Frequency penalty": "頻率損失", "Full visitor details": "完整的訪客詳細信息", "Generate conversations data": "產生對話數據", "Generate user expressions": "生成用戶表情", "Get configuration URL": "取得配置網址", "Get it from the APP_KEY value of the file .env located in the root directory of Active eCommerce.": "從位於 Active eCommerce 根目錄下的檔案 .env 的 APP_KEY 值取得。", "Get it from the APP_KEY value of the file .env located in the root directory of Martfury.": "從位於 Martfury 根目錄下的檔案 .env 的 APP_KEY 值取得。", "Get Path": "獲取路徑", "Get Service Worker path": "取得 Service Worker 路徑", "Get URL": "取得網址", "Google and Dialogflow settings.": "Google 和 Dialogflow 設定。", "Google search": "谷歌搜尋", "Header": "標頭", "Header background image": "標題背景圖片", "Header brand image": "標題品牌形象", "Header message": "標頭訊息", "Header parameters": "標頭參數", "Header title": "標題標題", "Header type": "標頭類型", "Hide": "隱藏", "Hide agent's profile image": "隱藏代理商的個人資料圖片", "Hide archived tickets": "隱藏存檔的票證", "Hide archived tickets from users.": "對使用者隱藏存檔的票證。", "Hide chat if no agents online": "如果沒有客服在線則隱藏聊天", "Hide chat outside of office hours": "在辦公時間之外隱藏聊天", "Hide conversations of other agents": "隱藏其他特工的對話", "Hide on mobile": "在手機上隱藏", "Hide the agent's profile image within the chat.": "在聊天中隱藏客服人員的個人資料圖片。", "Hide tickets from the chat widget and chats from the ticket area.": "隱藏聊天小工具中的票證和票證區域中的聊天。", "Hide timetable": "隱藏時間表", "Host": "主持人", "Human takeover": "人類接管", "If no agents respond within the specified time interval, a message will be sent to request the user's details, such as their email.": "如果在指定的時間間隔內沒有代理回應，則會發送訊息來請求使用者的詳細訊息，例如他們的電子郵件。", "If the chatbot doesn't understand a user's question, forwards the conversation to an agent.": "如果聊天機器人無法理解使用者的問題，會將對話轉發給客服人員。", "Image": "影像", "Import admins": "導入管理員", "Import all settings.": "導入所有設定。", "Import articles": "匯入文章", "Import contacts": "導入聯絡人", "Import customers": "進口客戶", "Import customers into Support Board. Only new customers will be imported.": "將客戶匯入到 Support Board。僅匯入新客戶。", "Import settings": "導入設定", "Import users": "導入用戶", "Import vendors": "進口供應商", "Import vendors into Support Board as agents. Only new vendors will be imported.": "將供應商作為代理匯入到 Support Board。僅導入新供應商。", "Improve chat performance with Pusher and WebSockets. This setting stops all AJAX/HTTP real-time requests that slow down your server and use instead the WebSockets.": "使用 Pusher 和 WebSocket 提高聊天效能。此設定會停止所有會降低伺服器速度的 AJAX/HTTP 即時請求並使用 WebSocket。", "Include custom fields": "包括自訂字段", "Include custom fields in the registration form.": "在註冊表中包含自訂欄位。", "Include the last name field in the registration form.": "在註冊表中包含姓氏欄位。", "Include the password field in the registration form.": "在註冊表中包含密碼欄位。", "Incoming conversations and messages": "傳入的對話和訊息", "Incoming conversations only": "僅傳入對話", "Incoming messages only": "僅傳入訊息", "Increase sales and connect you and sellers with customers in real-time by integrating Active eCommerce with Support Board.": "透過將 Active eCommerce 與 Support Board 集成，提高銷售量並將您和賣家與客戶即時連結。", "Increase sales, provide better support, and faster solutions, by integrating WooCommerce with Support Board.": "透過將 WooCommerce 與 Support Board 集成，提高銷售量、提供更好的支援和更快的解決方案。", "Info message": "資訊訊息", "Initialize and display the chat widget and tickets only for members.": "僅為會員初始化並顯示聊天小工具和票證。", "Initialize and display the chat widget only when the user is logged in.": "僅當使用者登入時初始化並顯示聊天小工具。", "Instance ID": "實例ID", "Integrate OpenCart with Support Board for real-time syncing of customers, order history access, and customer cart visibility.": "將 OpenCart 與 Support Board 集成，以即時同步客戶、存取訂單歷史記錄和客戶購物車可見性。", "Interval (sec)": "間隔（秒）", "Label": "標籤", "Language": "語言", "Language detection": "語言偵測", "Language detection message": "語言偵測訊息", "Last name": "姓", "Leave it blank if you don't know what this setting is! Entering an incorrect value will break the chat. Sets the main domain where chat is used to enable login and conversations sharing between the main domain and sub domains.": "如果您不知道此設定是什麼，請將其留空！輸入不正確的值將會中斷聊天。設定聊天使用的主網域，以實現主網域和子網域之間的登入和對話共用。", "Left": "左邊", "Left panel": "左側面板", "Left profile image": "左側個人資料圖片", "Let the bot search on Google to find answers to user questions.": "讓機器人在 Google 上搜尋以尋找用戶問題的答案。", "Let the bot to search on Google to find answers to user questions.": "讓機器人在 Google 上搜尋以尋找用戶問題的答案。", "Lets your users reach you via Twitter. Read and reply to messages sent to your Twitter account directly from Support Board.": "讓您的用戶透過 Twitter 與您聯繫。閱讀並回覆直接從 Support Board 發送到您的 Twitter 帳戶的訊息。", "Lets your users reach you via WeChat. Read and reply to all messages sent to your WeChat official account directly from Support Board.": "讓您的用戶透過微信與您聯繫。閱讀並回覆直接從 Support Board 發送到您的微信公眾號的所有訊息。", "Lets your users reach you via WhatsApp. Read and reply to all messages sent to your WhatsApp Business account directly from Support Board.": "讓您的用戶透過 WhatsApp 與您聯繫。閱讀並回覆直接從 Support Board 發送到您的 WhatsApp Business 帳戶的所有訊息。", "Link each agent with the corresponding Slack user, so when an agent replies via Slack it will be displayed as the assigned agent.": "將每個客服人員與相應的 Slack 用戶鏈接，因此當客服人員透過 Slack 回复時，它將顯示為指定的客服人員。", "Link name": "連結名稱", "Login form": "登入表單", "Login initialization": "登入初始化", "Login verification URL": "登入驗證網址", "Logit bias": "邏輯偏差", "Make a backup of your Dialogflow agent first. This operation can take several minutes.": "首先備份 Dialogflow 代理程式。此操作可能需要幾分鐘。", "Make the registration phone field mandatory.": "將註冊電話欄位設定為必填項。", "Manage": "管理", "Manage here the departments settings.": "在此管理部門設定。", "Manage personalized questions and answers.": "管理個人化問題和答案。", "Manage the tags settings.": "管理標籤設定。", "Manifest file URL": "Manifest 檔案網址", "Manual": "手動的", "Manual initialization": "手動初始化", "Martfury root directory path, e.g. /var/www/": "Martfury根目錄路徑，例如/var/www/", "Martfury shop URL, e.g. https://shop.com": "Martfury 商店網址，例如 https://shop.com", "Max message limit": "最大訊息限制", "Max tokens": "最大代幣數", "Members only": "內部使用", "Members with an active paid plan only": "僅限擁有有效付費方案的會員", "Message": "訊息", "Message rewrite button": "訊息重寫按鈕", "Message template": "留言模板", "Message type": "訊息類型", "Messaging channels": "訊息傳遞管道", "Messenger and Instagram settings": "Messenger 和 Instagram 設定", "Minify JS": "縮小 JS", "Minimal": "最小", "Model": "模型", "Multilingual": "多種語言", "Multilingual plugin": "多語言插件", "Multilingual via translation": "透過翻譯實現多語言", "Multlilingual training sources": "多語言培訓資源", "Name": "姓名", "Namespace": "命名空間", "New conversation email": "新對話電子郵件", "New conversation notification": "新對話通知", "New ticket button": "新票按鈕", "Newsletter": "通訊", "No delay": "無延遲", "No results found.": "未找到結果。", "No, we don't ship in": "不，我們不發貨", "None": "沒有任何", "Note data scraping": "注意資料抓取", "Notes": "筆記", "Notifications icon": "通知圖示", "Notify the user when their message is sent outside of the scheduled office hours or or all agents are offline.": "當訊息在預定辦公時間之外發送或所有客服人員均離線時通知使用者。", "Offline message": "離線留言", "Offset": "抵銷", "Omit previous messages": "省略之前的訊息", "On chat open": "聊天時打開", "On page load": "頁面載入時", "Online users notification": "線上用戶通知", "Only desktop": "僅桌面版", "Only general questions": "僅一般性問題", "Only mobile devices": "僅行動裝置", "Only questions related to your sources": "僅與您的消息來源相關的問題", "Open automatically": "自動開啟", "Open chat": "打開聊天室", "Open the chat window automatically when a new message is received.": "收到新訊息時自動開啟聊天視窗。", "OpenAI - Questions and answers": "OpenAI - 問題與解答", "OpenAI - Training sources": "OpenAI - 培訓來源", "OpenAI - Training sources - PDF and Text Files": "OpenAI - 培訓資源 - PDF 和文字文件", "OpenAI - Training sources options": "OpenAI - 訓練來源選項", "OpenAI Assistants - Department linking": "OpenAI 助理 - 部門鏈接", "OpenAI settings.": "開啟AI設定。", "Optional link": "可選連結", "Optional registration fields": "可選註冊字段", "Options associated with the training procedure.": "與培訓程序相關的選項。", "Order webhook": "訂購網路鉤子", "Other": "其他", "Outgoing SMTP server information.": "傳出 SMTP 伺服器資訊。", "Page ID": "頁面ID", "Page IDs": "頁面 ID", "Page name": "頁面名稱", "Page token": "頁面令牌", "Panel height": "面板高度", "Panel name": "面板名稱", "Panel title": "面板標題", "Panels arrows": "面板箭頭", "Password": "密碼", "Perfex URL": "佩菲克斯網址", "Performance optimization": "效能最佳化", "Phone": "電話", "Phone codes": "電話號碼", "Phone number ID": "電話號碼 ID", "Phone required": "需要電話", "Place ID": "地點 ID", "Placeholder text": "佔位符文字", "Play a sound when a user receives an incoming message or sends an outgoing message.": "當用戶收到傳入訊息或發送傳出訊息時播放聲音。", "Play a sound when an agent receives an incoming message or sends an outgoing message.": "當客服人員收到傳入訊息或發送傳出訊息時播放聲音。", "Popup message": "彈出訊息", "Port": "港口", "Post Type slugs": "Post Type 蛞蝓", "Presence penalty": "存在處罰", "Prevent admins from receiving email notifications.": "防止管理員收到電子郵件通知。", "Prevent agents from viewing conversations assigned to other agents. This setting is automatically enabled if routing or queue is active.": "防止客服人員查看分配給其他客服人員的對話。如果路由或佇列處於活動狀態，則會自動啟用此設定。", "Prevent any abuse from users by limiting the number of messages sent to the chatbot from one device.": "透過限制從一台設備發送到聊天機器人的訊息數量，防止用戶濫用。", "Priority": "優先事項", "Privacy link": "隱私連結", "Privacy message": "隱私資訊", "Private chat": "私人聊天", "Private chat linking": "私人聊天連結", "Private key": "私鑰", "Product IDs": "產品 ID", "Product removed notification": "產品已刪除通知", "Product removed notification - Email": "產品已刪除通知 - 電子郵件", "Profile image": "個人資料圖片", "Project ID": "項目編號", "Project ID or Agent Name": "專案 ID 或代理名稱", "Prompt": "迅速的", "Prompt - Message rewriting": "提示-訊息重寫", "Protect the tickets area from spam and abuse with Google reCAPTCHA.": "透過 Google reCAPTCHA 保護門票區域免受垃圾郵件和濫用行為的影響。", "Provide help desk support to your customers by including a ticket area, with all chat features included, on any web page in seconds.": "透過在任何網頁上快速新增一個包含所有聊天功能的票證區域，為您的客戶提供幫助台支援。", "Provider": "提供者", "Purchase button text": "購買按鈕文字", "Push notifications": "推播通知", "Push notifications settings.": "推播通知設定。", "Questions and answers": "問題與解答", "Queue": "佇列", "Read and reply to messages sent from Google Search, Maps and brand-owned channels directly in Support Board.": "直接在 Support Board 中閱讀並回覆從 Google 搜尋、地圖和品牌擁有的管道發送的訊息。", "Read, manage and reply to all messages sent to your Facebook pages and Instagram accounts directly from Support Board.": "閱讀、管理和回覆直接從 Support Board 發送到您的 Facebook 頁面和 Instagram 帳戶的所有訊息。", "Reconnect": "重新連接", "Redirect the user to the registration link instead of showing the registration form.": "將使用者重定向到註冊鏈接，而不是顯示註冊表單。", "Redirect the user to the specified URL if the registration is required and the user is not logged in. Leave blank to use the default registration form.": "如果需要註冊且使用者未登錄，則將使用者重新導向至指定的 URL。留空以使用預設註冊表單。", "Register all visitors": "登記所有訪客", "Register all visitors automatically. When this option is not active, only the visitors that start a chat will be registered.": "自動登記所有訪客。當此選項未啟動時，只有開始聊天的訪客才會被註冊。", "Registration / Login": "註冊/登入", "Registration and login form": "註冊和登入表格", "Registration form": "報名表格", "Registration link": "註冊連結", "Registration redirect": "註冊重定向", "Remove the email field from the registration form.": "從登錄中刪除電子郵件欄位。", "Rename the articles archive panel title. Default is 'Help Center'.": "重新命名文章存檔面板標題。預設為「幫助中心」。", "Rename the chat bot. Default is 'Bot'.": "重命名聊天機器人。預設為“機器人”。", "Rename the visitor name prefix. Default is 'User'.": "重新命名訪客姓名前綴。預設為“用戶”。", "Repeat": "重複", "Repeat - admin": "重複 - 管理員", "Replace the admin login page message.": "取代管理員登入頁面訊息。", "Replace the brand logo on the admin login page.": "替換管理員登入頁面上的品牌標誌。", "Replace the header title with the user's first name and last name when available.": "如果可用，請將標題標題替換為使用者的名字和姓氏。", "Replace the top-left brand icon on the admin area and the browser favicon.": "替換管理區域左上角的品牌圖示和瀏覽器圖示。", "Reply to user emails": "回覆使用者電子郵件", "Reply to user text messages": "回覆用戶簡訊", "Reports": "報告", "Reports area": "報告區", "Request a valid Envato purchase code for registration.": "請求有效的 Envato 購買代碼進行註冊。", "Request the user to provide their email address and then send a confirmation email to the user.": "要求使用者提供其電子郵件地址，然後向使用者發送確認電子郵件。", "Require phone": "需要電話", "Require registration": "需要註冊", "Require the user registration or login before start a chat. To enable the login area the password field must be included.": "開始聊天之前需要用戶註冊或登入。若要啟用登入區域，必須包含密碼欄位。", "Require the user registration or login in order to use the tickets area.": "需要用戶註冊或登入才能使用門票區域。", "Required": "必需的", "Response time": "回應時間", "Returning visitor message": "回訪留言", "Rich messages": "Rich messages", "Rich messages are code snippets that can be utilized within a chat message. They can contain HTML code and are automatically rendered in the chat. Rich messages can be used with the following syntax: [rich-message-name]. There are a tonne of built-in rich messages to choose from.": "Rich messages 是可在聊天訊息中使用的程式碼片段。它們可以包含 HTML 程式碼並在聊天中自動呈現。 Rich messages 可以與下列語法一起使用：[rich-message-name]。有大量內建的 rich messages 可供選擇。", "Right": "正確的", "Right panel": "右面板", "Routing": "路由", "Routing if offline": "離線時路由", "RTL": "RTL", "Save useful information like user country and language also for visitors.": "也為訪客保存有用的信息，例如用戶國家/地區和語言。", "Saved replies": "已儲存的回复", "Scheduled office hours": "預定辦公時間", "Search engine ID": "搜尋引擎ID", "Second chat message": "第二個聊天訊息", "Second reminder delay (hours)": "第二次提醒延遲（小時）", "Secondary color": "次要顏色", "Secret key": "金鑰", "Send a message to allow customers to be notified when they can purchase a product they are interested in, but that is currently out of stock. You can use the following merge fields: {user_name}, {product_name}.": "發送訊息，以便客戶在可以購買他們感興趣但目前缺貨的產品時收到通知。您可以使用下列合併欄位：{user_name}、{product_name}。", "Send a message to new users when they create the first ticket. Text formatting and merge fields are supported.": "當新用戶建立第一個票證時向他們發送訊息。支援文字格式和合併欄位。", "Send a message to new users when they visit the website for the first time.": "當新用戶第一次造訪網站時向他們發送訊息。", "Send a message to the customer after a product has been removed from the cart. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.": "從購物車中刪除產品後向客戶發送訊息。您可以使用下列合併欄位及更多欄位：{coupon}、{discount_price}、{original_price}、{product_names}、{user_name}、{purchase_button}。", "Send a message to the customers who complete a purchase asking to share the product they just bought. You can use the following merge fields and more: {product_name}, {user_name}.": "向完成購買的客戶發送訊息，要求分享他們剛購買的產品。您可以使用下列合併欄位及更多欄位：{product_name}、{user_name}。", "Send a message to the customers who complete a purchase. You can use the following merge fields and more: {coupon}, {product_names}, {user_name}.": "向完成購買的客戶發送訊息。您可以使用下列合併欄位及更多欄位：{coupon}、{product_names}、{user_name}。", "Send a message to the user when the agent archive the conversation.": "當代理程式存檔對話時向使用者發送訊息。", "Send a message to users who visit the website again after at least 24 hours. You can use the following merge fields and more: {coupon}, {user_name}. See the docs for more details.": "向至少 24 小時後再次造訪該網站的用戶發送訊息。您可以使用以下合併欄位及更多欄位：{coupon}、{user_name}。請參閱文件以了解更多詳細資訊。", "Send a test agent notification email to verify email settings.": "發送測試代理通知電子郵件以驗證電子郵件設定。", "Send a test message to your Slack channel. This only tests the sending functionality of outgoing messages.": "向您的 Slack 頻道發送測試訊息。這僅測試傳出訊息的發送功能。", "Send a test user notification email to verify email settings.": "發送測試使用者通知電子郵件以驗證電子郵件設定。", "Send a text message to the provided phone number.": "向提供的電話號碼發送簡訊。", "Send a user email notification": "發送用戶電子郵件通知", "Send a user text message notifcation": "發送用戶簡訊通知", "Send a user text message notification": "發送用戶簡訊通知", "Send an agent email notification": "發送代理電子郵件通知", "Send an agent text message notification": "發送代理簡訊通知", "Send an agent user text notification": "發送代理用戶文字通知", "Send an email notification to the provided email address.": "向提供的電子郵件地址發送電子郵件通知。", "Send an email to an agent when a user replies and the agent is offline. An email is automatically sent to all agents for new conversations.": "當使用者回覆且客服人員離線時向客服人員發送電子郵件。系統會自動向所有客服人員發送電子郵件以進行新對話。", "Send an email to the user when a new conversation is created.": "建立新對話時向使用者發送電子郵件。", "Send an email to the user when a new conversation or ticket is created": "建立新對話或票證時向使用者發送電子郵件", "Send an email to the user when an agent replies and the user is offline.": "當客服回覆且用戶離線時向用戶發送電子郵件。", "Send email": "發電子郵件", "Send login details to the specified URL and allow access only if the response is positive.": "將登入詳細資訊傳送到指定的 URL，並且僅當回應是肯定時才允許存取。", "Send message": "傳訊息", "Send message to Slack": "向 Slack 發送訊息", "Send message via enter button": "透過輸入按鈕發送訊息", "Send text message": "傳簡訊", "Send the message template to a WhatsApp number.": "將訊息範本傳送到 WhatsApp 號碼。", "Send the message via the ENTER keyboard button.": "透過 ENTER 鍵盤按鈕發送訊息。", "Send the user details of the registration form and email rich messages to Dialogflow.": "將註冊表單的使用者詳細資料和電子郵件 rich messages 發送至 Dialogflow。", "Send the WhatsApp order details to the URL provided.": "將 WhatsApp 訂單詳細資料發送至提供的 URL。", "Send to user's email": "發送到使用者的電子郵件", "Send transcript to user's email": "將成績單傳送到使用者的電子郵件", "Send user details": "發送用戶詳細資訊", "Sender email": "寄件者電子郵件", "Sender name": "寄件者名稱", "Sender number": "寄件者號碼", "Sends a text message if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.": "如果 WhatsApp 訊息發送失敗，則發送簡訊。您可以使用文字和以下合併欄位：{conversation_url_parameter}、{message}、{recipient_name}、{recipient_email}。", "Sends a WhatsApp Template notification if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {recipient_name}, {recipient_email}.": "如果 WhatsApp 訊息發送失敗，則發送 WhatsApp 範本通知。您可以使用文字和以下合併欄位：{conversation_url_parameter}、{recipient_name}、{recipient_email}。", "Service": "服務", "Service Worker path": "Service Worker 路徑", "Service Worker URL": "Service Worker 網址", "Set a dedicated Dialogflow agent for each department.": "為每個部門設定專用的 Dialogflow 代理程式。", "Set a dedicated OpenAI Assistants for each department.": "為每個部門設定專門的OpenAI助手。", "Set a dedicated Slack channel for each department.": "為每個部門設定專用的 Slack 通道。", "Set a profile image for the chat bot.": "為聊天機器人設定個人資料影像。", "Set the avatar image shown next to the message. It must be a JPG image of 1024x1024px with a maximum size of 50KB.": "設定訊息旁邊顯示的頭像圖像。它必須是 1024x1024 像素的 JPG 圖片，最大尺寸為 50KB。", "Set the currency symbol of the membership prices.": "設定會員價格的貨幣符號。", "Set the currency symbol used by your system.": "設定您的系統使用的貨幣符號。", "Set the default departments for all tickets. Enter the department ID.": "設定所有工單的預設部門。輸入部門 ID。", "Set the default email header that will be prepended to automated emails and direct emails.": "設定將會新增到自動電子郵件和直接電子郵件前面的預設電子郵件標頭。", "Set the default email signature that will be appended to automated emails and direct emails.": "設定將附加到自動電子郵件和直接電子郵件的預設電子郵件簽名。", "Set the default form to display if the registraion is required.": "設定需要註冊時顯示的預設表單。", "Set the default name to use for conversations without a name.": "設定用於沒有名稱的對話的預設名稱。", "Set the default office hours for when agents are shown as available. These settings are also used for all other settings that rely on office hours.": "設定代理顯示為可用的預設辦公時間。這些設定也用於依賴辦公時間的所有其他設定。", "Set the default username to use in bot messages and emails when the user doesn't have a name.": "設定當使用者沒有姓名時在機器人訊息和電子郵件中使用的預設使用者名稱。", "Set the header appearance.": "設定標題外觀。", "Set the maximum height of the tickets panel.": "設定票證面板的最大高度。", "Set the multilingual plugin you're using, or leave it disabled if your site uses only one language.": "設定您正在使用的多語言插件，或者如果您的網站僅使用一種語言，請將其停用。", "Set the offline status automatically when the agent or admin remains inactive in the admin area for at least 10 minutes.": "當座席或管理員在管理區域保持非活動狀態至少 10 分鐘時，自動設定離線狀態。", "Set the position of the chat widget.": "設定聊天小部件的位置。", "Set the primary color of the admin area.": "設定管理區域的主要顏色。", "Set the primary color of the chat widget.": "設定聊天小工具的主顏色。", "Set the secondary color of the admin area.": "設定管理區域的輔助色。", "Set the secondary color of the chat widget.": "設定聊天小工具的輔助色彩。", "Set the tertiary color of the chat widget.": "設定聊天小工具的第三層顏色。", "Set the title of the administration area.": "設定管理區域的標題。", "Set the title of the conversations panel.": "設定對話面板的標題。", "Set the UTC offset of the office hours timetable. The correct value can be negative, and it's generated automatically once you click this input field, if it's empty.": "設定辦公時間時間表的 UTC 偏移量。正確的值可以是負數，如果該輸入欄位為空，則按一下此輸入欄位後會自動產生該值。", "Set which actions to allow agents.": "設定允許代理執行哪些操作。", "Set which actions to allow supervisors.": "設定允許主管執行哪些操作。", "Set which user details to send to the main channel. Add comma separated values.": "設定要傳送到主頻道的使用者詳細資料。新增逗號分隔值。", "Settings area": "設定區", "settings information": "設定訊息", "Shop": "店鋪", "Show": "展示", "Show a browser tab notification when a new message is received.": "收到新訊息時顯示瀏覽器標籤通知。", "Show a desktop notification when a new message is received.": "收到新訊息時顯示桌面通知。", "Show a notification and play a sound when a new user is online.": "當新用戶在線上時顯示通知並播放聲音。", "Show a pop-up notification to all users.": "向所有使用者顯示彈出通知。", "Show profile images": "顯示個人資料圖片", "Show sender's name": "顯示寄件者姓名", "Show the agents menu in the dashboard and force the user to choose an agent to start a conversation.": "在儀表板中顯示代理選單並強制使用者選擇代理來開始對話。", "Show the follow up message when a visitor add an item to the cart. The message is sent only if the user has not provided an email yet.": "當訪客將商品加入購物車時顯示後續訊息。只有當使用者尚未提供電子郵件時才會發送該訊息。", "Show the list of all Slack channels.": "顯示所有 Slack 頻道的清單。", "Show the profile image of agents and users within the conversation.": "顯示對話中代理程式和使用者的個人資料影像。", "Show the sender's name in every message.": "在每封郵件中顯示寄件者姓名。", "Single label": "單一標籤", "Single phone country code": "單一電話國家代碼", "Site key": "站點密鑰", "Slug": "蛞蝓", "Smart Reply": "智慧回覆", "Social share message": "社群分享訊息", "Sort conversations by date": "按日期對對話進行排序", "Sound": "聲音", "Sound settings": "聲音設定", "Sounds": "聲音", "Sounds - admin": "聲音 - 管理員", "Source links": "來源連結", "Speech recognition": "語音辨識", "Spelling correction": "拼字糾正", "Start importing": "開始導入", "Stop crawler from scanning other pages": "阻止爬蟲掃描其他頁面", "Store name": "店家名稱", "Subject": "主題", "Subscribe": "訂閱", "Subscribe message": "訂閱訊息", "Subscribe message - Email": "訂閱訊息 - 電子郵件", "Subscribe message email template sent to the user when the user communicate the email via the subscribe message form. You can use text, HTML, and the following merge fields: {user_name}, {user_email}.": "當使用者透過訂閱訊息表單傳達電子郵件時，發送給使用者的訂閱訊息電子郵件範本。您可以使用文字、HTML 和以下合併欄位：{user_name}、{user_email}。", "Subscribe users to your preferred newsletter service when they provide an email.": "當用戶提供電子郵件時，訂閱您首選的電子報服務。", "Subtract the offset value from the height value.": "從高度值中減去偏移值。", "Success message": "成功訊息", "Supervisor": "導師", "Support Board path": "Support Board 路徑", "Sync admin and staff accounts with Support Board. Staff users will be registered as agents, while admins as admins. Only new users will be imported.": "將管理者和員工帳號與 Support Board 同步。員工用戶將註冊為代理，而管理員用戶將註冊為管理員。僅匯入新用戶。", "Sync all contacts of all clients with Support Board. Only new contacts will be imported.": "將所有客戶的所有聯絡人與 Support Board 同步。僅匯入新聯絡人。", "Sync all users with Support Board. Only new users will be imported.": "將所有使用者與 Support Board 同步。僅匯入新用戶。", "Sync all WordPress users with Support Board. Only new users will be imported.": "將所有 WordPress 使用者與 Support Board 同步。僅匯入新用戶。", "Sync knowledge base articles with Support Board. Only new articles will be imported.": "將知識庫文章與 Support Board 同步。僅匯入新文章。", "Sync mode": "同步模式", "Synchronization": "同步", "Synchronize": "同步", "Synchronize customers, enable ticket and chat support for subscribers only, view subscription plans in the admin area.": "同步客戶、僅為訂閱者啟用票證和聊天支援、在管理區域查看訂閱方案。", "Synchronize emails": "同步電子郵件", "Synchronize Entities": "同步Entities", "Synchronize Entities now": "立即同步 Entities", "Synchronize now": "立即同步", "Synchronize users": "同步用戶", "Synchronize your customers in real-time, chat with them and boost their engagement, or provide a better and faster support.": "即時同步您的客戶，與他們聊天並提高他們的參與度，或提供更好、更快的支援。", "Synchronize your Messenger and Instagram accounts.": "同步您的 Messenger 和 Instagram 帳號。", "Synchronize your Perfex customers in real-time and let them contact you via chat! View profile details, proactively engage them, and more.": "即時同步您的 Perfex 客戶，讓他們透過聊天與您聯繫！查看個人資料詳細資訊、主動與他們互動等等。", "Synchronize your WhatsApp Cloud API account.": "同步您的 WhatsApp Cloud API 帳號。", "System requirements": "系統需求", "Tags": "標籤", "Tags settings": "標籤設定", "Template default language": "模板預設語言", "Template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "代理回覆時發送給使用者的電子郵件範本。您可以使用文字、HTML 和以下合併欄位：{conversation_url_parameter}、{recipient_name}、{sender_name}、{sender_profile_image}、{message}、{attachments}。", "Template for the email sent to an agent when a user sends a new message. You can use text, HTML, and the following merge fields: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "當使用者發送新訊息時發送給代理商的電子郵件範本。您可以使用文字、HTML 和以下合併欄位：{conversation_link}、{recipient_name}、{sender_name}、{sender_profile_image}、{message}、{attachments}。", "Template for the email sent to the user when a new conversation is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}.": "建立新對話時發送給使用者的電子郵件範本。您可以使用文字、HTML 和以下合併欄位：{conversation_url_parameter}、{user_name}、{message}、{attachments}、{conversation_id}。", "Template for the email sent to the user when a new conversation or ticket is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}.": "建立新對話或票證時發送給使用者的電子郵件範本。您可以使用文字、HTML 和以下合併欄位：{conversation_url_parameter}、{user_name}、{message}、{attachments}。", "Template languages": "模板語言", "Template name": "模板名稱", "Template of the admin notification email. You can use text, HTML, and the following merge field and more: {carts}. Enter the email you want to send notifications to in the email address field.": "管理員通知電子郵件的範本。您可以使用文字、HTML 和以下合併欄位等：{carts}。在電子郵件地址欄位中輸入您想要傳送通知給其的電子郵件。", "Template of the email sent to the customer after a product has been removed from the cart. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "從購物車中刪除產品後發送給客戶的電子郵件範本。您可以使用文字、HTML 和以下合併欄位等：{html_products_list}、{coupon}、{discount_price}、{original_price}、{product_names}、{user_name} 。", "Template of the first notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "第一封通知電子郵件的範本。您可以使用文字、HTML 和以下合併欄位等：{html_products_list}、{coupon}、{discount_price}、{original_price}、{product_names}、{user_name} 。", "Template of the second notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "第二封通知電子郵件的範本。您可以使用文字、HTML 和以下合併欄位等：{html_products_list}、{coupon}、{discount_price}、{original_price}、{product_names}、{user_name} 。", "Template of the waiting list notification email. You can use text, HTML, and the following merge field and more: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}.": "等待名單通知電子郵件的範本。您可以使用文字、HTML 和以下合併欄位等：{html_product_card}、{product_description}、{product_image}、{product_name}、{product_link}。", "Terms link": "條款連結", "Tertiary color": "第三色", "Test Slack": "測試鬆弛", "Test template": "測試模板", "Text": "文字", "Text message fallback": "簡訊回退", "Text message notifications": "簡訊通知", "Text messages": "簡訊", "The product is not in the cart.": "該產品不在購物車中。", "The workspace name you are using to synchronize Slack.": "您用於同步 Slack 的 workspace 名稱。", "This is your main Slack channel ID, which is usually the #general channel. You will get this code by completing the Slack synchronization.": "這是您的主要 Slack 頻道 ID，通常是#general 頻道。您將透過完成 Slack 同步獲得此程式碼。", "This returns the Support Board path of your server.": "這將傳回伺服器的 Support Board 路徑。", "This returns your Support Board URL.": "這將返回您的 Support Board URL。", "Ticket custom fields": "工單自訂字段", "Ticket email": "門票電子郵件", "Ticket field names": "票證欄位名稱", "Ticket fields": "票證字段", "Ticket only": "僅限門票", "Ticket products selector": "門票產品選擇器", "Title": "標題", "Top": "頂部", "Train your chatbot": "訓練你的聊天機器人", "Transcript": "成績單", "Transcript settings.": "轉錄設定。", "Translate automatically": "自動翻譯", "Translate the chat widget's text elements automatically to match the user language.": "自動翻譯聊天小工具的文字元素以符合使用者語言。", "Trigger": "扳機", "Trigger the Dialogflow Welcome Intent for new visitors when the welcome message is active.": "當歡迎訊息處於活動狀態時，為新訪客觸發 Dialogflow Welcome Intent。", "Twilio settings": "<PERSON><PERSON><PERSON> 設定", "Twilio template": "<PERSON><PERSON><PERSON> 模板", "Upload attachments to Amazon S3.": "將附件上傳到 Amazon S3。", "Upload the PDF and text files you want to train the OpenAI chatbot from.": "上傳您想要訓練 OpenAI 聊天機器人的 PDF 和文字檔案。", "Usage Limit": "使用限制", "Use this option to change the PWA icon. See the docs for more details.": "使用此選項變更 PWA 圖示。請參閱文件以了解更多詳細資訊。", "User details": "用戶詳細信息", "User email notifications": "使用者電子郵件通知", "User login form information.": "使用者登入表單資訊。", "User message template": "用戶留言模板", "User name as title": "使用者名稱作為標題", "User notification email": "使用者通知電子郵件", "User registration form information.": "用戶註冊表資訊。", "User roles": "使用者角色", "User system": "使用者係統", "Username": "使用者名稱", "Users and agents": "使用者和代理商", "Users area": "使用者區", "Users only": "僅限用戶", "Users table additional columns": "使用者表附加列", "UTC offset": "UTC 偏移量", "View channels": "查看頻道", "View unassigned conversations": "查看未指派的對話", "Visibility": "能見度", "Visitor default name": "訪客預設名稱", "Visitor name prefix": "訪客姓名前綴", "Volume": "體積", "Volume - admin": "音量 - 管理", "Waiting list": "等候名單", "Waiting list - Email": "等候名單 - 電子郵件", "Webhook URL": "網路鉤子 URL", "Webhooks": "網路鉤子", "Webhooks are information sent in background to a unique URL defined by you when something happens.": "Webhook 是當發生某些情況時在背景傳送到您定義的唯一 URL 的資訊。", "Website": "網站", "WeChat settings": "微信設定", "Welcome message": "歡迎留言", "Whmcs admin URL": "WHMCS 管理 URL", "Whmcs admin URL. Ex. https://example.com/whmcs/admin/": "WHMCS 管理 URL。前任。 https://example.com/whmcs/admin/", "WordPress registration": "WordPress 註冊", "Yes, we ship in": "是的，我們發貨", "You haven't placed an order yet.": "您還沒有下訂單。", "You will get this code by completing the Dialogflow synchronization.": "您將透過完成 Dialogflow 同步來取得此程式碼。", "You will get this code by completing the Slack synchronization.": "您將透過完成 Slack 同步獲得此程式碼。", "You will get this information by completing the synchronization.": "完成同步後您將獲得此資訊。", "Your cart is empty.": "您的購物車是空的。", "Your turn message": "輪到你的消息", "Your username": "您的使用者名稱", "Your WhatsApp catalogue details.": "您的 WhatsApp 目錄詳細資料。", "Zendesk settings": "Zendesk 設定"}
{"{product_name} has no {product_attribute_name} variants.": "{product_name} не має {product_attribute_name} варіантів.", "360dialog settings": "Налаштування діалогу 360", "360dialog template": "Шабл<PERSON>н 360dialog", "Abandoned cart notification": "Повідомлення про покинутий кошик", "Abandoned cart notification - Admin email": "Повідомлення про покинутий кошик – електронна адреса адміністратора", "Abandoned cart notification - First email": "Сповіщення про покинутий кошик – перший лист", "Abandoned cart notification - Second email": "Сповіщення про покинутий кошик – другий електронний лист", "Accept button text": "Прийняти текст кнопки", "Account SID": "SID облікового запису", "Activate the Right-To-Left (RTL) reading layout for the admin area.": "Активуйте макет читання справа наліво (RTL) для області адміністратора.", "Activate the Right-To-Left (RTL) reading layout.": "Активуйте макет читання справа наліво (RTL).", "Activate the Slack integration.": "Активуйте інтеграц<PERSON>ю Slack.", "Activate the Zendesk integration": "Активуйте інтеграцію Zendesk", "Activate this option if you don't want to translate the settings area.": "Активуйте цю опцію, якщо ви не хочете перекладати область налаштувань.", "Active": "Активний", "Active - admin": "Ак<PERSON>ивний - адмін", "Active eCommerce CMS URL. Ex. https://shop.com/": "Active eCommerce URL-адреса CMS. Напр. https://shop.com/", "Active eCommerce URL": "Active eCommerce URL", "Active for agents": "Активний для агентів", "Active for users": "Активний для користувачів", "Active webhooks": "Активні веб-хуки", "Add a delay (ms) to the bot's responses. Default is 2000.": "Додайте затримку (мс) до відповідей бота. За замовчуванням 2000.", "Add and manage additional support departments.": "Додайте додаткові відділи підтримки та керуйте ними.", "Add and manage saved replies that can be used by agents in the chat editor. Saved replies can be printed by typing # followed by the reply name plus space. Use \\n to do a line break.": "Додайте та керуйте збереженими відповідями, які можуть використовуватися агентами в редакторі чату. Збережені відповіді можна роздрукувати, ввівши #, а потім назву відповіді та пробіл. Використовуйте \\n, щоб зробити розрив рядка.", "Add and manage tags.": "Додавайте теги та керуйте ними.", "Add comma separated WordPress user roles. The Support Board administration area will be available for new roles, in addition to the default one: editor, administrator, author.": "Додайте ролі користувачів WordPress, розділені комами. Область адміністрування Ради підтримки буде доступна для нових ролей, крім стандартної: редактор, адміністратор, автор.", "Add custom fields to the new ticket form.": "Додайте спеціальні поля до нової форми квитка.", "Add custom fields to the user profile details.": "Додайте спеціальні поля до даних профілю користувача.", "Add Intents": "Дода<PERSON>и <PERSON>", "Add Intents to saved replies": "Додайте Intents до збережених відповідей", "Add WhatsApp phone number details here.": "Додайте тут деталі номера телефону WhatsApp.", "Adjust the chat button position. Values are in px.": "Налаштуйте положення кнопки чату. Значення в px.", "Admin icon": "Значок адміністратора", "Admin IDs": "Ідентифікатори адміністратора", "Admin login logo": "Логотип для входу адміністратора", "Admin login message": "Повідомлення для входу адміністратора", "Admin notifications": "Сповіщення адміністратора", "Admin title": "Звання адміністратора", "Agent area": "Агентська зона", "Agent details": "Дані агента", "Agent email notifications": "Сповіщення агента електронною поштою", "Agent ID": "ID агента", "Agent linking": "Зв’язування агента", "Agent message template": "Шаблон повідомлення агента", "Agent notification email": "Повідомлення агента електронною поштою", "Agent privileges": "Привілеї агента", "Agents": "Агенти", "Agents and admins tab": "Вкладка Агенти та адміністратори", "Agents menu": "Меню агентів", "Agents only": "Тільки агенти", "All": "Всі", "All channels": "Усі канали", "All messages": "Усі повідомлення", "All questions": "Всі питання", "Allow only extended licenses": "Дозволити лише розширені ліцензії", "Allow only one conversation": "Дозволити лише одну розмову", "Allow only one conversation per user.": "Дозволити лише одну бесіду для кожного користувача.", "Allow the chatbot to reply to the user's emails if the answer is known and email piping is active.": "Дозвольте чат-боту відповідати на електронні листи користувача, якщо відповідь відома і передача електронної пошти активна.", "Allow the chatbot to reply to the user's text messages if the answer is known.": "Дозвольте чат-боту відповідати на текстові повідомлення користувача, якщо відповідь відома.", "Allow the user to archive a conversation and hide archived conversations.": "Дозволити користувачеві архівувати бесіду та приховувати заархівовані бесіди.", "Allow users to contact you via their favorite messaging apps.": "Дозвольте користувачам зв’язуватися з вами через їхні улюблені програми обміну повідомленнями.", "Allow users to select a product on ticket creation.": "Дозволити користувачам вибрати продукт під час створення квитка.", "Always all messages": "Завжди всі повідомлення", "Always incoming messages only": "Завжди тільки вхідні повідомлення", "Always sort conversations by date in the admin area.": "Завжди сортуйте бесіди за датою в області адміністрування.", "API key": "Ключ API", "Append the registration user details to the success message.": "Додайте реєстраційні дані користувача до повідомлення про успіх.", "Apply a custom background image for the header area.": "Застосуйте власне фонове зображення для області заголовка.", "Apply changes": "Застосувати зміни", "Apply to": "Застосувати до", "Archive all user channels in the Slack app. This operation may take a long time to complete. Important: All of your slack channels will be archived.": "Заархівуйте всі канали користувачів у програмі Slack. Ця операція може зайняти багато часу. Важливо: усі ваші слабкі канали буде заархівовано.", "Archive automatically the conversations marked as read every 24h.": "Кожні 24 години автоматично архівуйте розмови, позначені як прочитані.", "Archive channels": "Архівні канали", "Archive channels now": "Архівуйте канали зараз", "Articles": "Статті", "Articles area": "Область статей", "Articles button link": "Посилання на кнопку &quot;Статті&quot;.", "Articles page URL": "URL-адреса сторінки статей", "Artificial Intelligence": "Штучний інтелект", "Assign a department to all conversations started from Google Business Messages. Enter the department ID.": "Призначте відділ для всіх розмов, розпочатих із Google Business Messages. Введіть ідентифікатор відділу.", "Assign a department to all conversations started from Twitter. Enter the department ID.": "Призначте відділ для всіх розмов, розпочатих із Twitter. Введіть ідентифікатор відділу.", "Assign a department to all conversations started from Viber. Enter the department ID.": "Призначити відділ для всіх розмов, розпочатих з Viber. Введіть ідентифікатор відділу.", "Assign a department to all conversations started from WeChat. Enter the department ID.": "Призначте відділ для всіх розмов, розпочатих із WeChat. Введіть ідентифікатор відділу.", "Assign different departments to conversations started from different Google Business Messages locations. This setting overrides the default department.": "Призначайте різні відділи для розмов, розпочатих із різних місць Google Business Messages. Цей параметр замінює відділ за замовчуванням.", "Assistant": "Помічник", "Assistant ID": "ID помічника", "Attachments list": "Список вкладень", "Audio file URL - admin": "URL аудіофайлу - адмін", "Automatic": "Автоматичний", "Automatic human takeover": "Автоматичне поглинання людини", "Automatic translation": "Автоматичний переклад", "Automatic updates": "Автоматичні оновлення", "Automatically archive conversations": "Автоматично архівувати бесіди", "Automatically assigns a department based on the user's active plans. Insert -1 as plan ID for users without any plan.": "Автоматично призначає відділ на основі активних планів користувача. Вставте -1 як ідентифікатор плану для користувачів без плану.", "Automatically check and install new updates. A valid Envato Purchase Code and valid apps's license keys are required.": "Автоматично перевіряйте та встановлюйте нові оновлення. Потрібні дійсний код покупки Envato та ліцензійні ключі дійсних програм.", "Automatically collapse the conversation details panel, and other panels, of the admin area.": "Автоматично згортати панель деталей розмови та інші панелі в області адміністратора.", "Automatically create a department for each website and route the conversations of each website to the right department. This setting requires a WordPress Multisite installation.": "Автоматично створюйте відділ для кожного веб-сайту та направляйте розмови кожного веб-сайту до потрібного відділу. Цей параметр вимагає багатосайтової інсталяції WordPress.", "Automatically hide the conversation details panel.": "Автоматично приховати панель деталей розмови.", "Automatically send cart reminders to customers with products in their carts. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "Автоматично надсилати нагадування про кошик клієнтам з продуктами в кошиках. Ви можете використовувати такі поля злиття та більше: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.", "Automatically sync Zendesk customers with {R}, view Zendesk tickets, or create new ones without leaving {R}.": "Автоматично синхронізуйте клієнтів Zendesk із {R}, переглядайте квитки Zendesk або створюйте нові, не виходячи з {R}.", "Automatically synchronize products, categories, tags, and more with Dialogflow, and enable the bot to answer autonomously to questions related to your shop.": "Автоматично синхронізуйте продукти, категорії, теги тощо за допомогою Dialogflow і дозвольте боту самостійно відповідати на запитання, пов’язані з вашим магазином.", "Automatically translate admin area": "Автоматично перекладати область адміністратора", "Automatically translate the admin area to match the agent profile language or browser language.": "Автоматично перекладайте область адміністратора відповідно до мови профілю агента або мови браузера.", "Avatar image": "Зображення аватара", "Away mode": "Режим «Вдома».", "Before initiating the chat, the user must accept a privacy message in order to gain access.": "Перш ніж почати чат, користувач повинен прийняти повідомлення про конфіденційність, щоб отримати доступ.", "Birthday": "День народження", "Body variables": "Змінні тіла", "Bot name": "Ім&#39;я бота", "Bot profile image": "Зображення профілю бота", "Bot response delay": "Затримка відповіді бота", "Bottom": "Нижня частина", "Brand": "<PERSON>р<PERSON><PERSON>д", "Built-in chat button icons": "Вбудовані значки кнопок чату", "Business Account ID": "Ідентифікатор бізнес-облікового запису", "Button action": "Дія кнопки", "Button name": "Назва кнопки", "Button text": "Текст кнопки", "Button variables": "Кнопкові змінні", "Cancel button text": "Текст кнопки скасування", "Cart": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Cart follow up message": "Повідомлення про подальше оновлення кошика", "Catalogue details": "Детал<PERSON> каталогу", "Catalogue ID": "Ідентифіка<PERSON>о<PERSON> каталогу", "Change the chat button image with a custom one.": "Змініть зображення кнопки чату на власне.", "Change the default field names.": "Змініть імена полів за замовчуванням.", "Change the message text in the header area of the chat widget. This text will be replaced by the agent headline once the first reply is sent.": "Змініть текст повідомлення в області заголовка віджета чату. Після надсилання першої відповіді цей текст буде замінено заголовком агента.", "Change the title text in the header area of the chat widget. This text will be replaced by the agent's name once the first reply is sent.": "Змініть текст заголовка в області заголовка віджета чату. Після надсилання першої відповіді цей текст буде замінено на ім’я агента.", "Channel ID": "Ідентифіка<PERSON><PERSON><PERSON> каналу", "Channels": "Канали", "Channels filter": "Фільтр ка<PERSON><PERSON><PERSON>в", "Chat": "Чат", "Chat and admin": "Чат і адмін", "Chat background": "Фон чату", "Chat button icon": "Значок кнопки чату", "Chat button offset": "Зміщення кнопки чату", "Chat message": "Повідомлення в чаті", "Chat only": "Тільки чат", "Chat position": "Позиція в чаті", "Chatbot": "Чат-бот", "Chatbot mode": "Режим чат-бота", "Check Requirements": "Перевірте вимоги", "Check the server configurations and make sure it has all the requirements.": "Перевірте конфігурацію сервера та переконайтеся, що він відповідає всім вимогам.", "Checkout": "Перевіряти", "Choose a background texture for the chat header and conversation area.": "Виберіть фонову текстуру для заголовка чату та області розмови.", "Choose where to display the chat. Enter the values separated by commas.": "Виберіть місце для відображення чату. Введіть значення через кому.", "Choose which fields to disable from the tickets area.": "Виберіть, які поля вимкнути в області квитків.", "Choose which fields to include in the new ticket form.": "Виберіть, які поля включити в нову форму квитка.", "Choose which fields to include in the registration form. The name field is included by default.": "Виберіть, які поля включити в реєстраційну форму. Поле імені включено за замовчуванням.", "Choose which user system the front-end chat will use to register and log in users.": "Виберіть, яку систему користувачів використовуватиме інтерфейсний чат для реєстрації та входу користувачів.", "City": "Місто", "Clear flows": "Ясні потоки", "Click the button to start the Dialogflow synchronization.": "Натисніть кнопку, щоб почати синхронізацію Dialogflow.", "Click the button to start the Slack synchronization. Localhost cannot and does not receive messages. Log in with another account or as a visitor to perform your tests.": "Натисніть кнопку, щоб почати синхронізацію Slack. Localhost не може і не отримує повідомлення. Увійдіть під іншим обліковим записом або як відвідувач, щоб виконати свої тести.", "Client email": "Електронна адреса клієнта", "Client ID": "Ідентифікатор клієнта", "Client token": "Токен клієнта", "Close chat": "Закрити чат", "Close message": "Закрити повідомлення", "Cloud API numbers": "Номери Cloud API", "Cloud API settings": "Налаштування Cloud API", "Cloud API template fallback": "Резервний шаблон Cloud API", "Code": "<PERSON>од", "Collapse panels": "Згорнути панелі", "Color": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Communicate with your users right from Slack. Send and receive messages and attachments, use emojis, and much more.": "Спілкуйтеся зі своїми користувачами прямо зі Slack. Надсилайте й отримуйте повідомлення та вкладення, використовуйте смайли та багато іншого.", "Company": "Компанія", "Concurrent chats": "Коти-конкуренти", "Configuration URL": "URL-адреса конфігурації", "Confirm button text": "Текст кнопки підтвердження", "Confirmation message": "Повідомлення про підтвердження", "Connect smart chatbots and automate conversations by using one of the most advanced forms of artificial intelligence in the world.": "Підключайте розумні чат-боти та автоматизуйте розмови за допомогою однієї з найсучасніших форм штучного інтелекту у світі.", "Connect stores to agents.": "Підключайте магазини до агентів.", "Connect your Telegram bot to {R} to read and reply to all messages sent to your Telegram bot directly in {R}.": "Підключіть свого бота Telegram до {R}, щоб читати та відповідати на всі повідомлення, надіслані вашому боту Telegram, безпосередньо в {R}.", "Connect your Viber bot to {R} to read and reply to all messages sent to your Viber bot directly in {R}.": "Підключіть свій бот Viber до {R}, щоб читати та відповідати на всі повідомлення, надіслані вашому боту Viber, безпосередньо в {R}.", "Connect your Zalo Official Account to {R} to read and reply to all messages sent to your Zalo Official Account directly in {R}.": "Підключіть свій офіційний обліковий запис Zalo до {R}, щоб читати та відповідати на всі повідомлення, надіслані на ваш офіційний обліковий запис Zalo безпосередньо в {R}.", "Content": "Зміст", "Content template SID": "SID шаблону вмісту", "Conversation profile": "Профіль розмови", "Conversations data": "Дані розмов", "Convert all emails": "Конвертувати всі електронні листи", "Cookie domain": "Домен файлів cookie", "Country": "Країна", "Coupon discount (%)": "Купонна знижка (%)", "Coupon expiration (days)": "Термін дії купона (днів)", "Coupon expiration (seconds)": "Термін дії купона (секунди)", "Create a WordPress user upon registration.": "Створіть користувача WordPress після реєстрації.", "Create Intents now": "Створіть Intents зараз", "Currency symbol": "Символ валюти", "Custom CSS": "Користувацький CSS", "Custom fields": "Користувацькі поля", "Custom JS": "Спеціальний JS", "Custom model ID": "Спеціальний ідентифікатор моделі", "Custom parameters": "Спеціальні параметри", "Customize the link for the 'All articles' button.": "Налаштуйте посилання для кнопки «Усі статті».", "Dashboard display": "Дисплей приладової панелі", "Dashboard title": "Назва інформаційної панелі", "Database details": "Деталі бази даних", "Database host": "Хост бази даних", "Database name": "Ім&#39;я бази даних", "Database password": "Пароль до бази даних", "Database prefix": "Префікс бази даних", "Database user": "Користувач бази даних", "Decline button text": "Текст кнопки &quot;Від<PERSON>илити&quot;.", "Declined message": "Відхилено повідомлення", "Default": "За замовчуванням", "Default body text": "Основний текст за умовчанням", "Default conversation name": "Назва бесіди за замовчуванням", "Default department": "Дефолтний відділ", "Default department ID": "Стандартний ідентифікатор відділу", "Default form": "Форма за замовчуванням", "Default header text": "Текст заголовка за умовчанням", "Delay (ms)": "Затримка (мс)", "Delete all leads and all messages and conversations linked to them.": "Видаліть усі потенційні клієнти та всі повідомлення та бесіди, пов’язані з ними.", "Delete conversation": "Видалити розмову", "Delete leads": "Видалити потенційних клієнтів", "Delete message": "Видалити повідомлення", "Delete the built-in flows.": "Видаліть вбудовані потоки.", "Delimiter": "Розмежувач", "Department": "<PERSON>і<PERSON><PERSON><PERSON>л", "Department ID": "Ідентифікатор відділу", "Departments": "Відділи", "Departments settings": "Налаштування відділів", "Desktop notifications": "Сповіщення на робочому столі", "Dialogflow - Department linking": "Dialogflow - зв&#39;язок відділів", "Dialogflow chatbot": "Чат-бот Dialogflow", "Dialogflow edition": "Видання Dialogflow", "Dialogflow Intent detection confidence": "Впевненість виявлення наміру Dialogflow", "Dialogflow location": "Розташування Dialogflow", "Dialogflow spelling correction": "Виправлення орфографії Dialogflow", "Dialogflow welcome Intent": "Намір привітання Dialogflow", "Disable agents check": "Вимкнути перевірку агентів", "Disable and hide the chat widget if all agents are offline.": "Вимкніть і приховайте віджет чату, якщо всі агенти в автономному режимі.", "Disable and hide the chat widget outside of scheduled office hours.": "Вимкніть та приховайте віджет чату поза графіком роботи.", "Disable any features that you don't need.": "Вимкніть усі непотрібні функції.", "Disable auto-initialization of the chat widget. When this setting is active you must initialize the chat widget with a custom JavaScript API code written by you. If the chat doesn't appear and this setting is enabled, disable it.": "Вимкніть автоініціалізацію віджета чату. Коли це налаштування активне, ви повинні ініціалізувати віджет чату за допомогою спеціального коду JavaScript API, написаного вами. Якщо чат не відображається, а цей параметр увімкнено, вимкніть його.", "Disable auto-initialization of the tickets area. When this setting is active you must initialize the tickets area with a custom JavaScript API code written by you. If the tickets area doesn't appear and this setting is enabled, disable it.": "Вимкніть автоініціалізацію області квитків. Коли це налаштування активне, ви повинні ініціалізувати область квитків за допомогою спеціального коду JavaScript API, написаного вами. Якщо область квитків не відображається, а цей параметр увімкнено, вимкніть його.", "Disable chatbot": "Вимкнути чат-бота", "Disable cron job": "Вимкнути роботу cron", "Disable dashboard": "Вимкнути інформаційну панель", "Disable during office hours": "Вимкнути в робочий час", "Disable features": "Вимкнути функції", "Disable features you don't use and improve the chat performance.": "Вимкніть функції, які ви не використовуєте, і покращте продуктивність чату.", "Disable file uploading capabilities within the chat.": "Вимкніть можливість завантаження файлів у чаті.", "Disable for messaging channels": "Вимкнути для каналів обміну повідомленнями", "Disable for the tickets area": "Вимкнути для області квитків", "Disable invitation": "Вимкнути запрошення", "Disable online status check": "Вимкнути перевірку онлайн-статусу", "Disable outside of office hours": "Вимкнути в неробочий час", "Disable password": "Вимкніть пароль", "Disable registration during office hours": "Вимкнути реєстрацію в робочий час", "Disable registration if agents online": "Вимкніть реєстрацію, якщо агенти онлайн", "Disable the automatic invitation of agents to the channels.": "Вимкніть автоматичне запрошення агентів на канали.", "Disable the channels filter.": "Вимкніть фільтр каналів.", "Disable the chatbot for the tickets area.": "Вимкніть чат-бота для зони квитків.", "Disable the chatbot for this channel only.": "Вимкніть чат-бота лише для цього каналу.", "Disable the dashboard, and allow only one conversation per user.": "Вимкніть інформаційну панель і дозвольте лише одну бесіду на користувача.", "Disable the login and remove the password field from the registration form.": "Вимкніть логін і видаліть поле пароля з форми реєстрації.", "Disable uploads": "Вимкнути завантаження", "Disable voice message capabilities within the chat.": "Вимкніть можливості голосових повідомлень у чаті.", "Disable voice messages": "Вимкнути голосові повідомлення", "Disabled": "Вимкнено", "Display a brand image in the header area. This only applies for the 'brand' header type.": "Відображення зображення бренду в області заголовка. Це стосується лише типу заголовка «бренд».", "Display categories": "Показати категорії", "Display images": "Відображення зображень", "Display in conversation list": "Відображення в списку розмов", "Display in dashboard": "Відображення на приладовій панелі", "Display online agents only": "Показати лише онлайн-агентів", "Display the articles section in the right area.": "Відобразіть розділ статей у правій області.", "Display the dashboard instead of the chat area on initialization.": "Відображати інформаційну панель замість області чату під час ініціалізації.", "Display the feedback form to rate the conversation when it is archived.": "Відобразити форму зворотнього зв’язку, щоб оцінити розмову, коли її заархівовано.", "Display the user full name in the left panel instead of the conversation title.": "Відображати повне ім’я користувача на панелі ліворуч замість назви бесіди.", "Display the user's profile image within the chat.": "Відображати зображення профілю користувача в чаті.", "Display user name in header": "Відобразити ім&#39;я користувача в заголовку", "Display user's profile image": "Показати зображення профілю користувача", "Displays additional columns in the user table. Enter the name of the fields to add.": "Відображає додаткові стовпці в таблиці користувача. Введіть назву полів, які потрібно додати.", "Distribute conversations proportionately between agents and notify visitors of their position within the queue. Response time is in minutes. You can use the following merge fields in the message: {position}, {minutes}. They will be replaced by the real values in real-time.": "Пропорційно розподіляйте розмови між агентами та повідомляйте відвідувачів про їхню позицію в черзі. Час відповіді в хвилинах. У повідомленні можна використовувати такі поля об’єднання: {position}, {minutes}. Вони будуть замінені реальними значеннями в режимі реального часу.", "Distribute conversations proportionately between agents, and block an agent from viewing the conversations of the other agents.": "Розподіліть розмови пропорційно між агентами та заблокуйте агенту перегляд розмов інших агентів.", "Do not send email notifications to admins": "Не надсилайте сповіщення електронною поштою адміністраторам", "Do not show tickets in chat": "Не показувати квитки в чаті", "Do not translate settings area": "Не перекладати область налаштувань", "Download": "Завант<PERSON><PERSON><PERSON>ти", "Edit profile": "Редагувати профіль", "Edit user": "Редагувати користувача", "Email address": "Адреса електронної пошти", "Email and ticket": "Електронна пошта та квиток", "Email header": "Заголовок електронного листа", "Email notification delay (hours)": "Затримка сповіщень електронною поштою (години)", "Email notifications via cron job": "Сповіщення електронною поштою через завдання cron", "Email only": "Лише електронна пошта", "Email piping": "Розсилка електронної пошти", "Email piping server information and more settings.": "Інформація про сервер електронної пошти та інші налаштування.", "Email request message": "Повідомлення із запитом електронною поштою", "Email signature": "Підпис електронної пошти", "Email template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "Шаблон електронного листа для електронного листа, який надсилається користувачеві, коли агент відповідає. Ви можете використовувати текст, HTML і такі поля злиття: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Email template for the email sent to an agent when a user sends a new message. You can use text, HTML, and the following merge fields: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "Шаблон електронної пошти для електронного листа, який надсилається агенту, коли користувач надсилає нове повідомлення. Ви можете використовувати текст, HTML і такі поля злиття: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Email template for the email sent to the user after submitting their email through the follow-up message form. You can use text, HTML, and the following merge fields: {user_name}, {user_email}.": "Шаблон електронної пошти для електронного листа, який надсилається користувачеві після надсилання електронної пошти через форму подальшого повідомлення. Ви можете використовувати текст, HTML і такі поля злиття: {user_name}, {user_email}.", "Email template for the email sent to the user to verify their email address. Include the {code} merge field within your content, it will be replaced with the one-time code.": "Шаблон електронної пошти для електронного листа, надісланого користувачеві для підтвердження його електронної адреси. Додайте поле злиття {code} у свій вміст, його буде замінено одноразовим кодом.", "Email verification": "Підтвердження електронної пошти", "Email verification content": "Вміст перевірки електронної пошти", "Enable email verification with OTP.": "Увімкніть перевірку електронної пошти за допомогою OTP.", "Enable logging of agent activity": "Увімкнути реєстрацію активності агента", "Enable logs": "Увімкнути журнали", "Enable the chatbot outside of scheduled office hours only.": "Увімкніть чат-бота лише у неробочий час.", "Enable the registration only if all agents are offline.": "Увімкніть реєстрацію, лише якщо всі агенти знаходяться в автономному режимі.", "Enable the registration outside of scheduled office hours only.": "Увімкніть реєстрацію лише в неробочий час.", "Enable this option if email notifications are sent via cron job.": "Увімкніть цей параметр, якщо сповіщення електронною поштою надсилаються через завдання cron.", "Enable ticket and chat support for subscribers only, view member profile details and subscription details in the admin area.": "Увімкніть підтримку квитків і чатів лише для передплатників, перегляньте дані профілю учасника та інформацію про підписку в області адміністратора.", "Enter the bot token and click the button to synchronize the Telegram bot. Localhost cannot receive messages.": "Введіть токен бота та натисніть кнопку для синхронізації бота Telegram. Localhost не може отримувати повідомлення.", "Enter the bot token and click the button to synchronize the Viber bot. Localhost cannot receive messages.": "Введіть токен бота та натисніть кнопку для синхронізації бота Viber. Localhost не може отримувати повідомлення.", "Enter the database details of the Active eCommerce CMS database.": "Введіть деталі бази даних Active eCommerce бази даних CMS.", "Enter the database details of the Martfury database.": "Введіть деталі бази даних бази даних Martfury.", "Enter the database details of the Perfex database.": "Введіть дані бази даних Perfex.", "Enter the database details of the WHMCS database.": "Введіть деталі бази даних WHMCS.", "Enter the default messages used by the chatbot when user question requires a dynamic answer.": "Введіть повідомлення за замовчуванням, які використовує чат-бот, коли запитання користувача вимагає динамічної відповіді.", "Enter the details of your Google Business Messages.": "Введіть деталі своїх повідомлень Google Business Messages.", "Enter the details of your Twitter app.": "Введіть деталі вашої програми Twitter.", "Enter the LINE details to start using it. Localhost cannot receive messages.": "Введіть дані LINE, щоб почати використовувати його. Localhost не може отримувати повідомлення.", "Enter the URL of a .css file, to load it automatically in the admin area.": "Введіть URL-адресу файлу .css, щоб він автоматично завантажився в адмінку.", "Enter the URL of a .js file, to load it automatically in the admin area.": "Введіть URL-адресу файлу .js, щоб він автоматично завантажився в області адміністратора.", "Enter the URL of the articles page.": "Введіть URL-адресу сторінки статей.", "Enter the URLs of your shop": "Введіть URL-адреси вашого магазину", "Enter the WeChat official account token. See the docs for more details.": "Введіть офіційний маркер облікового запису WeChat. Додаткову інформацію дивіться в документах.", "Enter the Zalo details to start using it. Localhost cannot receive messages.": "Введіть дан<PERSON>, щоб почати використовувати його. Localhost не може отримувати повідомлення.", "Enter your 360dialog account settings information.": "Введіть інформацію про налаштування облікового запису 360dialog.", "Enter your Envato Purchase Code to activate automatic updates and unlock all the features.": "Введіть свій код покупки Envato, щоб активувати автоматичні оновлення та розблокувати всі функції.", "Enter your Twilio account details. You can use text and the following merge fields: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.": "Введіть дані свого облікового запис<PERSON> Twilio. Ви можете використовувати текстові та такі поля злиття: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.", "Enter your Twilio account settings information.": "Введіть інформацію про налаштування облікового запису Twilio.", "Enter your WeChat Official Account information.": "Введіть інформацію свого офіційного облікового запису WeChat.", "Enter your Zendesk information.": "Введіть інформацію про Zendesk.", "Entities": "Entities", "Envato Purchase Code": "Код покупки Envato", "Envato purchase code validation": "Перевірка коду покупки Envato", "Exclude products": "Виключити продукти", "Export all settings.": "Експортувати всі налаштування.", "Export settings": "Експортувати налаштування", "Facebook pages": "Сторінки у Facebook", "Fallback message": "Резервне повідомлення", "Filters": "Фільтри", "First chat message": "Перше повідомлення в чаті", "First reminder delay (hours)": "Затримка першого нагадування (години)", "First ticket form": "Пер<PERSON>ий бланк квитка", "Flash notifications": "Flash-сповіщення", "Follow up - Email": "Далі – електронна пошта", "Follow up email": "Подальший електронний лист", "Follow up message": "Наступне повідомлення", "Follows a conversation between a human agent and an end user and provide response suggestions to the human agent in real-time.": "Слідкує за розмовою між агентом-людиною та кінцевим користувачем та надає пропозиції відповідей агенту-людині в режимі реального часу.", "Follow-up email template. You can use text, HTML, and the following merge fields and more: {coupon}, {product_names}, {user_name}.": "Шаблон подальшої електронної пошти. Ви можете використовувати текст, HTML, наступні поля злиття та багато іншого: {coupon}, {product_names}, {user_name}.", "Force language": "Примусова мова", "Force log out": "Примусово вийти", "Force the chat to ignore the language preferences, and to use always the same language.": "Змусьте чат ігнорувати мовні налаштування та завжди використовувати ту саму мову.", "Force the loggout of Support Board agents if they are not logged in WordPress.": "Примусово вийти з системи агентів Support Board, якщо вони не ввійшли в WordPress.", "Force users to use a different conversation for each store and hide conversations from other stores from store administrators.": "Змусити користувачів використовувати різні розмови для кожного магазину та приховати розмови з інших магазинів від адміністраторів магазинів.", "Force users to use only one phone country code.": "Змусити користувачів використовувати лише один телефонний код країни.", "Form message": "Форма повідомлення", "Form title": "Назва форми", "Frequency penalty": "Штраф за частоту", "Full visitor details": "Повна інформація про відвідувач<PERSON>в", "Function name": "Назва функції", "Generate conversations data": "Створення даних розмов", "Generate user questions": "Створюйте запитання користувачів", "Get configuration URL": "Отримати URL-адресу конфігурації", "Get it from the APP_KEY value of the file .env located in the root directory of Active eCommerce.": "Отримайте його зі значення APP_KEY файлу .env, розташованого в кореневому каталозі Active eCommerce.", "Get it from the APP_KEY value of the file .env located in the root directory of Martfury.": "Отримайте його зі значення APP_KEY файлу .env, розташованого в кореневому каталозі Martfury.", "Get Path": "Отримати шлях", "Get Service Worker path": "Отримати шлях Service Worker", "Get URL": "Отримати URL", "Google and Dialogflow settings.": "Налаштування Google і Dialogflow.", "Google search": "Пошук Google", "Header": "Заголовок", "Header background image": "Фонове зображення заголовка", "Header brand image": "Зображення бренду в заголовку", "Header message": "Заголовок повідомлення", "Header title": "Назва заголовка", "Header type": "Тип заголовка", "Header variables": "Змінні заголовка", "Hide": "Сховати", "Hide agent's profile image": "Приховати зображення профілю агента", "Hide archived tickets": "Сховати заархівовані квитки", "Hide archived tickets from users.": "Приховати архівні квитки від користувачів.", "Hide chat if no agents online": "Приховати чат, якщо в мережі немає агентів", "Hide chat outside of office hours": "Приховати чат у неробочий час", "Hide conversation details panel": "Приховати панель деталей розмови", "Hide conversations of other agents": "Приховати розмови інших агентів", "Hide on mobile": "Приховати на мобільному", "Hide the agent's profile image within the chat.": "Приховати зображення профілю агента в чаті.", "Hide tickets from the chat widget and chats from the ticket area.": "Приховати квитки від віджета чату та чати в області квитків.", "Hide timetable": "Приховати розклад", "Host": "Ведучий", "Human takeover": "Поглинання людини", "If no agents respond within the specified time interval, a message will be sent to request the user's details, such as their email.": "Якщо жоден агент не відповість протягом указаного інтервалу часу, буде надіслано повідомлення із запитом інформації про користувача, наприклад його електронної адреси.", "If the chatbot doesn't understand a user's question, forwards the conversation to an agent.": "Якщо чат-бот не розуміє запитання користувача, перенаправляє розмову агенту.", "Image": "Зображення", "Import admins": "Імпорт адміністратор<PERSON>в", "Import all settings.": "Імпортувати всі налаштування.", "Import articles": "Імпорт статей", "Import contacts": "Імпорт контактів", "Import customers": "Імпортні клієнти", "Import customers into Support Board. Only new customers will be imported.": "Імпорт клієнтів у Support Board. Імпортуватимуться лише нові клієнти.", "Import settings": "Налаштування імпорту", "Import users": "Імпорт користувачів", "Import users from a CSV file.": "Імпортуйте користувачів із файлу CSV.", "Import vendors": "Імпортні постачальники", "Import vendors into Support Board as agents. Only new vendors will be imported.": "Імпортуйте постачальників у Support Board як агентів. Імпортуватимуться лише нові постачальники.", "Improve chat performance with Pusher and WebSockets. This setting stops all AJAX/HTTP real-time requests that slow down your server and use instead the WebSockets.": "Підвищте продуктивність чату за допомогою Pusher і WebSockets. Цей параметр зупиняє всі запити AJAX/HTTP в реальному часі, які сповільнюють роботу вашого сервера і замість цього використовують WebSockets.", "Include custom fields": "Включіть спеціальні поля", "Include custom fields in the registration form.": "Включіть спеціальні поля в реєстраційну форму.", "Include the password field in the registration form.": "Включіть поле пароля в реєстраційну форму.", "Incoming conversations and messages": "Вхідні розмови та повідомлення", "Incoming conversations only": "Лише вхідні розмови", "Incoming messages only": "Тільки вхідні повідомлення", "Increase sales and connect you and sellers with customers in real-time by integrating Active eCommerce with Support Board.": "Збільште продажі та зв’яжіть себе та продавців із клієнтами в режимі реального часу, інтегрувавши Active eCommerce із Support Board.", "Increase sales, provide better support, and faster solutions, by integrating WooCommerce with Support Board.": "Збільште продажі, забезпечте кращу підтримку та швидші рішення, інтегрувавши WooCommerce з Support Board.", "Info message": "Інформаційне повідомлення", "Initialize and display the chat widget and tickets only for members.": "Ініціалізуйте та відображайте віджет чату та квитки лише для учасників.", "Initialize and display the chat widget only when the user is logged in.": "Ініціалізувати та відображати віджет чату лише тоді, коли користувач увійшов у систему.", "Instance ID": "Ідентифікатор екземпляра", "Integrate OpenCart with {R} for real-time syncing of customers, order history access, and customer cart visibility.": "Інтегруйте OpenCart із {R} для синхронізації клієнтів у реальному часі, доступу до історії замовлень і видимості кошика клієнтів.", "Interval (sec)": "Інтервал (сек)", "IP banning": "IP заборона", "Label": "Етикетка", "Language": "Мова", "Language detection": "Виявлення мови", "Language detection message": "Повідомлення про визначення мови", "Last name": "Прізвище", "Leave it blank if you don't know what this setting is! Entering an incorrect value will break the chat. Sets the main domain where chat is used to enable login and conversations sharing between the main domain and sub domains.": "Залиште поле порожнім, якщо ви не знаєте, що це за налаштування! Введення неправильного значення призведе до розриву чату. Встановлює основний домен, у якому використовується чат, щоб увімкнути доступ до входу та розмов між основним доменом і субдоменами.", "Left": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Left panel": "Ліва панель", "Left profile image": "Зображення лівого профілю", "Let the bot to search on Google to find answers to user questions.": "Дозвольте бота шукати в Google, щоб знайти відповіді на запитання користувачів.", "Let the chatbot search on Google to find answers to user questions.": "Дозвольте чат-боту шукати в Google відповіді на запитання користувачів.", "Lets your users reach you via Twitter. Read and reply to messages sent to your Twitter account directly from {R}.": "Дозволяє вашим користувачам зв’язуватися з вами через Twitter. Читайте та відповідайте на повідомлення, надіслані на ваш обліковий запис Twitter безпосередньо з {R}.", "Lets your users reach you via WeChat. Read and reply to all messages sent to your WeChat official account directly from {R}.": "Дозволяє користувачам зв’язуватися з вами через WeChat. Читайте та відповідайте на всі повідомлення, надіслані на ваш офіційний обліковий запис WeChat безпосередньо з {R}.", "Lets your users reach you via WhatsApp. Read and reply to all messages sent to your WhatsApp Business account directly from {R}.": "Дозволяє користувачам зв’язуватися з вами через WhatsApp. Читайте та відповідайте на всі повідомлення, надіслані на ваш обліковий запис WhatsApp Business безпосередньо з {R}.", "Link each agent with the corresponding Slack user, so when an agent replies via Slack it will be displayed as the assigned agent.": "Зв’яжіть кожного агента з відповідним користувачем Slack, тому, коли агент відповідає через Slack, він відображатиметься як призначений агент.", "Link name": "Назва посилання", "Login form": "Форма входу", "Login initialization": "Ініціалізація входу", "Login verification URL": "URL підтвердження входу", "Logit bias": "Логічне зміщення", "Make a backup of your Dialogflow agent first. This operation can take several minutes.": "Спершу зробіть резервну копію агента Dialogflow. Ця операція може тривати кілька хвилин.", "Make the registration phone field mandatory.": "Зробіть поле реєстраційного телефону обов’язковим.", "Manage": "Керувати", "Manage here the departments settings.": "Керуйте тут налаштуваннями відділів.", "Manage the tags settings.": "Керуйте налаштуваннями тегів.", "Manifest file URL": "URL-адреса файлу Manifest", "Manual": "Інструкція", "Manual initialization": "Ручна ініціалізація", "Martfury root directory path, e.g. /var/www/": "Шлях кореневого каталогу Martfury, наприклад /var/www/", "Martfury shop URL, e.g. https://shop.com": "URL-адреса магазин<PERSON> Martfury, наприклад https://shop.com", "Max message limit": "Максимальний ліміт повідомлень", "Max tokens": "Макс жетонів", "Members only": "Тільки члени", "Members with an active paid plan only": "Лише для учасників з активним платним планом", "Message": "Повідомлення", "Message area": "Область повідомлень", "Message rewrite button": "Кнопка перезапису повідомлення", "Message template": "Шаблон повідомлення", "Message type": "Тип повідомлення", "Messaging channels": "Канали обміну повідомленнями", "Messenger and Instagram settings": "Messenger і налаштування Instagram", "Minify JS": "Мінімізувати JS", "Minimal": "Мінімальний", "Model": "Модель", "Multilingual": "Багатомовний", "Multilingual plugin": "Багатомовний плагін", "Multilingual via translation": "Багатомовний через переклад", "Multlilingual training sources": "Багатомовні навчальні джерела", "Name": "Ім&#39;я", "Namespace": "Простір імен", "New conversation email": "Нова розмова електронною поштою", "New conversation notification": "Сповіщення про нову розмову", "New ticket button": "Кнопка нового квитка", "Newsletter": "Інформац<PERSON>йний бюлетень", "No delay": "Без затримки", "No results found.": "Нічого не знайдено.", "No, we don't ship in": "Ні, ми не відправляємо", "None": "Жодного", "Note data scraping": "Зверніть увагу на збирання даних", "Notes": "Примітки", "Notifications icon": "Значок сповіщень", "Notify the user when their message is sent outside of the scheduled office hours or all agents are offline.": "Повідомляйте користувача, коли його повідомлення надіслано поза запланованими робочими годинами або всі агенти офлайн.", "OA secret key": "Секретний ключ OA", "Offline message": "Офлайн-повідомлення", "Offset": "Зміщення", "On chat open": "У чаті відкрито", "On page load": "При завантаженні сторінки", "One conversation per agent": "Одна розмова на агента", "One conversation per department": "Одна розмова на відділ", "Online users notification": "Повідомлення онлайн-користува<PERSON>ів", "Only desktop": "Тільки робочий стіл", "Only general questions": "Тільки загальні питання", "Only mobile devices": "Тільки мобільні пристрої", "Only questions related to your sources": "Лише запитання, пов’язані з вашими джерелами", "Open automatically": "Відкривається автоматично", "Open chat": "Відкрити чат", "Open the chat window automatically when a new message is received.": "Відкривати вікно чату автоматично при отриманні нового повідомлення.", "OpenAI Assistants - Department linking": "Помічники OpenAI - зв&#39;язок між відділами", "OpenAI settings.": "Налаштування OpenAI.", "Optional link": "Додаткове посилання", "Order webhook": "Замовити вебхук", "Other": "Ін<PERSON><PERSON>", "Outgoing SMTP server information.": "Інформація про вихідний SMTP-сервер.", "Page ID": "Ідентифік<PERSON><PERSON><PERSON><PERSON> сторінки", "Page IDs": "Ідентифікатори сторінок", "Page name": "Назва сторінки", "Page token": "То<PERSON><PERSON><PERSON> сторінки", "Panel height": "Висота панелі", "Panel name": "Назва панелі", "Panel title": "Назва панелі", "Panels arrows": "Панелі стрілки", "Password": "Пароль", "Perfex URL": "Perfex URL", "Performance optimization": "Оптимізація продуктивності", "Phone": "Телефон", "Phone number ID": "ID номера телефону", "Phone required": "Потрібен телефон", "Place ID": "ID місця", "Placeholder text": "Текст-заповнювач", "Play a sound for new messages and conversations.": "Відтворення звуку для нових повідомлень і розмов.", "Popup message": "Спливаюче повідомлення", "Port": "Порт", "Post Type slugs": "Post Type слимаків", "Presence penalty": "Штраф за присутність", "Prevent admins from receiving email notifications.": "Заборонити адміністраторам отримувати сповіщення електронною поштою.", "Prevent agents from viewing conversations assigned to other agents. This setting is automatically enabled if routing or queue is active.": "Заборонити агентам переглядати розмови, призначені іншим агентам. Цей параметр автоматично вмикається, якщо маршрутизація або черга активні.", "Prevent any abuse from users by limiting the number of messages sent to the chatbot from one device.": "Запобігайте будь-якому зловживанню з боку користувачів, обмеживши кількість повідомлень, надісланих до чат-бота з одного пристрою.", "Primary color": "Основний колір", "Priority": "Пріоритет", "Privacy link": "Посилання на конфіденційність", "Privacy message": "Повідомлення про конфіденційність", "Private chat": "Приватний чат", "Private chat linking": "Посилання на приватний чат", "Private key": "Прива<PERSON><PERSON><PERSON> ключ", "Product IDs": "Ідентифікатори продуктів", "Product removed notification": "Повідомлення про вилучений продукт", "Product removed notification - Email": "Повідомлення про вилучений продукт – електронна пошта", "Profile image": "Зображення профілю", "Project ID": "Ідентифікатор проекту", "Project ID or Agent Name": "Ідентифікатор проекту або ім&#39;я агента", "Prompt": "Підка<PERSON><PERSON>ть", "Prompt - Message rewriting": "Підказка - переписування повідомлення", "Protect the tickets area from spam and abuse with Google reCAPTCHA.": "Захистіть область квитків від спаму та зловживань за допомогою Google reCAPTCHA.", "Provide help desk support to your customers by including a ticket area, with all chat features included, on any web page in seconds.": "Забезпечте підтримку довідкової служби своїм клієнтам, включивши на будь-яку веб-сторінку за лічені секунди зону квитка з усіма функціями чату.", "Provider": "Провайдер", "Purchase button text": "Текст кнопки &quot;Придбати&quot;.", "Push notifications": "Push-повідомлення", "Push notifications settings.": "Налаштування push-повідомлень.", "Queue": "Черга", "Rating": "<PERSON>ей<PERSON>инг", "Read and reply to messages sent from Google Search, Maps and brand-owned channels directly in {R}.": "Читайте та відповідайте на повідомлення, надіслані з Пошуку Google, Карт і каналів бренду, безпосередньо в {R}.", "Read, manage and reply to all messages sent to your Facebook pages and Instagram accounts directly from {R}.": "Чит<PERSON>йте, керуйте та відповідайте на всі повідомлення, надіслані на ваші сторінки Facebook і облікові записи Instagram безпосередньо з {R}.", "Reconnect": "Повторне підключення", "Redirect the user to the registration link instead of showing the registration form.": "Перенаправити користувача на посилання для реєстрації замість показу форми реєстрації.", "Redirect the user to the specified URL if the registration is required and the user is not logged in. Leave blank to use the default registration form.": "Переспрямуйте користувача на вказану URL-адресу, якщо потрібна реєстрація, а користувач не ввійшов у систему. Залиште порожнім, щоб використовувати форму реєстрації за замовчуванням.", "Refresh token": "Оновити маркер", "Register all visitors": "Зареєструйте всіх відвідувачів", "Register all visitors automatically. When this option is not active, only the visitors that start a chat will be registered.": "Автоматична реєстрація всіх відвідувачів. Якщо ця опція не активна, будуть зареєстровані лише ті відвідувачі, які розпочали чат.", "Registration / Login": "Реєстрація / Вхід", "Registration and login form": "Форма реєстрації та входу", "Registration fields": "Реєстраційні поля", "Registration form": "Реєстраційний формуляр", "Registration link": "Посилання на реєстрацію", "Registration redirect": "Переадресація реєстрації", "Rename the chat bot. Default is 'Bot'.": "Перейменуйте чат-бота. За замовчуванням — «Бот».", "Rename the visitor name prefix. Default is 'User'.": "Перейменуйте префікс імені відвідувача. За замовчуванням — «Користувач».", "Repeat": "Повторіть", "Repeat - admin": "Повторюю - адмін", "Replace the admin login page message.": "Замініть повідомлення на сторінці входу адміністратора.", "Replace the brand logo on the admin login page.": "Замініть логотип бренду на сторінці входу адміністратора.", "Replace the header title with the user's first name and last name when available.": "Замініть заголовок заголовка на ім’я та прізвище користувача, якщо вони доступні.", "Replace the top-left brand icon on the admin area and the browser favicon.": "Замініть верхню ліву іконку бренду в області адміністратора та піктограму браузера.", "Reply to user emails": "Відповідь на електронні листи користувачів", "Reply to user text messages": "Відповідайте на текстові повідомлення користувача", "Reports": "Звіти", "Reports area": "Область звітів", "Request a valid Envato purchase code for registration.": "Запитуйте дійсний код покупки Envato для реєстрації.", "Request the user to provide their email address and then send a confirmation email to the user.": "Попросіть користувача надати свою електронну адресу, а потім надішліть йому електронний лист із підтвердженням.", "Require phone": "Потрібний телефон", "Require registration": "Вимагають реєстрації", "Require the user registration or login before start a chat. To enable the login area the password field must be included.": "Перед початком чату потрібна реєстрація або вхід користувача. Щоб увімкнути область входу, необхідно додати поле пароля.", "Require the user registration or login in order to use the tickets area.": "Вимагайте реєстрацію або вхід користувача, щоб використовувати зону квитків.", "Required": "Вимагається", "Response time": "Час реакції", "Restrict chat access by blocking IPs. List IPs with commas.": "Обмежте доступ до чату, заблокувавши IP-адреси. Перелік IP-адрес через кому.", "Returning visitor message": "Повідомлення відвідувача, що повертається", "Rich messages": "Розширені повідомлення", "Rich messages are code snippets that can be utilized within a chat message. They can contain HTML code and are automatically rendered in the chat. Rich messages can be used with the following syntax: [rich-message-name]. There are a tonne of built-in rich messages to choose from.": "Розширені повідомлення – це фрагменти коду, які можна використовувати в повідомленні чату. Вони можуть містити HTML-код і автоматично відображаються в чаті. Розширені повідомлення можна використовувати з таким синтаксисом: [rich-message-name]. На вибір є маса вбудованих розширених повідомлень.", "Right": "Правильно", "Right panel": "Права панель", "Routing": "Мар<PERSON>рутизація", "Routing if offline": "Маршрутизація в автономному режимі", "RTL": "RTL", "Save useful information like user country and language also for visitors.": "Зберігайте корисну інформацію, як-от країну та мову користувача, також для відвідувачів.", "Saved replies": "Збережені відповіді", "Scheduled office hours": "Заплановані години роботи", "Search engine ID": "Ідентифікатор пошукової системи", "Second chat message": "Друге повідомлення чату", "Second reminder delay (hours)": "Затримка другого нагадування (години)", "Secondary color": "Вторин<PERSON>ий колір", "Secret key": "Секретний ключ", "Send a message to allow customers to be notified when they can purchase a product they are interested in, but that is currently out of stock. You can use the following merge fields: {user_name}, {product_name}.": "Надішліть повідомлення, щоб клієнти отримували сповіщення, коли вони зможуть придбати продукт, який їх цікавить, але наразі його немає в наявності. Ви можете використовувати такі поля злиття: {user_name}, {product_name}.", "Send a message to new users when they create the first ticket. Text formatting and merge fields are supported.": "Надішліть повідомлення новим користувачам, коли вони створять перший квиток. Підтримуються форматування тексту та поля злиття.", "Send a message to new users when they visit the website for the first time.": "Надсилайте повідомлення новим користувачам, коли вони відвідують веб-сайт уперше.", "Send a message to the customer after a product has been removed from the cart. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.": "Надішліть клієнту повідомлення після того, як товар буде видалено з кошика. Ви можете використовувати такі поля злиття та багато іншого: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.", "Send a message to the customers who complete a purchase asking to share the product they just bought. You can use the following merge fields and more: {product_name}, {user_name}.": "Надішліть повідомлення клієнтам, які завершують покупку, із проханням поділитися продуктом, який вони щойно придбали. Ви можете використовувати такі поля злиття та інші: {product_name}, {user_name}.", "Send a message to the customers who complete a purchase. You can use the following merge fields and more: {coupon}, {product_names}, {user_name}.": "Надішліть повідомлення клієнтам, які завершили покупку. Ви можете використовувати такі поля злиття та інші: {coupon}, {product_names}, {user_name}.", "Send a message to the user when the agent archive the conversation.": "Надішліть користувачеві повідомлення, коли агент заархівує бесіду.", "Send a message to users who visit the website again after at least 24 hours. You can use the following merge fields and more: {coupon}, {user_name}. See the docs for more details.": "Надішліть повідомлення користувачам, які знову відвідують веб-сайт принаймні через 24 години. Ви можете використовувати такі поля злиття та інші: {coupon}, {user_name}. Додаткову інформацію дивіться в документації.", "Send a test agent notification email to verify email settings.": "Надішліть електронний лист із сповіщенням тестового агента, щоб перевірити налаштування електронної пошти.", "Send a test message to your Slack channel. This only tests the sending functionality of outgoing messages.": "Надішліть тестове повідомлення на свій канал Slack. Це лише перевіряє функціональність відправлення вихідних повідомлень.", "Send a test user notification email to verify email settings.": "Надішліть електронний лист із сповіщенням для тестового користувача, щоб перевірити налаштування електронної пошти.", "Send a text message to the provided phone number.": "Надішліть текстове повідомлення на вказаний номер телефону.", "Send a user email notification": "Надіслати сповіщення користувача електронною поштою", "Send a user text message notifcation": "Надіслати текстове повідомлення користувача", "Send a user text message notification": "Надіслати текстове повідомлення користувача", "Send an agent email notification": "Надіслати агенту сповіщення електронною поштою", "Send an agent text message notification": "Надіслати агенту текстове повідомлення", "Send an agent user text notification": "Надіслати текстове сповіщення користувача агента", "Send an email notification to the provided email address.": "Надішліть сповіщення електронною поштою на вказану електронну адресу.", "Send an email to an agent when a user replies and the agent is offline. An email is automatically sent to all agents for new conversations.": "Надсилайте електронного листа агенту, коли користувач відповідає, а агент знаходиться в автономному режимі. Електронна пошта автоматично надсилається всім агентам для нових розмов.", "Send an email to the user when a new conversation is created.": "Надсилати електронного листа користувачеві, коли створюється нова розмова.", "Send an email to the user when a new conversation or ticket is created": "Надішліть користувачеві електронний лист, коли буде створено нову бесіду або квиток", "Send an email to the user when an agent replies and the user is offline.": "Надсилайте електронний лист користувачеві, коли агент відповідає, а користувач перебуває в автономному режимі.", "Send email": "Відправити лист", "Send login details to the specified URL and allow access only if the response is positive.": "Надішліть дані для входу на вказану URL-адресу та дозвольте доступ, лише якщо відповідь буде позитивною.", "Send message": "Відправити повідомлення", "Send message to Slack": "Надіслати повідомлення в Slack", "Send message via enter button": "Надіслати повідомлення за допомогою кнопки введення", "Send text message": "Надіслати текстове повідомлення", "Send the message template to a WhatsApp number.": "Надішліть шаблон повідомлення на номер WhatsApp.", "Send the message via the ENTER keyboard button.": "Надішліть повідомлення за допомогою кнопки ENTER на клавіатурі.", "Send the user details of the registration form and email rich messages to Dialogflow.": "Надсилайте дані користувача реєстраційної форми та розширені повідомлення електронною поштою до Dialogflow.", "Send the WhatsApp order details to the URL provided.": "Надішліть деталі замовлення WhatsApp за вказаною URL-адресою.", "Send to user's email": "Надіслати на електронну пошту користувача", "Send transcript to user's email": "Надіслати стенограму на електронну пошту користувача", "Send user details": "Надіслати дані користувача", "Sender": "Відправник", "Sender email": "Надсилання електронної пошти", "Sender name": "Ім&#39;я відправника", "Sender number": "Номер відправника", "Sends a text message if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.": "Надсилає текстове повідомлення, якщо надіслати повідомлення WhatsApp не вдалося. Ви можете використовувати текст і такі поля злиття: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.", "Sends a WhatsApp Template notification if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {recipient_name}, {recipient_email}.": "Надсилає сповіщення шаблону WhatsApp, якщо надіслати повідомлення WhatsApp не вдалося. Ви можете використовувати текст і такі поля злиття: {conversation_url_parameter}, {recipient_name}, {recipient_email}.", "Service": "Обслуговування", "Service Worker path": "Service Worker шлях", "Service Worker URL": "Service Worker URL", "Set a dedicated Dialogflow agent for each department.": "Встановіть спеціального агента Dialogflow для кожного відділу.", "Set a dedicated OpenAI Assistants for each department.": "Встановіть спеціальних помічників OpenAI для кожного відділу.", "Set a dedicated Slack channel for each department.": "Встановіть окремий канал Slack для кожного відділу.", "Set a profile image for the chat bot.": "Встановіть зображення профілю для чат-бота.", "Set the articles panel title. Default is 'Help Center'.": "Встановіть назву панелі статей. За замовчуванням це «Довідковий центр».", "Set the avatar image shown next to the message. It must be a JPG image of 1024x1024px with a maximum size of 50KB.": "Встановіть зображення аватара, яке відображатиметься поруч із повідомленням. Це має бути зображення JPG розміром 1024x1024 пікселів і максимальний розмір 50 КБ.", "Set the chat language or translate it automatically to match the user language. Default is English.": "Встановіть мову чату або перекладіть її автоматично відповідно до мови користувача. За замовчуванням англійська.", "Set the currency symbol of the membership prices.": "Встановіть символ валюти цін членства.", "Set the currency symbol used by your system.": "Встановіть символ валюти, який використовується вашою системою.", "Set the default departments for all tickets. Enter the department ID.": "Установіть відділи за замовчуванням для всіх квитків. Введіть ідентифікатор відділу.", "Set the default email header that will be prepended to automated emails and direct emails.": "Встановіть заголовок електронної пошти за замовчуванням, який буде передувати автоматичним і прямим електронним листам.", "Set the default email signature that will be appended to automated emails and direct emails.": "Встановіть підпис електронної пошти за замовчуванням, який додаватиметься до автоматизованих листів і прямих листів.", "Set the default form to display if the registraion is required.": "Встановіть форму за замовчуванням для відображення, якщо потрібна реєстрація.", "Set the default name to use for conversations without a name.": "Встановіть ім’я за замовчуванням для розмов без імені.", "Set the default notifications icon. The icon will be used as a profile image if the user doesn't have one.": "Установіть піктограму сповіщень за замовчуванням. Значок буде використовуватися як зображення профілю, якщо його у користувача немає.", "Set the default office hours for when agents are shown as available. These settings are also used for all other settings that rely on office hours.": "Установіть робочі години за замовчуванням, коли агенти відображаються як доступні. Ці налаштування також використовуються для всіх інших налаштувань, які залежать від робочого часу.", "Set the default username to use in bot messages and emails when the user doesn't have a name.": "Встановіть ім’я користувача за замовчуванням для використання в повідомленнях ботів і електронних листах, коли користувач не має імені.", "Set the header appearance.": "Налаштувати вигляд заголовка.", "Set the maximum height of the tickets panel.": "Встановіть максимальну висоту панелі квитків.", "Set the multilingual plugin you're using, or leave it disabled if your site uses only one language.": "Налаштуйте багатомовний плагін, який ви використовуєте, або залиште його вимкненим, якщо ваш сайт використовує лише одну мову.", "Set the offline status automatically when the agent or admin remains inactive in the admin area for at least 10 minutes.": "Автоматично встановлюйте статус офлайн, якщо агент або адміністратор залишаються неактивними в області адміністратора принаймні 10 хвилин.", "Set the position of the chat widget.": "Встановіть положення віджета чату.", "Set the primary color of the admin area.": "Встановіть основний колір адміністративної області.", "Set the primary color of the chat widget.": "Встановіть основний колір віджета чату.", "Set the secondary color of the admin area.": "Встановіть додатковий колір адміністративної області.", "Set the secondary color of the chat widget.": "Встановіть додатковий колір віджета чату.", "Set the tertiary color of the chat widget.": "Установіть третинний колір віджета чату.", "Set the title of the administration area.": "Встановіть назву адміністративної області.", "Set the title of the conversations panel.": "Встановіть назву панелі розмов.", "Set the UTC offset of the office hours timetable. The correct value can be negative, and it's generated automatically once you click this input field, if it's empty.": "Встановіть зміщення UTC у графіку робочого часу. Правильне значення може бути від’ємним, і воно генерується автоматично, коли ви клацнете це поле введення, якщо воно порожнє.", "Set which actions to allow agents.": "Установіть, які дії дозволити агентам.", "Set which actions to allow supervisors.": "Встановіть, які дії дозволити керівникам.", "Set which user details to send to the main channel. Add comma separated values.": "Установіть, які дані користувача надсилати на основний канал. Додайте значення, розділені комами.", "Settings area": "Область налаштувань", "settings information": "інформацію про налаштування", "Shop": "Мага<PERSON>ин", "Show": "Показати", "Show a browser tab notification when a new message is received.": "Показувати сповіщення вкладки браузера, коли надходить нове повідомлення.", "Show a desktop notification when a new message is received.": "Показувати сповіщення на робочому столі, коли надходить нове повідомлення.", "Show a notification and play a sound when a new user is online.": "Показувати сповіщення та відтворювати звук, коли новий користувач перебуває в мережі.", "Show a pop-up notification to all users.": "Показати спливаюче сповіщення для всіх користувачів.", "Show profile images": "Показати зображення профілю", "Show sender's name": "Показати ім&#39;я відправника", "Show the agents menu in the dashboard and force the user to choose an agent to start a conversation.": "Покажіть меню агентів на інформаційній панелі та змусьте користувача вибрати агента, щоб почати розмову.", "Show the articles panel on the chat dashboard.": "Показати панель статей на інформаційній панелі чату.", "Show the categories instead of the articles list.": "Показати категорії замість списку статей.", "Show the follow up message when a visitor add an item to the cart. The message is sent only if the user has not provided an email yet.": "Показувати наступне повідомлення, коли відвідувач додасть товар у кошик. Повідомлення надсилається лише в тому випадку, якщо користувач ще не вказав електронний лист.", "Show the list of all Slack channels.": "Показати список усіх каналі<PERSON> Slack.", "Show the profile image of agents and users within the conversation.": "Показувати зображення профілю агентів і користувачів у розмові.", "Show the sender's name in every message.": "Показувати ім’я відправника в кожному повідомленні.", "Single label": "Одна етикетка", "Single phone country code": "Єдиний телефонний код країни", "Site key": "<PERSON><PERSON><PERSON><PERSON> сайту", "Slug": "Слизняк", "Social share message": "Повідомлення в соціальних мережах", "Sort conversations by date": "Сортувати бесіди за датою", "Sound": "Звук", "Sound settings": "Налаштування звуку", "Sounds": "Звуки", "Sounds - admin": "Звуки - адмін", "Source links": "Посилання на джерела", "Speech recognition": "Розпізнавання мови", "Spelling correction": "Виправлення орфографії", "Starred tag": "Тег із зірочкою", "Start importing": "Почніть імпорт", "Store name": "Назва магазину", "Subject": "Тема", "Subscribe": "Підпишіться", "Subscribe users to your preferred newsletter service when they provide an email.": "Підпишіться користувачів на бажану службу розсилки новин, коли вони надішлють електронний лист.", "Subtract the offset value from the height value.": "Від значення висоти відніміть значення зміщення.", "Success message": "Повідомлення про успіх", "Supervisors": "Нагляда<PERSON>і", "Support Board path": "Шлях до ради підтримки", "Sync admin and staff accounts with Support Board. Staff users will be registered as agents, while admins as admins. Only new users will be imported.": "Синхронізуйте облікові записи адміністратора та персоналу з Радою підтримки. Персональні користувачі будуть зареєстровані як агенти, а адміністратори як адміністратори. Будуть імпортовані лише нові користувачі.", "Sync all contacts of all clients with Support Board. Only new contacts will be imported.": "Синхронізуйте всі контакти всіх клієнтів з дошкою підтримки. Будуть імпортовані лише нові контакти.", "Sync all users with Support Board. Only new users will be imported.": "Синхронізуйте всіх користувачів з дошкою підтримки. Будуть імпортовані лише нові користувачі.", "Sync all WordPress users with Support Board. Only new users will be imported.": "Синхронізуйте всіх користувачів WordPress з дошкою підтримки. Будуть імпортовані лише нові користувачі.", "Sync knowledge base articles with Support Board. Only new articles will be imported.": "Синхронізуйте статті бази знань з дошкою підтримки. Імпортуватимуться лише нові статті.", "Sync mode": "Режим синхронізації", "Synchronization": "Синхронізація", "Synchronize": "Синхронізувати", "Synchronize customers, enable ticket and chat support for subscribers only, view subscription plans in the admin area.": "Синхронізуйте клієнтів, увімкніть підтримку квитків і чатів лише для передплатників, переглядайте плани підписки в області адміністратора.", "Synchronize emails": "Синхронізувати електронні листи", "Synchronize Entities": "Синхронізувати Entities", "Synchronize Entities now": "Синхронізувати Entities зараз", "Synchronize now": "Синхронізувати зараз", "Synchronize users": "Синхронізувати користувачів", "Synchronize your customers in real-time, chat with them and boost their engagement, or provide a better and faster support.": "Синхронізуйте своїх клієнтів у режимі реального часу, спілкуйтеся з ними та підвищуйте їхню зацікавленість або забезпечуйте кращу та швидшу підтримку.", "Synchronize your Messenger and Instagram accounts.": "Синхронізуйте облікові записи Messenger та Instagram.", "Synchronize your Perfex customers in real-time and let them contact you via chat! View profile details, proactively engage them, and more.": "Синхронізуйте своїх клієнтів Perfex в режимі реального часу і дозвольте їм зв’язатися з вами через чат! Переглядайте деталі профілю, активно залучайте їх тощо.", "Synchronize your WhatsApp Cloud API account.": "Синхронізуйте обліковий запис WhatsApp Cloud API.", "System requirements": "Системні вимоги", "Tags": "Теги", "Tags settings": "Налаштування тегів", "Template default language": "Мова шаблону за замовчуванням", "Template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "Шаблон електронного листа, який надсилається користувачеві, коли агент відповідає. Ви можете використовувати текст, HTML та такі поля злиття: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Template for the email sent to the user when a new conversation is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}.": "Шаб<PERSON>он електронного листа, який надсилається користувачеві під час створення нової розмови. Ви можете використовувати текст, HTML і такі поля злиття: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}.", "Template for the email sent to the user when a new conversation or ticket is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}.": "Ша<PERSON><PERSON>он електронного листа, надісланого користувачеві під час створення нової бесіди або квитка. Ви можете використовувати текст, HTML та такі поля злиття: {conversation_url_parameter}, {user_name}, {message}, {attachments}.", "Template languages": "Мови шаблонів", "Template name": "Назва шаблону", "Template of the admin notification email. You can use text, HTML, and the following merge field and more: {carts}. Enter the email you want to send notifications to in the email address field.": "Шаблон електронного листа з повідомленням адміністратора. Ви можете використовувати текст, HTML, наступне поле злиття та багато іншого: {carts}. Введіть адресу електронної пошти, на яку ви хочете надсилати сповіщення, у поле адреси електронної пошти.", "Template of the email sent to the customer after a product has been removed from the cart. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "Шаблон електронного листа, надісланого клієнту після видалення товару з кошика. Ви можете використовувати текст, HTML, такі поля злиття та багато іншого: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the first notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "Шаблон першого електронного листа-повідомлення. Ви можете використовувати текст, HTML, такі поля злиття та багато іншого: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the second notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "Шаблон другого електронного листа-повідомлення. Ви можете використовувати текст, HTML, такі поля злиття та багато іншого: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the waiting list notification email. You can use text, HTML, and the following merge field and more: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}.": "Шаблон повідомлення електронної пошти про лист очікування. Ви можете використовувати текст, HTML, таке поле злиття та багато іншого: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}.", "Terms link": "Посилання на умови", "Tertiary color": "Третин<PERSON>ий колір", "Test Slack": "Тесту<PERSON><PERSON><PERSON>", "Test template": "Тестовий шаблон", "Text": "Текст", "Text message fallback": "Резервне текстове повідомлення", "Text message notifications": "Текстові повідомлення", "Text messages": "Текстові повідомлення", "The product is not in the cart.": "Товару немає в кошику.", "The workspace name you are using to synchronize Slack.": "Ім&#39;я workspace, яке ви використовуєте для синхронізації Slack.", "This is your main Slack channel ID, which is usually the #general channel. You will get this code by completing the Slack synchronization.": "Це ваш основний ідентифікатор канал<PERSON>, який зазвичай є #general каналом. Ви отримаєте цей код, завершивши синхронізацію Slack.", "This returns the Support Board path of your server.": "Це повертає Support Board шлях вашого сервера.", "Ticket custom fields": "Спеціальні поля квитків", "Ticket email": "Електронна пошта про квитки", "Ticket field names": "Назви полів квитків", "Ticket fields": "Поля квитків", "Ticket only": "Тільки квиток", "Ticket products selector": "Селектор квиткових продуктів", "Title": "Назва", "Top": "Топ", "Top bar": "Верхня панель", "Training via cron job": "Навчання через роботу cron", "Transcript": "Стенограма", "Transcript settings.": "Налаштування транскрипції.", "Trigger": "Тригер", "Trigger the Dialogflow Welcome Intent for new visitors when the welcome message is active.": "Активуйте діалоговий потік Welcome Intent для нових відвідувачів, коли вітальне повідомлення активне.", "Troubleshoot": "Усунення несправностей", "Troubleshoot problems": "Усунення несправностей", "Twilio settings": "Налаштування Twilio", "Twilio template": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Unsubscribe": "Відписатися", "Upload attachments to Amazon S3.": "Завантажте вкладені файли в Amazon S3.", "Usage Limit": "Ліміт використання", "Use this option to change the PWA icon. See the docs for more details.": "Використовуйте цей параметр, щоб змінити піктограму PWA. Додаткову інформацію дивіться в документах.", "User details": "Дані користувача", "User details in success message": "Інформація про користувача в повідомленні про успіх", "User email notifications": "Сповіщення користувача електронною поштою", "User login form information.": "Інформація про форму входу користувача.", "User message template": "Шаблон повідомлення користувача", "User name as title": "Ім&#39;я користувача як заголовок", "User notification email": "Сповіщення користувача електронною поштою", "User registration form information.": "Інформація про форму реєстрації користувача.", "User roles": "Ролі користувачів", "User system": "Система користувача", "Username": "Ім&#39;я користувача", "Users and agents": "Користувачі та агенти", "Users area": "<PERSON><PERSON>на користува<PERSON>ів", "Users only": "Лише для користувачів", "Users table additional columns": "Користувачі наводять додаткові стовпці", "UTC offset": "Зміщення UTC", "Variables": "Змінн<PERSON>", "View channels": "Перегля<PERSON> каналів", "View unassigned conversations": "Переглянути непризначені бесіди", "Visibility": "Наочність", "Visitor default name": "Ім’я відвідувача за замовчуванням", "Visitor name prefix": "Префікс імені відвідувача", "Volume": "Обсяг", "Volume - admin": "Обсяг - адмін", "Waiting list": "Лист очікування", "Waiting list - Email": "Лист очікування - Email", "Webhook URL": "URL-адреса вебхука", "Webhooks": "Веб-хуки", "Webhooks are information sent in background to a unique URL defined by you when something happens.": "Вебхуки – це інформація, яка надсилається у фоновому режимі на унікальну URL-адресу, визначену вами, коли щось відбувається.", "Website": "Веб-сайт", "WeChat settings": "Налаштування WeChat", "Welcome message": "Вітальне повідомлення", "Whmcs admin URL": "URL-адреса адміністратора Whmcs", "Whmcs admin URL. Ex. https://example.com/whmcs/admin/": "URL-адреса адміністратора Whmcs. Напр. https://example.com/whmcs/admin/", "WordPress registration": "Реєстрація WordPress", "Yes, we ship in": "Так, ми відправляємо", "You haven't placed an order yet.": "Ви ще не зробили замовлення.", "You will get this code by completing the Dialogflow synchronization.": "Ви отримаєте цей код, завершивши синхронізацію Dialogflow.", "You will get this code by completing the Slack synchronization.": "Ви отримаєте цей код, завершивши синхронізацію Slack.", "You will get this information by completing the synchronization.": "Ви отримаєте цю інформацію, завершивши синхронізацію.", "Your cart is empty.": "Ваш кошик порожній.", "Your turn message": "Повідомлення про вашу чергу", "Your username": "Ваше ім&#39;я користувача", "Your WhatsApp catalogue details.": "Деталі вашого каталогу WhatsApp.", "Zendesk settings": "Налаштування Zendesk", "Smart Reply": "Розумна відповідь", "This returns your Support Board URL.": "Це повертає вашу URL-адресу Support Board.", "Update conversation department": "Оновити розмовний відділ"}
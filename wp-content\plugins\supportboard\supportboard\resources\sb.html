<div class="sb-main sb-admin-box sb-hide">
    <div class="sb-input">
        <span>Enter your Envato Purchase Code</span>
        <input type="text" />
    </div>
    <p>
        Please enter your Envato Purchase Code to enter the administration area. Restricted to one live domain, a testing secondary domain, or localhost.
        Save the code in Settings > Miscellaneous > Envato Purchase Code to to hide this message permanently.
        <a class="btn-text" href="https://help.market.envato.com/hc/en-us/articles/202822600-Where-Is-My-Purchase-Code-" rel="nofollow" target="_blank">Where is my purchase code?</a>
    </p>
    <a href="#" class="sb-btn">Activate</a>
</div>
<style>
    .sb-input {
        display: block;
    }

    .sb-input > span {
        display: block;
        width: auto;
        font-size: 17px;
        margin-bottom: 15px;
    }

    p {
        color: #566069;
        font-size: 15px;
        line-height: 25px;
    }

    p a {
        color: #566069;
    }
</style>
<script>
    var SB_DISABLED = true;
    (function ($) {
        let button;
        sbvpc('auto');
        $(document).ready(function () {
            button = $('.sb-btn');
            $(button).on('click', function () {
                sbvpc($.trim($('.sb-input input').val()));
            });
        });

        function sbvpc(code) {
            if (!code || $(button).hasClass('sb-loading')) {
                return;
            }
            $(button).addClass('sb-loading');
            $.ajax({
                method: 'POST',
                url: SB_AJAX_URL,
                data: { function: 'ver' + 'ification-cookie', code: code, domain: SB_URL }
            }).done((response) => {
                if (typeof response === 'string' || response instanceof String) {
                    response = JSON.parse(response);
                }
                if (response[1][0]) {
                    SBF.cookie('SA_' + 'VGCKMENS', response[1][1], 60, 'set');
                    location.reload();
                } else {
                    if (response[1][1]) {
                        alert(response[1][1]);
                    }
                    $(button).removeClass('sb-loading');
                    $('.sb-hide').removeClass('sb-hide');
                }
            });
        }
    }(jQuery));
</script>
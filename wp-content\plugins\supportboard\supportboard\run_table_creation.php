
<?php

/**
 * Manual script to create sb_usages table
 * Run this file directly to create the table
 */

// Include WordPress
$wp_load_path = dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';
if (file_exists($wp_load_path)) {
    require_once($wp_load_path);
} else {
    die('WordPress not found. Please ensure the path is correct.');
}

// Include the table creation functions
require_once(dirname(__FILE__) . '/include/create_usages_table.php');

echo "<h2>SupportBoard Usages Table Creation Script</h2>\n";

// Run the table creation
echo "<p>Creating sb_usages table...</p>\n";

$result = sb_create_usages_table();

if ($result) {
    echo "<p style='color: green;'><strong>Success!</strong> Table created successfully.</p>\n";
    
    // Add some sample data for testing
    echo "<p>Adding sample data...</p>\n";
    
    $sample_data = array(
        array('openai', 150, 75, 0),
        array('openai', 200, 100, 25),
        array('openai', 180, 90, 10),
        array('openai', 220, 110, 30),
        array('openai', 160, 80, 0)
    );
    
    foreach ($sample_data as $data) {
        $insert_result = sb_add_usage_record($data[0], $data[1], $data[2], $data[3]);
        if ($insert_result) {
            echo "<p>Sample record added with ID: {$insert_result}</p>\n";
        }
    }
    
    echo "<p style='color: green;'><strong>Complete!</strong> You can now access the Reports page in the SupportBoard admin.</p>\n";
    echo "<p><a href='" . admin_url('admin.php?page=sb-reports') . "'>View Reports</a></p>\n";
    
} else {
    echo "<p style='color: red;'><strong>Error!</strong> Failed to create table. Check error logs for details.</p>\n";
}

// Show current table structure
global $wpdb;
$table_name = $wpdb->prefix . 'sb_usages';
$columns = $wpdb->get_results("DESCRIBE $table_name");

if ($columns) {
    echo "<h3>Table Structure:</h3>\n";
    echo "<table border='1' cellpadding='5' cellspacing='0'>\n";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column->Field}</td>";
        echo "<td>{$column->Type}</td>";
        echo "<td>{$column->Null}</td>";
        echo "<td>{$column->Key}</td>";
        echo "<td>{$column->Default}</td>";
        echo "<td>{$column->Extra}</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
}

?>
